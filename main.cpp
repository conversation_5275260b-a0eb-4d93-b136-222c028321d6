#include "utils/logger.h"
#include "ThreadManager/infer_server.h"
#include "ThreadManager/web_server.h"
#include "ThreadManager/lkm_server.h"
#include "ThreadManager/blkm_server.h"
#include "utils/gmodels.h"
#include "backend/blsql/mysql_db.h"
#include "utils/wolf240hz_patch.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <atomic>

// 信号处理函数
void signalHandler(int signum) {
    LOG_INFO("收到信号 {}，准备退出程序...", signum);
    // 设置全局退出标志
    thread_control::g_thread_running = false;
}

int main() {
    // 首先应用240Hz补丁
    // std::cout << "正在应用240Hz显示器补丁..." << std::endl;
    // try {
    //     apply_wolf240hz_patch("/dev/video0");
    //     std::cout << "240Hz补丁应用完成，等待30秒后启动系统..." << std::endl;
    //     std::this_thread::sleep_for(std::chrono::seconds(30));
    // } catch (const std::exception& e) {
    //     std::cerr << "应用240Hz补丁时出错: " << e.what() << std::endl;
    //     std::cout << "继续启动系统..." << std::endl;
    // }
    
    // 设置线程运行标志
    thread_control::g_thread_running = true;
    
    // 注册信号处理
    signal(SIGINT, signalHandler);  // Ctrl+C
    signal(SIGTERM, signalHandler); // 终止信号
    
    LOG_INFO("BL系统启动中...");

    // 初始化WSQ全局队列
    LOG_INFO("正在初始化WSQ流水线队列...");
    wsq_models::g_command_queue = std::make_unique<wsq_models::CommandWSQ>(1024);
    LOG_INFO("WSQ流水线队列初始化完成，容量: 1024");

    // 初始化并启动推理服务
    LOG_INFO("正在初始化推理服务...");
    aibox::InferServer inferServer;
    try {
        inferServer.Run();
        LOG_INFO("推理服务启动成功");
    } catch (const std::exception& e) {
        LOG_ERROR("推理服务启动失败: {}", e.what());
        thread_control::g_thread_running = false;
        // 确保程序退出
        LOG_INFO("由于推理服务启动失败，程序将强制退出");
        // 使用exit()强制退出程序，不等待其他线程
        exit(1);
    }

    // 短暂休眠确保服务正常启动
    std::this_thread::sleep_for(std::chrono::seconds(1));

    // 初始化并启动LKM服务
    LOG_INFO("正在初始化LKM服务...");
    if (!thread_manager::startLkmThreads()) {
        LOG_ERROR("LKM服务启动失败");
        thread_control::g_thread_running = false;
        return 1;
    }
    LOG_INFO("LKM服务启动成功");
    
    // 初始化MySQL数据库连接
    LOG_INFO("正在初始化数据库连接...");
    if (!blweb::MySQLDB::getInstance().init()) {
        LOG_ERROR("数据库连接初始化失败: {}", blweb::MySQLDB::getInstance().getLastError());
        LOG_WARNING("系统将在没有数据库支持的情况下继续运行，某些功能可能受限");
    } else {
        LOG_INFO("数据库连接初始化成功");
        webui::header::databaseStatus = true;  // 更新数据库连接状态
    }

    // 初始化并启动BLKM卡密验证服务
    LOG_INFO("正在初始化卡密验证服务...");
    if (!thread_manager::initBlkmServer()) {
        LOG_ERROR("卡密验证服务初始化失败");
        thread_control::g_thread_running = false;
        thread_manager::stopLkmThreads();
        return 1;
    }
    LOG_INFO("当前卡密: {}", jfkm_config::g_card_key);
    LOG_INFO("应用标识: {}", jfkm_config::g_app_flag);
    LOG_INFO("标准版API地址: {}", jfkm_config::g_api_host_standard);
    LOG_INFO("Pro版API地址: {}", jfkm_config::g_api_host_pro);
    
    // 创建并启动卡密验证线程
    std::unique_ptr<std::thread> verify_thread = std::make_unique<std::thread>(thread_manager::cardVerifyThreadFunc);
    LOG_INFO("卡密验证服务启动成功");

    // 初始化并启动Web服务器
    LOG_INFO("正在初始化Web服务器...");
    WebServer& webServer = WebServer::getInstance();
    if (!webServer.initialize()) {
        LOG_ERROR("Web服务器初始化失败");
        thread_control::g_thread_running = false;
        thread_manager::stopLkmThreads();
        return 1;
    }
    
    if (!webServer.start()) {
        LOG_ERROR("Web服务器启动失败");
        thread_control::g_thread_running = false;
        thread_manager::stopLkmThreads();
        return 1;
    }
    LOG_INFO("Web服务器启动成功");
    
    LOG_INFO("BL系统启动完成，所有服务运行中");
    std::cout << "BL系统已启动，按Ctrl+C退出程序" << std::endl;
    
    // 主循环，等待退出信号
    while (thread_control::g_thread_running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        // 更新页头的卡密状态
        webui::header::cardKeyStatus = (jfkm_config::g_auth_status == 1);
    }
    
    // 按照依赖关系的逆序关闭服务
    LOG_INFO("正在关闭所有服务...");
    
    // 确保先关闭推理服务，因为它可能依赖于全局变量
    LOG_INFO("关闭推理服务...");
    // 推理服务已经在signalHandler中通过设置thread_control::g_thread_running = false触发了停止
    // 我们只需要等待它的线程完成即可，这会在inferServer的析构函数中自动处理
    
    LOG_INFO("关闭Web服务器...");
    webServer.stop();
    
    LOG_INFO("关闭LKM服务...");
    thread_manager::stopLkmThreads();
    
    // 关闭数据库连接
    LOG_INFO("关闭数据库连接...");
    blweb::MySQLDB::getInstance().close();
    
    // 等待卡密验证线程结束
    LOG_INFO("关闭卡密验证服务...");
    if (verify_thread && verify_thread->joinable()) {
        verify_thread->join();
        verify_thread.reset();
    }
    
    LOG_INFO("BL系统已正常关闭");
    return 0;
} 