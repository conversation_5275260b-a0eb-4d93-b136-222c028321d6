# 算法模块CMake配置
# 本文件被主CMakeLists.txt包含

# 定义算法模块源文件
set(ALGORITHM_SOURCES
    ${CMAKE_SOURCE_DIR}/algorithm/pid_controller.cpp
    ${CMAKE_SOURCE_DIR}/algorithm/MouseFov.cpp
)

# 定义算法模块头文件
set(ALGORITHM_HEADERS
    ${CMAKE_SOURCE_DIR}/algorithm/pid_controller.h
    ${CMAKE_SOURCE_DIR}/algorithm/MouseFov.hpp
)

# 创建算法静态库
add_library(algorithm_lib STATIC ${ALGORITHM_SOURCES})
target_include_directories(algorithm_lib PUBLIC
    ${CMAKE_SOURCE_DIR}/algorithm
)
target_link_libraries(algorithm_lib PUBLIC
    ${OpenCV_LIBS}
    Threads::Threads
    ${ATOMIC_LIBRARY}
)

# 导出变量以便主CMakeLists.txt使用
set(ALGORITHM_INCLUDE_DIRS
    ${CMAKE_SOURCE_DIR}/algorithm
)

message(STATUS "算法模块配置完成，将作为algorithm_lib静态库使用") 