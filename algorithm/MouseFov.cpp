#include "MouseFov.hpp"
// 移除对keymouse.h的依赖，只包含必要的头文件
// #include "../lkm/keymouse.h"
#include <chrono>
#include <thread>
#include <random>
#include "../utils/gmodels.h"
#include "../utils/logger.h"  // 添加日志头文件

// 初始化静态成员
MouseFov* MouseFov::instance = nullptr;

/**
 * @brief 构造函数
 */
MouseFov::MouseFov() {
    LOG_INFO("MouseFov实例创建");
}

/**
 * @brief 析构函数
 */
MouseFov::~MouseFov() {
    LOG_INFO("MouseFov实例销毁");
}

/**
 * @brief 获取单例实例
 * @return MouseFov& 单例实例的引用
 */
MouseFov& MouseFov::getInstance() {
    if (instance == nullptr) {
        instance = new MouseFov();
    }
    return *instance;
}

/**
 * @brief 以目标为基准的FOV测量方法
 * @param distance 目标距离
 * @param targetX 目标X坐标
 * @param targetY 目标Y坐标
 * @return bool 测量结果
 */
bool MouseFov::measureFovWithTarget(double distance, double targetX, double targetY) {
    // 如果目标距离不为0，执行鼠标移动
    if (distance > 1.5) {
        // 使用全局变量中的屏幕中心点
        double delta_x = targetX - webui::header::width / 2.0;
        double delta_y = targetY - webui::header::height / 2.0;
        LOG_INFO("目标距离: {}, 偏移量: dx={}, dy={}", distance, delta_x, delta_y);
        return false;  // 返回false表示测量暂未完成
    } else {
        LOG_INFO("目标距离已足够小，可以开始FOV测量: {}", distance);
        return true;  // 返回true表示测量可以继续
    }
}

/**
 * @brief 全局函数版本，为了兼容现有代码
 * @param distance 目标距离
 * @param targetX 目标X坐标
 * @param targetY 目标Y坐标
 * @return bool 测量结果
 */
bool measureFovWithTarget(double distance, double targetX, double targetY) {
    return MouseFov::getInstance().measureFovWithTarget(distance, targetX, targetY);
}
