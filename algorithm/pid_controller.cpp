#include "pid_controller.h"
#include <cmath>
#include <iostream>
#include "../utils/gmodels.h"
#include "../utils/logger.h"

namespace infer {
namespace algorithm {

// 构造函数
PidController::PidController(double kp, double ki, double kd, 
                            double deadzone, double integralLimit,
                            double reboundStrength, double maxMovement)
    : prevErrorX_(0.0), integralX_(0.0), remainderX_(0.0), lastErrorDeltaX_(0.0),
      prevErrorY_(0.0), integralY_(0.0), remainderY_(0.0), lastErrorDeltaY_(0.0),
      kp_(kp / 10.0), ki_(ki / 1.0), kd_(kd / 100.0), 
      deadzone_(deadzone), integralLimit_(integralLimit),
      reboundStrength_(reboundStrength), maxMovement_(maxMovement), 
      movementCompensation_(0.15) {}

// 重置状态
void PidController::reset() {
    prevErrorX_ = 0.0;
    integralX_ = 0.0;
    remainderX_ = 0.0;
    lastErrorDeltaX_ = 0.0;
    
    prevErrorY_ = 0.0;
    integralY_ = 0.0;
    remainderY_ = 0.0;
    lastErrorDeltaY_ = 0.0;
}

// 设置PID参数
void PidController::setPidParameters(double kp, double ki, double kd, 
                                    double deadzone, double integralLimit,
                                    double reboundStrength, double maxMovement) {
    if (kp >= 0.0) kp_ = kp / 10.0;
    if (ki >= 0.0) ki_ = ki / 1.0;
    if (kd >= 0.0) kd_ = kd / 100.0;
    if (deadzone >= 0.0) deadzone_ = deadzone;
    if (integralLimit >= 0.0) integralLimit_ = integralLimit * 1.0;
    if (reboundStrength >= 0.0 && reboundStrength <= 1.0) reboundStrength_ = reboundStrength;
    if (maxMovement >= 0.0) maxMovement_ = maxMovement;
    
    // 每1000次调用打印一次参数设置信息
    static int call_count = 0;
    // if (++call_count % 1000 == 0) {
    //     LOG_INFO("PID参数设置: kp={:.3f}(原值{:.3f}), ki={:.3f}(原值{:.3f}), kd={:.3f}(原值{:.3f}), deadzone={:.3f}, integralLimit={:.3f}, reboundStrength={:.3f}, maxMovement={:.3f}",
    //             kp_, kp, ki_, ki, kd_, kd, deadzone_, integralLimit_, reboundStrength_, maxMovement_);
    // }
}

// 计算PID控制器输出
double PidController::calculateOutput(double pTerm, double iTerm, double dTerm, double& remainder) {
    // 计算总输出 (P + I + D)
    double output = pTerm + iTerm + dTerm;
    
    // 应用限幅
    if (output > maxMovement_) {
        output = maxMovement_;
    } else if (output < -maxMovement_) {
        output = -maxMovement_;
    }
    
    // 将输出转换为整数，但保留小数部分以便下次使用
    int outputInt = static_cast<int>(output);
    remainder += output - outputInt;
    
    // 如果累积的小数部分超过1，则应用它
    int extraMovement = static_cast<int>(remainder);
    outputInt += extraMovement;
    remainder -= extraMovement;
    
    return static_cast<double>(outputInt);
}

// 更新PID控制器
std::pair<int, int> PidController::update(double errorX, double errorY, double deltaTime) {
    // -------------------处理X方向-------------------
    // 应用死区
    double effectiveErrorX = (std::abs(errorX) < deadzone_) ? 0.0 : errorX;
    
    // 误差变化率(一阶导数)
    double errorDeltaX = (effectiveErrorX - prevErrorX_) / deltaTime;
    
    // 方向更改检测
    bool directionChangedX = (prevErrorX_ != 0 && ((prevErrorX_ > 0 && effectiveErrorX < 0) || 
                                                  (prevErrorX_ < 0 && effectiveErrorX > 0)));
    
    // 如果方向改变，根据回弹强度衰减积分项以减少回弹
    if (directionChangedX) {
        double originalIntegralX = integralX_;
        // 当reboundStrength为1.0时不回弹（不衰减积分），小于1.0时才进行衰减
        if (reboundStrength_ < 1.0) {
            integralX_ *= reboundStrength_;
        }
        // 添加移动补偿
        double compensationX = errorX * movementCompensation_;
        effectiveErrorX += compensationX;
        
        // 显示回弹强度的应用效果（每100次打印一次）
        static int rebound_count_x = 0;
        // if (++rebound_count_x % 100 == 0) {
        //     LOG_DEBUG("PID - X轴方向改变: 原始积分={:.3f}, 衰减后={:.3f}, 回弹强度={:.3f}, 移动补偿={:.3f}", 
        //              originalIntegralX, integralX_, reboundStrength_, compensationX);
        // }
    }
    
    // 更新积分项，但仅在目标在移动/有误差且不在死区时
    if (effectiveErrorX != 0) {
        integralX_ += effectiveErrorX * deltaTime;
    }
    
    // 积分限幅
    if (integralLimit_ > 0.0) {
        double originalIntegralX = integralX_;
        if (integralX_ > integralLimit_) {
            integralX_ = integralLimit_;
        } else if (integralX_ < -integralLimit_) {
            integralX_ = -integralLimit_;
        }
        
        // 显示积分限制的应用效果（仅在实际限制时打印）
        if (originalIntegralX != integralX_) {
            static int limit_count_x = 0;
            // if (++limit_count_x % 50 == 0) {  // 每50次限制打印一次
            //     LOG_DEBUG("PID - X轴积分限制应用: 原始={:.3f}, 限制后={:.3f}, 限制值={:.3f}", 
            //              originalIntegralX, integralX_, integralLimit_);
            // }
        }
    }
    
    // 计算PID项
    double pTermX = kp_ * effectiveErrorX;
    double iTermX = ki_ * integralX_;
    double dTermX = kd_ * errorDeltaX;
    
    // 计算输出
    double outX = calculateOutput(pTermX, iTermX, dTermX, remainderX_);
    
    // 更新上一个误差
    prevErrorX_ = effectiveErrorX;
    lastErrorDeltaX_ = errorDeltaX;
    
    // -------------------处理Y方向-------------------
    // 应用死区
    double effectiveErrorY = (std::abs(errorY) < deadzone_) ? 0.0 : errorY;
    
    // 误差变化率(一阶导数)
    double errorDeltaY = (effectiveErrorY - prevErrorY_) / deltaTime;
    
    // 方向更改检测
    bool directionChangedY = (prevErrorY_ != 0 && ((prevErrorY_ > 0 && effectiveErrorY < 0) || 
                                                 (prevErrorY_ < 0 && effectiveErrorY > 0)));
    
    // 如果方向改变，根据回弹强度衰减积分项以减少回弹
    if (directionChangedY) {
        double originalIntegralY = integralY_;
        // 当reboundStrength为1.0时不回弹（不衰减积分），小于1.0时才进行衰减
        if (reboundStrength_ < 1.0) {
            integralY_ *= reboundStrength_;
        }
        // 添加移动补偿
        double compensationY = errorY * movementCompensation_;
        effectiveErrorY += compensationY;
        
        // 显示回弹强度的应用效果（每100次打印一次）
        static int rebound_count_y = 0;
        // if (++rebound_count_y % 100 == 0) {
        //     LOG_DEBUG("PID - Y轴方向改变: 原始积分={:.3f}, 衰减后={:.3f}, 回弹强度={:.3f}, 移动补偿={:.3f}", 
        //              originalIntegralY, integralY_, reboundStrength_, compensationY);
        // }
    }
    
    // 更新积分项，但仅在目标在移动/有误差且不在死区时
    if (effectiveErrorY != 0) {
        integralY_ += effectiveErrorY * deltaTime;
    }
    
    // 积分限幅
    if (integralLimit_ > 0.0) {
        double originalIntegralY = integralY_;
        if (integralY_ > integralLimit_) {
            integralY_ = integralLimit_;
        } else if (integralY_ < -integralLimit_) {
            integralY_ = -integralLimit_;
        }
        
        // 显示积分限制的应用效果（仅在实际限制时打印）
        if (originalIntegralY != integralY_) {
            static int limit_count_y = 0;
            // if (++limit_count_y % 50 == 0) {  // 每50次限制打印一次
            //     LOG_DEBUG("PID - Y轴积分限制应用: 原始={:.3f}, 限制后={:.3f}, 限制值={:.3f}", 
            //              originalIntegralY, integralY_, integralLimit_);
            // }
        }
    }
    
    // 计算Y轴PID项 - 应用Y轴系数到PID参数上
    double pTermY = (kp_ * webui::pid::g_y_axis_factor) * effectiveErrorY;
    double iTermY = (ki_ * webui::pid::g_y_axis_factor) * integralY_;
    double dTermY = (kd_ * webui::pid::g_y_axis_factor) * errorDeltaY;
    
    // 计算输出
    double outY = calculateOutput(pTermY, iTermY, dTermY, remainderY_);
    
    // 更新上一个误差
    prevErrorY_ = effectiveErrorY;
    lastErrorDeltaY_ = errorDeltaY;
    
    // 输出调试信息（每500次打印一次详细信息）
    static int debug_output_count = 0;
    // if (++debug_output_count % 500 == 0) {
    //     LOG_DEBUG("PID - 误差X: {:.3f}, Y: {:.3f}", effectiveErrorX, effectiveErrorY);
    //     LOG_DEBUG("PID - P项X: {:.3f}, I项X: {:.3f}, D项X: {:.3f}", pTermX, iTermX, dTermX);
    //     LOG_DEBUG("PID - Y轴系数: {:.3f}, Y轴Kp: {:.3f}, Y轴Ki: {:.3f}, Y轴Kd: {:.3f}", 
    //               webui::pid::g_y_axis_factor, kp_ * webui::pid::g_y_axis_factor, 
    //               ki_ * webui::pid::g_y_axis_factor, kd_ * webui::pid::g_y_axis_factor);
    //     LOG_DEBUG("PID - P项Y: {:.3f}, I项Y: {:.3f}, D项Y: {:.3f} (应用Y轴系数后)", pTermY, iTermY, dTermY);
    //     LOG_DEBUG("PID - 输出X: {:.3f}, Y: {:.3f}", outX, outY);
    // }
    
    // 将结果转为整数
    int moveX = static_cast<int>(outX);
    int moveY = static_cast<int>(outY);
    
    return std::make_pair(moveX, moveY);
}

// 设置移动补偿系数
void PidController::setMovementCompensation(double compensation) {
    if (compensation >= 0.0 && compensation <= 1.0) {
        movementCompensation_ = compensation;
    }
}

// 获取当前移动补偿系数
double PidController::getMovementCompensation() const {
    return movementCompensation_;
}

} // namespace algorithm
} // namespace infer