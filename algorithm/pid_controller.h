#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

#include <cmath>
#include <chrono>
#include <utility> // 添加utility头文件以使用std::pair

namespace infer {
namespace algorithm {

/**
 * @brief 增强型PID控制器类，适用于射击游戏场景
 * 
 * 该控制器结合了增量式PID算法与位置式PID的优点：
 * - 增量式PID计算控制量增量
 * - 延迟积分启用机制
 * - 小数累积功能（输出整数值但保留小数部分）
 * - 死区处理（小于阈值的误差被忽略）
 * - 时间因素考虑（deltaTime参数）
 * - 最大移动距离限制（防止输出过大）
 */
class PidController {
public:
    /**
     * @brief 构造函数
     * 
     * @param kp 比例系数
     * @param ki 积分系数
     * @param kd 微分系数
     * @param deadzone 死区大小（低于此值的误差将被忽略）
     * @param integralLimit 积分上限（防止积分饱和）
     * @param reboundStrength 方向变化时积分值的衰减系数（0-1之间，默认0.5）
     * @param maxMovement 最大移动距离限制（单方向，默认320.0f）
     */
    PidController(double kp = 0.0, double ki = 0.0, double kd = 0.0, 
                  double deadzone = 0.0, double integralLimit = 0.0,
                  double reboundStrength = 0.5, double maxMovement = 320.0);

    /**
     * @brief 重置控制器状态
     */
    void reset();

    /**
     * @brief 设置所有PID参数
     * 
     * @param kp 比例系数，为null时保持当前值
     * @param ki 积分系数，为null时保持当前值
     * @param kd 微分系数，为null时保持当前值
     * @param deadzone 死区大小，为null时保持当前值
     * @param integralLimit 积分上限，为null时保持当前值
     * @param reboundStrength 方向变化时积分值的衰减系数（0-1之间）
     * @param maxMovement 最大移动距离限制
     */
    void setPidParameters(double kp = -1.0, double ki = -1.0, double kd = -1.0, 
                          double deadzone = -1.0, double integralLimit = -1.0,
                          double reboundStrength = -1.0, double maxMovement = -1.0);

    /**
     * @brief 更新控制器并返回计算的移动值
     * 
     * @param errorX X方向误差
     * @param errorY Y方向误差
     * @param deltaTime 时间间隔（秒），用于积分和微分计算
     * @return std::pair<int, int> 返回计算后的X和Y移动值
     */
    std::pair<int, int> update(double errorX, double errorY, double deltaTime = 1.0);
    
    /**
     * @brief 设置移动补偿系数
     * 
     * @param compensation 方向变化时的移动补偿系数（0-1之间）
     */
    void setMovementCompensation(double compensation);
    
    /**
     * @brief 获取当前移动补偿系数
     * 
     * @return double 当前移动补偿系数
     */
    double getMovementCompensation() const;

private:
    // X方向状态
    double prevErrorX_;
    double integralX_;
    double remainderX_;
    double lastErrorDeltaX_;
    
    // Y方向状态
    double prevErrorY_;
    double integralY_;
    double remainderY_;
    double lastErrorDeltaY_;
    
    // 共用参数
    double kp_;
    double ki_;
    double kd_;
    double deadzone_;
    double integralLimit_;
    double reboundStrength_;  // 方向变化时积分值的衰减系数
    double maxMovement_;      // 最大移动距离限制
    double movementCompensation_ = 0.2; // 方向变化时的移动补偿系数
    
    // 辅助函数
    double calculateOutput(double pTerm, double iTerm, double dTerm, double& remainder);
};

} // namespace algorithm
} // namespace infer

#endif // PID_CONTROLLER_H