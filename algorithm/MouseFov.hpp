#include "../utils/gmodels.h"
#include "../utils/logger.h"

/**
 * @brief MouseFov类，用于处理FOV相关功能
 * 
 * 仅提供measureFovWithTarget方法用于测量FOV
 */
class MouseFov {
public:
    /**
     * @brief 获取单例实例
     * @return MouseFov& 单例实例的引用
     */
    static MouseFov& getInstance();
    
    /**
     * @brief 直接传递距离和目标坐标的FOV测量方法
     * @param distance 目标距离
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @return bool 测量是否成功
     */
    bool measureFovWithTarget(double distance, double targetX, double targetY);

private:
    // 单例实例
    static MouseFov* instance;
    
    // 私有构造和析构函数，防止外部创建实例
    MouseFov();
    ~MouseFov();
    
    // 禁用拷贝构造和赋值操作
    MouseFov(const MouseFov&) = delete;
    MouseFov& operator=(const MouseFov&) = delete;
};

/**
 * @brief 全局函数版本，为了兼容现有代码
 */
bool measureFovWithTarget(double distance, double targetX, double targetY);
