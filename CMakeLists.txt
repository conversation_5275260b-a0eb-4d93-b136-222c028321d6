cmake_minimum_required(VERSION 3.15.0)
project(AiBox VERSION 1.0.0 LANGUAGES C CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置静态库编译
set(BUILD_SHARED_LIBS OFF)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/lib)

# 设置编译优化级别为O3
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3")

# 防止库重复定义
set(CMAKE_POLICY_DEFAULT_CMP0077 NEW)

# 首先添加 fmt 库，因为 spdlog 依赖于它
if(NOT TARGET fmt::fmt)
    add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/fmt EXCLUDE_FROM_ALL)
endif()

# 配置 spdlog 使用外部 fmt
option(SPDLOG_FMT_EXTERNAL "Use external fmt library instead of bundled" ON)
option(FMT_INSTALL "Generate the install target" OFF)

# 添加spdlog子目录
add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/spdlog EXCLUDE_FROM_ALL)

# 查找线程库
find_package(Threads REQUIRED)

# 查找atomic库（ARM平台原子操作需要）
find_library(ATOMIC_LIBRARY NAMES atomic)
if(ATOMIC_LIBRARY)
    message(STATUS "找到atomic库: ${ATOMIC_LIBRARY}")
else()
    message(STATUS "未找到atomic库，将尝试直接链接")
    set(ATOMIC_LIBRARY "atomic")
endif()

# 查找spdlog库(用于日志记录) - 现在spdlog已经通过add_subdirectory添加
# find_package(spdlog REQUIRED)

# 设置全局包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/utils
    ${CMAKE_CURRENT_SOURCE_DIR}/ThreadManager
    ${CMAKE_CURRENT_SOURCE_DIR}/infer
    ${CMAKE_CURRENT_SOURCE_DIR}/lkm
    ${CMAKE_CURRENT_SOURCE_DIR}/algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/rknn/include
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/spdlog/include
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/fmt/include
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/nlohmann_json/include
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/Catch2/include
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/CSerialPort-master/include
    /usr/include
)

# 添加外部依赖（但不再包含 fmt，因为已经在前面添加）
add_subdirectory(3rdparty)

# 查找Poco库
find_package(Poco REQUIRED COMPONENTS Net Foundation Util JSON)
message(STATUS "Poco库版本: ${Poco_VERSION}")
message(STATUS "Poco库: ${Poco_LIBRARIES}")

# 包含LKM模块CMake文件（在utils之前包含，确保CSerialPort配置优先）
include(${CMAKE_CURRENT_SOURCE_DIR}/lkm/lkm.make)

# 包含utils模块CMake文件
include(${CMAKE_CURRENT_SOURCE_DIR}/utils/utils.cmake)

# 为兼容性添加别名目标 blweb_utils -> utils
if(TARGET utils AND NOT TARGET blweb_utils)
    add_library(blweb_utils ALIAS utils)
endif()

# 包含算法模块CMake文件
include(${CMAKE_CURRENT_SOURCE_DIR}/algorithm/algorithm.cmake)

# 添加 ThreadManager 目录
add_subdirectory(ThreadManager)

# 包含推理模块CMake文件
include(${CMAKE_CURRENT_SOURCE_DIR}/infer/infer.cmake)

# 为兼容性添加别名目标 inference_lib -> core
if(TARGET core AND NOT TARGET inference_lib)
    add_library(inference_lib ALIAS core)
endif()

# 包含BLKM模块CMake文件
include(${CMAKE_CURRENT_SOURCE_DIR}/blkm/blkm.make)

# 添加backend子目录
add_subdirectory(backend)

# 添加BL客户端可执行文件
add_executable(BL main.cpp)
target_include_directories(BL PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
target_link_libraries(BL PRIVATE 
    utils
    thread_manager
    Threads::Threads
    ${Poco_LIBRARIES}
    blweb_backend
    inference_lib
    algorithm_lib
    lkmapi
    blkmapi
    spdlog::spdlog
    ${ATOMIC_LIBRARY}
)

# 添加推理模块可执行文件
set(INFER_SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/infer/infer_test.cpp)
add_executable(bl_infer ${INFER_SOURCES})
target_include_directories(bl_infer PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${INFER_INCLUDE_DIRS}
)
target_link_libraries(bl_infer PRIVATE
    ${INFER_LIBRARIES}
    Threads::Threads
    utils
    thread_manager
    algorithm_lib
    spdlog::spdlog
    ${ATOMIC_LIBRARY}
)

# 添加LKM测试可执行文件
add_executable(lkm_test ${CMAKE_CURRENT_SOURCE_DIR}/lkm/lkm_test.cpp)
target_include_directories(lkm_test PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/lkm
    ${CMAKE_CURRENT_SOURCE_DIR}/ThreadManager
)
target_link_libraries(lkm_test PRIVATE 
    lkmapi
    utils
    thread_manager
    Threads::Threads
    algorithm_lib
    spdlog::spdlog
    ${ATOMIC_LIBRARY}
)

# 安装规则
install(TARGETS BL bl_server bl_infer lkm_test lkmapi algorithm_lib blkmapi blkm_test utils
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION bin/lib
    ARCHIVE DESTINATION bin/lib
)

# 如果lkm.make中创建了CSerialPort库的静态版本，也安装它
if(NOT CSerialPort_FOUND AND TARGET cserialport)
    install(TARGETS cserialport
        ARCHIVE DESTINATION bin/lib
    )
endif()

# 安装RKNN运行时库
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/rknn/linux/librknnrt.so
    DESTINATION ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)

# 安装3rdparty中的RGA库
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/librga/linux/librga.a
    DESTINATION ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib
    PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ
)

# 安装model文件夹
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/infer/model/
    DESTINATION ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/model
    FILES_MATCHING PATTERN "*"
    PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ
)

# 添加自定义命令，确保每次构建都复制库文件和模型文件
add_custom_command(TARGET bl_infer POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/rknn/linux/librknnrt.so ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib
    # 复制3rdparty中的RGA库
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/librga/linux/librga.a ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib/
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/model
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/model
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_CURRENT_SOURCE_DIR}/infer/model ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/model
    COMMENT "复制 librknnrt.so、RGA库和模型文件到输出目录"
)

message(STATUS "顶层构建配置完成，将构建BL客户端、backend模块、bl_infer模块和lkm_test模块...") 