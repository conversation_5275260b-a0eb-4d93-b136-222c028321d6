# This is the CMakeCache file.
# For build in directory: /home/<USER>/mywork/poco_serverdemo/build_new
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a library.
ATOMIC_LIBRARY:FILEPATH=/usr/lib/libatomic.so

//Value Computed by CMake
AiBox_BINARY_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new

//Value Computed by <PERSON><PERSON>ake
AiBox_IS_TOP_LEVEL:STATIC=ON

//Value Computed by <PERSON><PERSON><PERSON>
AiBox_SOURCE_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo

//Read docs/configuration.md for details
CATCH_CONFIG_ANDROID_LOGWRITE:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_BAZEL_SUPPORT:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_COLOUR_WIN32:BOOL=OFF

//Read docs/configuration.md for details. Must form a valid integer
// literal.
CATCH_CONFIG_CONSOLE_WIDTH:STRING=80

//Read docs/configuration.md for details
CATCH_CONFIG_COUNTER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_CPP11_TO_STRING:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_CPP17_BYTE:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_CPP17_OPTIONAL:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_CPP17_STRING_VIEW:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_CPP17_UNCAUGHT_EXCEPTIONS:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_CPP17_VARIANT:BOOL=OFF

//Read docs/configuration.md for details. The name of the reporter
// should be without quotes.
CATCH_CONFIG_DEFAULT_REPORTER:STRING=console

//Read docs/configuration.md for details
CATCH_CONFIG_DISABLE:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_DISABLE_EXCEPTIONS:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_DISABLE_EXCEPTIONS_CUSTOM_HANDLER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_DISABLE_STRINGIFICATION:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_ENABLE_ALL_STRINGMAKERS:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_ENABLE_OPTIONAL_STRINGMAKER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_ENABLE_PAIR_STRINGMAKER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_ENABLE_TUPLE_STRINGMAKER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_ENABLE_VARIANT_STRINGMAKER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_EXPERIMENTAL_REDIRECT:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_EXPERIMENTAL_STATIC_ANALYSIS_SUPPORT:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_FAST_COMPILE:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_GETENV:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_GLOBAL_NEXTAFTER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NOSTDOUT:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_ANDROID_LOGWRITE:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_BAZEL_SUPPORT:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_COLOUR_WIN32:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_COUNTER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_CPP11_TO_STRING:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_CPP17_BYTE:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_CPP17_OPTIONAL:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_CPP17_STRING_VIEW:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_CPP17_UNCAUGHT_EXCEPTIONS:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_CPP17_VARIANT:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_EXPERIMENTAL_STATIC_ANALYSIS_SUPPORT:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_GETENV:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_GLOBAL_NEXTAFTER:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_POSIX_SIGNALS:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_USE_ASYNC:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_USE_BUILTIN_CONSTANT_P:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_WCHAR:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_NO_WINDOWS_SEH:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_POSIX_SIGNALS:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_PREFIX_ALL:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_PREFIX_MESSAGES:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_USE_ASYNC:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_USE_BUILTIN_CONSTANT_P:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_WCHAR:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_WINDOWS_CRTDBG:BOOL=OFF

//Read docs/configuration.md for details
CATCH_CONFIG_WINDOWS_SEH:BOOL=OFF

//Build tests, enable warnings, enable Werror, etc
CATCH_DEVELOPMENT_BUILD:BOOL=OFF

//Add compiler flags for improving build reproducibility
CATCH_ENABLE_REPRODUCIBLE_BUILD:BOOL=ON

//Install documentation alongside library
CATCH_INSTALL_DOCS:BOOL=ON

//Install extras (CMake scripts, debugger helpers) alongside library
CATCH_INSTALL_EXTRAS:BOOL=ON

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose Release or Debug
CMAKE_BUILD_TYPE:STRING=Release

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=aibox

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for CSerialPort.
CSerialPort_DIR:PATH=CSerialPort_DIR-NOTFOUND

//Value Computed by CMake
Catch2_BINARY_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2

//Value Computed by CMake
Catch2_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Catch2_SOURCE_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2

//Value Computed by CMake
FMT_BINARY_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/fmt

//Installation directory for cmake files, a relative path that
// will be joined with /usr/local or an absolute path.
FMT_CMAKE_DIR:STRING=lib/cmake/fmt

//Generate the cuda-test target.
FMT_CUDA_TEST:BOOL=OFF

//Debug library postfix.
FMT_DEBUG_POSTFIX:STRING=d

//Generate the doc target.
FMT_DOC:BOOL=OFF

//Generate the fuzz target.
FMT_FUZZ:BOOL=OFF

//Installation directory for include files, a relative path that
// will be joined with /usr/local or an absolute path.
FMT_INC_DIR:STRING=include

//Generate the install target
FMT_INSTALL:BOOL=ON

//Value Computed by CMake
FMT_IS_TOP_LEVEL:STATIC=OFF

//Installation directory for libraries, a relative path that will
// be joined to /usr/local or an absolute path.
FMT_LIB_DIR:STRING=lib

//Build a module instead of a traditional library.
FMT_MODULE:BOOL=OFF

//Include OS-specific APIs.
FMT_OS:BOOL=ON

//Enable extra warnings and expensive tests.
FMT_PEDANTIC:BOOL=OFF

//Installation directory for pkgconfig (.pc) files, a relative
// path that will be joined with /usr/local or an absolute path.
FMT_PKGCONFIG_DIR:STRING=lib/pkgconfig

//Value Computed by CMake
FMT_SOURCE_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt

//Expose headers with marking them as system.
FMT_SYSTEM_HEADERS:BOOL=OFF

//Generate the test target.
FMT_TEST:BOOL=OFF

//Enable Unicode support.
FMT_UNICODE:BOOL=ON

//Halt the compilation with an error on compiler warnings.
FMT_WERROR:BOOL=OFF

//Install header files
INSTALL_HEADERS:BOOL=OFF

//Build Example Applications
LIBUSB_BUILD_EXAMPLES:BOOL=OFF

//Build Shared Libraries for libusb
LIBUSB_BUILD_SHARED_LIBS:BOOL=OFF

//Build Tests
LIBUSB_BUILD_TESTING:BOOL=OFF

//Enable Debug Logging
LIBUSB_ENABLE_DEBUG_LOGGING:BOOL=OFF

//Enable Logging
LIBUSB_ENABLE_LOGGING:BOOL=ON

//Enable udev backend for device enumeration
LIBUSB_ENABLE_UDEV:BOOL=ON

//Install libusb targets
LIBUSB_INSTALL_TARGETS:BOOL=ON

//Make targets include paths System
LIBUSB_TARGETS_INCLUDE_USING_SYSTEM:BOOL=ON

//Path to a file.
MYSQL_INCLUDE_DIR:PATH=/usr/include/mysql

//Path to a library.
MYSQL_LIBRARY:FILEPATH=/usr/lib/libmysqlclient.so

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/usr/lib/libcrypto.so

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/usr/lib/libssl.so

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=/usr/lib/cmake/opencv4

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//The directory containing a CMake configuration file for PocoCrypto.
PocoCrypto_DIR:PATH=/usr/lib/cmake/Poco

//The directory containing a CMake configuration file for PocoFoundation.
PocoFoundation_DIR:PATH=/usr/lib/cmake/Poco

//The directory containing a CMake configuration file for PocoJSON.
PocoJSON_DIR:PATH=/usr/lib/cmake/Poco

//The directory containing a CMake configuration file for PocoNetSSL.
PocoNetSSL_DIR:PATH=/usr/lib/cmake/Poco

//The directory containing a CMake configuration file for PocoNet.
PocoNet_DIR:PATH=/usr/lib/cmake/Poco

//The directory containing a CMake configuration file for PocoUtil.
PocoUtil_DIR:PATH=/usr/lib/cmake/Poco

//The directory containing a CMake configuration file for PocoXML.
PocoXML_DIR:PATH=/usr/lib/cmake/Poco

//The directory containing a CMake configuration file for Poco.
Poco_DIR:PATH=/usr/lib/cmake/Poco

//Path to a file.
RKNN_INCLUDE_DIR:PATH=/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/include

//Path to a library.
RKNN_LIBRARY:FILEPATH=/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so

//Build all artifacts
SPDLOG_BUILD_ALL:BOOL=OFF

//Build benchmarks (Requires https://github.com/google/benchmark.git
// to be installed)
SPDLOG_BUILD_BENCH:BOOL=OFF

//Build example
SPDLOG_BUILD_EXAMPLE:BOOL=OFF

//Build header only example
SPDLOG_BUILD_EXAMPLE_HO:BOOL=OFF

//Build position independent code (-fPIC)
SPDLOG_BUILD_PIC:BOOL=OFF

//Build shared library
SPDLOG_BUILD_SHARED:BOOL=OFF

//Build tests
SPDLOG_BUILD_TESTS:BOOL=OFF

//Build tests using the header only version
SPDLOG_BUILD_TESTS_HO:BOOL=OFF

//Enable compiler warnings
SPDLOG_BUILD_WARNINGS:BOOL=OFF

//Use CLOCK_REALTIME_COARSE instead of the regular clock,
SPDLOG_CLOCK_COARSE:BOOL=OFF

//Disable default logger creation
SPDLOG_DISABLE_DEFAULT_LOGGER:BOOL=OFF

//Build static or shared library using precompiled header to speed
// up compilation time
SPDLOG_ENABLE_PCH:BOOL=OFF

//Use external fmt library instead of bundled
SPDLOG_FMT_EXTERNAL:BOOL=ON

//Use external fmt header-only library instead of bundled
SPDLOG_FMT_EXTERNAL_HO:BOOL=OFF

//Use the unlocked variant of fwrite. Leave this on unless your
// libc doesn't have it
SPDLOG_FWRITE_UNLOCKED:BOOL=ON

//Generate the install target
SPDLOG_INSTALL:BOOL=OFF

//prevent spdlog from using of std::atomic log levels (use only
// if your code never modifies log levels concurrently
SPDLOG_NO_ATOMIC_LEVELS:BOOL=OFF

//Compile with -fno-exceptions. Call abort() on any spdlog exceptions
SPDLOG_NO_EXCEPTIONS:BOOL=OFF

//prevent spdlog from querying the thread id on each log call if
// thread id is not needed
SPDLOG_NO_THREAD_ID:BOOL=OFF

//prevent spdlog from using thread local storage
SPDLOG_NO_TLS:BOOL=OFF

//Prevent from child processes to inherit log file descriptors
SPDLOG_PREVENT_CHILD_FD:BOOL=OFF

//Enable address sanitizer in tests
SPDLOG_SANITIZE_ADDRESS:BOOL=OFF

//Enable thread sanitizer in tests
SPDLOG_SANITIZE_THREAD:BOOL=OFF

//Include as system headers (skip for clang-tidy).
SPDLOG_SYSTEM_INCLUDES:BOOL=OFF

//run clang-tidy
SPDLOG_TIDY:BOOL=OFF

//Use std::format instead of fmt library.
SPDLOG_USE_STD_FORMAT:BOOL=OFF

//non supported option
SPDLOG_WCHAR_CONSOLE:BOOL=OFF

//non supported option
SPDLOG_WCHAR_FILENAMES:BOOL=OFF

//non supported option
SPDLOG_WCHAR_SUPPORT:BOOL=OFF

//Value Computed by CMake
aibox_BINARY_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new

//Value Computed by CMake
aibox_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
aibox_SOURCE_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo

//Dependencies for the target
base_LIB_DEPENDS:STATIC=general;utils;

//Dependencies for the target
blweb_backend_LIB_DEPENDS:STATIC=general;blweb_utils;general;spdlog::spdlog;general;PocoFoundation;general;PocoNet;general;PocoJSON;general;PocoUtil;general;PocoXML;general;PocoZip;general;algorithm_lib;general;blkmapi;general;/usr/lib/libatomic.so;general;PocoData;general;PocoDataMySQL;general;/usr/lib/libmysqlclient.so;

//Value Computed by CMake
blweb_server_BINARY_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new/backend

//Value Computed by CMake
blweb_server_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
blweb_server_SOURCE_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/backend

//Dependencies for the target
core_LIB_DEPENDS:STATIC=general;base;general;/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so;general;/home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a;general;opencv_core;general;opencv_imgproc;general;opencv_highgui;general;opencv_imgcodecs;general;-lpthread;general;/usr/lib/libatomic.so;

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=/usr/lib/cmake/fmt

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/usr/lib/libcrypto.so

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/usr/lib/libssl.so

//Value Computed by CMake
spdlog_BINARY_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog

//Value Computed by CMake
spdlog_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
spdlog_SOURCE_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog

//Value Computed by CMake
usb-1.0_BINARY_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake

//Value Computed by CMake
usb-1.0_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
usb-1.0_SOURCE_DIR:STATIC=/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake

//Dependencies for the target
utils_LIB_DEPENDS:STATIC=general;fmt::fmt;general;spdlog::spdlog;general;/usr/lib/libatomic.so;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CATCH_CONFIG_ANDROID_LOGWRITE
CATCH_CONFIG_ANDROID_LOGWRITE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_BAZEL_SUPPORT
CATCH_CONFIG_BAZEL_SUPPORT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_COLOUR_WIN32
CATCH_CONFIG_COLOUR_WIN32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_CONSOLE_WIDTH
CATCH_CONFIG_CONSOLE_WIDTH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_COUNTER
CATCH_CONFIG_COUNTER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_CPP11_TO_STRING
CATCH_CONFIG_CPP11_TO_STRING-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_CPP17_BYTE
CATCH_CONFIG_CPP17_BYTE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_CPP17_OPTIONAL
CATCH_CONFIG_CPP17_OPTIONAL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_CPP17_STRING_VIEW
CATCH_CONFIG_CPP17_STRING_VIEW-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_CPP17_UNCAUGHT_EXCEPTIONS
CATCH_CONFIG_CPP17_UNCAUGHT_EXCEPTIONS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_CPP17_VARIANT
CATCH_CONFIG_CPP17_VARIANT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_DEFAULT_REPORTER
CATCH_CONFIG_DEFAULT_REPORTER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_DISABLE
CATCH_CONFIG_DISABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_DISABLE_EXCEPTIONS
CATCH_CONFIG_DISABLE_EXCEPTIONS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_DISABLE_EXCEPTIONS_CUSTOM_HANDLER
CATCH_CONFIG_DISABLE_EXCEPTIONS_CUSTOM_HANDLER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_DISABLE_STRINGIFICATION
CATCH_CONFIG_DISABLE_STRINGIFICATION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_ENABLE_ALL_STRINGMAKERS
CATCH_CONFIG_ENABLE_ALL_STRINGMAKERS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_ENABLE_OPTIONAL_STRINGMAKER
CATCH_CONFIG_ENABLE_OPTIONAL_STRINGMAKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_ENABLE_PAIR_STRINGMAKER
CATCH_CONFIG_ENABLE_PAIR_STRINGMAKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_ENABLE_TUPLE_STRINGMAKER
CATCH_CONFIG_ENABLE_TUPLE_STRINGMAKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_ENABLE_VARIANT_STRINGMAKER
CATCH_CONFIG_ENABLE_VARIANT_STRINGMAKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_EXPERIMENTAL_REDIRECT
CATCH_CONFIG_EXPERIMENTAL_REDIRECT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_EXPERIMENTAL_STATIC_ANALYSIS_SUPPORT
CATCH_CONFIG_EXPERIMENTAL_STATIC_ANALYSIS_SUPPORT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_FAST_COMPILE
CATCH_CONFIG_FAST_COMPILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_GETENV
CATCH_CONFIG_GETENV-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_GLOBAL_NEXTAFTER
CATCH_CONFIG_GLOBAL_NEXTAFTER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NOSTDOUT
CATCH_CONFIG_NOSTDOUT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_ANDROID_LOGWRITE
CATCH_CONFIG_NO_ANDROID_LOGWRITE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_BAZEL_SUPPORT
CATCH_CONFIG_NO_BAZEL_SUPPORT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_COLOUR_WIN32
CATCH_CONFIG_NO_COLOUR_WIN32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_COUNTER
CATCH_CONFIG_NO_COUNTER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_CPP11_TO_STRING
CATCH_CONFIG_NO_CPP11_TO_STRING-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_CPP17_BYTE
CATCH_CONFIG_NO_CPP17_BYTE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_CPP17_OPTIONAL
CATCH_CONFIG_NO_CPP17_OPTIONAL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_CPP17_STRING_VIEW
CATCH_CONFIG_NO_CPP17_STRING_VIEW-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_CPP17_UNCAUGHT_EXCEPTIONS
CATCH_CONFIG_NO_CPP17_UNCAUGHT_EXCEPTIONS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_CPP17_VARIANT
CATCH_CONFIG_NO_CPP17_VARIANT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_EXPERIMENTAL_STATIC_ANALYSIS_SUPPORT
CATCH_CONFIG_NO_EXPERIMENTAL_STATIC_ANALYSIS_SUPPORT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_GETENV
CATCH_CONFIG_NO_GETENV-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_GLOBAL_NEXTAFTER
CATCH_CONFIG_NO_GLOBAL_NEXTAFTER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_POSIX_SIGNALS
CATCH_CONFIG_NO_POSIX_SIGNALS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_USE_ASYNC
CATCH_CONFIG_NO_USE_ASYNC-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_USE_BUILTIN_CONSTANT_P
CATCH_CONFIG_NO_USE_BUILTIN_CONSTANT_P-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_WCHAR
CATCH_CONFIG_NO_WCHAR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_NO_WINDOWS_SEH
CATCH_CONFIG_NO_WINDOWS_SEH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_POSIX_SIGNALS
CATCH_CONFIG_POSIX_SIGNALS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_PREFIX_ALL
CATCH_CONFIG_PREFIX_ALL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_PREFIX_MESSAGES
CATCH_CONFIG_PREFIX_MESSAGES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_USE_ASYNC
CATCH_CONFIG_USE_ASYNC-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_USE_BUILTIN_CONSTANT_P
CATCH_CONFIG_USE_BUILTIN_CONSTANT_P-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_WCHAR
CATCH_CONFIG_WCHAR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_WINDOWS_CRTDBG
CATCH_CONFIG_WINDOWS_CRTDBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CATCH_CONFIG_WINDOWS_SEH
CATCH_CONFIG_WINDOWS_SEH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/mywork/poco_serverdemo/build_new
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/mywork/poco_serverdemo
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=0
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=14
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Test COMPILER_SUPPORTS_MARCH_NATIVE
COMPILER_SUPPORTS_MARCH_NATIVE:INTERNAL=1
//Project GUID
Catch2WithMain_GUID_CMAKE:INTERNAL=8bd3552a-2cfb-4a59-ab15-2031b97ada1e
//Project GUID
Catch2_GUID_CMAKE:INTERNAL=8d538cbe-01bf-4a2e-a98a-6c368fdf13d7
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[/usr][found components: core imgproc highgui imgcodecs ][v4.11.0(4)]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/usr/lib/libcrypto.so][/usr/include][found components: SSL ][v3.5.0()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Have include asm/types.h
HAVE_ASM_TYPES_H:INTERNAL=1
//Have function clock_gettime
HAVE_CLOCK_GETTIME:INTERNAL=1
//Have function eventfd
HAVE_EVENTFD:INTERNAL=1
//Test HAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__
HAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__:INTERNAL=1
//Have symbol fwrite_unlocked
HAVE_FWRITE_UNLOCKED:INTERNAL=1
//Have include netinet/in.h
HAVE_NETINET_IN_H:INTERNAL=1
//Have symbol nfds_t
HAVE_NFDS_T:INTERNAL=
//Have function pipe2
HAVE_PIPE2:INTERNAL=1
//Have function pthread_condattr_setclock
HAVE_PTHREAD_CONDATTR_SETCLOCK:INTERNAL=1
//Have function pthread_setname_np
HAVE_PTHREAD_SETNAME_NP:INTERNAL=1
//Have function pthread_threadid_np
HAVE_PTHREAD_THREADID_NP:INTERNAL=
//Have symbol SO_REUSEPORT
HAVE_SO_REUSEPORT:INTERNAL=1
//Have include string.h
HAVE_STRING_H:INTERNAL=1
//Test HAVE_STRUCT_TIMESPEC
HAVE_STRUCT_TIMESPEC:INTERNAL=1
//Have function syslog
HAVE_SYSLOG:INTERNAL=1
//Have include sys/time.h
HAVE_SYS_TIME_H:INTERNAL=1
//Have symbol timerfd_create
HAVE_TIMERFD:INTERNAL=1
//Test HAVE_VISIBILITY
HAVE_VISIBILITY:INTERNAL=1
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
_OPENSSL_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/usr/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_LDFLAGS:INTERNAL=-L/usr/lib;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/usr/lib
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/usr/lib
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/usr
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lssl;-lcrypto;-ldl;-pthread
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto;dl
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.5.0
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib

