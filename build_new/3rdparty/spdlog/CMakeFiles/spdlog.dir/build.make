# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include 3rdparty/spdlog/CMakeFiles/spdlog.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 3rdparty/spdlog/CMakeFiles/spdlog.dir/compiler_depend.make

# Include the progress variables for this target.
include 3rdparty/spdlog/CMakeFiles/spdlog.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/spdlog/CMakeFiles/spdlog.dir/flags.make

3rdparty/spdlog/CMakeFiles/spdlog.dir/codegen:
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/codegen

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/flags.make
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/spdlog.cpp
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o -MF CMakeFiles/spdlog.dir/src/spdlog.cpp.o.d -o CMakeFiles/spdlog.dir/src/spdlog.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/spdlog.cpp

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/spdlog.dir/src/spdlog.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/spdlog.cpp > CMakeFiles/spdlog.dir/src/spdlog.cpp.i

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/spdlog.dir/src/spdlog.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/spdlog.cpp -o CMakeFiles/spdlog.dir/src/spdlog.cpp.s

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/flags.make
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/stdout_sinks.cpp
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o -MF CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o.d -o CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/stdout_sinks.cpp

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/stdout_sinks.cpp > CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.i

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/stdout_sinks.cpp -o CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.s

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/flags.make
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/color_sinks.cpp
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o -MF CMakeFiles/spdlog.dir/src/color_sinks.cpp.o.d -o CMakeFiles/spdlog.dir/src/color_sinks.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/color_sinks.cpp

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/spdlog.dir/src/color_sinks.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/color_sinks.cpp > CMakeFiles/spdlog.dir/src/color_sinks.cpp.i

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/spdlog.dir/src/color_sinks.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/color_sinks.cpp -o CMakeFiles/spdlog.dir/src/color_sinks.cpp.s

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/flags.make
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/file_sinks.cpp
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o -MF CMakeFiles/spdlog.dir/src/file_sinks.cpp.o.d -o CMakeFiles/spdlog.dir/src/file_sinks.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/file_sinks.cpp

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/spdlog.dir/src/file_sinks.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/file_sinks.cpp > CMakeFiles/spdlog.dir/src/file_sinks.cpp.i

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/spdlog.dir/src/file_sinks.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/file_sinks.cpp -o CMakeFiles/spdlog.dir/src/file_sinks.cpp.s

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/flags.make
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/async.cpp
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o -MF CMakeFiles/spdlog.dir/src/async.cpp.o.d -o CMakeFiles/spdlog.dir/src/async.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/async.cpp

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/spdlog.dir/src/async.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/async.cpp > CMakeFiles/spdlog.dir/src/async.cpp.i

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/spdlog.dir/src/async.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/async.cpp -o CMakeFiles/spdlog.dir/src/async.cpp.s

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/flags.make
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/cfg.cpp
3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o: 3rdparty/spdlog/CMakeFiles/spdlog.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o -MF CMakeFiles/spdlog.dir/src/cfg.cpp.o.d -o CMakeFiles/spdlog.dir/src/cfg.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/cfg.cpp

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/spdlog.dir/src/cfg.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/cfg.cpp > CMakeFiles/spdlog.dir/src/cfg.cpp.i

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/spdlog.dir/src/cfg.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/cfg.cpp -o CMakeFiles/spdlog.dir/src/cfg.cpp.s

# Object files for target spdlog
spdlog_OBJECTS = \
"CMakeFiles/spdlog.dir/src/spdlog.cpp.o" \
"CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o" \
"CMakeFiles/spdlog.dir/src/color_sinks.cpp.o" \
"CMakeFiles/spdlog.dir/src/file_sinks.cpp.o" \
"CMakeFiles/spdlog.dir/src/async.cpp.o" \
"CMakeFiles/spdlog.dir/src/cfg.cpp.o"

# External object files for target spdlog
spdlog_EXTERNAL_OBJECTS =

bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o
bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o
bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o
bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o
bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o
bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o
bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/build.make
bin/lib/libspdlog.a: 3rdparty/spdlog/CMakeFiles/spdlog.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX static library ../../bin/lib/libspdlog.a"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && $(CMAKE_COMMAND) -P CMakeFiles/spdlog.dir/cmake_clean_target.cmake
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/spdlog.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/spdlog/CMakeFiles/spdlog.dir/build: bin/lib/libspdlog.a
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/build

3rdparty/spdlog/CMakeFiles/spdlog.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog && $(CMAKE_COMMAND) -P CMakeFiles/spdlog.dir/cmake_clean.cmake
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/clean

3rdparty/spdlog/CMakeFiles/spdlog.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog/CMakeFiles/spdlog.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/depend

