
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/async.cpp" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o" "gcc" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/async.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/cfg.cpp" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o" "gcc" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/cfg.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/color_sinks.cpp" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o" "gcc" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/file_sinks.cpp" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o" "gcc" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/spdlog.cpp" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o" "gcc" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/stdout_sinks.cpp" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o" "gcc" "3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
