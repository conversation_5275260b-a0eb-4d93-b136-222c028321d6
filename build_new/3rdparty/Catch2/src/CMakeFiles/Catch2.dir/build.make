# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.make

# Include the progress variables for this target.
include 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/codegen:
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/codegen

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/catch_chronometer.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o -MF CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/catch_chronometer.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/catch_chronometer.cpp > CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/catch_chronometer.cpp -o CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_analyse.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o -MF CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_analyse.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_analyse.cpp > CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_analyse.cpp -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_benchmark_function.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o -MF CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_benchmark_function.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_benchmark_function.cpp > CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_benchmark_function.cpp -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_run_for_at_least.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o -MF CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_run_for_at_least.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_run_for_at_least.cpp > CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_run_for_at_least.cpp -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_stats.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o -MF CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_stats.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_stats.cpp > CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/benchmark/detail/catch_stats.cpp -o CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generator_exception.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o -MF CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generator_exception.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generator_exception.cpp > CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generator_exception.cpp -o CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o -MF CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators.cpp > CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators.cpp -o CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators_random.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o -MF CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators_random.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators_random.cpp > CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/generators/catch_generators_random.cpp -o CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_automake.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_automake.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_automake.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_automake.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_common_base.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_common_base.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_common_base.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_common_base.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_compact.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_compact.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_compact.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_compact.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_console.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_console.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_console.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_console.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_cumulative_base.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_cumulative_base.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_cumulative_base.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_cumulative_base.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_event_listener.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_event_listener.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_event_listener.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_event_listener.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_helpers.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_helpers.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_helpers.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_helpers.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_json.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_json.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_json.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_json.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_junit.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_junit.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_junit.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_junit.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_multi.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_multi.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_multi.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_multi.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_registrars.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_registrars.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_registrars.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_registrars.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_sonarqube.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_sonarqube.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_sonarqube.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_sonarqube.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_streaming_base.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_streaming_base.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_streaming_base.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_streaming_base.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_tap.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_tap.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_tap.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_tap.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_teamcity.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_teamcity.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_teamcity.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_teamcity.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_xml.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o -MF CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_xml.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_xml.cpp > CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/reporters/catch_reporter_xml.cpp -o CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_capture.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_capture.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_capture.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_capture.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_config.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_config.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_config.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_config.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_exception.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_exception.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_exception.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_exception.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_generatortracker.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_generatortracker.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_generatortracker.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_generatortracker.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_registry_hub.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_registry_hub.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_registry_hub.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_registry_hub.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter_factory.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter_factory.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter_factory.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_reporter_factory.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_testcase.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o -MF CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_testcase.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_testcase.cpp > CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/interfaces/catch_interfaces_testcase.cpp -o CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_approx.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_approx.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_approx.cpp > CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_approx.cpp -o CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_assertion_result.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_assertion_result.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_assertion_result.cpp > CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_assertion_result.cpp -o CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_config.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_config.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_config.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_config.cpp > CMakeFiles/Catch2.dir/catch2/catch_config.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_config.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_config.cpp -o CMakeFiles/Catch2.dir/catch2/catch_config.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_get_random_seed.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_get_random_seed.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_get_random_seed.cpp > CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_get_random_seed.cpp -o CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_message.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_message.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_message.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_message.cpp > CMakeFiles/Catch2.dir/catch2/catch_message.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_message.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_message.cpp -o CMakeFiles/Catch2.dir/catch2/catch_message.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_registry_hub.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_registry_hub.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_registry_hub.cpp > CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_registry_hub.cpp -o CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_session.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_session.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_session.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_session.cpp > CMakeFiles/Catch2.dir/catch2/catch_session.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_session.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_session.cpp -o CMakeFiles/Catch2.dir/catch2/catch_session.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tag_alias_autoregistrar.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tag_alias_autoregistrar.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tag_alias_autoregistrar.cpp > CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tag_alias_autoregistrar.cpp -o CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_case_info.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_case_info.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_case_info.cpp > CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_case_info.cpp -o CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_spec.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_spec.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_spec.cpp > CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_test_spec.cpp -o CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_timer.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_timer.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_timer.cpp > CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_timer.cpp -o CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tostring.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tostring.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tostring.cpp > CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_tostring.cpp -o CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_totals.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_totals.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_totals.cpp > CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_totals.cpp -o CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_translate_exception.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_translate_exception.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_translate_exception.cpp > CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_translate_exception.cpp -o CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_version.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o -MF CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_version.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/catch_version.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_version.cpp > CMakeFiles/Catch2.dir/catch2/catch_version.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/catch_version.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_version.cpp -o CMakeFiles/Catch2.dir/catch2/catch_version.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_assertion_handler.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_assertion_handler.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_assertion_handler.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_assertion_handler.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_case_insensitive_comparisons.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_case_insensitive_comparisons.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_case_insensitive_comparisons.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_case_insensitive_comparisons.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_clara.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_clara.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_clara.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_clara.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_commandline.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_commandline.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_commandline.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_commandline.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_console_colour.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_console_colour.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_console_colour.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_console_colour.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_context.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_context.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_context.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_context.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debug_console.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debug_console.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debug_console.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debug_console.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debugger.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debugger.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debugger.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_debugger.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_decomposer.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_decomposer.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_decomposer.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_decomposer.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enforce.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enforce.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enforce.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enforce.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enum_values_registry.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enum_values_registry.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enum_values_registry.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_enum_values_registry.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_errno_guard.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_errno_guard.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_errno_guard.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_errno_guard.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_exception_translator_registry.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_exception_translator_registry.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_exception_translator_registry.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_exception_translator_registry.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_fatal_condition_handler.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_fatal_condition_handler.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_fatal_condition_handler.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_fatal_condition_handler.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_floating_point_helpers.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_floating_point_helpers.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_floating_point_helpers.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_floating_point_helpers.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_getenv.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_getenv.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_getenv.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_getenv.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_istream.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_istream.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_istream.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_istream.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_jsonwriter.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_jsonwriter.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_jsonwriter.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_jsonwriter.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_lazy_expr.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_lazy_expr.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_lazy_expr.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_lazy_expr.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_leak_detector.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_leak_detector.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_leak_detector.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_leak_detector.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_list.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_list.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_list.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_list.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_message_info.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_message_info.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_message_info.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_message_info.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_output_redirect.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_output_redirect.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_output_redirect.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_output_redirect.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_parse_numbers.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_parse_numbers.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_parse_numbers.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_parse_numbers.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_polyfills.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_polyfills.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_polyfills.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_polyfills.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_number_generator.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_number_generator.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_number_generator.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_number_generator.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_seed_generation.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_seed_generation.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_seed_generation.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_random_seed_generation.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_registry.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_registry.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_registry.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_registry.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_spec_parser.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_spec_parser.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_spec_parser.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reporter_spec_parser.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reusable_string_stream.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reusable_string_stream.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reusable_string_stream.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_reusable_string_stream.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_run_context.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_run_context.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_run_context.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_run_context.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_section.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_79) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_section.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_section.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_section.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_singletons.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_80) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_singletons.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_singletons.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_singletons.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_source_line_info.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_81) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_source_line_info.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_source_line_info.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_source_line_info.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_startup_exception_registry.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_82) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_startup_exception_registry.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_startup_exception_registry.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_startup_exception_registry.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stdstreams.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_83) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stdstreams.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stdstreams.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stdstreams.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_string_manip.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_84) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_string_manip.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_string_manip.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_string_manip.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stringref.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_85) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stringref.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stringref.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_stringref.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_tag_alias_registry.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_86) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_tag_alias_registry.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_tag_alias_registry.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_tag_alias_registry.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_info_hasher.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_87) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_info_hasher.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_info_hasher.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_info_hasher.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_registry_impl.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_88) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_registry_impl.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_registry_impl.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_registry_impl.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_tracker.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_89) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_tracker.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_tracker.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_case_tracker.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_failure_exception.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_90) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_failure_exception.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_failure_exception.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_failure_exception.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_registry.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_91) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_registry.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_registry.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_registry.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_spec_parser.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_92) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_spec_parser.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_spec_parser.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_test_spec_parser.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_textflow.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_93) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_textflow.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_textflow.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_textflow.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_uncaught_exceptions.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_94) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_uncaught_exceptions.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_uncaught_exceptions.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_uncaught_exceptions.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_wildcard_pattern.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_95) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_wildcard_pattern.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_wildcard_pattern.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_wildcard_pattern.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_xmlwriter.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_96) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o -MF CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_xmlwriter.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_xmlwriter.cpp > CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_xmlwriter.cpp -o CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_97) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_container_properties.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_98) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_container_properties.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_container_properties.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_container_properties.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_exception.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_99) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_exception.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_exception.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_exception.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_floating_point.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_100) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_floating_point.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_floating_point.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_floating_point.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_predicate.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_101) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_predicate.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_predicate.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_predicate.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_quantifiers.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_102) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_quantifiers.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_quantifiers.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_quantifiers.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_string.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_103) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_string.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_string.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_string.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_templated.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_104) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_templated.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_templated.cpp > CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/catch_matchers_templated.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.s

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/internal/catch_matchers_impl.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_105) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o -MF CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o.d -o CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/internal/catch_matchers_impl.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/internal/catch_matchers_impl.cpp > CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/matchers/internal/catch_matchers_impl.cpp -o CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.s

# Object files for target Catch2
Catch2_OBJECTS = \
"CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o" \
"CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o"

# External object files for target Catch2
Catch2_EXTERNAL_OBJECTS =

bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make
bin/lib/libCatch2.a: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_106) "Linking CXX static library ../../../bin/lib/libCatch2.a"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && $(CMAKE_COMMAND) -P CMakeFiles/Catch2.dir/cmake_clean_target.cmake
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/Catch2.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build: bin/lib/libCatch2.a
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src && $(CMAKE_COMMAND) -P CMakeFiles/Catch2.dir/cmake_clean.cmake
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/clean

3rdparty/Catch2/src/CMakeFiles/Catch2.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src/CMakeFiles/Catch2.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/depend

