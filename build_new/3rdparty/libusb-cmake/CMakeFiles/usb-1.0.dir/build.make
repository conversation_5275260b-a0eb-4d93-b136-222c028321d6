# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.make

# Include the progress variables for this target.
include 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/codegen:
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/codegen

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/core.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/core.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/core.c > CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/core.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/descriptor.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/descriptor.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/descriptor.c > CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/descriptor.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/hotplug.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/hotplug.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/hotplug.c > CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/hotplug.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/io.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/io.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/io.c > CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/io.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/strerror.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/strerror.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/strerror.c > CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/strerror.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/sync.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/sync.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/sync.c > CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/sync.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.c > CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.c > CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.c > CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.s

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/flags.make
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_udev.c
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o -MF CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o.d -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_udev.c

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_udev.c > CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.i

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_udev.c -o CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.s

# Object files for target usb-1.0
usb__1_0_OBJECTS = \
"CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o" \
"CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o"

# External object files for target usb-1.0
usb__1_0_EXTERNAL_OBJECTS =

bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make
bin/lib/libusb-1.0.a: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking C static library ../../bin/lib/libusb-1.0.a"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && $(CMAKE_COMMAND) -P CMakeFiles/usb-1.0.dir/cmake_clean_target.cmake
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/usb-1.0.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build: bin/lib/libusb-1.0.a
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake && $(CMAKE_COMMAND) -P CMakeFiles/usb-1.0.dir/cmake_clean.cmake
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/clean

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/depend

