# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/core.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/version.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/version_nano.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syslog-path.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/syslog.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/syslog.h \
  /usr/include/time.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/descriptor.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/hotplug.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/io.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/eventfd.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timerfd.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/eventfd.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/time.h \
  /usr/include/sys/timerfd.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_udev.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/sysmacros.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/libudev.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/magic.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/sysmacros.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/ioctl.h \
  /usr/include/asm-generic/ioctls.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/ioctl.h \
  /usr/include/asm/ioctls.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/ioctl-types.h \
  /usr/include/bits/ioctls.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/mman-linux.h \
  /usr/include/bits/mman-map-flags-generic.h \
  /usr/include/bits/mman-shared.h \
  /usr/include/bits/mman.h \
  /usr/include/bits/mman_ext.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/statfs.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/utsname.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/ctype.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/ioctl.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/magic.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/ioctl.h \
  /usr/include/sys/mman.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/statfs.h \
  /usr/include/sys/time.h \
  /usr/include/sys/ttydefaults.h \
  /usr/include/sys/types.h \
  /usr/include/sys/utsname.h \
  /usr/include/sys/vfs.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/strerror.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/sync.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h \
  3rdparty/libusb-cmake/gen_include/config.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/poll.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/poll.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/poll.h \
  /usr/include/sys/select.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/time.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h


/usr/include/sys/syscall.h:

/usr/include/bits/syscall.h:

/usr/include/sys/vfs.h:

/usr/include/sys/utsname.h:

/usr/include/asm/unistd.h:

/usr/include/sys/mman.h:

/usr/include/sys/ioctl.h:

/usr/include/bits/mman_ext.h:

/usr/include/bits/mman.h:

/usr/include/bits/mman-shared.h:

/usr/include/bits/mman-map-flags-generic.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.c:

/usr/include/bits/utsname.h:

/usr/include/bits/ioctl-types.h:

/usr/include/bits/dirent_ext.h:

/usr/include/bits/dirent.h:

/usr/include/asm/ioctls.h:

/usr/include/asm/ioctl.h:

/usr/include/asm-generic/ioctls.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.c:

/usr/include/linux/magic.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_udev.c:

/usr/include/unistd.h:

/usr/include/sys/timerfd.h:

/usr/include/linux/falloc.h:

/usr/include/linux/errno.h:

/usr/include/errno.h:

/usr/include/libudev.h:

/usr/include/bits/unistd_ext.h:

/usr/include/bits/types/struct_iovec.h:

/usr/include/bits/types/error_t.h:

/usr/include/bits/timerfd.h:

/usr/include/bits/getopt_posix.h:

/usr/include/bits/getopt_core.h:

/usr/include/bits/fcntl.h:

/usr/include/bits/local_lim.h:

/usr/include/bits/types/__FILE.h:

/usr/include/bits/types/FILE.h:

/usr/include/limits.h:

/usr/include/bits/types.h:

/usr/include/bits/endian.h:

/usr/include/asm/errno.h:

/usr/include/bits/thread-shared-types.h:

/usr/include/linux/ioctl.h:

/usr/include/linux/types.h:

/usr/include/bits/struct_stat.h:

/usr/include/bits/types/time_t.h:

/usr/include/bits/stdlib-bsearch.h:

/usr/include/time.h:

/usr/include/bits/types/struct_timeval.h:

/usr/include/bits/stdio.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusbi.h:

/usr/include/sys/sysmacros.h:

/usr/include/linux/close_range.h:

/usr/include/bits/stdint-intn.h:

/usr/include/bits/setjmp.h:

/usr/include/bits/types/struct___jmp_buf_tag.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/io.c:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/bits/select.h:

/usr/include/asm-generic/ioctl.h:

/usr/include/bits/sched.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/bits/types/cookie_io_functions_t.h:

/usr/include/endian.h:

/usr/include/bits/pthreadtypes.h:

/usr/include/bits/pthread_stack_min-dynamic.h:

/usr/include/stdc-predef.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/bits/types/__sigset_t.h:

/usr/include/asm/types.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/strerror.c:

/usr/include/bits/types/struct_FILE.h:

/usr/include/linux/sched/types.h:

/usr/include/asm-generic/int-ll64.h:

3rdparty/libusb-cmake/gen_include/config.h:

/usr/include/bits/byteswap.h:

/usr/include/bits/syslog-path.h:

/usr/include/bits/confname.h:

/usr/include/asm/bitsperlong.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/version.h:

/usr/include/pthread.h:

/usr/include/bits/types/__locale_t.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/libusb.h:

/usr/include/string.h:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/bits/stdio_lim.h:

/usr/include/bits/time.h:

/usr/include/asm/posix_types.h:

/usr/include/bits/posix1_lim.h:

/usr/include/sys/ttydefaults.h:

/usr/include/bits/posix2_lim.h:

/usr/include/inttypes.h:

/usr/include/bits/stdint-least.h:

/usr/include/bits/mman-linux.h:

/usr/include/alloca.h:

/usr/include/bits/floatn.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/core.c:

/usr/include/bits/floatn-common.h:

/usr/include/sys/poll.h:

/usr/include/bits/posix_opt.h:

/usr/include/bits/atomic_wide_counter.h:

/usr/include/bits/types/clockid_t.h:

/usr/include/dirent.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.h:

/usr/include/bits/stat.h:

/usr/include/assert.h:

/usr/include/bits/struct_mutex.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/descriptor.c:

/usr/include/bits/cpu-set.h:

/usr/include/bits/typesizes.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h:

/usr/include/bits/uintn-identity.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/version_nano.h:

/usr/include/bits/xopen_lim.h:

/usr/include/asm/unistd_64.h:

/usr/include/bits/eventfd.h:

/usr/include/bits/long-double.h:

/usr/include/bits/libc-header-start.h:

/usr/include/linux/posix_types.h:

/usr/include/bits/poll.h:

/usr/include/bits/ioctls.h:

/usr/include/bits/types/__fpos_t.h:

/usr/include/bits/types/clock_t.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h:

/usr/include/bits/statfs.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.h:

/usr/include/bits/types/locale_t.h:

/usr/include/bits/types/sigset_t.h:

/usr/include/bits/struct_rwlock.h:

/usr/include/bits/types/struct_itimerspec.h:

/usr/include/linux/stddef.h:

/usr/include/sys/types.h:

/usr/include/sys/eventfd.h:

/usr/include/bits/types/struct_sched_param.h:

/usr/include/ctype.h:

/usr/include/bits/waitflags.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdatomic.h:

/usr/include/bits/types/struct_timespec.h:

/usr/include/sys/statfs.h:

/usr/include/bits/types/struct_tm.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/bits/wchar.h:

/usr/include/bits/types/timer_t.h:

/usr/include/bits/sysmacros.h:

/usr/include/stdio.h:

/usr/include/bits/uio_lim.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/sync.c:

/usr/include/bits/timesize.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/wordsize.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h:

/usr/include/bits/endianness.h:

/usr/include/features.h:

/usr/include/gnu/stubs-lp64.h:

/usr/include/fcntl.h:

/usr/include/bits/pthreadtypes-arch.h:

/usr/include/gnu/stubs.h:

/usr/include/bits/stdlib-float.h:

/usr/include/bits/time64.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/hotplug.c:

/usr/include/features-time64.h:

/usr/include/poll.h:

/usr/include/sys/cdefs.h:

/usr/include/linux/limits.h:

/usr/include/stdint.h:

/usr/include/stdlib.h:

/usr/include/bits/fcntl-linux.h:

/usr/include/strings.h:

/usr/include/sys/select.h:

/usr/include/bits/environments.h:

/usr/include/sys/syslog.h:

/usr/include/sys/time.h:

/usr/include/syslog.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h:

/usr/include/sched.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.c:

/usr/include/asm-generic/types.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/asm-generic/errno.h:

/usr/include/bits/timex.h:

/usr/include/bits/errno.h:
