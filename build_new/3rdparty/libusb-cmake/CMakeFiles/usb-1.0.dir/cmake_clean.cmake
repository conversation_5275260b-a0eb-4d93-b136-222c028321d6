file(REMOVE_RECURSE
  "../../bin/lib/libusb-1.0.a"
  "../../bin/lib/libusb-1.0.pdb"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o.d"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o"
  "CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o.d"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/usb-1.0.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
