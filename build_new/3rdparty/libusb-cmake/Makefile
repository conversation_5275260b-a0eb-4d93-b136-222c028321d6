# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\" \"fmt-core\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake//CMakeFiles/progress.marks
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusb-cmake/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusb-cmake/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusb-cmake/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusb-cmake/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/rule
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/rule

# Convenience name for target.
usb-1.0: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/rule
.PHONY : usb-1.0

# fast build rule for target.
usb-1.0/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build
.PHONY : usb-1.0/fast

libusb/libusb/core.o: libusb/libusb/core.c.o
.PHONY : libusb/libusb/core.o

# target to build an object file
libusb/libusb/core.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o
.PHONY : libusb/libusb/core.c.o

libusb/libusb/core.i: libusb/libusb/core.c.i
.PHONY : libusb/libusb/core.i

# target to preprocess a source file
libusb/libusb/core.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.i
.PHONY : libusb/libusb/core.c.i

libusb/libusb/core.s: libusb/libusb/core.c.s
.PHONY : libusb/libusb/core.s

# target to generate assembly for a file
libusb/libusb/core.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.s
.PHONY : libusb/libusb/core.c.s

libusb/libusb/descriptor.o: libusb/libusb/descriptor.c.o
.PHONY : libusb/libusb/descriptor.o

# target to build an object file
libusb/libusb/descriptor.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o
.PHONY : libusb/libusb/descriptor.c.o

libusb/libusb/descriptor.i: libusb/libusb/descriptor.c.i
.PHONY : libusb/libusb/descriptor.i

# target to preprocess a source file
libusb/libusb/descriptor.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.i
.PHONY : libusb/libusb/descriptor.c.i

libusb/libusb/descriptor.s: libusb/libusb/descriptor.c.s
.PHONY : libusb/libusb/descriptor.s

# target to generate assembly for a file
libusb/libusb/descriptor.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.s
.PHONY : libusb/libusb/descriptor.c.s

libusb/libusb/hotplug.o: libusb/libusb/hotplug.c.o
.PHONY : libusb/libusb/hotplug.o

# target to build an object file
libusb/libusb/hotplug.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o
.PHONY : libusb/libusb/hotplug.c.o

libusb/libusb/hotplug.i: libusb/libusb/hotplug.c.i
.PHONY : libusb/libusb/hotplug.i

# target to preprocess a source file
libusb/libusb/hotplug.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.i
.PHONY : libusb/libusb/hotplug.c.i

libusb/libusb/hotplug.s: libusb/libusb/hotplug.c.s
.PHONY : libusb/libusb/hotplug.s

# target to generate assembly for a file
libusb/libusb/hotplug.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.s
.PHONY : libusb/libusb/hotplug.c.s

libusb/libusb/io.o: libusb/libusb/io.c.o
.PHONY : libusb/libusb/io.o

# target to build an object file
libusb/libusb/io.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o
.PHONY : libusb/libusb/io.c.o

libusb/libusb/io.i: libusb/libusb/io.c.i
.PHONY : libusb/libusb/io.i

# target to preprocess a source file
libusb/libusb/io.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.i
.PHONY : libusb/libusb/io.c.i

libusb/libusb/io.s: libusb/libusb/io.c.s
.PHONY : libusb/libusb/io.s

# target to generate assembly for a file
libusb/libusb/io.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.s
.PHONY : libusb/libusb/io.c.s

libusb/libusb/os/events_posix.o: libusb/libusb/os/events_posix.c.o
.PHONY : libusb/libusb/os/events_posix.o

# target to build an object file
libusb/libusb/os/events_posix.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o
.PHONY : libusb/libusb/os/events_posix.c.o

libusb/libusb/os/events_posix.i: libusb/libusb/os/events_posix.c.i
.PHONY : libusb/libusb/os/events_posix.i

# target to preprocess a source file
libusb/libusb/os/events_posix.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.i
.PHONY : libusb/libusb/os/events_posix.c.i

libusb/libusb/os/events_posix.s: libusb/libusb/os/events_posix.c.s
.PHONY : libusb/libusb/os/events_posix.s

# target to generate assembly for a file
libusb/libusb/os/events_posix.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.s
.PHONY : libusb/libusb/os/events_posix.c.s

libusb/libusb/os/linux_udev.o: libusb/libusb/os/linux_udev.c.o
.PHONY : libusb/libusb/os/linux_udev.o

# target to build an object file
libusb/libusb/os/linux_udev.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o
.PHONY : libusb/libusb/os/linux_udev.c.o

libusb/libusb/os/linux_udev.i: libusb/libusb/os/linux_udev.c.i
.PHONY : libusb/libusb/os/linux_udev.i

# target to preprocess a source file
libusb/libusb/os/linux_udev.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.i
.PHONY : libusb/libusb/os/linux_udev.c.i

libusb/libusb/os/linux_udev.s: libusb/libusb/os/linux_udev.c.s
.PHONY : libusb/libusb/os/linux_udev.s

# target to generate assembly for a file
libusb/libusb/os/linux_udev.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.s
.PHONY : libusb/libusb/os/linux_udev.c.s

libusb/libusb/os/linux_usbfs.o: libusb/libusb/os/linux_usbfs.c.o
.PHONY : libusb/libusb/os/linux_usbfs.o

# target to build an object file
libusb/libusb/os/linux_usbfs.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o
.PHONY : libusb/libusb/os/linux_usbfs.c.o

libusb/libusb/os/linux_usbfs.i: libusb/libusb/os/linux_usbfs.c.i
.PHONY : libusb/libusb/os/linux_usbfs.i

# target to preprocess a source file
libusb/libusb/os/linux_usbfs.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.i
.PHONY : libusb/libusb/os/linux_usbfs.c.i

libusb/libusb/os/linux_usbfs.s: libusb/libusb/os/linux_usbfs.c.s
.PHONY : libusb/libusb/os/linux_usbfs.s

# target to generate assembly for a file
libusb/libusb/os/linux_usbfs.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.s
.PHONY : libusb/libusb/os/linux_usbfs.c.s

libusb/libusb/os/threads_posix.o: libusb/libusb/os/threads_posix.c.o
.PHONY : libusb/libusb/os/threads_posix.o

# target to build an object file
libusb/libusb/os/threads_posix.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o
.PHONY : libusb/libusb/os/threads_posix.c.o

libusb/libusb/os/threads_posix.i: libusb/libusb/os/threads_posix.c.i
.PHONY : libusb/libusb/os/threads_posix.i

# target to preprocess a source file
libusb/libusb/os/threads_posix.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.i
.PHONY : libusb/libusb/os/threads_posix.c.i

libusb/libusb/os/threads_posix.s: libusb/libusb/os/threads_posix.c.s
.PHONY : libusb/libusb/os/threads_posix.s

# target to generate assembly for a file
libusb/libusb/os/threads_posix.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.s
.PHONY : libusb/libusb/os/threads_posix.c.s

libusb/libusb/strerror.o: libusb/libusb/strerror.c.o
.PHONY : libusb/libusb/strerror.o

# target to build an object file
libusb/libusb/strerror.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o
.PHONY : libusb/libusb/strerror.c.o

libusb/libusb/strerror.i: libusb/libusb/strerror.c.i
.PHONY : libusb/libusb/strerror.i

# target to preprocess a source file
libusb/libusb/strerror.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.i
.PHONY : libusb/libusb/strerror.c.i

libusb/libusb/strerror.s: libusb/libusb/strerror.c.s
.PHONY : libusb/libusb/strerror.s

# target to generate assembly for a file
libusb/libusb/strerror.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.s
.PHONY : libusb/libusb/strerror.c.s

libusb/libusb/sync.o: libusb/libusb/sync.c.o
.PHONY : libusb/libusb/sync.o

# target to build an object file
libusb/libusb/sync.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o
.PHONY : libusb/libusb/sync.c.o

libusb/libusb/sync.i: libusb/libusb/sync.c.i
.PHONY : libusb/libusb/sync.i

# target to preprocess a source file
libusb/libusb/sync.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.i
.PHONY : libusb/libusb/sync.c.i

libusb/libusb/sync.s: libusb/libusb/sync.c.s
.PHONY : libusb/libusb/sync.s

# target to generate assembly for a file
libusb/libusb/sync.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.s
.PHONY : libusb/libusb/sync.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... usb-1.0"
	@echo "... libusb/libusb/core.o"
	@echo "... libusb/libusb/core.i"
	@echo "... libusb/libusb/core.s"
	@echo "... libusb/libusb/descriptor.o"
	@echo "... libusb/libusb/descriptor.i"
	@echo "... libusb/libusb/descriptor.s"
	@echo "... libusb/libusb/hotplug.o"
	@echo "... libusb/libusb/hotplug.i"
	@echo "... libusb/libusb/hotplug.s"
	@echo "... libusb/libusb/io.o"
	@echo "... libusb/libusb/io.i"
	@echo "... libusb/libusb/io.s"
	@echo "... libusb/libusb/os/events_posix.o"
	@echo "... libusb/libusb/os/events_posix.i"
	@echo "... libusb/libusb/os/events_posix.s"
	@echo "... libusb/libusb/os/linux_udev.o"
	@echo "... libusb/libusb/os/linux_udev.i"
	@echo "... libusb/libusb/os/linux_udev.s"
	@echo "... libusb/libusb/os/linux_usbfs.o"
	@echo "... libusb/libusb/os/linux_usbfs.i"
	@echo "... libusb/libusb/os/linux_usbfs.s"
	@echo "... libusb/libusb/os/threads_posix.o"
	@echo "... libusb/libusb/os/threads_posix.i"
	@echo "... libusb/libusb/os/threads_posix.s"
	@echo "... libusb/libusb/strerror.o"
	@echo "... libusb/libusb/strerror.i"
	@echo "... libusb/libusb/strerror.s"
	@echo "... libusb/libusb/sync.o"
	@echo "... libusb/libusb/sync.i"
	@echo "... libusb/libusb/sync.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

