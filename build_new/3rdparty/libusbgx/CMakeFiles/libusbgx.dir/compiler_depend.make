# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ether.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/net.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ffs.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/ffs.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/hid.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/hid.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/loopback.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/loopback.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/midi.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/midi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ms.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/ms.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/statx-generic.h \
  /usr/include/bits/statx.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_statx.h \
  /usr/include/bits/types/struct_statx_timestamp.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stat.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/stat.h \
  /usr/include/sys/types.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/phonet.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/phonet.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/printer.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/serial.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/serial.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uac2.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/uac2.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uvc.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/uvc.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/statx-generic.h \
  /usr/include/bits/statx.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_statx.h \
  /usr/include/bits/types/struct_statx_timestamp.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/ftw.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stat.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/stat.h \
  /usr/include/sys/types.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/statx-generic.h \
  /usr/include/bits/statx.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_statx.h \
  /usr/include/bits/types/struct_statx_timestamp.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/ctype.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stat.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/stat.h \
  /usr/include/sys/types.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_common.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/statx-generic.h \
  /usr/include/bits/statx.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/sysmacros.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_statx.h \
  /usr/include/bits/types/struct_statx_timestamp.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stat.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/stat.h \
  /usr/include/sys/sysmacros.h \
  /usr/include/sys/types.h \
  /usr/include/unistd.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_error.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_schemes_none.c \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h \
  3rdparty/libusbgx/usbg_version.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/dirent.h \
  /usr/include/bits/dirent_ext.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/select.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/if_ether.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/malloc.h \
  /usr/include/net/ethernet.h \
  /usr/include/net/if_arp.h \
  /usr/include/netinet/ether.h \
  /usr/include/netinet/if_ether.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/queue.h \
  /usr/include/sys/select.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/types.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h


/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_error.c:

/usr/include/stdlib.h:

/usr/include/ctype.h:

/usr/include/bits/waitflags.h:

/usr/include/bits/stdlib-bsearch.h:

/usr/include/alloca.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/uvc.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/uac2.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uac2.c:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/serial.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/serial.c:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/phonet.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/phonet.c:

/usr/include/unistd.h:

/usr/include/linux/errno.h:

/usr/include/bits/unistd_ext.h:

/usr/include/bits/types/struct_statx_timestamp.h:

/usr/include/bits/types/struct_statx.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_common.c:

/usr/include/bits/types/error_t.h:

/usr/include/bits/statx.h:

/usr/include/bits/statx-generic.h:

/usr/include/bits/types/clockid_t.h:

/usr/include/bits/local_lim.h:

/usr/include/bits/types/__FILE.h:

/usr/include/limits.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/ms.h:

/usr/include/bits/types.h:

/usr/include/bits/getopt_core.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/timesize.h:

/usr/include/bits/endian.h:

/usr/include/bits/thread-shared-types.h:

/usr/include/bits/struct_rwlock.h:

/usr/include/bits/struct_stat.h:

/usr/include/bits/types/time_t.h:

/usr/include/bits/stdio.h:

/usr/include/bits/stdint-least.h:

/usr/include/sys/sysmacros.h:

/usr/include/linux/close_range.h:

/usr/include/bits/stdint-intn.h:

/usr/include/bits/socket_type.h:

/usr/include/ftw.h:

/usr/include/asm/errno.h:

/usr/include/bits/types/struct_osockaddr.h:

/usr/include/bits/socket.h:

/usr/include/bits/types/cookie_io_functions_t.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/endian.h:

/usr/include/bits/struct_mutex.h:

/usr/include/bits/sockaddr.h:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/bits/select.h:

/usr/include/bits/pthreadtypes.h:

/usr/include/bits/pthread_stack_min-dynamic.h:

/usr/include/asm/sockios.h:

/usr/include/bits/types/FILE.h:

/usr/include/netinet/ether.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ms.c:

/usr/include/errno.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ether.c:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/loopback.h:

/usr/include/asm-generic/socket.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_schemes_none.c:

/usr/include/features-time64.h:

/usr/include/asm/posix_types.h:

/usr/include/bits/types/locale_t.h:

/usr/include/asm/bitsperlong.h:

/usr/include/asm/socket.h:

/usr/include/bits/types/__locale_t.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/loopback.c:

/usr/include/bits/floatn.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h:

/usr/include/bits/posix2_lim.h:

/usr/include/asm-generic/int-ll64.h:

/usr/include/linux/if_ether.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg.h:

/usr/include/stdc-predef.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/bits/types/__sigset_t.h:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/bits/stdio_lim.h:

/usr/include/bits/types/struct_timeval.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/net.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h:

3rdparty/libusbgx/usbg_version.h:

/usr/include/bits/posix1_lim.h:

/usr/include/sys/stat.h:

/usr/include/bits/types/struct_FILE.h:

/usr/include/linux/types.h:

/usr/include/sys/queue.h:

/usr/include/bits/byteswap.h:

/usr/include/bits/dirent.h:

/usr/include/bits/floatn-common.h:

/usr/include/bits/libc-header-start.h:

/usr/include/linux/posix_types.h:

/usr/include/bits/dirent_ext.h:

/usr/include/strings.h:

/usr/include/bits/long-double.h:

/usr/include/asm-generic/sockios.h:

/usr/include/bits/types/sigset_t.h:

/usr/include/bits/types/struct_iovec.h:

/usr/include/bits/types/struct_timespec.h:

/usr/include/linux/stat.h:

/usr/include/bits/types/timer_t.h:

/usr/include/bits/sysmacros.h:

/usr/include/stdio.h:

/usr/include/bits/typesizes.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h:

/usr/include/bits/uintn-identity.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg.c:

/usr/include/bits/uio_lim.h:

/usr/include/string.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/midi.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/bits/wchar.h:

/usr/include/bits/wordsize.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h:

/usr/include/bits/xopen_lim.h:

/usr/include/bits/stdlib-float.h:

/usr/include/bits/time64.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h:

/usr/include/dirent.h:

/usr/include/bits/endianness.h:

/usr/include/features.h:

/usr/include/gnu/stubs-lp64.h:

/usr/include/bits/pthreadtypes-arch.h:

/usr/include/gnu/stubs.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal_none.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_internal.h:

/usr/include/linux/stddef.h:

/usr/include/malloc.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/printer.c:

/usr/include/asm/types.h:

/usr/include/net/ethernet.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uvc.c:

/usr/include/sys/types.h:

/usr/include/net/if_arp.h:

/usr/include/netinet/if_ether.h:

/usr/include/linux/limits.h:

/usr/include/stdint.h:

/usr/include/sys/socket.h:

/usr/include/sys/cdefs.h:

/usr/include/sys/select.h:

/usr/include/bits/environments.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdbool.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ffs.c:

/usr/include/bits/atomic_wide_counter.h:

/usr/include/bits/posix_opt.h:

/usr/include/bits/types/__fpos_t.h:

/usr/include/bits/types/clock_t.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/hid.c:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/hid.h:

/usr/include/bits/stat.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/function/ffs.h:

/usr/include/assert.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/midi.c:

/usr/include/asm-generic/types.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/asm-generic/errno.h:

/usr/include/bits/confname.h:

/usr/include/bits/errno.h:

/usr/include/bits/getopt_posix.h:
