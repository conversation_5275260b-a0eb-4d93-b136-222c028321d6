# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.make

# Include the progress variables for this target.
include 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/codegen:
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/codegen

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o -MF CMakeFiles/libusbgx.dir/src/usbg.c.o.d -o CMakeFiles/libusbgx.dir/src/usbg.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/usbg.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg.c > CMakeFiles/libusbgx.dir/src/usbg.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/usbg.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg.c -o CMakeFiles/libusbgx.dir/src/usbg.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_common.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o -MF CMakeFiles/libusbgx.dir/src/usbg_common.c.o.d -o CMakeFiles/libusbgx.dir/src/usbg_common.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_common.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/usbg_common.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_common.c > CMakeFiles/libusbgx.dir/src/usbg_common.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/usbg_common.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_common.c -o CMakeFiles/libusbgx.dir/src/usbg_common.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_error.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o -MF CMakeFiles/libusbgx.dir/src/usbg_error.c.o.d -o CMakeFiles/libusbgx.dir/src/usbg_error.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_error.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/usbg_error.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_error.c > CMakeFiles/libusbgx.dir/src/usbg_error.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/usbg_error.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_error.c -o CMakeFiles/libusbgx.dir/src/usbg_error.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_schemes_none.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o -MF CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o.d -o CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_schemes_none.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_schemes_none.c > CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/usbg_schemes_none.c -o CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ether.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o -MF CMakeFiles/libusbgx.dir/src/function/ether.c.o.d -o CMakeFiles/libusbgx.dir/src/function/ether.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ether.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/ether.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ether.c > CMakeFiles/libusbgx.dir/src/function/ether.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/ether.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ether.c -o CMakeFiles/libusbgx.dir/src/function/ether.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ffs.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o -MF CMakeFiles/libusbgx.dir/src/function/ffs.c.o.d -o CMakeFiles/libusbgx.dir/src/function/ffs.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ffs.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/ffs.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ffs.c > CMakeFiles/libusbgx.dir/src/function/ffs.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/ffs.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ffs.c -o CMakeFiles/libusbgx.dir/src/function/ffs.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/hid.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o -MF CMakeFiles/libusbgx.dir/src/function/hid.c.o.d -o CMakeFiles/libusbgx.dir/src/function/hid.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/hid.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/hid.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/hid.c > CMakeFiles/libusbgx.dir/src/function/hid.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/hid.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/hid.c -o CMakeFiles/libusbgx.dir/src/function/hid.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/loopback.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o -MF CMakeFiles/libusbgx.dir/src/function/loopback.c.o.d -o CMakeFiles/libusbgx.dir/src/function/loopback.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/loopback.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/loopback.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/loopback.c > CMakeFiles/libusbgx.dir/src/function/loopback.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/loopback.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/loopback.c -o CMakeFiles/libusbgx.dir/src/function/loopback.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/midi.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o -MF CMakeFiles/libusbgx.dir/src/function/midi.c.o.d -o CMakeFiles/libusbgx.dir/src/function/midi.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/midi.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/midi.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/midi.c > CMakeFiles/libusbgx.dir/src/function/midi.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/midi.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/midi.c -o CMakeFiles/libusbgx.dir/src/function/midi.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ms.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o -MF CMakeFiles/libusbgx.dir/src/function/ms.c.o.d -o CMakeFiles/libusbgx.dir/src/function/ms.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ms.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/ms.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ms.c > CMakeFiles/libusbgx.dir/src/function/ms.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/ms.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/ms.c -o CMakeFiles/libusbgx.dir/src/function/ms.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/phonet.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o -MF CMakeFiles/libusbgx.dir/src/function/phonet.c.o.d -o CMakeFiles/libusbgx.dir/src/function/phonet.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/phonet.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/phonet.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/phonet.c > CMakeFiles/libusbgx.dir/src/function/phonet.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/phonet.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/phonet.c -o CMakeFiles/libusbgx.dir/src/function/phonet.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/printer.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o -MF CMakeFiles/libusbgx.dir/src/function/printer.c.o.d -o CMakeFiles/libusbgx.dir/src/function/printer.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/printer.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/printer.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/printer.c > CMakeFiles/libusbgx.dir/src/function/printer.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/printer.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/printer.c -o CMakeFiles/libusbgx.dir/src/function/printer.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/serial.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o -MF CMakeFiles/libusbgx.dir/src/function/serial.c.o.d -o CMakeFiles/libusbgx.dir/src/function/serial.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/serial.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/serial.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/serial.c > CMakeFiles/libusbgx.dir/src/function/serial.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/serial.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/serial.c -o CMakeFiles/libusbgx.dir/src/function/serial.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uac2.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o -MF CMakeFiles/libusbgx.dir/src/function/uac2.c.o.d -o CMakeFiles/libusbgx.dir/src/function/uac2.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uac2.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/uac2.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uac2.c > CMakeFiles/libusbgx.dir/src/function/uac2.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/uac2.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uac2.c -o CMakeFiles/libusbgx.dir/src/function/uac2.c.s

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/flags.make
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uvc.c
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o -MF CMakeFiles/libusbgx.dir/src/function/uvc.c.o.d -o CMakeFiles/libusbgx.dir/src/function/uvc.c.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uvc.c

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libusbgx.dir/src/function/uvc.c.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uvc.c > CMakeFiles/libusbgx.dir/src/function/uvc.c.i

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libusbgx.dir/src/function/uvc.c.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/src/function/uvc.c -o CMakeFiles/libusbgx.dir/src/function/uvc.c.s

# Object files for target libusbgx
libusbgx_OBJECTS = \
"CMakeFiles/libusbgx.dir/src/usbg.c.o" \
"CMakeFiles/libusbgx.dir/src/usbg_common.c.o" \
"CMakeFiles/libusbgx.dir/src/usbg_error.c.o" \
"CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o" \
"CMakeFiles/libusbgx.dir/src/function/ether.c.o" \
"CMakeFiles/libusbgx.dir/src/function/ffs.c.o" \
"CMakeFiles/libusbgx.dir/src/function/hid.c.o" \
"CMakeFiles/libusbgx.dir/src/function/loopback.c.o" \
"CMakeFiles/libusbgx.dir/src/function/midi.c.o" \
"CMakeFiles/libusbgx.dir/src/function/ms.c.o" \
"CMakeFiles/libusbgx.dir/src/function/phonet.c.o" \
"CMakeFiles/libusbgx.dir/src/function/printer.c.o" \
"CMakeFiles/libusbgx.dir/src/function/serial.c.o" \
"CMakeFiles/libusbgx.dir/src/function/uac2.c.o" \
"CMakeFiles/libusbgx.dir/src/function/uvc.c.o"

# External object files for target libusbgx
libusbgx_EXTERNAL_OBJECTS =

bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make
bin/lib/liblibusbgx.a: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Linking C static library ../../bin/lib/liblibusbgx.a"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && $(CMAKE_COMMAND) -P CMakeFiles/libusbgx.dir/cmake_clean_target.cmake
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/libusbgx.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build: bin/lib/liblibusbgx.a
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx && $(CMAKE_COMMAND) -P CMakeFiles/libusbgx.dir/cmake_clean.cmake
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/clean

3rdparty/libusbgx/CMakeFiles/libusbgx.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx /home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx/CMakeFiles/libusbgx.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/depend

