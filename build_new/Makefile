# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\" \"fmt-core\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles /home/<USER>/mywork/poco_serverdemo/build_new//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named cserialport

# Build rule for target.
cserialport: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cserialport
.PHONY : cserialport

# fast build rule for target.
cserialport/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/build
.PHONY : cserialport/fast

#=============================================================================
# Target rules for targets named lkmapi

# Build rule for target.
lkmapi: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lkmapi
.PHONY : lkmapi

# fast build rule for target.
lkmapi/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/build
.PHONY : lkmapi/fast

#=============================================================================
# Target rules for targets named utils

# Build rule for target.
utils: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 utils
.PHONY : utils

# fast build rule for target.
utils/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/build
.PHONY : utils/fast

#=============================================================================
# Target rules for targets named algorithm_lib

# Build rule for target.
algorithm_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 algorithm_lib
.PHONY : algorithm_lib

# fast build rule for target.
algorithm_lib/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/build
.PHONY : algorithm_lib/fast

#=============================================================================
# Target rules for targets named base

# Build rule for target.
base: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 base
.PHONY : base

# fast build rule for target.
base/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/build
.PHONY : base/fast

#=============================================================================
# Target rules for targets named core

# Build rule for target.
core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core
.PHONY : core

# fast build rule for target.
core/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/build
.PHONY : core/fast

#=============================================================================
# Target rules for targets named tests

# Build rule for target.
tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests
.PHONY : tests

# fast build rule for target.
tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/build
.PHONY : tests/fast

#=============================================================================
# Target rules for targets named infer_test

# Build rule for target.
infer_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 infer_test
.PHONY : infer_test

# fast build rule for target.
infer_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/build
.PHONY : infer_test/fast

#=============================================================================
# Target rules for targets named blkmapi

# Build rule for target.
blkmapi: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 blkmapi
.PHONY : blkmapi

# fast build rule for target.
blkmapi/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/build
.PHONY : blkmapi/fast

#=============================================================================
# Target rules for targets named blkm_test

# Build rule for target.
blkm_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 blkm_test
.PHONY : blkm_test

# fast build rule for target.
blkm_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/build
.PHONY : blkm_test/fast

#=============================================================================
# Target rules for targets named BL

# Build rule for target.
BL: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 BL
.PHONY : BL

# fast build rule for target.
BL/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/build
.PHONY : BL/fast

#=============================================================================
# Target rules for targets named bl_infer

# Build rule for target.
bl_infer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bl_infer
.PHONY : bl_infer

# fast build rule for target.
bl_infer/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/build
.PHONY : bl_infer/fast

#=============================================================================
# Target rules for targets named lkm_test

# Build rule for target.
lkm_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lkm_test
.PHONY : lkm_test

# fast build rule for target.
lkm_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/build
.PHONY : lkm_test/fast

#=============================================================================
# Target rules for targets named fmt

# Build rule for target.
fmt: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fmt
.PHONY : fmt

# fast build rule for target.
fmt/fast:
	$(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/build
.PHONY : fmt/fast

#=============================================================================
# Target rules for targets named spdlog

# Build rule for target.
spdlog: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 spdlog
.PHONY : spdlog

# fast build rule for target.
spdlog/fast:
	$(MAKE) $(MAKESILENT) -f 3rdparty/spdlog/CMakeFiles/spdlog.dir/build.make 3rdparty/spdlog/CMakeFiles/spdlog.dir/build
.PHONY : spdlog/fast

#=============================================================================
# Target rules for targets named libusbgx

# Build rule for target.
libusbgx: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libusbgx
.PHONY : libusbgx

# fast build rule for target.
libusbgx/fast:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build
.PHONY : libusbgx/fast

#=============================================================================
# Target rules for targets named Catch2

# Build rule for target.
Catch2: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Catch2
.PHONY : Catch2

# fast build rule for target.
Catch2/fast:
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build
.PHONY : Catch2/fast

#=============================================================================
# Target rules for targets named Catch2WithMain

# Build rule for target.
Catch2WithMain: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Catch2WithMain
.PHONY : Catch2WithMain

# fast build rule for target.
Catch2WithMain/fast:
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build
.PHONY : Catch2WithMain/fast

#=============================================================================
# Target rules for targets named usb-1.0

# Build rule for target.
usb-1.0: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 usb-1.0
.PHONY : usb-1.0

# fast build rule for target.
usb-1.0/fast:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build
.PHONY : usb-1.0/fast

#=============================================================================
# Target rules for targets named thread_manager

# Build rule for target.
thread_manager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thread_manager
.PHONY : thread_manager

# fast build rule for target.
thread_manager/fast:
	$(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/build
.PHONY : thread_manager/fast

#=============================================================================
# Target rules for targets named blweb_backend

# Build rule for target.
blweb_backend: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 blweb_backend
.PHONY : blweb_backend

# fast build rule for target.
blweb_backend/fast:
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/build
.PHONY : blweb_backend/fast

#=============================================================================
# Target rules for targets named bl_server

# Build rule for target.
bl_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bl_server
.PHONY : bl_server

# fast build rule for target.
bl_server/fast:
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/build
.PHONY : bl_server/fast

3rdparty/CSerialPort-master/src/SerialPort.o: 3rdparty/CSerialPort-master/src/SerialPort.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPort.o

# target to build an object file
3rdparty/CSerialPort-master/src/SerialPort.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPort.cpp.o

3rdparty/CSerialPort-master/src/SerialPort.i: 3rdparty/CSerialPort-master/src/SerialPort.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPort.i

# target to preprocess a source file
3rdparty/CSerialPort-master/src/SerialPort.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPort.cpp.i

3rdparty/CSerialPort-master/src/SerialPort.s: 3rdparty/CSerialPort-master/src/SerialPort.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPort.s

# target to generate assembly for a file
3rdparty/CSerialPort-master/src/SerialPort.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPort.cpp.s

3rdparty/CSerialPort-master/src/SerialPortBase.o: 3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortBase.o

# target to build an object file
3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o

3rdparty/CSerialPort-master/src/SerialPortBase.i: 3rdparty/CSerialPort-master/src/SerialPortBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortBase.i

# target to preprocess a source file
3rdparty/CSerialPort-master/src/SerialPortBase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortBase.cpp.i

3rdparty/CSerialPort-master/src/SerialPortBase.s: 3rdparty/CSerialPort-master/src/SerialPortBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortBase.s

# target to generate assembly for a file
3rdparty/CSerialPort-master/src/SerialPortBase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortBase.cpp.s

3rdparty/CSerialPort-master/src/SerialPortInfo.o: 3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfo.o

# target to build an object file
3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o

3rdparty/CSerialPort-master/src/SerialPortInfo.i: 3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfo.i

# target to preprocess a source file
3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.i

3rdparty/CSerialPort-master/src/SerialPortInfo.s: 3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfo.s

# target to generate assembly for a file
3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.s

3rdparty/CSerialPort-master/src/SerialPortInfoBase.o: 3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoBase.o

# target to build an object file
3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o

3rdparty/CSerialPort-master/src/SerialPortInfoBase.i: 3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoBase.i

# target to preprocess a source file
3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.i

3rdparty/CSerialPort-master/src/SerialPortInfoBase.s: 3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoBase.s

# target to generate assembly for a file
3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.s

3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.o: 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.o

# target to build an object file
3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o

3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.i: 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.i

# target to preprocess a source file
3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.i

3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.s: 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.s

# target to generate assembly for a file
3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.s

3rdparty/CSerialPort-master/src/SerialPortUnixBase.o: 3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortUnixBase.o

# target to build an object file
3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o

3rdparty/CSerialPort-master/src/SerialPortUnixBase.i: 3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortUnixBase.i

# target to preprocess a source file
3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.i
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.i

3rdparty/CSerialPort-master/src/SerialPortUnixBase.s: 3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortUnixBase.s

# target to generate assembly for a file
3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.s
.PHONY : 3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.s

algorithm/MouseFov.o: algorithm/MouseFov.cpp.o
.PHONY : algorithm/MouseFov.o

# target to build an object file
algorithm/MouseFov.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o
.PHONY : algorithm/MouseFov.cpp.o

algorithm/MouseFov.i: algorithm/MouseFov.cpp.i
.PHONY : algorithm/MouseFov.i

# target to preprocess a source file
algorithm/MouseFov.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.i
.PHONY : algorithm/MouseFov.cpp.i

algorithm/MouseFov.s: algorithm/MouseFov.cpp.s
.PHONY : algorithm/MouseFov.s

# target to generate assembly for a file
algorithm/MouseFov.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.s
.PHONY : algorithm/MouseFov.cpp.s

algorithm/pid_controller.o: algorithm/pid_controller.cpp.o
.PHONY : algorithm/pid_controller.o

# target to build an object file
algorithm/pid_controller.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o
.PHONY : algorithm/pid_controller.cpp.o

algorithm/pid_controller.i: algorithm/pid_controller.cpp.i
.PHONY : algorithm/pid_controller.i

# target to preprocess a source file
algorithm/pid_controller.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.i
.PHONY : algorithm/pid_controller.cpp.i

algorithm/pid_controller.s: algorithm/pid_controller.cpp.s
.PHONY : algorithm/pid_controller.s

# target to generate assembly for a file
algorithm/pid_controller.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.s
.PHONY : algorithm/pid_controller.cpp.s

blkm/blkm_test.o: blkm/blkm_test.cpp.o
.PHONY : blkm/blkm_test.o

# target to build an object file
blkm/blkm_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/blkm/blkm_test.cpp.o
.PHONY : blkm/blkm_test.cpp.o

blkm/blkm_test.i: blkm/blkm_test.cpp.i
.PHONY : blkm/blkm_test.i

# target to preprocess a source file
blkm/blkm_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/blkm/blkm_test.cpp.i
.PHONY : blkm/blkm_test.cpp.i

blkm/blkm_test.s: blkm/blkm_test.cpp.s
.PHONY : blkm/blkm_test.s

# target to generate assembly for a file
blkm/blkm_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/blkm/blkm_test.cpp.s
.PHONY : blkm/blkm_test.cpp.s

blkm/blkmsdk.o: blkm/blkmsdk.cpp.o
.PHONY : blkm/blkmsdk.o

# target to build an object file
blkm/blkmsdk.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o
.PHONY : blkm/blkmsdk.cpp.o

blkm/blkmsdk.i: blkm/blkmsdk.cpp.i
.PHONY : blkm/blkmsdk.i

# target to preprocess a source file
blkm/blkmsdk.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.i
.PHONY : blkm/blkmsdk.cpp.i

blkm/blkmsdk.s: blkm/blkmsdk.cpp.s
.PHONY : blkm/blkmsdk.s

# target to generate assembly for a file
blkm/blkmsdk.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.s
.PHONY : blkm/blkmsdk.cpp.s

blkm/cloud_update.o: blkm/cloud_update.cpp.o
.PHONY : blkm/cloud_update.o

# target to build an object file
blkm/cloud_update.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o
.PHONY : blkm/cloud_update.cpp.o

blkm/cloud_update.i: blkm/cloud_update.cpp.i
.PHONY : blkm/cloud_update.i

# target to preprocess a source file
blkm/cloud_update.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.i
.PHONY : blkm/cloud_update.cpp.i

blkm/cloud_update.s: blkm/cloud_update.cpp.s
.PHONY : blkm/cloud_update.s

# target to generate assembly for a file
blkm/cloud_update.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.s
.PHONY : blkm/cloud_update.cpp.s

blkm/crypto_utils.o: blkm/crypto_utils.cpp.o
.PHONY : blkm/crypto_utils.o

# target to build an object file
blkm/crypto_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o
.PHONY : blkm/crypto_utils.cpp.o

blkm/crypto_utils.i: blkm/crypto_utils.cpp.i
.PHONY : blkm/crypto_utils.i

# target to preprocess a source file
blkm/crypto_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.i
.PHONY : blkm/crypto_utils.cpp.i

blkm/crypto_utils.s: blkm/crypto_utils.cpp.s
.PHONY : blkm/crypto_utils.s

# target to generate assembly for a file
blkm/crypto_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.s
.PHONY : blkm/crypto_utils.cpp.s

infer/base/file_utils.o: infer/base/file_utils.cpp.o
.PHONY : infer/base/file_utils.o

# target to build an object file
infer/base/file_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/infer/base/file_utils.cpp.o
.PHONY : infer/base/file_utils.cpp.o

infer/base/file_utils.i: infer/base/file_utils.cpp.i
.PHONY : infer/base/file_utils.i

# target to preprocess a source file
infer/base/file_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/infer/base/file_utils.cpp.i
.PHONY : infer/base/file_utils.cpp.i

infer/base/file_utils.s: infer/base/file_utils.cpp.s
.PHONY : infer/base/file_utils.s

# target to generate assembly for a file
infer/base/file_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/infer/base/file_utils.cpp.s
.PHONY : infer/base/file_utils.cpp.s

infer/core/aimbot/aimbot_debug.o: infer/core/aimbot/aimbot_debug.cpp.o
.PHONY : infer/core/aimbot/aimbot_debug.o

# target to build an object file
infer/core/aimbot/aimbot_debug.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o
.PHONY : infer/core/aimbot/aimbot_debug.cpp.o

infer/core/aimbot/aimbot_debug.i: infer/core/aimbot/aimbot_debug.cpp.i
.PHONY : infer/core/aimbot/aimbot_debug.i

# target to preprocess a source file
infer/core/aimbot/aimbot_debug.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.i
.PHONY : infer/core/aimbot/aimbot_debug.cpp.i

infer/core/aimbot/aimbot_debug.s: infer/core/aimbot/aimbot_debug.cpp.s
.PHONY : infer/core/aimbot/aimbot_debug.s

# target to generate assembly for a file
infer/core/aimbot/aimbot_debug.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.s
.PHONY : infer/core/aimbot/aimbot_debug.cpp.s

infer/core/aimbot/aimbot_fps.o: infer/core/aimbot/aimbot_fps.cpp.o
.PHONY : infer/core/aimbot/aimbot_fps.o

# target to build an object file
infer/core/aimbot/aimbot_fps.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o
.PHONY : infer/core/aimbot/aimbot_fps.cpp.o

infer/core/aimbot/aimbot_fps.i: infer/core/aimbot/aimbot_fps.cpp.i
.PHONY : infer/core/aimbot/aimbot_fps.i

# target to preprocess a source file
infer/core/aimbot/aimbot_fps.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.i
.PHONY : infer/core/aimbot/aimbot_fps.cpp.i

infer/core/aimbot/aimbot_fps.s: infer/core/aimbot/aimbot_fps.cpp.s
.PHONY : infer/core/aimbot/aimbot_fps.s

# target to generate assembly for a file
infer/core/aimbot/aimbot_fps.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.s
.PHONY : infer/core/aimbot/aimbot_fps.cpp.s

infer/core/aimbot/data_collector.o: infer/core/aimbot/data_collector.cpp.o
.PHONY : infer/core/aimbot/data_collector.o

# target to build an object file
infer/core/aimbot/data_collector.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o
.PHONY : infer/core/aimbot/data_collector.cpp.o

infer/core/aimbot/data_collector.i: infer/core/aimbot/data_collector.cpp.i
.PHONY : infer/core/aimbot/data_collector.i

# target to preprocess a source file
infer/core/aimbot/data_collector.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.i
.PHONY : infer/core/aimbot/data_collector.cpp.i

infer/core/aimbot/data_collector.s: infer/core/aimbot/data_collector.cpp.s
.PHONY : infer/core/aimbot/data_collector.s

# target to generate assembly for a file
infer/core/aimbot/data_collector.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.s
.PHONY : infer/core/aimbot/data_collector.cpp.s

infer/core/aimbot/infer_collector.o: infer/core/aimbot/infer_collector.cpp.o
.PHONY : infer/core/aimbot/infer_collector.o

# target to build an object file
infer/core/aimbot/infer_collector.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o
.PHONY : infer/core/aimbot/infer_collector.cpp.o

infer/core/aimbot/infer_collector.i: infer/core/aimbot/infer_collector.cpp.i
.PHONY : infer/core/aimbot/infer_collector.i

# target to preprocess a source file
infer/core/aimbot/infer_collector.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.i
.PHONY : infer/core/aimbot/infer_collector.cpp.i

infer/core/aimbot/infer_collector.s: infer/core/aimbot/infer_collector.cpp.s
.PHONY : infer/core/aimbot/infer_collector.s

# target to generate assembly for a file
infer/core/aimbot/infer_collector.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.s
.PHONY : infer/core/aimbot/infer_collector.cpp.s

infer/core/model/model.o: infer/core/model/model.cpp.o
.PHONY : infer/core/model/model.o

# target to build an object file
infer/core/model/model.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/model.cpp.o
.PHONY : infer/core/model/model.cpp.o

infer/core/model/model.i: infer/core/model/model.cpp.i
.PHONY : infer/core/model/model.i

# target to preprocess a source file
infer/core/model/model.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/model.cpp.i
.PHONY : infer/core/model/model.cpp.i

infer/core/model/model.s: infer/core/model/model.cpp.s
.PHONY : infer/core/model/model.s

# target to generate assembly for a file
infer/core/model/model.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/model.cpp.s
.PHONY : infer/core/model/model.cpp.s

infer/core/model/object_detector.o: infer/core/model/object_detector.cpp.o
.PHONY : infer/core/model/object_detector.o

# target to build an object file
infer/core/model/object_detector.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o
.PHONY : infer/core/model/object_detector.cpp.o

infer/core/model/object_detector.i: infer/core/model/object_detector.cpp.i
.PHONY : infer/core/model/object_detector.i

# target to preprocess a source file
infer/core/model/object_detector.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/object_detector.cpp.i
.PHONY : infer/core/model/object_detector.cpp.i

infer/core/model/object_detector.s: infer/core/model/object_detector.cpp.s
.PHONY : infer/core/model/object_detector.s

# target to generate assembly for a file
infer/core/model/object_detector.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/object_detector.cpp.s
.PHONY : infer/core/model/object_detector.cpp.s

infer/core/model/rknn/model_rknn.o: infer/core/model/rknn/model_rknn.cpp.o
.PHONY : infer/core/model/rknn/model_rknn.o

# target to build an object file
infer/core/model/rknn/model_rknn.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o
.PHONY : infer/core/model/rknn/model_rknn.cpp.o

infer/core/model/rknn/model_rknn.i: infer/core/model/rknn/model_rknn.cpp.i
.PHONY : infer/core/model/rknn/model_rknn.i

# target to preprocess a source file
infer/core/model/rknn/model_rknn.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.i
.PHONY : infer/core/model/rknn/model_rknn.cpp.i

infer/core/model/rknn/model_rknn.s: infer/core/model/rknn/model_rknn.cpp.s
.PHONY : infer/core/model/rknn/model_rknn.s

# target to generate assembly for a file
infer/core/model/rknn/model_rknn.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.s
.PHONY : infer/core/model/rknn/model_rknn.cpp.s

infer/core/model/yolo/yolo.o: infer/core/model/yolo/yolo.cpp.o
.PHONY : infer/core/model/yolo/yolo.o

# target to build an object file
infer/core/model/yolo/yolo.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o
.PHONY : infer/core/model/yolo/yolo.cpp.o

infer/core/model/yolo/yolo.i: infer/core/model/yolo/yolo.cpp.i
.PHONY : infer/core/model/yolo/yolo.i

# target to preprocess a source file
infer/core/model/yolo/yolo.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.i
.PHONY : infer/core/model/yolo/yolo.cpp.i

infer/core/model/yolo/yolo.s: infer/core/model/yolo/yolo.cpp.s
.PHONY : infer/core/model/yolo/yolo.s

# target to generate assembly for a file
infer/core/model/yolo/yolo.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.s
.PHONY : infer/core/model/yolo/yolo.cpp.s

infer/core/model/yolo/yolo_v6.o: infer/core/model/yolo/yolo_v6.cpp.o
.PHONY : infer/core/model/yolo/yolo_v6.o

# target to build an object file
infer/core/model/yolo/yolo_v6.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o
.PHONY : infer/core/model/yolo/yolo_v6.cpp.o

infer/core/model/yolo/yolo_v6.i: infer/core/model/yolo/yolo_v6.cpp.i
.PHONY : infer/core/model/yolo/yolo_v6.i

# target to preprocess a source file
infer/core/model/yolo/yolo_v6.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.i
.PHONY : infer/core/model/yolo/yolo_v6.cpp.i

infer/core/model/yolo/yolo_v6.s: infer/core/model/yolo/yolo_v6.cpp.s
.PHONY : infer/core/model/yolo/yolo_v6.s

# target to generate assembly for a file
infer/core/model/yolo/yolo_v6.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.s
.PHONY : infer/core/model/yolo/yolo_v6.cpp.s

infer/core/model/yolo/yolo_v8.o: infer/core/model/yolo/yolo_v8.cpp.o
.PHONY : infer/core/model/yolo/yolo_v8.o

# target to build an object file
infer/core/model/yolo/yolo_v8.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o
.PHONY : infer/core/model/yolo/yolo_v8.cpp.o

infer/core/model/yolo/yolo_v8.i: infer/core/model/yolo/yolo_v8.cpp.i
.PHONY : infer/core/model/yolo/yolo_v8.i

# target to preprocess a source file
infer/core/model/yolo/yolo_v8.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.i
.PHONY : infer/core/model/yolo/yolo_v8.cpp.i

infer/core/model/yolo/yolo_v8.s: infer/core/model/yolo/yolo_v8.cpp.s
.PHONY : infer/core/model/yolo/yolo_v8.s

# target to generate assembly for a file
infer/core/model/yolo/yolo_v8.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.s
.PHONY : infer/core/model/yolo/yolo_v8.cpp.s

infer/core/video/video_capture.o: infer/core/video/video_capture.cpp.o
.PHONY : infer/core/video/video_capture.o

# target to build an object file
infer/core/video/video_capture.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o
.PHONY : infer/core/video/video_capture.cpp.o

infer/core/video/video_capture.i: infer/core/video/video_capture.cpp.i
.PHONY : infer/core/video/video_capture.i

# target to preprocess a source file
infer/core/video/video_capture.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/video/video_capture.cpp.i
.PHONY : infer/core/video/video_capture.cpp.i

infer/core/video/video_capture.s: infer/core/video/video_capture.cpp.s
.PHONY : infer/core/video/video_capture.s

# target to generate assembly for a file
infer/core/video/video_capture.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/infer/core/video/video_capture.cpp.s
.PHONY : infer/core/video/video_capture.cpp.s

infer/infer_test.o: infer/infer_test.cpp.o
.PHONY : infer/infer_test.o

# target to build an object file
infer/infer_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/infer/infer_test.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o
.PHONY : infer/infer_test.cpp.o

infer/infer_test.i: infer/infer_test.cpp.i
.PHONY : infer/infer_test.i

# target to preprocess a source file
infer/infer_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/infer/infer_test.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/infer/infer_test.cpp.i
.PHONY : infer/infer_test.cpp.i

infer/infer_test.s: infer/infer_test.cpp.s
.PHONY : infer/infer_test.s

# target to generate assembly for a file
infer/infer_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/infer/infer_test.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/infer/infer_test.cpp.s
.PHONY : infer/infer_test.cpp.s

infer/tests/core/video_capture.o: infer/tests/core/video_capture.cpp.o
.PHONY : infer/tests/core/video_capture.o

# target to build an object file
infer/tests/core/video_capture.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o
.PHONY : infer/tests/core/video_capture.cpp.o

infer/tests/core/video_capture.i: infer/tests/core/video_capture.cpp.i
.PHONY : infer/tests/core/video_capture.i

# target to preprocess a source file
infer/tests/core/video_capture.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.i
.PHONY : infer/tests/core/video_capture.cpp.i

infer/tests/core/video_capture.s: infer/tests/core/video_capture.cpp.s
.PHONY : infer/tests/core/video_capture.s

# target to generate assembly for a file
infer/tests/core/video_capture.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.s
.PHONY : infer/tests/core/video_capture.cpp.s

infer/tests/external/librga.o: infer/tests/external/librga.cpp.o
.PHONY : infer/tests/external/librga.o

# target to build an object file
infer/tests/external/librga.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o
.PHONY : infer/tests/external/librga.cpp.o

infer/tests/external/librga.i: infer/tests/external/librga.cpp.i
.PHONY : infer/tests/external/librga.i

# target to preprocess a source file
infer/tests/external/librga.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/infer/tests/external/librga.cpp.i
.PHONY : infer/tests/external/librga.cpp.i

infer/tests/external/librga.s: infer/tests/external/librga.cpp.s
.PHONY : infer/tests/external/librga.s

# target to generate assembly for a file
infer/tests/external/librga.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/infer/tests/external/librga.cpp.s
.PHONY : infer/tests/external/librga.cpp.s

lkm/KeyMouseConfig.o: lkm/KeyMouseConfig.cpp.o
.PHONY : lkm/KeyMouseConfig.o

# target to build an object file
lkm/KeyMouseConfig.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o
.PHONY : lkm/KeyMouseConfig.cpp.o

lkm/KeyMouseConfig.i: lkm/KeyMouseConfig.cpp.i
.PHONY : lkm/KeyMouseConfig.i

# target to preprocess a source file
lkm/KeyMouseConfig.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.i
.PHONY : lkm/KeyMouseConfig.cpp.i

lkm/KeyMouseConfig.s: lkm/KeyMouseConfig.cpp.s
.PHONY : lkm/KeyMouseConfig.s

# target to generate assembly for a file
lkm/KeyMouseConfig.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.s
.PHONY : lkm/KeyMouseConfig.cpp.s

lkm/LkmApi.o: lkm/LkmApi.cpp.o
.PHONY : lkm/LkmApi.o

# target to build an object file
lkm/LkmApi.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o
.PHONY : lkm/LkmApi.cpp.o

lkm/LkmApi.i: lkm/LkmApi.cpp.i
.PHONY : lkm/LkmApi.i

# target to preprocess a source file
lkm/LkmApi.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.i
.PHONY : lkm/LkmApi.cpp.i

lkm/LkmApi.s: lkm/LkmApi.cpp.s
.PHONY : lkm/LkmApi.s

# target to generate assembly for a file
lkm/LkmApi.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.s
.PHONY : lkm/LkmApi.cpp.s

lkm/keymouse.o: lkm/keymouse.cpp.o
.PHONY : lkm/keymouse.o

# target to build an object file
lkm/keymouse.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o
.PHONY : lkm/keymouse.cpp.o

lkm/keymouse.i: lkm/keymouse.cpp.i
.PHONY : lkm/keymouse.i

# target to preprocess a source file
lkm/keymouse.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.i
.PHONY : lkm/keymouse.cpp.i

lkm/keymouse.s: lkm/keymouse.cpp.s
.PHONY : lkm/keymouse.s

# target to generate assembly for a file
lkm/keymouse.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.s
.PHONY : lkm/keymouse.cpp.s

lkm/lkm_test.o: lkm/lkm_test.cpp.o
.PHONY : lkm/lkm_test.o

# target to build an object file
lkm/lkm_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/lkm/lkm_test.cpp.o
.PHONY : lkm/lkm_test.cpp.o

lkm/lkm_test.i: lkm/lkm_test.cpp.i
.PHONY : lkm/lkm_test.i

# target to preprocess a source file
lkm/lkm_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/lkm/lkm_test.cpp.i
.PHONY : lkm/lkm_test.cpp.i

lkm/lkm_test.s: lkm/lkm_test.cpp.s
.PHONY : lkm/lkm_test.s

# target to generate assembly for a file
lkm/lkm_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/lkm/lkm_test.cpp.s
.PHONY : lkm/lkm_test.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/main.cpp.s
.PHONY : main.cpp.s

utils/gmodels.o: utils/gmodels.cpp.o
.PHONY : utils/gmodels.o

# target to build an object file
utils/gmodels.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/gmodels.cpp.o
.PHONY : utils/gmodels.cpp.o

utils/gmodels.i: utils/gmodels.cpp.i
.PHONY : utils/gmodels.i

# target to preprocess a source file
utils/gmodels.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/gmodels.cpp.i
.PHONY : utils/gmodels.cpp.i

utils/gmodels.s: utils/gmodels.cpp.s
.PHONY : utils/gmodels.s

# target to generate assembly for a file
utils/gmodels.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/gmodels.cpp.s
.PHONY : utils/gmodels.cpp.s

utils/logger.o: utils/logger.cpp.o
.PHONY : utils/logger.o

# target to build an object file
utils/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/logger.cpp.o
.PHONY : utils/logger.cpp.o

utils/logger.i: utils/logger.cpp.i
.PHONY : utils/logger.i

# target to preprocess a source file
utils/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/logger.cpp.i
.PHONY : utils/logger.cpp.i

utils/logger.s: utils/logger.cpp.s
.PHONY : utils/logger.s

# target to generate assembly for a file
utils/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/logger.cpp.s
.PHONY : utils/logger.cpp.s

utils/wolf240hz_patch.o: utils/wolf240hz_patch.cpp.o
.PHONY : utils/wolf240hz_patch.o

# target to build an object file
utils/wolf240hz_patch.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o
.PHONY : utils/wolf240hz_patch.cpp.o

utils/wolf240hz_patch.i: utils/wolf240hz_patch.cpp.i
.PHONY : utils/wolf240hz_patch.i

# target to preprocess a source file
utils/wolf240hz_patch.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.i
.PHONY : utils/wolf240hz_patch.cpp.i

utils/wolf240hz_patch.s: utils/wolf240hz_patch.cpp.s
.PHONY : utils/wolf240hz_patch.s

# target to generate assembly for a file
utils/wolf240hz_patch.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.s
.PHONY : utils/wolf240hz_patch.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... BL"
	@echo "... Catch2"
	@echo "... Catch2WithMain"
	@echo "... algorithm_lib"
	@echo "... base"
	@echo "... bl_infer"
	@echo "... bl_server"
	@echo "... blkm_test"
	@echo "... blkmapi"
	@echo "... blweb_backend"
	@echo "... core"
	@echo "... cserialport"
	@echo "... fmt"
	@echo "... infer_test"
	@echo "... libusbgx"
	@echo "... lkm_test"
	@echo "... lkmapi"
	@echo "... spdlog"
	@echo "... tests"
	@echo "... thread_manager"
	@echo "... usb-1.0"
	@echo "... utils"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPort.o"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPort.i"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPort.s"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortBase.o"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortBase.i"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortBase.s"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfo.o"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfo.i"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfo.s"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfoBase.o"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfoBase.i"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfoBase.s"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.o"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.i"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.s"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortUnixBase.o"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortUnixBase.i"
	@echo "... 3rdparty/CSerialPort-master/src/SerialPortUnixBase.s"
	@echo "... algorithm/MouseFov.o"
	@echo "... algorithm/MouseFov.i"
	@echo "... algorithm/MouseFov.s"
	@echo "... algorithm/pid_controller.o"
	@echo "... algorithm/pid_controller.i"
	@echo "... algorithm/pid_controller.s"
	@echo "... blkm/blkm_test.o"
	@echo "... blkm/blkm_test.i"
	@echo "... blkm/blkm_test.s"
	@echo "... blkm/blkmsdk.o"
	@echo "... blkm/blkmsdk.i"
	@echo "... blkm/blkmsdk.s"
	@echo "... blkm/cloud_update.o"
	@echo "... blkm/cloud_update.i"
	@echo "... blkm/cloud_update.s"
	@echo "... blkm/crypto_utils.o"
	@echo "... blkm/crypto_utils.i"
	@echo "... blkm/crypto_utils.s"
	@echo "... infer/base/file_utils.o"
	@echo "... infer/base/file_utils.i"
	@echo "... infer/base/file_utils.s"
	@echo "... infer/core/aimbot/aimbot_debug.o"
	@echo "... infer/core/aimbot/aimbot_debug.i"
	@echo "... infer/core/aimbot/aimbot_debug.s"
	@echo "... infer/core/aimbot/aimbot_fps.o"
	@echo "... infer/core/aimbot/aimbot_fps.i"
	@echo "... infer/core/aimbot/aimbot_fps.s"
	@echo "... infer/core/aimbot/data_collector.o"
	@echo "... infer/core/aimbot/data_collector.i"
	@echo "... infer/core/aimbot/data_collector.s"
	@echo "... infer/core/aimbot/infer_collector.o"
	@echo "... infer/core/aimbot/infer_collector.i"
	@echo "... infer/core/aimbot/infer_collector.s"
	@echo "... infer/core/model/model.o"
	@echo "... infer/core/model/model.i"
	@echo "... infer/core/model/model.s"
	@echo "... infer/core/model/object_detector.o"
	@echo "... infer/core/model/object_detector.i"
	@echo "... infer/core/model/object_detector.s"
	@echo "... infer/core/model/rknn/model_rknn.o"
	@echo "... infer/core/model/rknn/model_rknn.i"
	@echo "... infer/core/model/rknn/model_rknn.s"
	@echo "... infer/core/model/yolo/yolo.o"
	@echo "... infer/core/model/yolo/yolo.i"
	@echo "... infer/core/model/yolo/yolo.s"
	@echo "... infer/core/model/yolo/yolo_v6.o"
	@echo "... infer/core/model/yolo/yolo_v6.i"
	@echo "... infer/core/model/yolo/yolo_v6.s"
	@echo "... infer/core/model/yolo/yolo_v8.o"
	@echo "... infer/core/model/yolo/yolo_v8.i"
	@echo "... infer/core/model/yolo/yolo_v8.s"
	@echo "... infer/core/video/video_capture.o"
	@echo "... infer/core/video/video_capture.i"
	@echo "... infer/core/video/video_capture.s"
	@echo "... infer/infer_test.o"
	@echo "... infer/infer_test.i"
	@echo "... infer/infer_test.s"
	@echo "... infer/tests/core/video_capture.o"
	@echo "... infer/tests/core/video_capture.i"
	@echo "... infer/tests/core/video_capture.s"
	@echo "... infer/tests/external/librga.o"
	@echo "... infer/tests/external/librga.i"
	@echo "... infer/tests/external/librga.s"
	@echo "... lkm/KeyMouseConfig.o"
	@echo "... lkm/KeyMouseConfig.i"
	@echo "... lkm/KeyMouseConfig.s"
	@echo "... lkm/LkmApi.o"
	@echo "... lkm/LkmApi.i"
	@echo "... lkm/LkmApi.s"
	@echo "... lkm/keymouse.o"
	@echo "... lkm/keymouse.i"
	@echo "... lkm/keymouse.s"
	@echo "... lkm/lkm_test.o"
	@echo "... lkm/lkm_test.i"
	@echo "... lkm/lkm_test.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... utils/gmodels.o"
	@echo "... utils/gmodels.i"
	@echo "... utils/gmodels.s"
	@echo "... utils/logger.o"
	@echo "... utils/logger.i"
	@echo "... utils/logger.s"
	@echo "... utils/wolf240hz_patch.o"
	@echo "... utils/wolf240hz_patch.i"
	@echo "... utils/wolf240hz_patch.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

