# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include backend/CMakeFiles/blweb_backend.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include backend/CMakeFiles/blweb_backend.dir/compiler_depend.make

# Include the progress variables for this target.
include backend/CMakeFiles/blweb_backend.dir/progress.make

# Include the compile flags for this target's objects.
include backend/CMakeFiles/blweb_backend.dir/flags.make

backend/CMakeFiles/blweb_backend.dir/codegen:
.PHONY : backend/CMakeFiles/blweb_backend.dir/codegen

backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/mysql_db.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/mysql_db.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/mysql_db.cpp > CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/mysql_db.cpp -o CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/poco_websocket_server.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/poco_websocket_server.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/poco_websocket_server.cpp > CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/poco_websocket_server.cpp -o CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/message_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/message_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/message_handlers.cpp > CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/message_handlers.cpp -o CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/fov_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/fov_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/fov_service.cpp > CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/fov_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/register_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/register_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/register_service.cpp > CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/register_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/home_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/home_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/home_service.cpp > CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/home_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/header_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/header_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/header_service.cpp > CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/header_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/function_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/function_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/function_service.cpp > CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/function_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/pid_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/pid_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/pid_service.cpp > CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/pid_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/aim_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/aim_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/aim_service.cpp > CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/aim_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/login_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/login_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/login_service.cpp > CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/login_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/update_models.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/update_models.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/update_models.cpp > CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/update_models.cpp -o CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/fire_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/fire_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/fire_service.cpp > CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/fire_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blserver/data_collection_service.cpp
backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o -MF CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o.d -o CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blserver/data_collection_service.cpp

backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blserver/data_collection_service.cpp > CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.i

backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blserver/data_collection_service.cpp -o CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/registerdb.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/registerdb.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/registerdb.cpp > CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/registerdb.cpp -o CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/function_configs_db.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/function_configs_db.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/function_configs_db.cpp > CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/function_configs_db.cpp -o CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/pid_configs_db.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/pid_configs_db.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/pid_configs_db.cpp > CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/pid_configs_db.cpp -o CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/aim_configs_db.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/aim_configs_db.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/aim_configs_db.cpp > CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/aim_configs_db.cpp -o CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/login_db.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/login_db.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/login_db.cpp > CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/login_db.cpp -o CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/fovconfgidb.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/fovconfgidb.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/fovconfgidb.cpp > CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/fovconfgidb.cpp -o CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/userinfodb.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/userinfodb.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/userinfodb.cpp > CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/userinfodb.cpp -o CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/fire_configs_db.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/fire_configs_db.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/fire_configs_db.cpp > CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/fire_configs_db.cpp -o CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.s

backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/blsql/data_collections_db.cpp
backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o -MF CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o.d -o CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/blsql/data_collections_db.cpp

backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/blsql/data_collections_db.cpp > CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.i

backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/blsql/data_collections_db.cpp -o CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.s

backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp
backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o -MF CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o.d -o CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o -c /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp

backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp > CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.i

backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp -o CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/pid_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/pid_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/pid_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/pid_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/function_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/function_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/function_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/function_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/home_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/home_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/home_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/home_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/aim_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/aim_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/aim_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/aim_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/fov_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/fov_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/fov_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/fov_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/login_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/login_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/login_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/login_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/register_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/register_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/register_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/register_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/header_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/header_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/header_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/header_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/fire_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/fire_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/fire_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/fire_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.s

backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/flags.make
backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/handlers/data_collection_handlers.cpp
backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o: backend/CMakeFiles/blweb_backend.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o -MF CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o.d -o CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/handlers/data_collection_handlers.cpp

backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/handlers/data_collection_handlers.cpp > CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.i

backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/handlers/data_collection_handlers.cpp -o CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.s

# Object files for target blweb_backend
blweb_backend_OBJECTS = \
"CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o" \
"CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o" \
"CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o" \
"CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o"

# External object files for target blweb_backend
blweb_backend_EXTERNAL_OBJECTS =

bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/build.make
bin/lib/libblweb_backend.a: backend/CMakeFiles/blweb_backend.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Linking CXX static library ../bin/lib/libblweb_backend.a"
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && $(CMAKE_COMMAND) -P CMakeFiles/blweb_backend.dir/cmake_clean_target.cmake
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/blweb_backend.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
backend/CMakeFiles/blweb_backend.dir/build: bin/lib/libblweb_backend.a
.PHONY : backend/CMakeFiles/blweb_backend.dir/build

backend/CMakeFiles/blweb_backend.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_new/backend && $(CMAKE_COMMAND) -P CMakeFiles/blweb_backend.dir/cmake_clean.cmake
.PHONY : backend/CMakeFiles/blweb_backend.dir/clean

backend/CMakeFiles/blweb_backend.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/backend /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/backend /home/<USER>/mywork/poco_serverdemo/build_new/backend/CMakeFiles/blweb_backend.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : backend/CMakeFiles/blweb_backend.dir/depend

