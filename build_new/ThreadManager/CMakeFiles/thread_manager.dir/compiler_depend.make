# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.cpp \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.h \
  /home/<USER>/mywork/poco_serverdemo/blkm/cloud_update.h \
  /home/<USER>/mywork/poco_serverdemo/blkm/crypto_utils.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h \
  /home/<USER>/mywork/poco_serverdemo/utils/gmodels.h \
  /home/<USER>/mywork/poco_serverdemo/utils/logger.h \
  /home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/types.h \
  /usr/include/Poco/AbstractDelegate.h \
  /usr/include/Poco/AbstractEvent.h \
  /usr/include/Poco/ActiveMethod.h \
  /usr/include/Poco/ActiveResult.h \
  /usr/include/Poco/ActiveRunnable.h \
  /usr/include/Poco/ActiveStarter.h \
  /usr/include/Poco/ActiveThreadPool.h \
  /usr/include/Poco/Alignment.h \
  /usr/include/Poco/Any.h \
  /usr/include/Poco/Ascii.h \
  /usr/include/Poco/AtomicCounter.h \
  /usr/include/Poco/AutoPtr.h \
  /usr/include/Poco/BasicEvent.h \
  /usr/include/Poco/BinaryReader.h \
  /usr/include/Poco/BinaryWriter.h \
  /usr/include/Poco/Buffer.h \
  /usr/include/Poco/Bugcheck.h \
  /usr/include/Poco/Config.h \
  /usr/include/Poco/Crypto/Cipher.h \
  /usr/include/Poco/Crypto/Crypto.h \
  /usr/include/Poco/Crypto/CryptoTransform.h \
  /usr/include/Poco/DateTime.h \
  /usr/include/Poco/DateTimeFormat.h \
  /usr/include/Poco/DateTimeFormatter.h \
  /usr/include/Poco/DateTimeParser.h \
  /usr/include/Poco/Debugger.h \
  /usr/include/Poco/DefaultStrategy.h \
  /usr/include/Poco/Dynamic/Struct.h \
  /usr/include/Poco/Dynamic/Var.h \
  /usr/include/Poco/Dynamic/VarHolder.h \
  /usr/include/Poco/Dynamic/VarIterator.h \
  /usr/include/Poco/Environment.h \
  /usr/include/Poco/Error.h \
  /usr/include/Poco/Event.h \
  /usr/include/Poco/Event_POSIX.h \
  /usr/include/Poco/Exception.h \
  /usr/include/Poco/FIFOBuffer.h \
  /usr/include/Poco/FPEnvironment.h \
  /usr/include/Poco/FPEnvironment_C99.h \
  /usr/include/Poco/Format.h \
  /usr/include/Poco/Foundation.h \
  /usr/include/Poco/JSON/Array.h \
  /usr/include/Poco/JSON/JSON.h \
  /usr/include/Poco/JSON/Object.h \
  /usr/include/Poco/JSON/Stringifier.h \
  /usr/include/Poco/JSONString.h \
  /usr/include/Poco/ListMap.h \
  /usr/include/Poco/LocalDateTime.h \
  /usr/include/Poco/MemoryStream.h \
  /usr/include/Poco/MetaProgramming.h \
  /usr/include/Poco/Mutex.h \
  /usr/include/Poco/Mutex_POSIX.h \
  /usr/include/Poco/Net/HTTPAuthenticationParams.h \
  /usr/include/Poco/Net/HTTPBasicCredentials.h \
  /usr/include/Poco/Net/HTTPClientSession.h \
  /usr/include/Poco/Net/HTTPCookie.h \
  /usr/include/Poco/Net/HTTPDigestCredentials.h \
  /usr/include/Poco/Net/HTTPMessage.h \
  /usr/include/Poco/Net/HTTPNTLMCredentials.h \
  /usr/include/Poco/Net/HTTPRequest.h \
  /usr/include/Poco/Net/HTTPResponse.h \
  /usr/include/Poco/Net/HTTPSession.h \
  /usr/include/Poco/Net/IPAddress.h \
  /usr/include/Poco/Net/IPAddressImpl.h \
  /usr/include/Poco/Net/MessageHeader.h \
  /usr/include/Poco/Net/NTLMCredentials.h \
  /usr/include/Poco/Net/NameValueCollection.h \
  /usr/include/Poco/Net/Net.h \
  /usr/include/Poco/Net/SSPINTLMCredentials.h \
  /usr/include/Poco/Net/Socket.h \
  /usr/include/Poco/Net/SocketAddress.h \
  /usr/include/Poco/Net/SocketAddressImpl.h \
  /usr/include/Poco/Net/SocketDefs.h \
  /usr/include/Poco/Net/SocketImpl.h \
  /usr/include/Poco/Net/StreamSocket.h \
  /usr/include/Poco/NotificationStrategy.h \
  /usr/include/Poco/Nullable.h \
  /usr/include/Poco/NumberFormatter.h \
  /usr/include/Poco/NumberParser.h \
  /usr/include/Poco/NumericString.h \
  /usr/include/Poco/OrderedMap.h \
  /usr/include/Poco/OrderedSet.h \
  /usr/include/Poco/Platform.h \
  /usr/include/Poco/Platform_POSIX.h \
  /usr/include/Poco/RefCountedObject.h \
  /usr/include/Poco/Runnable.h \
  /usr/include/Poco/ScopedLock.h \
  /usr/include/Poco/SharedPtr.h \
  /usr/include/Poco/SignalHandler.h \
  /usr/include/Poco/SingletonHolder.h \
  /usr/include/Poco/StreamUtil.h \
  /usr/include/Poco/String.h \
  /usr/include/Poco/Thread.h \
  /usr/include/Poco/Thread_POSIX.h \
  /usr/include/Poco/Timespan.h \
  /usr/include/Poco/Timestamp.h \
  /usr/include/Poco/Types.h \
  /usr/include/Poco/URI.h \
  /usr/include/Poco/UTF8String.h \
  /usr/include/Poco/UTFString.h \
  /usr/include/Poco/UUID.h \
  /usr/include/Poco/UnicodeConverter.h \
  /usr/include/Poco/ordered_hash.h \
  /usr/include/Poco/ordered_map.h \
  /usr/include/Poco/ordered_set.h \
  /usr/include/alloca.h \
  /usr/include/arpa/inet.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/ioctl.h \
  /usr/include/asm-generic/ioctls.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/ioctl.h \
  /usr/include/asm/ioctls.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/fenv.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/in.h \
  /usr/include/bits/ioctl-types.h \
  /usr/include/bits/ioctls.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/netdb.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/__sigval_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigevent_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio-ext.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cxxabi_tweaks.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/algorithm \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/deque.tcc \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/list.tcc \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algo.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_function.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_deque.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_list.h \
  /usr/include/c++/14.2.1/bits/stl_map.h \
  /usr/include/c++/14.2.1/bits/stl_multimap.h \
  /usr/include/c++/14.2.1/bits/stl_multiset.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_set.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_tree.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/stream_iterator.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/unordered_set.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/cxxabi.h \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/deque \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/fenv.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/functional \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/iostream \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/iterator \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/list \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/map \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/set \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stdlib.h \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/unordered_set \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/fenv.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/ioctl.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/net/if.h \
  /usr/include/netdb.h \
  /usr/include/netinet/in.h \
  /usr/include/netinet/tcp.h \
  /usr/include/openssl/bio.h \
  /usr/include/openssl/bioerr.h \
  /usr/include/openssl/configuration.h \
  /usr/include/openssl/core.h \
  /usr/include/openssl/crypto.h \
  /usr/include/openssl/cryptoerr.h \
  /usr/include/openssl/cryptoerr_legacy.h \
  /usr/include/openssl/e_os2.h \
  /usr/include/openssl/err.h \
  /usr/include/openssl/lhash.h \
  /usr/include/openssl/macros.h \
  /usr/include/openssl/opensslconf.h \
  /usr/include/openssl/opensslv.h \
  /usr/include/openssl/safestack.h \
  /usr/include/openssl/stack.h \
  /usr/include/openssl/symhacks.h \
  /usr/include/openssl/types.h \
  /usr/include/pthread.h \
  /usr/include/rpc/netdb.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/setjmp.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/ioctl.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/ttydefaults.h \
  /usr/include/sys/types.h \
  /usr/include/sys/uio.h \
  /usr/include/sys/un.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.cpp \
  /home/<USER>/mywork/poco_serverdemo/utils/gmodels.h \
  /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortInfo.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortListener.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort_global.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/include/rknn_api.h \
  /home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/fps_counter.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/macros.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/types.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h \
  /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_fps.h \
  /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/data_collector.h \
  /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/infer_collector.h \
  /home/<USER>/mywork/poco_serverdemo/infer/core/model/model.h \
  /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo.h \
  /home/<USER>/mywork/poco_serverdemo/utils/logger.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/ioctl.h \
  /usr/include/asm-generic/ioctls.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/ioctl.h \
  /usr/include/asm/ioctls.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/ioctl-types.h \
  /usr/include/bits/ioctls.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/opt_random.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/algorithm \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/deque.tcc \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/list.tcc \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/random.h \
  /usr/include/c++/14.2.1/bits/random.tcc \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algo.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_function.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_deque.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_list.h \
  /usr/include/c++/14.2.1/bits/stl_map.h \
  /usr/include/c++/14.2.1/bits/stl_multimap.h \
  /usr/include/c++/14.2.1/bits/stl_multiset.h \
  /usr/include/c++/14.2.1/bits/stl_numeric.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_queue.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_set.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_tree.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/cfloat \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/complex \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/deque \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/functional \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/iostream \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/list \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/map \
  /usr/include/c++/14.2.1/math.h \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/numeric \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_numeric_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/queue \
  /usr/include/c++/14.2.1/random \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/set \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stdlib.h \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/input-event-codes.h \
  /usr/include/linux/input.h \
  /usr/include/linux/ioctl.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/linux/uinput.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/opencv4/opencv2/calib3d.hpp \
  /usr/include/opencv4/opencv2/core.hpp \
  /usr/include/opencv4/opencv2/core/affine.hpp \
  /usr/include/opencv4/opencv2/core/async.hpp \
  /usr/include/opencv4/opencv2/core/base.hpp \
  /usr/include/opencv4/opencv2/core/bufferpool.hpp \
  /usr/include/opencv4/opencv2/core/check.hpp \
  /usr/include/opencv4/opencv2/core/cuda.hpp \
  /usr/include/opencv4/opencv2/core/cuda.inl.hpp \
  /usr/include/opencv4/opencv2/core/cuda_types.hpp \
  /usr/include/opencv4/opencv2/core/cv_cpu_dispatch.h \
  /usr/include/opencv4/opencv2/core/cvdef.h \
  /usr/include/opencv4/opencv2/core/cvstd.hpp \
  /usr/include/opencv4/opencv2/core/cvstd.inl.hpp \
  /usr/include/opencv4/opencv2/core/cvstd_wrapper.hpp \
  /usr/include/opencv4/opencv2/core/fast_math.hpp \
  /usr/include/opencv4/opencv2/core/hal/interface.h \
  /usr/include/opencv4/opencv2/core/mat.hpp \
  /usr/include/opencv4/opencv2/core/mat.inl.hpp \
  /usr/include/opencv4/opencv2/core/matx.hpp \
  /usr/include/opencv4/opencv2/core/matx.inl.hpp \
  /usr/include/opencv4/opencv2/core/neon_utils.hpp \
  /usr/include/opencv4/opencv2/core/operations.hpp \
  /usr/include/opencv4/opencv2/core/optim.hpp \
  /usr/include/opencv4/opencv2/core/ovx.hpp \
  /usr/include/opencv4/opencv2/core/persistence.hpp \
  /usr/include/opencv4/opencv2/core/saturate.hpp \
  /usr/include/opencv4/opencv2/core/traits.hpp \
  /usr/include/opencv4/opencv2/core/types.hpp \
  /usr/include/opencv4/opencv2/core/utility.hpp \
  /usr/include/opencv4/opencv2/core/utils/logger.defines.hpp \
  /usr/include/opencv4/opencv2/core/utils/logger.hpp \
  /usr/include/opencv4/opencv2/core/utils/logtag.hpp \
  /usr/include/opencv4/opencv2/core/version.hpp \
  /usr/include/opencv4/opencv2/core/vsx_utils.hpp \
  /usr/include/opencv4/opencv2/dnn.hpp \
  /usr/include/opencv4/opencv2/dnn/dict.hpp \
  /usr/include/opencv4/opencv2/dnn/dnn.hpp \
  /usr/include/opencv4/opencv2/dnn/dnn.inl.hpp \
  /usr/include/opencv4/opencv2/dnn/layer.hpp \
  /usr/include/opencv4/opencv2/dnn/utils/inference_engine.hpp \
  /usr/include/opencv4/opencv2/dnn/version.hpp \
  /usr/include/opencv4/opencv2/features2d.hpp \
  /usr/include/opencv4/opencv2/flann.hpp \
  /usr/include/opencv4/opencv2/flann/all_indices.h \
  /usr/include/opencv4/opencv2/flann/allocator.h \
  /usr/include/opencv4/opencv2/flann/any.h \
  /usr/include/opencv4/opencv2/flann/autotuned_index.h \
  /usr/include/opencv4/opencv2/flann/composite_index.h \
  /usr/include/opencv4/opencv2/flann/config.h \
  /usr/include/opencv4/opencv2/flann/defines.h \
  /usr/include/opencv4/opencv2/flann/dist.h \
  /usr/include/opencv4/opencv2/flann/dynamic_bitset.h \
  /usr/include/opencv4/opencv2/flann/flann_base.hpp \
  /usr/include/opencv4/opencv2/flann/general.h \
  /usr/include/opencv4/opencv2/flann/ground_truth.h \
  /usr/include/opencv4/opencv2/flann/heap.h \
  /usr/include/opencv4/opencv2/flann/hierarchical_clustering_index.h \
  /usr/include/opencv4/opencv2/flann/index_testing.h \
  /usr/include/opencv4/opencv2/flann/kdtree_index.h \
  /usr/include/opencv4/opencv2/flann/kdtree_single_index.h \
  /usr/include/opencv4/opencv2/flann/kmeans_index.h \
  /usr/include/opencv4/opencv2/flann/linear_index.h \
  /usr/include/opencv4/opencv2/flann/logger.h \
  /usr/include/opencv4/opencv2/flann/lsh_index.h \
  /usr/include/opencv4/opencv2/flann/lsh_table.h \
  /usr/include/opencv4/opencv2/flann/matrix.h \
  /usr/include/opencv4/opencv2/flann/miniflann.hpp \
  /usr/include/opencv4/opencv2/flann/nn_index.h \
  /usr/include/opencv4/opencv2/flann/params.h \
  /usr/include/opencv4/opencv2/flann/random.h \
  /usr/include/opencv4/opencv2/flann/result_set.h \
  /usr/include/opencv4/opencv2/flann/sampling.h \
  /usr/include/opencv4/opencv2/flann/saving.h \
  /usr/include/opencv4/opencv2/flann/timer.h \
  /usr/include/opencv4/opencv2/highgui.hpp \
  /usr/include/opencv4/opencv2/imgcodecs.hpp \
  /usr/include/opencv4/opencv2/imgproc.hpp \
  /usr/include/opencv4/opencv2/imgproc/segmentation.hpp \
  /usr/include/opencv4/opencv2/ml.hpp \
  /usr/include/opencv4/opencv2/ml/ml.inl.hpp \
  /usr/include/opencv4/opencv2/objdetect.hpp \
  /usr/include/opencv4/opencv2/objdetect/aruco_board.hpp \
  /usr/include/opencv4/opencv2/objdetect/aruco_detector.hpp \
  /usr/include/opencv4/opencv2/objdetect/aruco_dictionary.hpp \
  /usr/include/opencv4/opencv2/objdetect/barcode.hpp \
  /usr/include/opencv4/opencv2/objdetect/charuco_detector.hpp \
  /usr/include/opencv4/opencv2/objdetect/detection_based_tracker.hpp \
  /usr/include/opencv4/opencv2/objdetect/face.hpp \
  /usr/include/opencv4/opencv2/objdetect/graphical_code_detector.hpp \
  /usr/include/opencv4/opencv2/opencv.hpp \
  /usr/include/opencv4/opencv2/opencv_modules.hpp \
  /usr/include/opencv4/opencv2/photo.hpp \
  /usr/include/opencv4/opencv2/stitching.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/blenders.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/camera.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/exposure_compensate.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/matchers.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/motion_estimators.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/seam_finders.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/util.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/util_inl.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/warpers.hpp \
  /usr/include/opencv4/opencv2/stitching/detail/warpers_inl.hpp \
  /usr/include/opencv4/opencv2/stitching/warpers.hpp \
  /usr/include/opencv4/opencv2/video.hpp \
  /usr/include/opencv4/opencv2/video/background_segm.hpp \
  /usr/include/opencv4/opencv2/video/tracking.hpp \
  /usr/include/opencv4/opencv2/videoio.hpp \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/ioctl.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/ttydefaults.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/arm_bf16.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/arm_fp16.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/arm_neon.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/float.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.cpp \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortInfo.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortListener.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort_global.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h \
  /home/<USER>/mywork/poco_serverdemo/utils/gmodels.h \
  /home/<USER>/mywork/poco_serverdemo/utils/logger.h \
  /home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.h \
  /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.h \
  /home/<USER>/mywork/poco_serverdemo/lkm/keymouse.h \
  /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/types.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/ioctl.h \
  /usr/include/asm-generic/ioctls.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/ioctl.h \
  /usr/include/asm/ioctls.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/ioctl-types.h \
  /usr/include/bits/ioctls.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/opt_random.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/random.h \
  /usr/include/c++/14.2.1/bits/random.tcc \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_numeric.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/iostream \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/numeric \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_numeric_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/random \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/input-event-codes.h \
  /usr/include/linux/input.h \
  /usr/include/linux/ioctl.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/linux/uinput.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/ioctl.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/ttydefaults.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.h \
  /home/<USER>/mywork/poco_serverdemo/backend/blserver/poco_websocket_server.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/types.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h \
  /home/<USER>/mywork/poco_serverdemo/utils/gmodels.h \
  /home/<USER>/mywork/poco_serverdemo/utils/logger.h \
  /usr/include/Poco/AbstractDelegate.h \
  /usr/include/Poco/AbstractEvent.h \
  /usr/include/Poco/ActiveMethod.h \
  /usr/include/Poco/ActiveResult.h \
  /usr/include/Poco/ActiveRunnable.h \
  /usr/include/Poco/ActiveStarter.h \
  /usr/include/Poco/ActiveThreadPool.h \
  /usr/include/Poco/Alignment.h \
  /usr/include/Poco/Any.h \
  /usr/include/Poco/Ascii.h \
  /usr/include/Poco/AtomicCounter.h \
  /usr/include/Poco/AutoPtr.h \
  /usr/include/Poco/BasicEvent.h \
  /usr/include/Poco/BinaryReader.h \
  /usr/include/Poco/BinaryWriter.h \
  /usr/include/Poco/Buffer.h \
  /usr/include/Poco/Bugcheck.h \
  /usr/include/Poco/Config.h \
  /usr/include/Poco/DateTime.h \
  /usr/include/Poco/DateTimeFormat.h \
  /usr/include/Poco/DateTimeFormatter.h \
  /usr/include/Poco/DateTimeParser.h \
  /usr/include/Poco/Debugger.h \
  /usr/include/Poco/DefaultStrategy.h \
  /usr/include/Poco/Dynamic/Struct.h \
  /usr/include/Poco/Dynamic/Var.h \
  /usr/include/Poco/Dynamic/VarHolder.h \
  /usr/include/Poco/Dynamic/VarIterator.h \
  /usr/include/Poco/Environment.h \
  /usr/include/Poco/Error.h \
  /usr/include/Poco/Event.h \
  /usr/include/Poco/Event_POSIX.h \
  /usr/include/Poco/Exception.h \
  /usr/include/Poco/FIFOBuffer.h \
  /usr/include/Poco/FPEnvironment.h \
  /usr/include/Poco/FPEnvironment_C99.h \
  /usr/include/Poco/Format.h \
  /usr/include/Poco/Foundation.h \
  /usr/include/Poco/JSON/Array.h \
  /usr/include/Poco/JSON/Handler.h \
  /usr/include/Poco/JSON/JSON.h \
  /usr/include/Poco/JSON/Object.h \
  /usr/include/Poco/JSON/ParseHandler.h \
  /usr/include/Poco/JSON/Parser.h \
  /usr/include/Poco/JSON/ParserImpl.h \
  /usr/include/Poco/JSON/Stringifier.h \
  /usr/include/Poco/JSONString.h \
  /usr/include/Poco/ListMap.h \
  /usr/include/Poco/LocalDateTime.h \
  /usr/include/Poco/MemoryStream.h \
  /usr/include/Poco/MetaProgramming.h \
  /usr/include/Poco/Mutex.h \
  /usr/include/Poco/Mutex_POSIX.h \
  /usr/include/Poco/Net/HTTPAuthenticationParams.h \
  /usr/include/Poco/Net/HTTPCookie.h \
  /usr/include/Poco/Net/HTTPCredentials.h \
  /usr/include/Poco/Net/HTTPDigestCredentials.h \
  /usr/include/Poco/Net/HTTPMessage.h \
  /usr/include/Poco/Net/HTTPNTLMCredentials.h \
  /usr/include/Poco/Net/HTTPRequest.h \
  /usr/include/Poco/Net/HTTPRequestHandler.h \
  /usr/include/Poco/Net/HTTPRequestHandlerFactory.h \
  /usr/include/Poco/Net/HTTPResponse.h \
  /usr/include/Poco/Net/HTTPServer.h \
  /usr/include/Poco/Net/HTTPServerParams.h \
  /usr/include/Poco/Net/HTTPServerRequest.h \
  /usr/include/Poco/Net/HTTPServerResponse.h \
  /usr/include/Poco/Net/IPAddress.h \
  /usr/include/Poco/Net/IPAddressImpl.h \
  /usr/include/Poco/Net/MessageHeader.h \
  /usr/include/Poco/Net/NTLMCredentials.h \
  /usr/include/Poco/Net/NameValueCollection.h \
  /usr/include/Poco/Net/Net.h \
  /usr/include/Poco/Net/NetException.h \
  /usr/include/Poco/Net/SSPINTLMCredentials.h \
  /usr/include/Poco/Net/ServerSocket.h \
  /usr/include/Poco/Net/Socket.h \
  /usr/include/Poco/Net/SocketAddress.h \
  /usr/include/Poco/Net/SocketAddressImpl.h \
  /usr/include/Poco/Net/SocketDefs.h \
  /usr/include/Poco/Net/SocketImpl.h \
  /usr/include/Poco/Net/StreamSocket.h \
  /usr/include/Poco/Net/TCPServer.h \
  /usr/include/Poco/Net/TCPServerConnection.h \
  /usr/include/Poco/Net/TCPServerConnectionFactory.h \
  /usr/include/Poco/Net/TCPServerParams.h \
  /usr/include/Poco/Net/WebSocket.h \
  /usr/include/Poco/NotificationStrategy.h \
  /usr/include/Poco/Nullable.h \
  /usr/include/Poco/NumberFormatter.h \
  /usr/include/Poco/NumberParser.h \
  /usr/include/Poco/NumericString.h \
  /usr/include/Poco/OrderedMap.h \
  /usr/include/Poco/OrderedSet.h \
  /usr/include/Poco/Platform.h \
  /usr/include/Poco/Platform_POSIX.h \
  /usr/include/Poco/RefCountedObject.h \
  /usr/include/Poco/Runnable.h \
  /usr/include/Poco/ScopedLock.h \
  /usr/include/Poco/SharedPtr.h \
  /usr/include/Poco/SignalHandler.h \
  /usr/include/Poco/SingletonHolder.h \
  /usr/include/Poco/StreamUtil.h \
  /usr/include/Poco/String.h \
  /usr/include/Poco/Thread.h \
  /usr/include/Poco/ThreadPool.h \
  /usr/include/Poco/Thread_POSIX.h \
  /usr/include/Poco/Timespan.h \
  /usr/include/Poco/Timestamp.h \
  /usr/include/Poco/Types.h \
  /usr/include/Poco/URI.h \
  /usr/include/Poco/UTF8String.h \
  /usr/include/Poco/UTFString.h \
  /usr/include/Poco/UUID.h \
  /usr/include/Poco/UnicodeConverter.h \
  /usr/include/Poco/ordered_hash.h \
  /usr/include/Poco/ordered_map.h \
  /usr/include/Poco/ordered_set.h \
  /usr/include/alloca.h \
  /usr/include/arpa/inet.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/ioctl.h \
  /usr/include/asm-generic/ioctls.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/ioctl.h \
  /usr/include/asm/ioctls.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/socket.h \
  /usr/include/asm/sockios.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/fenv.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/in.h \
  /usr/include/bits/ioctl-types.h \
  /usr/include/bits/ioctls.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/netdb.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/sockaddr.h \
  /usr/include/bits/socket.h \
  /usr/include/bits/socket_type.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/__sigval_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigevent_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_osockaddr.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio-ext.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cxxabi_tweaks.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/algorithm \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/deque.tcc \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/list.tcc \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algo.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_function.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_deque.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_list.h \
  /usr/include/c++/14.2.1/bits/stl_map.h \
  /usr/include/c++/14.2.1/bits/stl_multimap.h \
  /usr/include/c++/14.2.1/bits/stl_multiset.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_set.h \
  /usr/include/c++/14.2.1/bits/stl_stack.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_tree.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/stream_iterator.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/unordered_set.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/cxxabi.h \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/deque \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/fenv.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/functional \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/iostream \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/iterator \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/list \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/map \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/set \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stack \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/unordered_set \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/fenv.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/ioctl.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/net/if.h \
  /usr/include/netdb.h \
  /usr/include/netinet/in.h \
  /usr/include/netinet/tcp.h \
  /usr/include/pthread.h \
  /usr/include/rpc/netdb.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/setjmp.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/ioctl.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/socket.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/ttydefaults.h \
  /usr/include/sys/types.h \
  /usr/include/sys/uio.h \
  /usr/include/sys/un.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h


/usr/include/c++/14.2.1/stack:

/usr/include/Poco/Net/TCPServerParams.h:

/usr/include/Poco/Net/TCPServerConnection.h:

/usr/include/Poco/Net/NetException.h:

/usr/include/Poco/Net/HTTPServerParams.h:

/usr/include/Poco/Net/HTTPServer.h:

/usr/include/Poco/Net/HTTPRequestHandler.h:

/usr/include/Poco/Net/HTTPCredentials.h:

/usr/include/Poco/JSON/ParseHandler.h:

/usr/include/Poco/JSON/Handler.h:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.h:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/arm_neon.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/arm_bf16.h:

/usr/include/opencv4/opencv2/video/tracking.hpp:

/usr/include/opencv4/opencv2/video.hpp:

/usr/include/opencv4/opencv2/stitching/detail/warpers_inl.hpp:

/usr/include/opencv4/opencv2/stitching/detail/warpers.hpp:

/usr/include/opencv4/opencv2/stitching/detail/matchers.hpp:

/usr/include/opencv4/opencv2/stitching/detail/blenders.hpp:

/usr/include/opencv4/opencv2/stitching.hpp:

/usr/include/opencv4/opencv2/opencv_modules.hpp:

/usr/include/opencv4/opencv2/opencv.hpp:

/usr/include/opencv4/opencv2/objdetect/graphical_code_detector.hpp:

/usr/include/opencv4/opencv2/objdetect/detection_based_tracker.hpp:

/usr/include/opencv4/opencv2/objdetect/charuco_detector.hpp:

/usr/include/opencv4/opencv2/objdetect/aruco_dictionary.hpp:

/usr/include/opencv4/opencv2/objdetect/aruco_detector.hpp:

/usr/include/opencv4/opencv2/objdetect.hpp:

/usr/include/opencv4/opencv2/ml/ml.inl.hpp:

/usr/include/opencv4/opencv2/ml.hpp:

/usr/include/opencv4/opencv2/imgproc.hpp:

/usr/include/opencv4/opencv2/imgcodecs.hpp:

/usr/include/opencv4/opencv2/flann/timer.h:

/usr/include/opencv4/opencv2/flann/params.h:

/usr/include/opencv4/opencv2/flann/nn_index.h:

/usr/include/opencv4/opencv2/flann/miniflann.hpp:

/usr/include/opencv4/opencv2/flann/matrix.h:

/usr/include/opencv4/opencv2/flann/lsh_index.h:

/usr/include/Poco/Net/WebSocket.h:

/usr/include/opencv4/opencv2/flann/linear_index.h:

/usr/include/opencv4/opencv2/flann/kdtree_single_index.h:

/usr/include/opencv4/opencv2/flann/index_testing.h:

/usr/include/opencv4/opencv2/flann/hierarchical_clustering_index.h:

/usr/include/opencv4/opencv2/flann/heap.h:

/usr/include/opencv4/opencv2/flann/defines.h:

/usr/include/opencv4/opencv2/flann/config.h:

/usr/include/opencv4/opencv2/flann/random.h:

/usr/include/opencv4/opencv2/flann/autotuned_index.h:

/usr/include/opencv4/opencv2/flann/any.h:

/usr/include/opencv4/opencv2/flann/allocator.h:

/usr/include/opencv4/opencv2/flann/all_indices.h:

/usr/include/opencv4/opencv2/flann.hpp:

/usr/include/opencv4/opencv2/features2d.hpp:

/usr/include/opencv4/opencv2/dnn/utils/inference_engine.hpp:

/usr/include/opencv4/opencv2/dnn/layer.hpp:

/usr/include/opencv4/opencv2/dnn/dnn.hpp:

/usr/include/opencv4/opencv2/dnn/dict.hpp:

/usr/include/opencv4/opencv2/core/vsx_utils.hpp:

/usr/include/opencv4/opencv2/core/version.hpp:

/usr/include/opencv4/opencv2/objdetect/face.hpp:

/usr/include/opencv4/opencv2/core/utils/logtag.hpp:

/usr/include/opencv4/opencv2/core/utility.hpp:

/usr/include/opencv4/opencv2/core/types.hpp:

/usr/include/opencv4/opencv2/core/saturate.hpp:

/usr/include/opencv4/opencv2/flann/dist.h:

/usr/include/opencv4/opencv2/core/persistence.hpp:

/usr/include/opencv4/opencv2/core/ovx.hpp:

/usr/include/opencv4/opencv2/core/optim.hpp:

/usr/include/opencv4/opencv2/core/neon_utils.hpp:

/usr/include/opencv4/opencv2/core/matx.hpp:

/usr/include/opencv4/opencv2/core/mat.inl.hpp:

/usr/include/opencv4/opencv2/dnn.hpp:

/usr/include/opencv4/opencv2/core/hal/interface.h:

/usr/include/opencv4/opencv2/core/cvstd_wrapper.hpp:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.cpp:

/usr/include/opencv4/opencv2/core/cvstd.inl.hpp:

/usr/include/opencv4/opencv2/core/utils/logger.defines.hpp:

/usr/include/opencv4/opencv2/core/cvdef.h:

/usr/include/opencv4/opencv2/core/cuda.inl.hpp:

/usr/include/opencv4/opencv2/core/cuda.hpp:

/usr/include/opencv4/opencv2/core/bufferpool.hpp:

/usr/include/opencv4/opencv2/core/async.hpp:

/usr/include/opencv4/opencv2/core.hpp:

/usr/include/opencv4/opencv2/calib3d.hpp:

/usr/include/c++/14.2.1/random:

/usr/include/c++/14.2.1/pstl/glue_numeric_defs.h:

/usr/include/c++/14.2.1/numeric:

/usr/include/c++/14.2.1/math.h:

/usr/include/c++/14.2.1/complex:

/usr/include/c++/14.2.1/cfloat:

/usr/include/c++/14.2.1/bits/stl_numeric.h:

/usr/include/c++/14.2.1/bits/random.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/opt_random.h:

/home/<USER>/mywork/poco_serverdemo/infer/core/model/model.h:

/home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/data_collector.h:

/usr/include/opencv4/opencv2/flann/lsh_table.h:

/home/<USER>/mywork/poco_serverdemo/infer/base/macros.h:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/include/rknn_api.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortInfo.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort.h:

/home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.h:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.cpp:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h:

/usr/include/opencv4/opencv2/flann/composite_index.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h:

/usr/include/sys/ttydefaults.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h:

/usr/include/bits/types/time_t.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cxxabi_tweaks.h:

/usr/include/Poco/Net/HTTPRequestHandlerFactory.h:

/usr/include/Poco/UnicodeConverter.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.h:

/usr/include/c++/14.2.1/bits/quoted_string.h:

/usr/include/Poco/JSON/ParserImpl.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h:

/usr/include/c++/14.2.1/tr1/legendre_function.tcc:

/usr/include/unistd.h:

/usr/include/bits/xopen_lim.h:

/usr/include/bits/wordsize.h:

/usr/include/stdio.h:

/usr/include/bits/waitflags.h:

/usr/include/bits/uio_lim.h:

/usr/include/bits/math-vector.h:

/usr/include/bits/uintn-identity.h:

/usr/include/opencv4/opencv2/core/operations.hpp:

/usr/include/sys/un.h:

/usr/include/bits/types/wint_t.h:

/usr/include/bits/types/timer_t.h:

/usr/include/bits/types/struct_tm.h:

/usr/include/c++/14.2.1/bits/invoke.h:

/usr/include/bits/types/struct___jmp_buf_tag.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/types/cookie_io_functions_t.h:

/usr/include/Poco/Net/SocketAddress.h:

/usr/include/c++/14.2.1/fenv.h:

/usr/include/bits/types/__locale_t.h:

/usr/include/bits/types/__FILE.h:

/usr/include/Poco/Net/TCPServer.h:

/usr/include/features.h:

/usr/include/bits/types/struct_itimerspec.h:

/usr/include/opencv4/opencv2/stitching/detail/exposure_compensate.hpp:

/usr/include/bits/sched.h:

/usr/include/bits/struct_rwlock.h:

/usr/include/bits/struct_mutex.h:

/home/<USER>/mywork/poco_serverdemo/lkm/keymouse.h:

/usr/include/c++/14.2.1/bits/locale_classes.tcc:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/bits/time.h:

/usr/include/asm/posix_types.h:

/usr/include/opencv4/opencv2/core/cuda_types.hpp:

/usr/include/c++/14.2.1/debug/assertions.h:

/usr/include/bits/stdio.h:

/usr/include/opencv4/opencv2/flann/dynamic_bitset.h:

/usr/include/Poco/ActiveMethod.h:

/usr/include/bits/wctype-wchar.h:

/usr/include/bits/stdint-intn.h:

/usr/include/bits/types/clockid_t.h:

/usr/include/bits/select.h:

/usr/include/c++/14.2.1/bits/algorithmfwd.h:

/usr/include/bits/pthreadtypes.h:

/usr/include/bits/pthreadtypes-arch.h:

/usr/include/bits/posix2_lim.h:

/usr/include/bits/mathcalls-narrow.h:

/usr/include/c++/14.2.1/bits/random.tcc:

/usr/include/bits/libc-header-start.h:

/usr/include/c++/14.2.1/ext/aligned_buffer.h:

/home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.h:

/home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/infer_collector.h:

/usr/include/bits/iscanonical.h:

/usr/include/bits/types/clock_t.h:

/usr/include/Poco/DateTime.h:

/usr/include/bits/types/__fpos_t.h:

/usr/include/asm/unistd_64.h:

/usr/include/Poco/StreamUtil.h:

/usr/include/c++/14.2.1/stop_token:

/usr/include/bits/mathcalls.h:

/usr/include/c++/14.2.1/bits/stl_stack.h:

/usr/include/bits/ioctls.h:

/usr/include/bits/ioctl-types.h:

/usr/include/Poco/Mutex_POSIX.h:

/usr/include/bits/socket.h:

/usr/include/bits/in.h:

/usr/include/c++/14.2.1/ratio:

/usr/include/bits/getopt_posix.h:

/usr/include/errno.h:

/usr/include/bits/getopt_core.h:

/usr/include/Poco/OrderedSet.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/float.h:

/usr/include/c++/14.2.1/locale:

/usr/include/bits/fp-fast.h:

/home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_fps.h:

/usr/include/wctype.h:

/usr/include/linux/posix_types.h:

/usr/include/bits/flt-eval-method.h:

/usr/include/bits/floatn.h:

/home/<USER>/mywork/poco_serverdemo/utils/logger.h:

/usr/include/bits/floatn-common.h:

/usr/include/c++/14.2.1/vector:

/usr/include/Poco/Config.h:

/usr/include/c++/14.2.1/array:

/usr/include/c++/14.2.1/type_traits:

/usr/include/bits/fcntl.h:

/usr/include/c++/14.2.1/tuple:

/usr/include/opencv4/opencv2/core/mat.hpp:

/usr/include/bits/errno.h:

/usr/include/bits/environments.h:

/usr/include/bits/types/struct_FILE.h:

/usr/include/bits/cpu-set.h:

/usr/include/bits/confname.h:

/usr/include/opencv4/opencv2/imgproc/segmentation.hpp:

/usr/include/bits/posix_opt.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortListener.h:

/usr/include/c++/14.2.1/bits/hashtable_policy.h:

/usr/include/c++/14.2.1/bits/specfun.h:

/usr/include/openssl/err.h:

/usr/include/bits/long-double.h:

/usr/include/asm/sockios.h:

/usr/include/Poco/Debugger.h:

/usr/include/c++/14.2.1/bits/uses_allocator_args.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h:

/usr/include/asm/ioctls.h:

/usr/include/c++/14.2.1/tr1/special_function_util.h:

/usr/include/asm/ioctl.h:

/usr/include/asm/bitsperlong.h:

/usr/include/asm-generic/sockios.h:

/usr/include/c++/14.2.1/semaphore:

/usr/include/openssl/bioerr.h:

/usr/include/Poco/AbstractDelegate.h:

/usr/include/Poco/FIFOBuffer.h:

/usr/include/bits/types.h:

/usr/include/opencv4/opencv2/core/cvstd.hpp:

/usr/include/Poco/Event_POSIX.h:

/usr/include/c++/14.2.1/bits/concept_check.h:

/usr/include/syscall.h:

/usr/include/alloca.h:

/usr/include/Poco/Error.h:

/usr/include/Poco/Environment.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h:

/usr/include/c++/14.2.1/ext/string_conversions.h:

/usr/include/bits/stdlib-bsearch.h:

/usr/include/c++/14.2.1/bits/basic_ios.tcc:

/home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h:

/usr/include/openssl/symhacks.h:

/usr/include/Poco/NumberParser.h:

/usr/include/Poco/Thread_POSIX.h:

/usr/include/bits/thread-shared-types.h:

/usr/include/opencv4/opencv2/flann/kdtree_index.h:

/usr/include/Poco/Net/HTTPMessage.h:

/usr/include/Poco/DateTimeFormatter.h:

/usr/include/opencv4/opencv2/dnn/version.hpp:

/usr/include/Poco/Net/IPAddressImpl.h:

/usr/include/bits/types/struct_sched_param.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h:

/usr/include/bits/fenv.h:

/usr/include/c++/14.2.1/bits/stl_uninitialized.h:

/usr/include/Poco/DateTimeFormat.h:

/usr/include/bits/local_lim.h:

/usr/include/Poco/Net/HTTPBasicCredentials.h:

/usr/include/c++/14.2.1/iomanip:

/usr/include/opencv4/opencv2/objdetect/barcode.hpp:

/usr/include/Poco/Timespan.h:

/usr/include/string.h:

/usr/include/c++/14.2.1/mutex:

/usr/include/c++/14.2.1/bits/uses_allocator.h:

/usr/include/opencv4/opencv2/flann/logger.h:

/usr/include/Poco/ActiveThreadPool.h:

/usr/include/opencv4/opencv2/stitching/detail/camera.hpp:

/usr/include/bits/types/FILE.h:

/usr/include/Poco/JSON/Array.h:

/usr/include/opencv4/opencv2/photo.hpp:

/usr/include/c++/14.2.1/bits/unicode-data.h:

/usr/include/bits/locale.h:

/usr/include/Poco/Net/SSPINTLMCredentials.h:

/usr/include/c++/14.2.1/utility:

/usr/include/bits/typesizes.h:

/usr/include/openssl/configuration.h:

/usr/include/Poco/NotificationStrategy.h:

/usr/include/Poco/Net/HTTPServerResponse.h:

/usr/include/c++/14.2.1/bits/atomic_timed_wait.h:

/usr/include/c++/14.2.1/bits/locale_facets.h:

/usr/include/c++/14.2.1/pstl/execution_defs.h:

/usr/include/asm/unistd.h:

/usr/include/c++/14.2.1/tr1/bessel_function.tcc:

/usr/include/Poco/JSON/Stringifier.h:

/usr/include/Poco/Buffer.h:

/usr/include/bits/fcntl-linux.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h:

/usr/include/c++/14.2.1/bits/enable_special_members.h:

/usr/include/asm/errno.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/c++/14.2.1/iostream:

/usr/include/bits/pthread_stack_min-dynamic.h:

/usr/include/c++/14.2.1/tr1/ell_integral.tcc:

/usr/include/Poco/Net/HTTPDigestCredentials.h:

/usr/include/opencv4/opencv2/flann/sampling.h:

/usr/include/Poco/AutoPtr.h:

/usr/include/bits/posix1_lim.h:

/usr/include/opencv4/opencv2/flann/kmeans_index.h:

/usr/include/c++/14.2.1/clocale:

/usr/include/wchar.h:

/usr/include/Poco/JSON/JSON.h:

/home/<USER>/mywork/poco_serverdemo/blkm/crypto_utils.h:

/usr/include/Poco/Foundation.h:

/usr/include/bits/timesize.h:

/usr/include/c++/14.2.1/list:

/usr/include/c++/14.2.1/bits/stl_queue.h:

/usr/include/Poco/DateTimeParser.h:

/usr/include/Poco/Net/Socket.h:

/usr/include/opencv4/opencv2/flann/general.h:

/usr/include/Poco/Net/HTTPClientSession.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h:

/usr/include/bits/timex.h:

/usr/include/c++/14.2.1/bits/stl_vector.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/arm_fp16.h:

/usr/include/time.h:

/usr/include/opencv4/opencv2/videoio.hpp:

/usr/include/c++/14.2.1/typeinfo:

/usr/include/libintl.h:

/usr/include/opencv4/opencv2/core/check.hpp:

/usr/include/Poco/ActiveStarter.h:

/usr/include/bits/sockaddr.h:

/usr/include/Poco/ordered_hash.h:

/usr/include/bits/types/error_t.h:

/home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h:

/usr/include/Poco/SignalHandler.h:

/usr/include/bits/setjmp.h:

/usr/include/c++/14.2.1/bits/charconv.h:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.cpp:

/usr/include/Poco/FPEnvironment.h:

/usr/include/c++/14.2.1/bits/functional_hash.h:

/usr/include/Poco/ActiveRunnable.h:

/usr/include/Poco/Crypto/Cipher.h:

/usr/include/sched.h:

/usr/include/netdb.h:

/usr/include/Poco/Platform.h:

/usr/include/c++/14.2.1/bits/semaphore_base.h:

/usr/include/assert.h:

/usr/include/Poco/Dynamic/Struct.h:

/usr/include/c++/14.2.1/bits/postypes.h:

/usr/include/bits/mathcalls-helper-functions.h:

/usr/include/asm/socket.h:

/usr/include/opencv4/opencv2/stitching/detail/seam_finders.hpp:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h:

/usr/include/c++/14.2.1/bits/node_handle.h:

/usr/include/bits/types/mbstate_t.h:

/usr/include/opencv4/opencv2/stitching/detail/motion_estimators.hpp:

/usr/include/Poco/Bugcheck.h:

/usr/include/Poco/Ascii.h:

/usr/include/asm-generic/types.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/c++/14.2.1/bits/chrono.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/c++/14.2.1/bits/cxxabi_forced.h:

/usr/include/bits/semaphore.h:

/usr/include/Poco/NumericString.h:

/usr/include/Poco/JSONString.h:

/usr/include/c++/14.2.1/bits/predefined_ops.h:

/usr/include/Poco/ThreadPool.h:

/usr/include/Poco/Net/TCPServerConnectionFactory.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/opencv4/opencv2/core/affine.hpp:

/usr/include/Poco/Format.h:

/usr/include/Poco/AtomicCounter.h:

/usr/include/c++/14.2.1/bits/unordered_set.h:

/usr/include/c++/14.2.1/tr1/riemann_zeta.tcc:

/usr/include/asm-generic/socket.h:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h:

/usr/include/bits/unistd_ext.h:

/usr/include/Poco/UTFString.h:

/usr/include/Poco/Net/HTTPResponse.h:

/usr/include/Poco/MemoryStream.h:

/usr/include/bits/endian.h:

/usr/include/bits/libm-simd-decl-stubs.h:

/usr/include/bits/atomic_wide_counter.h:

/usr/include/c++/14.2.1/span:

/usr/include/Poco/Net/HTTPCookie.h:

/usr/include/Poco/Net/StreamSocket.h:

/usr/include/Poco/LocalDateTime.h:

/usr/include/Poco/Net/HTTPRequest.h:

/usr/include/linux/falloc.h:

/usr/include/c++/14.2.1/bits/uniform_int_dist.h:

/usr/include/opencv4/opencv2/core/fast_math.hpp:

/usr/include/Poco/ActiveResult.h:

/usr/include/locale.h:

/usr/include/bits/mathcalls-macros.h:

/usr/include/Poco/Net/HTTPSession.h:

/usr/include/bits/struct_stat.h:

/usr/include/opencv4/opencv2/video/background_segm.hpp:

/usr/include/bits/types/struct_timespec.h:

/usr/include/Poco/Net/IPAddress.h:

/usr/include/Poco/Net/SocketAddressImpl.h:

/usr/include/Poco/Event.h:

/usr/include/Poco/URI.h:

/usr/include/c++/14.2.1/bits/cxxabi_init_exception.h:

/usr/include/opencv4/opencv2/flann/result_set.h:

/usr/include/Poco/Net/NTLMCredentials.h:

/usr/include/openssl/e_os2.h:

/usr/include/bits/time64.h:

/usr/include/c++/14.2.1/bits/stl_tempbuf.h:

/usr/include/bits/stdlib-float.h:

/usr/include/linux/uinput.h:

/usr/include/bits/types/__sigval_t.h:

/usr/include/c++/14.2.1/bits/ranges_base.h:

/home/<USER>/mywork/poco_serverdemo/utils/gmodels.h:

/usr/include/Poco/ordered_set.h:

/usr/include/bits/endianness.h:

/usr/include/Poco/Mutex.h:

/usr/include/Poco/Net/NameValueCollection.h:

/usr/include/Poco/Crypto/Crypto.h:

/usr/include/Poco/Net/HTTPAuthenticationParams.h:

/usr/include/Poco/Net/SocketDefs.h:

/usr/include/Poco/ordered_map.h:

/home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.h:

/usr/include/c++/14.2.1/bits/basic_string.h:

/usr/include/bits/stdio_lim.h:

/usr/include/Poco/SingletonHolder.h:

/usr/include/Poco/Timestamp.h:

/usr/include/Poco/Net/SocketImpl.h:

/usr/include/Poco/Nullable.h:

/usr/include/Poco/Dynamic/VarIterator.h:

/usr/include/opencv4/opencv2/stitching/detail/util.hpp:

/usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h:

/usr/include/bits/types/struct_timeval.h:

/usr/include/Poco/Dynamic/VarHolder.h:

/usr/include/Poco/Platform_POSIX.h:

/usr/include/bits/netdb.h:

/usr/include/c++/14.2.1/thread:

/usr/include/Poco/UUID.h:

/usr/include/Poco/RefCountedObject.h:

/usr/include/Poco/Net/Net.h:

/usr/include/c++/14.2.1/istream:

/usr/include/c++/14.2.1/bits/ptr_traits.h:

/usr/include/c++/14.2.1/bits/ranges_cmp.h:

/home/<USER>/mywork/poco_serverdemo/infer/base/types.h:

/usr/include/Poco/DefaultStrategy.h:

/usr/include/arpa/inet.h:

/usr/include/asm-generic/int-ll64.h:

/usr/include/Poco/Thread.h:

/usr/include/Poco/Net/HTTPNTLMCredentials.h:

/usr/include/opencv4/opencv2/dnn/dnn.inl.hpp:

/usr/include/Poco/Types.h:

/usr/include/Poco/Dynamic/Var.h:

/usr/include/c++/14.2.1/tr1/beta_function.tcc:

/usr/include/sys/uio.h:

/usr/include/asm-generic/ioctl.h:

/usr/include/Poco/ListMap.h:

/usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h:

/usr/include/Poco/Exception.h:

/usr/include/linux/input-event-codes.h:

/usr/include/c++/14.2.1/functional:

/usr/include/bits/types/locale_t.h:

/usr/include/linux/stddef.h:

/usr/include/bits/fp-logb.h:

/usr/include/linux/ioctl.h:

/usr/include/c++/14.2.1/chrono:

/usr/include/bits/uio-ext.h:

/usr/include/c++/14.2.1/cwchar:

/usr/include/c++/14.2.1/tr1/poly_laguerre.tcc:

/usr/include/Poco/Alignment.h:

/usr/include/c++/14.2.1/bits/memoryfwd.h:

/usr/include/bits/types/sigset_t.h:

/usr/include/Poco/Runnable.h:

/usr/include/c++/14.2.1/atomic:

/usr/include/c++/14.2.1/backward/binders.h:

/usr/include/Poco/Any.h:

/usr/include/c++/14.2.1/limits:

/usr/include/sys/time.h:

/usr/include/c++/14.2.1/bits/vector.tcc:

/usr/include/c++/14.2.1/bits/align.h:

/usr/include/c++/14.2.1/bits/alloc_traits.h:

/usr/include/c++/14.2.1/bits/sstream.tcc:

/usr/include/c++/14.2.1/bits/allocated_ptr.h:

/usr/include/c++/14.2.1/bits/atomic_base.h:

/usr/include/c++/14.2.1/bits/atomic_wait.h:

/usr/include/Poco/NumberFormatter.h:

/usr/include/c++/14.2.1/bits/basic_ios.h:

/usr/include/opencv4/opencv2/core/traits.hpp:

/home/<USER>/mywork/poco_serverdemo/blkm/cloud_update.h:

/usr/include/c++/14.2.1/bits/stl_relops.h:

/usr/include/sys/syscall.h:

/usr/include/bits/byteswap.h:

/usr/include/c++/14.2.1/bits/basic_string.tcc:

/usr/include/c++/14.2.1/bits/char_traits.h:

/usr/include/c++/14.2.1/queue:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort_global.h:

/usr/include/bits/types/struct_osockaddr.h:

/usr/include/c++/14.2.1/bits/chrono_io.h:

/usr/include/bits/socket_type.h:

/usr/include/c++/14.2.1/bits/codecvt.h:

/usr/include/c++/14.2.1/bits/cpp_type_traits.h:

/usr/include/opencv4/opencv2/core/matx.inl.hpp:

/usr/include/opencv4/opencv2/core/cv_cpu_dispatch.h:

/usr/include/c++/14.2.1/cctype:

/usr/include/c++/14.2.1/bits/deque.tcc:

/usr/include/Poco/Net/HTTPServerRequest.h:

/usr/include/c++/14.2.1/bits/erase_if.h:

/usr/include/c++/14.2.1/bits/stl_tree.h:

/usr/include/c++/14.2.1/bits/exception_defines.h:

/usr/include/Poco/Crypto/CryptoTransform.h:

/usr/include/c++/14.2.1/bits/functexcept.h:

/usr/include/c++/14.2.1/bits/hash_bytes.h:

/usr/include/Poco/BasicEvent.h:

/usr/include/c++/14.2.1/bits/stl_deque.h:

/usr/include/c++/14.2.1/bits/hashtable.h:

/usr/include/c++/14.2.1/bits/ios_base.h:

/usr/include/c++/14.2.1/bits/istream.tcc:

/usr/include/c++/14.2.1/bits/iterator_concepts.h:

/usr/include/opencv4/opencv2/core/utils/logger.hpp:

/usr/include/c++/14.2.1/bits/list.tcc:

/usr/include/c++/14.2.1/bits/locale_classes.h:

/usr/include/c++/14.2.1/bits/locale_conv.h:

/usr/include/c++/14.2.1/bits/locale_facets.tcc:

/usr/include/c++/14.2.1/bits/locale_facets_nonio.h:

/usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc:

/usr/include/gnu/stubs.h:

/usr/include/c++/14.2.1/bits/stream_iterator.h:

/usr/include/c++/14.2.1/bits/max_size_type.h:

/usr/include/asm-generic/ioctls.h:

/usr/include/c++/14.2.1/bits/memory_resource.h:

/usr/include/Poco/FPEnvironment_C99.h:

/usr/include/c++/14.2.1/bits/nested_exception.h:

/usr/include/c++/14.2.1/bits/new_allocator.h:

/usr/include/c++/14.2.1/bits/ostream.tcc:

/usr/include/endian.h:

/usr/include/stdint.h:

/usr/include/c++/14.2.1/bits/parse_numbers.h:

/usr/include/c++/14.2.1/bits/range_access.h:

/usr/include/c++/14.2.1/bits/shared_ptr.h:

/usr/include/c++/14.2.1/bits/ranges_algo.h:

/usr/include/bits/types/__sigset_t.h:

/usr/include/c++/14.2.1/bit:

/usr/include/c++/14.2.1/condition_variable:

/usr/include/c++/14.2.1/bits/ranges_algobase.h:

/usr/include/c++/14.2.1/bits/ranges_uninitialized.h:

/usr/include/opencv4/opencv2/stitching/warpers.hpp:

/usr/include/linux/sched/types.h:

/usr/include/c++/14.2.1/bits/ranges_util.h:

/usr/include/c++/14.2.1/bits/refwrap.h:

/usr/include/c++/14.2.1/bits/requires_hosted.h:

/usr/include/c++/14.2.1/bits/shared_ptr_atomic.h:

/usr/include/c++/14.2.1/format:

/usr/include/linux/input.h:

/usr/include/bits/types/struct_iovec.h:

/usr/include/c++/14.2.1/bits/shared_ptr_base.h:

/usr/include/c++/14.2.1/bits/std_abs.h:

/usr/include/c++/14.2.1/bits/stl_bvector.h:

/usr/include/Poco/JSON/Parser.h:

/usr/include/c++/14.2.1/bits/std_function.h:

/usr/include/bits/types/sigevent_t.h:

/usr/include/c++/14.2.1/bits/std_mutex.h:

/usr/include/c++/14.2.1/bits/std_thread.h:

/usr/include/Poco/Net/ServerSocket.h:

/usr/include/c++/14.2.1/bits/stl_algo.h:

/usr/include/c++/14.2.1/bits/stl_algobase.h:

/usr/include/c++/14.2.1/cstdlib:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp:

/usr/include/c++/14.2.1/bits/stl_construct.h:

/usr/include/c++/14.2.1/bits/utility.h:

/usr/include/c++/14.2.1/ext/atomicity.h:

/usr/include/c++/14.2.1/stdlib.h:

/usr/include/features-time64.h:

/usr/include/c++/14.2.1/bits/stl_heap.h:

/usr/include/c++/14.2.1/bits/stl_iterator.h:

/home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo.h:

/usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h:

/usr/include/bits/stdint-least.h:

/usr/include/c++/14.2.1/bits/stl_iterator_base_types.h:

/usr/include/c++/14.2.1/bits/stl_list.h:

/usr/include/c++/14.2.1/bits/stl_map.h:

/usr/include/Poco/ScopedLock.h:

/usr/include/Poco/SharedPtr.h:

/usr/include/c++/14.2.1/bits/stl_multimap.h:

/usr/include/c++/14.2.1/bits/stl_multiset.h:

/usr/include/c++/14.2.1/bits/stl_pair.h:

/usr/include/Poco/JSON/Object.h:

/usr/include/c++/14.2.1/iosfwd:

/usr/include/c++/14.2.1/bits/streambuf.tcc:

/usr/include/c++/14.2.1/bits/streambuf_iterator.h:

/usr/include/c++/14.2.1/bits/string_view.tcc:

/usr/include/c++/14.2.1/bits/stringfwd.h:

/usr/include/c++/14.2.1/bits/move.h:

/usr/include/c++/14.2.1/bits/this_thread_sleep.h:

/usr/include/c++/14.2.1/bits/unicode.h:

/usr/include/c++/14.2.1/bits/unique_lock.h:

/usr/include/c++/14.2.1/bits/unique_ptr.h:

/usr/include/Poco/AbstractEvent.h:

/usr/include/c++/14.2.1/algorithm:

/usr/include/c++/14.2.1/bits/unordered_map.h:

/usr/include/c++/14.2.1/bits/version.h:

/usr/include/c++/14.2.1/cassert:

/usr/include/c++/14.2.1/cerrno:

/usr/include/c++/14.2.1/charconv:

/usr/include/c++/14.2.1/climits:

/usr/include/c++/14.2.1/bits/ostream_insert.h:

/usr/include/c++/14.2.1/cmath:

/usr/include/c++/14.2.1/compare:

/usr/include/Poco/UTF8String.h:

/usr/include/c++/14.2.1/concepts:

/usr/include/bits/wchar.h:

/usr/include/c++/14.2.1/numbers:

/usr/include/c++/14.2.1/cstddef:

/usr/include/c++/14.2.1/bits/stl_set.h:

/usr/include/c++/14.2.1/cstdint:

/usr/include/c++/14.2.1/cstdio:

/usr/include/Poco/Net/MessageHeader.h:

/usr/include/c++/14.2.1/cstring:

/usr/include/c++/14.2.1/ctime:

/usr/include/c++/14.2.1/cwctype:

/usr/include/opencv4/opencv2/highgui.hpp:

/usr/include/c++/14.2.1/cxxabi.h:

/usr/include/strings.h:

/home/<USER>/mywork/poco_serverdemo/infer/base/fps_counter.h:

/usr/include/c++/14.2.1/bits/allocator.h:

/usr/include/c++/14.2.1/debug/debug.h:

/usr/include/c++/14.2.1/deque:

/usr/include/c++/14.2.1/exception:

/usr/include/Poco/String.h:

/usr/include/sys/types.h:

/usr/include/c++/14.2.1/ext/concurrence.h:

/usr/include/c++/14.2.1/ext/numeric_traits.h:

/usr/include/bits/stat.h:

/usr/include/c++/14.2.1/ext/type_traits.h:

/usr/include/c++/14.2.1/initializer_list:

/usr/include/asm/types.h:

/usr/include/c++/14.2.1/ios:

/usr/include/opencv4/opencv2/flann/flann_base.hpp:

/usr/include/Poco/MetaProgramming.h:

/usr/include/setjmp.h:

/usr/include/math.h:

/usr/include/c++/14.2.1/iterator:

/usr/include/openssl/opensslv.h:

/usr/include/c++/14.2.1/bits/exception.h:

/usr/include/c++/14.2.1/map:

/usr/include/opencv4/opencv2/stitching/detail/util_inl.hpp:

/usr/include/c++/14.2.1/memory:

/usr/include/c++/14.2.1/new:

/usr/include/Poco/OrderedMap.h:

/usr/include/c++/14.2.1/optional:

/usr/include/c++/14.2.1/pstl/glue_memory_defs.h:

/usr/include/c++/14.2.1/set:

/usr/include/openssl/cryptoerr_legacy.h:

/usr/include/c++/14.2.1/sstream:

/usr/include/openssl/cryptoerr.h:

/usr/include/c++/14.2.1/stdexcept:

/usr/include/c++/14.2.1/streambuf:

/usr/include/c++/14.2.1/backward/auto_ptr.h:

/usr/include/c++/14.2.1/string:

/usr/include/pthread.h:

/usr/include/linux/errno.h:

/usr/include/c++/14.2.1/string_view:

/usr/include/c++/14.2.1/system_error:

/usr/include/opencv4/opencv2/flann/saving.h:

/usr/include/Poco/BinaryReader.h:

/usr/include/c++/14.2.1/tr1/exp_integral.tcc:

/usr/include/opencv4/opencv2/core/base.hpp:

/usr/include/c++/14.2.1/tr1/gamma.tcc:

/usr/include/c++/14.2.1/ext/alloc_traits.h:

/usr/include/c++/14.2.1/tr1/hypergeometric.tcc:

/usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc:

/usr/include/c++/14.2.1/tr1/poly_hermite.tcc:

/usr/include/Poco/BinaryWriter.h:

/usr/include/c++/14.2.1/unordered_map:

/usr/include/c++/14.2.1/unordered_set:

/usr/include/c++/14.2.1/bits/localefwd.h:

/usr/include/openssl/crypto.h:

/usr/include/c++/14.2.1/bits/stl_function.h:

/usr/include/ctype.h:

/usr/include/gnu/stubs-lp64.h:

/usr/include/fcntl.h:

/usr/include/openssl/macros.h:

/usr/include/sys/socket.h:

/usr/include/limits.h:

/usr/include/c++/14.2.1/ostream:

/usr/include/linux/close_range.h:

/usr/include/linux/limits.h:

/usr/include/linux/types.h:

/usr/include/c++/14.2.1/bits/exception_ptr.h:

/usr/include/openssl/opensslconf.h:

/usr/include/net/if.h:

/usr/include/netinet/in.h:

/usr/include/c++/14.2.1/variant:

/usr/include/netinet/tcp.h:

/home/<USER>/mywork/poco_serverdemo/backend/blserver/poco_websocket_server.h:

/usr/include/bits/syscall.h:

/usr/include/c++/14.2.1/pstl/pstl_config.h:

/usr/include/openssl/bio.h:

/usr/include/openssl/core.h:

/usr/include/openssl/lhash.h:

/usr/include/openssl/safestack.h:

/usr/include/openssl/stack.h:

/usr/include/fenv.h:

/usr/include/openssl/types.h:

/usr/include/opencv4/opencv2/objdetect/aruco_board.hpp:

/usr/include/rpc/netdb.h:

/usr/include/semaphore.h:

/usr/include/stdc-predef.h:

/usr/include/stdlib.h:

/usr/include/sys/cdefs.h:

/usr/include/sys/ioctl.h:

/usr/include/sys/select.h:

/usr/include/asm-generic/errno.h:

/usr/include/sys/single_threaded.h:

/usr/include/opencv4/opencv2/flann/ground_truth.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h:
