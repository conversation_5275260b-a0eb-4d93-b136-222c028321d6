
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.cpp" "ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o" "gcc" "ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.cpp" "ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o" "gcc" "ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.cpp" "ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o" "gcc" "ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp" "ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o" "gcc" "ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
