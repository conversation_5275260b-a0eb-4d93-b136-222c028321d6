# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/cserialport.dir/all
all: CMakeFiles/lkmapi.dir/all
all: CMakeFiles/utils.dir/all
all: CMakeFiles/algorithm_lib.dir/all
all: CMakeFiles/base.dir/all
all: CMakeFiles/core.dir/all
all: CMakeFiles/tests.dir/all
all: CMakeFiles/infer_test.dir/all
all: CMakeFiles/blkmapi.dir/all
all: CMakeFiles/blkm_test.dir/all
all: CMakeFiles/BL.dir/all
all: CMakeFiles/bl_infer.dir/all
all: CMakeFiles/lkm_test.dir/all
all: 3rdparty/all
all: ThreadManager/all
all: backend/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/cserialport.dir/codegen
codegen: CMakeFiles/lkmapi.dir/codegen
codegen: CMakeFiles/utils.dir/codegen
codegen: CMakeFiles/algorithm_lib.dir/codegen
codegen: CMakeFiles/base.dir/codegen
codegen: CMakeFiles/core.dir/codegen
codegen: CMakeFiles/tests.dir/codegen
codegen: CMakeFiles/infer_test.dir/codegen
codegen: CMakeFiles/blkmapi.dir/codegen
codegen: CMakeFiles/blkm_test.dir/codegen
codegen: CMakeFiles/BL.dir/codegen
codegen: CMakeFiles/bl_infer.dir/codegen
codegen: CMakeFiles/lkm_test.dir/codegen
codegen: 3rdparty/codegen
codegen: ThreadManager/codegen
codegen: backend/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: 3rdparty/preinstall
preinstall: ThreadManager/preinstall
preinstall: backend/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/cserialport.dir/clean
clean: CMakeFiles/lkmapi.dir/clean
clean: CMakeFiles/utils.dir/clean
clean: CMakeFiles/algorithm_lib.dir/clean
clean: CMakeFiles/base.dir/clean
clean: CMakeFiles/core.dir/clean
clean: CMakeFiles/tests.dir/clean
clean: CMakeFiles/infer_test.dir/clean
clean: CMakeFiles/blkmapi.dir/clean
clean: CMakeFiles/blkm_test.dir/clean
clean: CMakeFiles/BL.dir/clean
clean: CMakeFiles/bl_infer.dir/clean
clean: CMakeFiles/lkm_test.dir/clean
clean: 3rdparty/fmt/clean
clean: 3rdparty/spdlog/clean
clean: 3rdparty/clean
clean: ThreadManager/clean
clean: backend/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty

# Recursive "all" directory target.
3rdparty/all: 3rdparty/libusbgx/all
3rdparty/all: 3rdparty/librga/all
3rdparty/all: 3rdparty/rknn/all
3rdparty/all: 3rdparty/hid-rp/all
3rdparty/all: 3rdparty/CImg/all
3rdparty/all: 3rdparty/Catch2/all
3rdparty/all: 3rdparty/libusb-cmake/all
.PHONY : 3rdparty/all

# Recursive "codegen" directory target.
3rdparty/codegen: 3rdparty/libusbgx/codegen
3rdparty/codegen: 3rdparty/librga/codegen
3rdparty/codegen: 3rdparty/rknn/codegen
3rdparty/codegen: 3rdparty/hid-rp/codegen
3rdparty/codegen: 3rdparty/CImg/codegen
3rdparty/codegen: 3rdparty/Catch2/codegen
3rdparty/codegen: 3rdparty/libusb-cmake/codegen
.PHONY : 3rdparty/codegen

# Recursive "preinstall" directory target.
3rdparty/preinstall: 3rdparty/libusbgx/preinstall
3rdparty/preinstall: 3rdparty/librga/preinstall
3rdparty/preinstall: 3rdparty/rknn/preinstall
3rdparty/preinstall: 3rdparty/hid-rp/preinstall
3rdparty/preinstall: 3rdparty/CImg/preinstall
3rdparty/preinstall: 3rdparty/Catch2/preinstall
3rdparty/preinstall: 3rdparty/libusb-cmake/preinstall
.PHONY : 3rdparty/preinstall

# Recursive "clean" directory target.
3rdparty/clean: 3rdparty/libusbgx/clean
3rdparty/clean: 3rdparty/librga/clean
3rdparty/clean: 3rdparty/rknn/clean
3rdparty/clean: 3rdparty/hid-rp/clean
3rdparty/clean: 3rdparty/CImg/clean
3rdparty/clean: 3rdparty/Catch2/clean
3rdparty/clean: 3rdparty/libusb-cmake/clean
.PHONY : 3rdparty/clean

#=============================================================================
# Directory level rules for directory 3rdparty/CImg

# Recursive "all" directory target.
3rdparty/CImg/all:
.PHONY : 3rdparty/CImg/all

# Recursive "codegen" directory target.
3rdparty/CImg/codegen:
.PHONY : 3rdparty/CImg/codegen

# Recursive "preinstall" directory target.
3rdparty/CImg/preinstall:
.PHONY : 3rdparty/CImg/preinstall

# Recursive "clean" directory target.
3rdparty/CImg/clean:
.PHONY : 3rdparty/CImg/clean

#=============================================================================
# Directory level rules for directory 3rdparty/Catch2

# Recursive "all" directory target.
3rdparty/Catch2/all: 3rdparty/Catch2/src/all
.PHONY : 3rdparty/Catch2/all

# Recursive "codegen" directory target.
3rdparty/Catch2/codegen: 3rdparty/Catch2/src/codegen
.PHONY : 3rdparty/Catch2/codegen

# Recursive "preinstall" directory target.
3rdparty/Catch2/preinstall: 3rdparty/Catch2/src/preinstall
.PHONY : 3rdparty/Catch2/preinstall

# Recursive "clean" directory target.
3rdparty/Catch2/clean: 3rdparty/Catch2/src/clean
.PHONY : 3rdparty/Catch2/clean

#=============================================================================
# Directory level rules for directory 3rdparty/Catch2/src

# Recursive "all" directory target.
3rdparty/Catch2/src/all: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/all
3rdparty/Catch2/src/all: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/all
.PHONY : 3rdparty/Catch2/src/all

# Recursive "codegen" directory target.
3rdparty/Catch2/src/codegen: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/codegen
3rdparty/Catch2/src/codegen: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/codegen
.PHONY : 3rdparty/Catch2/src/codegen

# Recursive "preinstall" directory target.
3rdparty/Catch2/src/preinstall:
.PHONY : 3rdparty/Catch2/src/preinstall

# Recursive "clean" directory target.
3rdparty/Catch2/src/clean: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/clean
3rdparty/Catch2/src/clean: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/clean
.PHONY : 3rdparty/Catch2/src/clean

#=============================================================================
# Directory level rules for directory 3rdparty/fmt

# Recursive "all" directory target.
3rdparty/fmt/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
.PHONY : 3rdparty/fmt/all

# Recursive "codegen" directory target.
3rdparty/fmt/codegen: 3rdparty/fmt/CMakeFiles/fmt.dir/codegen
.PHONY : 3rdparty/fmt/codegen

# Recursive "preinstall" directory target.
3rdparty/fmt/preinstall:
.PHONY : 3rdparty/fmt/preinstall

# Recursive "clean" directory target.
3rdparty/fmt/clean: 3rdparty/fmt/CMakeFiles/fmt.dir/clean
.PHONY : 3rdparty/fmt/clean

#=============================================================================
# Directory level rules for directory 3rdparty/hid-rp

# Recursive "all" directory target.
3rdparty/hid-rp/all:
.PHONY : 3rdparty/hid-rp/all

# Recursive "codegen" directory target.
3rdparty/hid-rp/codegen:
.PHONY : 3rdparty/hid-rp/codegen

# Recursive "preinstall" directory target.
3rdparty/hid-rp/preinstall:
.PHONY : 3rdparty/hid-rp/preinstall

# Recursive "clean" directory target.
3rdparty/hid-rp/clean:
.PHONY : 3rdparty/hid-rp/clean

#=============================================================================
# Directory level rules for directory 3rdparty/librga

# Recursive "all" directory target.
3rdparty/librga/all:
.PHONY : 3rdparty/librga/all

# Recursive "codegen" directory target.
3rdparty/librga/codegen:
.PHONY : 3rdparty/librga/codegen

# Recursive "preinstall" directory target.
3rdparty/librga/preinstall:
.PHONY : 3rdparty/librga/preinstall

# Recursive "clean" directory target.
3rdparty/librga/clean:
.PHONY : 3rdparty/librga/clean

#=============================================================================
# Directory level rules for directory 3rdparty/libusb-cmake

# Recursive "all" directory target.
3rdparty/libusb-cmake/all: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/all
.PHONY : 3rdparty/libusb-cmake/all

# Recursive "codegen" directory target.
3rdparty/libusb-cmake/codegen: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/codegen
.PHONY : 3rdparty/libusb-cmake/codegen

# Recursive "preinstall" directory target.
3rdparty/libusb-cmake/preinstall:
.PHONY : 3rdparty/libusb-cmake/preinstall

# Recursive "clean" directory target.
3rdparty/libusb-cmake/clean: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/clean
.PHONY : 3rdparty/libusb-cmake/clean

#=============================================================================
# Directory level rules for directory 3rdparty/libusbgx

# Recursive "all" directory target.
3rdparty/libusbgx/all: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/all
.PHONY : 3rdparty/libusbgx/all

# Recursive "codegen" directory target.
3rdparty/libusbgx/codegen: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/codegen
.PHONY : 3rdparty/libusbgx/codegen

# Recursive "preinstall" directory target.
3rdparty/libusbgx/preinstall:
.PHONY : 3rdparty/libusbgx/preinstall

# Recursive "clean" directory target.
3rdparty/libusbgx/clean: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/clean
.PHONY : 3rdparty/libusbgx/clean

#=============================================================================
# Directory level rules for directory 3rdparty/rknn

# Recursive "all" directory target.
3rdparty/rknn/all:
.PHONY : 3rdparty/rknn/all

# Recursive "codegen" directory target.
3rdparty/rknn/codegen:
.PHONY : 3rdparty/rknn/codegen

# Recursive "preinstall" directory target.
3rdparty/rknn/preinstall:
.PHONY : 3rdparty/rknn/preinstall

# Recursive "clean" directory target.
3rdparty/rknn/clean:
.PHONY : 3rdparty/rknn/clean

#=============================================================================
# Directory level rules for directory 3rdparty/spdlog

# Recursive "all" directory target.
3rdparty/spdlog/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
.PHONY : 3rdparty/spdlog/all

# Recursive "codegen" directory target.
3rdparty/spdlog/codegen: 3rdparty/spdlog/CMakeFiles/spdlog.dir/codegen
.PHONY : 3rdparty/spdlog/codegen

# Recursive "preinstall" directory target.
3rdparty/spdlog/preinstall:
.PHONY : 3rdparty/spdlog/preinstall

# Recursive "clean" directory target.
3rdparty/spdlog/clean: 3rdparty/spdlog/CMakeFiles/spdlog.dir/clean
.PHONY : 3rdparty/spdlog/clean

#=============================================================================
# Directory level rules for directory ThreadManager

# Recursive "all" directory target.
ThreadManager/all: ThreadManager/CMakeFiles/thread_manager.dir/all
.PHONY : ThreadManager/all

# Recursive "codegen" directory target.
ThreadManager/codegen: ThreadManager/CMakeFiles/thread_manager.dir/codegen
.PHONY : ThreadManager/codegen

# Recursive "preinstall" directory target.
ThreadManager/preinstall:
.PHONY : ThreadManager/preinstall

# Recursive "clean" directory target.
ThreadManager/clean: ThreadManager/CMakeFiles/thread_manager.dir/clean
.PHONY : ThreadManager/clean

#=============================================================================
# Directory level rules for directory backend

# Recursive "all" directory target.
backend/all: backend/CMakeFiles/blweb_backend.dir/all
backend/all: backend/CMakeFiles/bl_server.dir/all
.PHONY : backend/all

# Recursive "codegen" directory target.
backend/codegen: backend/CMakeFiles/blweb_backend.dir/codegen
backend/codegen: backend/CMakeFiles/bl_server.dir/codegen
.PHONY : backend/codegen

# Recursive "preinstall" directory target.
backend/preinstall:
.PHONY : backend/preinstall

# Recursive "clean" directory target.
backend/clean: backend/CMakeFiles/blweb_backend.dir/clean
backend/clean: backend/CMakeFiles/bl_server.dir/clean
.PHONY : backend/clean

#=============================================================================
# Target rules for target CMakeFiles/cserialport.dir

# All Build rule for target.
CMakeFiles/cserialport.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=73,74,75 "Built target cserialport"
.PHONY : CMakeFiles/cserialport.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/cserialport.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/cserialport.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/cserialport.dir/rule

# Convenience name for target.
cserialport: CMakeFiles/cserialport.dir/rule
.PHONY : cserialport

# codegen rule for target.
CMakeFiles/cserialport.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=73,74,75 "Finished codegen for target cserialport"
.PHONY : CMakeFiles/cserialport.dir/codegen

# clean rule for target.
CMakeFiles/cserialport.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cserialport.dir/build.make CMakeFiles/cserialport.dir/clean
.PHONY : CMakeFiles/cserialport.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/lkmapi.dir

# All Build rule for target.
CMakeFiles/lkmapi.dir/all: CMakeFiles/cserialport.dir/all
CMakeFiles/lkmapi.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/lkmapi.dir/all: CMakeFiles/algorithm_lib.dir/all
CMakeFiles/lkmapi.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/lkmapi.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=86,87 "Built target lkmapi"
.PHONY : CMakeFiles/lkmapi.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/lkmapi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/lkmapi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/lkmapi.dir/rule

# Convenience name for target.
lkmapi: CMakeFiles/lkmapi.dir/rule
.PHONY : lkmapi

# codegen rule for target.
CMakeFiles/lkmapi.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=86,87 "Finished codegen for target lkmapi"
.PHONY : CMakeFiles/lkmapi.dir/codegen

# clean rule for target.
CMakeFiles/lkmapi.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkmapi.dir/build.make CMakeFiles/lkmapi.dir/clean
.PHONY : CMakeFiles/lkmapi.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/utils.dir

# All Build rule for target.
CMakeFiles/utils.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/utils.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=99,100 "Built target utils"
.PHONY : CMakeFiles/utils.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/utils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/utils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/utils.dir/rule

# Convenience name for target.
utils: CMakeFiles/utils.dir/rule
.PHONY : utils

# codegen rule for target.
CMakeFiles/utils.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=99,100 "Finished codegen for target utils"
.PHONY : CMakeFiles/utils.dir/codegen

# clean rule for target.
CMakeFiles/utils.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/clean
.PHONY : CMakeFiles/utils.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/algorithm_lib.dir

# All Build rule for target.
CMakeFiles/algorithm_lib.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=47 "Built target algorithm_lib"
.PHONY : CMakeFiles/algorithm_lib.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/algorithm_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/algorithm_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/algorithm_lib.dir/rule

# Convenience name for target.
algorithm_lib: CMakeFiles/algorithm_lib.dir/rule
.PHONY : algorithm_lib

# codegen rule for target.
CMakeFiles/algorithm_lib.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=47 "Finished codegen for target algorithm_lib"
.PHONY : CMakeFiles/algorithm_lib.dir/codegen

# clean rule for target.
CMakeFiles/algorithm_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm_lib.dir/build.make CMakeFiles/algorithm_lib.dir/clean
.PHONY : CMakeFiles/algorithm_lib.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/base.dir

# All Build rule for target.
CMakeFiles/base.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/base.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/base.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=48 "Built target base"
.PHONY : CMakeFiles/base.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/base.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/base.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/base.dir/rule

# Convenience name for target.
base: CMakeFiles/base.dir/rule
.PHONY : base

# codegen rule for target.
CMakeFiles/base.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=48 "Finished codegen for target base"
.PHONY : CMakeFiles/base.dir/codegen

# clean rule for target.
CMakeFiles/base.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/base.dir/build.make CMakeFiles/base.dir/clean
.PHONY : CMakeFiles/base.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/core.dir

# All Build rule for target.
CMakeFiles/core.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/core.dir/all: CMakeFiles/base.dir/all
CMakeFiles/core.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/core.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=68,69,70,71,72 "Built target core"
.PHONY : CMakeFiles/core.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/core.dir/rule

# Convenience name for target.
core: CMakeFiles/core.dir/rule
.PHONY : core

# codegen rule for target.
CMakeFiles/core.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=68,69,70,71,72 "Finished codegen for target core"
.PHONY : CMakeFiles/core.dir/codegen

# clean rule for target.
CMakeFiles/core.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core.dir/build.make CMakeFiles/core.dir/clean
.PHONY : CMakeFiles/core.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/tests.dir

# All Build rule for target.
CMakeFiles/tests.dir/all: CMakeFiles/cserialport.dir/all
CMakeFiles/tests.dir/all: CMakeFiles/lkmapi.dir/all
CMakeFiles/tests.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/tests.dir/all: CMakeFiles/algorithm_lib.dir/all
CMakeFiles/tests.dir/all: CMakeFiles/base.dir/all
CMakeFiles/tests.dir/all: CMakeFiles/core.dir/all
CMakeFiles/tests.dir/all: CMakeFiles/blkmapi.dir/all
CMakeFiles/tests.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/tests.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
CMakeFiles/tests.dir/all: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/all
CMakeFiles/tests.dir/all: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/all
CMakeFiles/tests.dir/all: ThreadManager/CMakeFiles/thread_manager.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=91 "Built target tests"
.PHONY : CMakeFiles/tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 69
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/tests.dir/rule

# Convenience name for target.
tests: CMakeFiles/tests.dir/rule
.PHONY : tests

# codegen rule for target.
CMakeFiles/tests.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=91 "Finished codegen for target tests"
.PHONY : CMakeFiles/tests.dir/codegen

# clean rule for target.
CMakeFiles/tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/clean
.PHONY : CMakeFiles/tests.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/infer_test.dir

# All Build rule for target.
CMakeFiles/infer_test.dir/all: CMakeFiles/cserialport.dir/all
CMakeFiles/infer_test.dir/all: CMakeFiles/lkmapi.dir/all
CMakeFiles/infer_test.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/infer_test.dir/all: CMakeFiles/algorithm_lib.dir/all
CMakeFiles/infer_test.dir/all: CMakeFiles/base.dir/all
CMakeFiles/infer_test.dir/all: CMakeFiles/core.dir/all
CMakeFiles/infer_test.dir/all: CMakeFiles/blkmapi.dir/all
CMakeFiles/infer_test.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/infer_test.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
CMakeFiles/infer_test.dir/all: ThreadManager/CMakeFiles/thread_manager.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num= "Built target infer_test"
.PHONY : CMakeFiles/infer_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/infer_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/infer_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/infer_test.dir/rule

# Convenience name for target.
infer_test: CMakeFiles/infer_test.dir/rule
.PHONY : infer_test

# codegen rule for target.
CMakeFiles/infer_test.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num= "Finished codegen for target infer_test"
.PHONY : CMakeFiles/infer_test.dir/codegen

# clean rule for target.
CMakeFiles/infer_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/infer_test.dir/build.make CMakeFiles/infer_test.dir/clean
.PHONY : CMakeFiles/infer_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/blkmapi.dir

# All Build rule for target.
CMakeFiles/blkmapi.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/blkmapi.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/blkmapi.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=52 "Built target blkmapi"
.PHONY : CMakeFiles/blkmapi.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/blkmapi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/blkmapi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/blkmapi.dir/rule

# Convenience name for target.
blkmapi: CMakeFiles/blkmapi.dir/rule
.PHONY : blkmapi

# codegen rule for target.
CMakeFiles/blkmapi.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=52 "Finished codegen for target blkmapi"
.PHONY : CMakeFiles/blkmapi.dir/codegen

# clean rule for target.
CMakeFiles/blkmapi.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkmapi.dir/build.make CMakeFiles/blkmapi.dir/clean
.PHONY : CMakeFiles/blkmapi.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/blkm_test.dir

# All Build rule for target.
CMakeFiles/blkm_test.dir/all: CMakeFiles/cserialport.dir/all
CMakeFiles/blkm_test.dir/all: CMakeFiles/lkmapi.dir/all
CMakeFiles/blkm_test.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/blkm_test.dir/all: CMakeFiles/algorithm_lib.dir/all
CMakeFiles/blkm_test.dir/all: CMakeFiles/base.dir/all
CMakeFiles/blkm_test.dir/all: CMakeFiles/core.dir/all
CMakeFiles/blkm_test.dir/all: CMakeFiles/blkmapi.dir/all
CMakeFiles/blkm_test.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/blkm_test.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
CMakeFiles/blkm_test.dir/all: ThreadManager/CMakeFiles/thread_manager.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=51 "Built target blkm_test"
.PHONY : CMakeFiles/blkm_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/blkm_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/blkm_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/blkm_test.dir/rule

# Convenience name for target.
blkm_test: CMakeFiles/blkm_test.dir/rule
.PHONY : blkm_test

# codegen rule for target.
CMakeFiles/blkm_test.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=51 "Finished codegen for target blkm_test"
.PHONY : CMakeFiles/blkm_test.dir/codegen

# clean rule for target.
CMakeFiles/blkm_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/blkm_test.dir/build.make CMakeFiles/blkm_test.dir/clean
.PHONY : CMakeFiles/blkm_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/BL.dir

# All Build rule for target.
CMakeFiles/BL.dir/all: CMakeFiles/cserialport.dir/all
CMakeFiles/BL.dir/all: CMakeFiles/lkmapi.dir/all
CMakeFiles/BL.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/BL.dir/all: CMakeFiles/algorithm_lib.dir/all
CMakeFiles/BL.dir/all: CMakeFiles/base.dir/all
CMakeFiles/BL.dir/all: CMakeFiles/core.dir/all
CMakeFiles/BL.dir/all: CMakeFiles/blkmapi.dir/all
CMakeFiles/BL.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/BL.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
CMakeFiles/BL.dir/all: ThreadManager/CMakeFiles/thread_manager.dir/all
CMakeFiles/BL.dir/all: backend/CMakeFiles/blweb_backend.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num= "Built target BL"
.PHONY : CMakeFiles/BL.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BL.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BL.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/BL.dir/rule

# Convenience name for target.
BL: CMakeFiles/BL.dir/rule
.PHONY : BL

# codegen rule for target.
CMakeFiles/BL.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num= "Finished codegen for target BL"
.PHONY : CMakeFiles/BL.dir/codegen

# clean rule for target.
CMakeFiles/BL.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BL.dir/build.make CMakeFiles/BL.dir/clean
.PHONY : CMakeFiles/BL.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/bl_infer.dir

# All Build rule for target.
CMakeFiles/bl_infer.dir/all: CMakeFiles/cserialport.dir/all
CMakeFiles/bl_infer.dir/all: CMakeFiles/lkmapi.dir/all
CMakeFiles/bl_infer.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/bl_infer.dir/all: CMakeFiles/algorithm_lib.dir/all
CMakeFiles/bl_infer.dir/all: CMakeFiles/base.dir/all
CMakeFiles/bl_infer.dir/all: CMakeFiles/core.dir/all
CMakeFiles/bl_infer.dir/all: CMakeFiles/blkmapi.dir/all
CMakeFiles/bl_infer.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/bl_infer.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
CMakeFiles/bl_infer.dir/all: ThreadManager/CMakeFiles/thread_manager.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=49 "Built target bl_infer"
.PHONY : CMakeFiles/bl_infer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/bl_infer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/bl_infer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/bl_infer.dir/rule

# Convenience name for target.
bl_infer: CMakeFiles/bl_infer.dir/rule
.PHONY : bl_infer

# codegen rule for target.
CMakeFiles/bl_infer.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=49 "Finished codegen for target bl_infer"
.PHONY : CMakeFiles/bl_infer.dir/codegen

# clean rule for target.
CMakeFiles/bl_infer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bl_infer.dir/build.make CMakeFiles/bl_infer.dir/clean
.PHONY : CMakeFiles/bl_infer.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/lkm_test.dir

# All Build rule for target.
CMakeFiles/lkm_test.dir/all: CMakeFiles/cserialport.dir/all
CMakeFiles/lkm_test.dir/all: CMakeFiles/lkmapi.dir/all
CMakeFiles/lkm_test.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/lkm_test.dir/all: CMakeFiles/algorithm_lib.dir/all
CMakeFiles/lkm_test.dir/all: CMakeFiles/base.dir/all
CMakeFiles/lkm_test.dir/all: CMakeFiles/core.dir/all
CMakeFiles/lkm_test.dir/all: CMakeFiles/blkmapi.dir/all
CMakeFiles/lkm_test.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
CMakeFiles/lkm_test.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
CMakeFiles/lkm_test.dir/all: ThreadManager/CMakeFiles/thread_manager.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=85 "Built target lkm_test"
.PHONY : CMakeFiles/lkm_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/lkm_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/lkm_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : CMakeFiles/lkm_test.dir/rule

# Convenience name for target.
lkm_test: CMakeFiles/lkm_test.dir/rule
.PHONY : lkm_test

# codegen rule for target.
CMakeFiles/lkm_test.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=85 "Finished codegen for target lkm_test"
.PHONY : CMakeFiles/lkm_test.dir/codegen

# clean rule for target.
CMakeFiles/lkm_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lkm_test.dir/build.make CMakeFiles/lkm_test.dir/clean
.PHONY : CMakeFiles/lkm_test.dir/clean

#=============================================================================
# Target rules for target 3rdparty/fmt/CMakeFiles/fmt.dir

# All Build rule for target.
3rdparty/fmt/CMakeFiles/fmt.dir/all:
	$(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/depend
	$(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=76,77 "Built target fmt"
.PHONY : 3rdparty/fmt/CMakeFiles/fmt.dir/all

# Build rule for subdir invocation for target.
3rdparty/fmt/CMakeFiles/fmt.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/fmt/CMakeFiles/fmt.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : 3rdparty/fmt/CMakeFiles/fmt.dir/rule

# Convenience name for target.
fmt: 3rdparty/fmt/CMakeFiles/fmt.dir/rule
.PHONY : fmt

# codegen rule for target.
3rdparty/fmt/CMakeFiles/fmt.dir/codegen:
	$(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=76,77 "Finished codegen for target fmt"
.PHONY : 3rdparty/fmt/CMakeFiles/fmt.dir/codegen

# clean rule for target.
3rdparty/fmt/CMakeFiles/fmt.dir/clean:
	$(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/clean
.PHONY : 3rdparty/fmt/CMakeFiles/fmt.dir/clean

#=============================================================================
# Target rules for target 3rdparty/spdlog/CMakeFiles/spdlog.dir

# All Build rule for target.
3rdparty/spdlog/CMakeFiles/spdlog.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
	$(MAKE) $(MAKESILENT) -f 3rdparty/spdlog/CMakeFiles/spdlog.dir/build.make 3rdparty/spdlog/CMakeFiles/spdlog.dir/depend
	$(MAKE) $(MAKESILENT) -f 3rdparty/spdlog/CMakeFiles/spdlog.dir/build.make 3rdparty/spdlog/CMakeFiles/spdlog.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=88,89,90 "Built target spdlog"
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/all

# Build rule for subdir invocation for target.
3rdparty/spdlog/CMakeFiles/spdlog.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/rule

# Convenience name for target.
spdlog: 3rdparty/spdlog/CMakeFiles/spdlog.dir/rule
.PHONY : spdlog

# codegen rule for target.
3rdparty/spdlog/CMakeFiles/spdlog.dir/codegen:
	$(MAKE) $(MAKESILENT) -f 3rdparty/spdlog/CMakeFiles/spdlog.dir/build.make 3rdparty/spdlog/CMakeFiles/spdlog.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=88,89,90 "Finished codegen for target spdlog"
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/codegen

# clean rule for target.
3rdparty/spdlog/CMakeFiles/spdlog.dir/clean:
	$(MAKE) $(MAKESILENT) -f 3rdparty/spdlog/CMakeFiles/spdlog.dir/build.make 3rdparty/spdlog/CMakeFiles/spdlog.dir/clean
.PHONY : 3rdparty/spdlog/CMakeFiles/spdlog.dir/clean

#=============================================================================
# Target rules for target 3rdparty/libusbgx/CMakeFiles/libusbgx.dir

# All Build rule for target.
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/all:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/depend
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=78,79,80,81,82,83,84 "Built target libusbgx"
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/all

# Build rule for subdir invocation for target.
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/rule

# Convenience name for target.
libusbgx: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/rule
.PHONY : libusbgx

# codegen rule for target.
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/codegen:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=78,79,80,81,82,83,84 "Finished codegen for target libusbgx"
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/codegen

# clean rule for target.
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/clean:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/clean
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/clean

#=============================================================================
# Target rules for target 3rdparty/Catch2/src/CMakeFiles/Catch2.dir

# All Build rule for target.
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/all:
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/depend
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45 "Built target Catch2"
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/all

# Build rule for subdir invocation for target.
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 45
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/rule

# Convenience name for target.
Catch2: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/rule
.PHONY : Catch2

# codegen rule for target.
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/codegen:
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45 "Finished codegen for target Catch2"
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/codegen

# clean rule for target.
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/clean:
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/clean
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/clean

#=============================================================================
# Target rules for target 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir

# All Build rule for target.
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/all: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/all
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/depend
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=46 "Built target Catch2WithMain"
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/all

# Build rule for subdir invocation for target.
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 46
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/rule

# Convenience name for target.
Catch2WithMain: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/rule
.PHONY : Catch2WithMain

# codegen rule for target.
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/codegen:
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=46 "Finished codegen for target Catch2WithMain"
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/codegen

# clean rule for target.
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/clean:
	$(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/clean
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/clean

#=============================================================================
# Target rules for target 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir

# All Build rule for target.
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/all:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/depend
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=94,95,96,97,98 "Built target usb-1.0"
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/all

# Build rule for subdir invocation for target.
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/rule

# Convenience name for target.
usb-1.0: 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/rule
.PHONY : usb-1.0

# codegen rule for target.
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/codegen:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=94,95,96,97,98 "Finished codegen for target usb-1.0"
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/codegen

# clean rule for target.
3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/clean:
	$(MAKE) $(MAKESILENT) -f 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/build.make 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/clean
.PHONY : 3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/clean

#=============================================================================
# Target rules for target ThreadManager/CMakeFiles/thread_manager.dir

# All Build rule for target.
ThreadManager/CMakeFiles/thread_manager.dir/all: CMakeFiles/cserialport.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: CMakeFiles/lkmapi.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: CMakeFiles/utils.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: CMakeFiles/algorithm_lib.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: CMakeFiles/base.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: CMakeFiles/core.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: CMakeFiles/blkmapi.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
ThreadManager/CMakeFiles/thread_manager.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/depend
	$(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=92,93 "Built target thread_manager"
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/all

# Build rule for subdir invocation for target.
ThreadManager/CMakeFiles/thread_manager.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ThreadManager/CMakeFiles/thread_manager.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/rule

# Convenience name for target.
thread_manager: ThreadManager/CMakeFiles/thread_manager.dir/rule
.PHONY : thread_manager

# codegen rule for target.
ThreadManager/CMakeFiles/thread_manager.dir/codegen:
	$(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=92,93 "Finished codegen for target thread_manager"
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/codegen

# clean rule for target.
ThreadManager/CMakeFiles/thread_manager.dir/clean:
	$(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/clean
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/clean

#=============================================================================
# Target rules for target backend/CMakeFiles/blweb_backend.dir

# All Build rule for target.
backend/CMakeFiles/blweb_backend.dir/all: CMakeFiles/utils.dir/all
backend/CMakeFiles/blweb_backend.dir/all: CMakeFiles/algorithm_lib.dir/all
backend/CMakeFiles/blweb_backend.dir/all: CMakeFiles/blkmapi.dir/all
backend/CMakeFiles/blweb_backend.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
backend/CMakeFiles/blweb_backend.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/depend
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=53,54,55,56,57,58,59,60,61,62,63,64,65,66,67 "Built target blweb_backend"
.PHONY : backend/CMakeFiles/blweb_backend.dir/all

# Build rule for subdir invocation for target.
backend/CMakeFiles/blweb_backend.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/CMakeFiles/blweb_backend.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : backend/CMakeFiles/blweb_backend.dir/rule

# Convenience name for target.
blweb_backend: backend/CMakeFiles/blweb_backend.dir/rule
.PHONY : blweb_backend

# codegen rule for target.
backend/CMakeFiles/blweb_backend.dir/codegen:
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=53,54,55,56,57,58,59,60,61,62,63,64,65,66,67 "Finished codegen for target blweb_backend"
.PHONY : backend/CMakeFiles/blweb_backend.dir/codegen

# clean rule for target.
backend/CMakeFiles/blweb_backend.dir/clean:
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/clean
.PHONY : backend/CMakeFiles/blweb_backend.dir/clean

#=============================================================================
# Target rules for target backend/CMakeFiles/bl_server.dir

# All Build rule for target.
backend/CMakeFiles/bl_server.dir/all: CMakeFiles/utils.dir/all
backend/CMakeFiles/bl_server.dir/all: CMakeFiles/algorithm_lib.dir/all
backend/CMakeFiles/bl_server.dir/all: CMakeFiles/blkmapi.dir/all
backend/CMakeFiles/bl_server.dir/all: 3rdparty/fmt/CMakeFiles/fmt.dir/all
backend/CMakeFiles/bl_server.dir/all: 3rdparty/spdlog/CMakeFiles/spdlog.dir/all
backend/CMakeFiles/bl_server.dir/all: backend/CMakeFiles/blweb_backend.dir/all
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/depend
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=50 "Built target bl_server"
.PHONY : backend/CMakeFiles/bl_server.dir/all

# Build rule for subdir invocation for target.
backend/CMakeFiles/bl_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/CMakeFiles/bl_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles 0
.PHONY : backend/CMakeFiles/bl_server.dir/rule

# Convenience name for target.
bl_server: backend/CMakeFiles/bl_server.dir/rule
.PHONY : bl_server

# codegen rule for target.
backend/CMakeFiles/bl_server.dir/codegen:
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=50 "Finished codegen for target bl_server"
.PHONY : backend/CMakeFiles/bl_server.dir/codegen

# clean rule for target.
backend/CMakeFiles/bl_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/clean
.PHONY : backend/CMakeFiles/bl_server.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

