{"InstallScripts": ["/home/<USER>/mywork/poco_serverdemo/build_new/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/fmt/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/spdlog/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusbgx/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/librga/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/rknn/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/hid-rp/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/CImg/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/Catch2/src/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/3rdparty/libusb-cmake/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/ThreadManager/cmake_install.cmake", "/home/<USER>/mywork/poco_serverdemo/build_new/backend/cmake_install.cmake"], "Parallel": false}