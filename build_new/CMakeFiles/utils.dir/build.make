# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include CMakeFiles/utils.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/utils.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/utils.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/utils.dir/flags.make

CMakeFiles/utils.dir/codegen:
.PHONY : CMakeFiles/utils.dir/codegen

CMakeFiles/utils.dir/utils/gmodels.cpp.o: CMakeFiles/utils.dir/flags.make
CMakeFiles/utils.dir/utils/gmodels.cpp.o: /home/<USER>/mywork/poco_serverdemo/utils/gmodels.cpp
CMakeFiles/utils.dir/utils/gmodels.cpp.o: CMakeFiles/utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/utils.dir/utils/gmodels.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/utils.dir/utils/gmodels.cpp.o -MF CMakeFiles/utils.dir/utils/gmodels.cpp.o.d -o CMakeFiles/utils.dir/utils/gmodels.cpp.o -c /home/<USER>/mywork/poco_serverdemo/utils/gmodels.cpp

CMakeFiles/utils.dir/utils/gmodels.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/utils.dir/utils/gmodels.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/utils/gmodels.cpp > CMakeFiles/utils.dir/utils/gmodels.cpp.i

CMakeFiles/utils.dir/utils/gmodels.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/utils.dir/utils/gmodels.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/utils/gmodels.cpp -o CMakeFiles/utils.dir/utils/gmodels.cpp.s

CMakeFiles/utils.dir/utils/logger.cpp.o: CMakeFiles/utils.dir/flags.make
CMakeFiles/utils.dir/utils/logger.cpp.o: /home/<USER>/mywork/poco_serverdemo/utils/logger.cpp
CMakeFiles/utils.dir/utils/logger.cpp.o: CMakeFiles/utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/utils.dir/utils/logger.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/utils.dir/utils/logger.cpp.o -MF CMakeFiles/utils.dir/utils/logger.cpp.o.d -o CMakeFiles/utils.dir/utils/logger.cpp.o -c /home/<USER>/mywork/poco_serverdemo/utils/logger.cpp

CMakeFiles/utils.dir/utils/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/utils.dir/utils/logger.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/utils/logger.cpp > CMakeFiles/utils.dir/utils/logger.cpp.i

CMakeFiles/utils.dir/utils/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/utils.dir/utils/logger.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/utils/logger.cpp -o CMakeFiles/utils.dir/utils/logger.cpp.s

CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o: CMakeFiles/utils.dir/flags.make
CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o: /home/<USER>/mywork/poco_serverdemo/utils/wolf240hz_patch.cpp
CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o: CMakeFiles/utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o -MF CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o.d -o CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o -c /home/<USER>/mywork/poco_serverdemo/utils/wolf240hz_patch.cpp

CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/utils/wolf240hz_patch.cpp > CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.i

CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/utils/wolf240hz_patch.cpp -o CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.s

# Object files for target utils
utils_OBJECTS = \
"CMakeFiles/utils.dir/utils/gmodels.cpp.o" \
"CMakeFiles/utils.dir/utils/logger.cpp.o" \
"CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o"

# External object files for target utils
utils_EXTERNAL_OBJECTS =

bin/lib/libutils.a: CMakeFiles/utils.dir/utils/gmodels.cpp.o
bin/lib/libutils.a: CMakeFiles/utils.dir/utils/logger.cpp.o
bin/lib/libutils.a: CMakeFiles/utils.dir/utils/wolf240hz_patch.cpp.o
bin/lib/libutils.a: CMakeFiles/utils.dir/build.make
bin/lib/libutils.a: CMakeFiles/utils.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library bin/lib/libutils.a"
	$(CMAKE_COMMAND) -P CMakeFiles/utils.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/utils.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/utils.dir/build: bin/lib/libutils.a
.PHONY : CMakeFiles/utils.dir/build

CMakeFiles/utils.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/utils.dir/cmake_clean.cmake
.PHONY : CMakeFiles/utils.dir/clean

CMakeFiles/utils.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/utils.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/utils.dir/depend

