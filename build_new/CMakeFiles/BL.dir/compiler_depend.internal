# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

CMakeFiles/BL.dir/main.cpp.o
 /home/<USER>/mywork/poco_serverdemo/main.cpp
 /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortInfo.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortListener.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort_global.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h
 /home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.h
 /home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.h
 /home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h
 /home/<USER>/mywork/poco_serverdemo/utils/gmodels.h
 /home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.h
 /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.h
 /home/<USER>/mywork/poco_serverdemo/lkm/keymouse.h
 /home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.h
 /home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.h
 /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.h
 /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/MySQL/include/Poco/Data/MySQL/Connector.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/MySQL/include/Poco/Data/MySQL/MySQL.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/MySQL/include/Poco/Data/MySQL/MySQLException.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/MySQL/include/Poco/Data/MySQL/ResultMetadata.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/MySQL/include/Poco/Data/MySQL/SessionHandle.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/MySQL/include/Poco/Data/MySQL/SessionImpl.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/MySQL/include/Poco/Data/MySQL/StatementExecutor.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/AbstractBinder.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/AbstractBinding.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/AbstractExtraction.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/AbstractExtractor.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/AbstractPreparation.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/AbstractPreparator.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/AbstractSessionImpl.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Binding.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Bulk.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/BulkExtraction.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Column.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Connector.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Constants.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Data.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/DataException.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Date.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Extraction.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/LOB.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Limit.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/MetaColumn.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Position.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Preparation.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Range.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/RecordSet.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Row.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/RowFilter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/RowFormatter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/RowIterator.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Session.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/SessionImpl.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/SimpleRowFormatter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Statement.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/StatementCreator.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/StatementImpl.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/Time.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Data/include/Poco/Data/TypeHandler.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/AbstractDelegate.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/AbstractEvent.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ActiveMethod.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ActiveResult.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ActiveRunnable.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ActiveStarter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ActiveThreadPool.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Alignment.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Any.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Ascii.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/AtomicCounter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/AutoPtr.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/BasicEvent.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/BinaryReader.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/BinaryWriter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Buffer.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Bugcheck.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Config.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/DateTime.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/DateTimeFormat.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/DateTimeFormatter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/DateTimeParser.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Debugger.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/DefaultStrategy.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Dynamic/Struct.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Dynamic/Var.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Dynamic/VarHolder.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Dynamic/VarIterator.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Environment.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Error.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Event.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Event_POSIX.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Exception.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/FIFOBuffer.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/FPEnvironment.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/FPEnvironment_C99.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Format.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Foundation.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/JSONString.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ListMap.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/LocalDateTime.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/MemoryStream.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/MetaProgramming.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Mutex.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Mutex_POSIX.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/NotificationStrategy.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Nullable.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/NumberFormatter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/NumberParser.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/NumericString.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Optional.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/OrderedMap.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/OrderedSet.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Platform.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Platform_POSIX.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/RefCountedObject.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Runnable.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ScopedLock.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/SharedPtr.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/SignalHandler.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/SingletonHolder.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/StreamUtil.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/String.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/TextEncoding.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Thread.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ThreadPool.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Thread_POSIX.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Timespan.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Timestamp.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Tuple.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/TypeList.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Types.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/URI.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/UTF8String.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/UTFString.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/UUID.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/UnicodeConverter.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/Void.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ordered_hash.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ordered_map.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Foundation/include/Poco/ordered_set.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/JSON/include/Poco/JSON/Array.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/JSON/include/Poco/JSON/JSON.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/JSON/include/Poco/JSON/Object.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/JSON/include/Poco/JSON/Stringifier.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPAuthenticationParams.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPBasicCredentials.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPClientSession.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPCookie.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPCredentials.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPDigestCredentials.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPMessage.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPNTLMCredentials.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPRequest.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPRequestHandler.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPRequestHandlerFactory.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPResponse.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPServer.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPServerParams.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPServerRequest.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPServerResponse.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/HTTPSession.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/IPAddress.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/IPAddressImpl.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/MessageHeader.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/NTLMCredentials.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/NameValueCollection.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/Net.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/SSPINTLMCredentials.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/ServerSocket.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/Socket.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/SocketAddress.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/SocketAddressImpl.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/SocketDefs.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/SocketImpl.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/StreamSocket.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/TCPServer.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/TCPServerConnection.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/TCPServerConnectionFactory.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/TCPServerParams.h
 /home/<USER>/mywork/poco_serverdemo/3rdparty/poco/Net/include/Poco/Net/WebSocket.h
 /home/<USER>/mywork/poco_serverdemo/backend/blsql/mysql_db.h
 /home/<USER>/mywork/poco_serverdemo/infer/base/types.h
 /home/<USER>/mywork/poco_serverdemo/utils/logger.h
 /home/<USER>/mywork/poco_serverdemo/utils/wolf240hz_patch.h
 /usr/include/Poco/Crypto/Cipher.h
 /usr/include/Poco/Crypto/Crypto.h
 /usr/include/Poco/Crypto/CryptoTransform.h
 /usr/include/alloca.h
 /usr/include/arpa/inet.h
 /usr/include/asm-generic/bitsperlong.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/int-ll64.h
 /usr/include/asm-generic/ioctl.h
 /usr/include/asm-generic/ioctls.h
 /usr/include/asm-generic/posix_types.h
 /usr/include/asm-generic/socket.h
 /usr/include/asm-generic/sockios.h
 /usr/include/asm-generic/types.h
 /usr/include/asm/bitsperlong.h
 /usr/include/asm/errno.h
 /usr/include/asm/ioctl.h
 /usr/include/asm/ioctls.h
 /usr/include/asm/posix_types.h
 /usr/include/asm/sigcontext.h
 /usr/include/asm/socket.h
 /usr/include/asm/sockios.h
 /usr/include/asm/sve_context.h
 /usr/include/asm/types.h
 /usr/include/asm/unistd.h
 /usr/include/asm/unistd_64.h
 /usr/include/assert.h
 /usr/include/bits/atomic_wide_counter.h
 /usr/include/bits/byteswap.h
 /usr/include/bits/confname.h
 /usr/include/bits/cpu-set.h
 /usr/include/bits/endian.h
 /usr/include/bits/endianness.h
 /usr/include/bits/environments.h
 /usr/include/bits/errno.h
 /usr/include/bits/fcntl-linux.h
 /usr/include/bits/fcntl.h
 /usr/include/bits/fenv.h
 /usr/include/bits/floatn-common.h
 /usr/include/bits/floatn.h
 /usr/include/bits/flt-eval-method.h
 /usr/include/bits/fp-fast.h
 /usr/include/bits/fp-logb.h
 /usr/include/bits/getopt_core.h
 /usr/include/bits/getopt_posix.h
 /usr/include/bits/in.h
 /usr/include/bits/ioctl-types.h
 /usr/include/bits/ioctls.h
 /usr/include/bits/iscanonical.h
 /usr/include/bits/libc-header-start.h
 /usr/include/bits/libm-simd-decl-stubs.h
 /usr/include/bits/local_lim.h
 /usr/include/bits/locale.h
 /usr/include/bits/long-double.h
 /usr/include/bits/math-vector.h
 /usr/include/bits/mathcalls-helper-functions.h
 /usr/include/bits/mathcalls-macros.h
 /usr/include/bits/mathcalls-narrow.h
 /usr/include/bits/mathcalls.h
 /usr/include/bits/netdb.h
 /usr/include/bits/posix1_lim.h
 /usr/include/bits/posix2_lim.h
 /usr/include/bits/posix_opt.h
 /usr/include/bits/procfs-extra.h
 /usr/include/bits/procfs-id.h
 /usr/include/bits/procfs-prregset.h
 /usr/include/bits/procfs.h
 /usr/include/bits/pthread_stack_min-dynamic.h
 /usr/include/bits/pthreadtypes-arch.h
 /usr/include/bits/pthreadtypes.h
 /usr/include/bits/sched.h
 /usr/include/bits/select.h
 /usr/include/bits/semaphore.h
 /usr/include/bits/setjmp.h
 /usr/include/bits/sigaction.h
 /usr/include/bits/sigcontext.h
 /usr/include/bits/sigevent-consts.h
 /usr/include/bits/siginfo-arch.h
 /usr/include/bits/siginfo-consts-arch.h
 /usr/include/bits/siginfo-consts.h
 /usr/include/bits/signal_ext.h
 /usr/include/bits/signum-arch.h
 /usr/include/bits/signum-generic.h
 /usr/include/bits/sigstack.h
 /usr/include/bits/sigstksz.h
 /usr/include/bits/sigthread.h
 /usr/include/bits/sockaddr.h
 /usr/include/bits/socket.h
 /usr/include/bits/socket_type.h
 /usr/include/bits/ss_flags.h
 /usr/include/bits/stat.h
 /usr/include/bits/stdint-intn.h
 /usr/include/bits/stdint-least.h
 /usr/include/bits/stdint-uintn.h
 /usr/include/bits/stdio.h
 /usr/include/bits/stdio_lim.h
 /usr/include/bits/stdlib-bsearch.h
 /usr/include/bits/stdlib-float.h
 /usr/include/bits/struct_mutex.h
 /usr/include/bits/struct_rwlock.h
 /usr/include/bits/struct_stat.h
 /usr/include/bits/syscall.h
 /usr/include/bits/thread-shared-types.h
 /usr/include/bits/time.h
 /usr/include/bits/time64.h
 /usr/include/bits/timesize.h
 /usr/include/bits/timex.h
 /usr/include/bits/types.h
 /usr/include/bits/types/FILE.h
 /usr/include/bits/types/__FILE.h
 /usr/include/bits/types/__fpos64_t.h
 /usr/include/bits/types/__fpos_t.h
 /usr/include/bits/types/__locale_t.h
 /usr/include/bits/types/__mbstate_t.h
 /usr/include/bits/types/__sigset_t.h
 /usr/include/bits/types/__sigval_t.h
 /usr/include/bits/types/clock_t.h
 /usr/include/bits/types/clockid_t.h
 /usr/include/bits/types/cookie_io_functions_t.h
 /usr/include/bits/types/error_t.h
 /usr/include/bits/types/locale_t.h
 /usr/include/bits/types/mbstate_t.h
 /usr/include/bits/types/sig_atomic_t.h
 /usr/include/bits/types/sigevent_t.h
 /usr/include/bits/types/siginfo_t.h
 /usr/include/bits/types/sigset_t.h
 /usr/include/bits/types/sigval_t.h
 /usr/include/bits/types/stack_t.h
 /usr/include/bits/types/struct_FILE.h
 /usr/include/bits/types/struct___jmp_buf_tag.h
 /usr/include/bits/types/struct_iovec.h
 /usr/include/bits/types/struct_itimerspec.h
 /usr/include/bits/types/struct_osockaddr.h
 /usr/include/bits/types/struct_sched_param.h
 /usr/include/bits/types/struct_sigstack.h
 /usr/include/bits/types/struct_timespec.h
 /usr/include/bits/types/struct_timeval.h
 /usr/include/bits/types/struct_tm.h
 /usr/include/bits/types/time_t.h
 /usr/include/bits/types/timer_t.h
 /usr/include/bits/types/wint_t.h
 /usr/include/bits/typesizes.h
 /usr/include/bits/uintn-identity.h
 /usr/include/bits/uio-ext.h
 /usr/include/bits/uio_lim.h
 /usr/include/bits/unistd_ext.h
 /usr/include/bits/waitflags.h
 /usr/include/bits/waitstatus.h
 /usr/include/bits/wchar.h
 /usr/include/bits/wctype-wchar.h
 /usr/include/bits/wordsize.h
 /usr/include/bits/xopen_lim.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cxxabi_tweaks.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/opt_random.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h
 /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h
 /usr/include/c++/14.2.1/algorithm
 /usr/include/c++/14.2.1/array
 /usr/include/c++/14.2.1/atomic
 /usr/include/c++/14.2.1/backward/auto_ptr.h
 /usr/include/c++/14.2.1/backward/binders.h
 /usr/include/c++/14.2.1/bit
 /usr/include/c++/14.2.1/bits/algorithmfwd.h
 /usr/include/c++/14.2.1/bits/align.h
 /usr/include/c++/14.2.1/bits/alloc_traits.h
 /usr/include/c++/14.2.1/bits/allocated_ptr.h
 /usr/include/c++/14.2.1/bits/allocator.h
 /usr/include/c++/14.2.1/bits/atomic_base.h
 /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h
 /usr/include/c++/14.2.1/bits/atomic_timed_wait.h
 /usr/include/c++/14.2.1/bits/atomic_wait.h
 /usr/include/c++/14.2.1/bits/basic_ios.h
 /usr/include/c++/14.2.1/bits/basic_ios.tcc
 /usr/include/c++/14.2.1/bits/basic_string.h
 /usr/include/c++/14.2.1/bits/basic_string.tcc
 /usr/include/c++/14.2.1/bits/char_traits.h
 /usr/include/c++/14.2.1/bits/charconv.h
 /usr/include/c++/14.2.1/bits/chrono.h
 /usr/include/c++/14.2.1/bits/chrono_io.h
 /usr/include/c++/14.2.1/bits/codecvt.h
 /usr/include/c++/14.2.1/bits/concept_check.h
 /usr/include/c++/14.2.1/bits/cpp_type_traits.h
 /usr/include/c++/14.2.1/bits/cxxabi_forced.h
 /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h
 /usr/include/c++/14.2.1/bits/deque.tcc
 /usr/include/c++/14.2.1/bits/enable_special_members.h
 /usr/include/c++/14.2.1/bits/erase_if.h
 /usr/include/c++/14.2.1/bits/exception.h
 /usr/include/c++/14.2.1/bits/exception_defines.h
 /usr/include/c++/14.2.1/bits/exception_ptr.h
 /usr/include/c++/14.2.1/bits/functexcept.h
 /usr/include/c++/14.2.1/bits/functional_hash.h
 /usr/include/c++/14.2.1/bits/hash_bytes.h
 /usr/include/c++/14.2.1/bits/hashtable.h
 /usr/include/c++/14.2.1/bits/hashtable_policy.h
 /usr/include/c++/14.2.1/bits/invoke.h
 /usr/include/c++/14.2.1/bits/ios_base.h
 /usr/include/c++/14.2.1/bits/istream.tcc
 /usr/include/c++/14.2.1/bits/iterator_concepts.h
 /usr/include/c++/14.2.1/bits/list.tcc
 /usr/include/c++/14.2.1/bits/locale_classes.h
 /usr/include/c++/14.2.1/bits/locale_classes.tcc
 /usr/include/c++/14.2.1/bits/locale_conv.h
 /usr/include/c++/14.2.1/bits/locale_facets.h
 /usr/include/c++/14.2.1/bits/locale_facets.tcc
 /usr/include/c++/14.2.1/bits/locale_facets_nonio.h
 /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc
 /usr/include/c++/14.2.1/bits/localefwd.h
 /usr/include/c++/14.2.1/bits/max_size_type.h
 /usr/include/c++/14.2.1/bits/memory_resource.h
 /usr/include/c++/14.2.1/bits/memoryfwd.h
 /usr/include/c++/14.2.1/bits/move.h
 /usr/include/c++/14.2.1/bits/nested_exception.h
 /usr/include/c++/14.2.1/bits/new_allocator.h
 /usr/include/c++/14.2.1/bits/node_handle.h
 /usr/include/c++/14.2.1/bits/ostream.tcc
 /usr/include/c++/14.2.1/bits/ostream_insert.h
 /usr/include/c++/14.2.1/bits/parse_numbers.h
 /usr/include/c++/14.2.1/bits/postypes.h
 /usr/include/c++/14.2.1/bits/predefined_ops.h
 /usr/include/c++/14.2.1/bits/ptr_traits.h
 /usr/include/c++/14.2.1/bits/quoted_string.h
 /usr/include/c++/14.2.1/bits/random.h
 /usr/include/c++/14.2.1/bits/random.tcc
 /usr/include/c++/14.2.1/bits/range_access.h
 /usr/include/c++/14.2.1/bits/ranges_algo.h
 /usr/include/c++/14.2.1/bits/ranges_algobase.h
 /usr/include/c++/14.2.1/bits/ranges_base.h
 /usr/include/c++/14.2.1/bits/ranges_cmp.h
 /usr/include/c++/14.2.1/bits/ranges_uninitialized.h
 /usr/include/c++/14.2.1/bits/ranges_util.h
 /usr/include/c++/14.2.1/bits/refwrap.h
 /usr/include/c++/14.2.1/bits/requires_hosted.h
 /usr/include/c++/14.2.1/bits/semaphore_base.h
 /usr/include/c++/14.2.1/bits/shared_ptr.h
 /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h
 /usr/include/c++/14.2.1/bits/shared_ptr_base.h
 /usr/include/c++/14.2.1/bits/specfun.h
 /usr/include/c++/14.2.1/bits/sstream.tcc
 /usr/include/c++/14.2.1/bits/std_abs.h
 /usr/include/c++/14.2.1/bits/std_function.h
 /usr/include/c++/14.2.1/bits/std_mutex.h
 /usr/include/c++/14.2.1/bits/std_thread.h
 /usr/include/c++/14.2.1/bits/stl_algo.h
 /usr/include/c++/14.2.1/bits/stl_algobase.h
 /usr/include/c++/14.2.1/bits/stl_bvector.h
 /usr/include/c++/14.2.1/bits/stl_construct.h
 /usr/include/c++/14.2.1/bits/stl_deque.h
 /usr/include/c++/14.2.1/bits/stl_function.h
 /usr/include/c++/14.2.1/bits/stl_heap.h
 /usr/include/c++/14.2.1/bits/stl_iterator.h
 /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h
 /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h
 /usr/include/c++/14.2.1/bits/stl_list.h
 /usr/include/c++/14.2.1/bits/stl_map.h
 /usr/include/c++/14.2.1/bits/stl_multimap.h
 /usr/include/c++/14.2.1/bits/stl_multiset.h
 /usr/include/c++/14.2.1/bits/stl_numeric.h
 /usr/include/c++/14.2.1/bits/stl_pair.h
 /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h
 /usr/include/c++/14.2.1/bits/stl_relops.h
 /usr/include/c++/14.2.1/bits/stl_set.h
 /usr/include/c++/14.2.1/bits/stl_tempbuf.h
 /usr/include/c++/14.2.1/bits/stl_tree.h
 /usr/include/c++/14.2.1/bits/stl_uninitialized.h
 /usr/include/c++/14.2.1/bits/stl_vector.h
 /usr/include/c++/14.2.1/bits/stream_iterator.h
 /usr/include/c++/14.2.1/bits/streambuf.tcc
 /usr/include/c++/14.2.1/bits/streambuf_iterator.h
 /usr/include/c++/14.2.1/bits/string_view.tcc
 /usr/include/c++/14.2.1/bits/stringfwd.h
 /usr/include/c++/14.2.1/bits/this_thread_sleep.h
 /usr/include/c++/14.2.1/bits/unicode-data.h
 /usr/include/c++/14.2.1/bits/unicode.h
 /usr/include/c++/14.2.1/bits/uniform_int_dist.h
 /usr/include/c++/14.2.1/bits/unique_lock.h
 /usr/include/c++/14.2.1/bits/unique_ptr.h
 /usr/include/c++/14.2.1/bits/unordered_map.h
 /usr/include/c++/14.2.1/bits/unordered_set.h
 /usr/include/c++/14.2.1/bits/uses_allocator.h
 /usr/include/c++/14.2.1/bits/uses_allocator_args.h
 /usr/include/c++/14.2.1/bits/utility.h
 /usr/include/c++/14.2.1/bits/vector.tcc
 /usr/include/c++/14.2.1/bits/version.h
 /usr/include/c++/14.2.1/cassert
 /usr/include/c++/14.2.1/cctype
 /usr/include/c++/14.2.1/cerrno
 /usr/include/c++/14.2.1/charconv
 /usr/include/c++/14.2.1/chrono
 /usr/include/c++/14.2.1/climits
 /usr/include/c++/14.2.1/clocale
 /usr/include/c++/14.2.1/cmath
 /usr/include/c++/14.2.1/compare
 /usr/include/c++/14.2.1/concepts
 /usr/include/c++/14.2.1/condition_variable
 /usr/include/c++/14.2.1/cstddef
 /usr/include/c++/14.2.1/cstdint
 /usr/include/c++/14.2.1/cstdio
 /usr/include/c++/14.2.1/cstdlib
 /usr/include/c++/14.2.1/cstring
 /usr/include/c++/14.2.1/ctime
 /usr/include/c++/14.2.1/cwchar
 /usr/include/c++/14.2.1/cwctype
 /usr/include/c++/14.2.1/cxxabi.h
 /usr/include/c++/14.2.1/debug/assertions.h
 /usr/include/c++/14.2.1/debug/debug.h
 /usr/include/c++/14.2.1/deque
 /usr/include/c++/14.2.1/exception
 /usr/include/c++/14.2.1/ext/aligned_buffer.h
 /usr/include/c++/14.2.1/ext/alloc_traits.h
 /usr/include/c++/14.2.1/ext/atomicity.h
 /usr/include/c++/14.2.1/ext/concurrence.h
 /usr/include/c++/14.2.1/ext/numeric_traits.h
 /usr/include/c++/14.2.1/ext/string_conversions.h
 /usr/include/c++/14.2.1/ext/type_traits.h
 /usr/include/c++/14.2.1/fenv.h
 /usr/include/c++/14.2.1/format
 /usr/include/c++/14.2.1/functional
 /usr/include/c++/14.2.1/initializer_list
 /usr/include/c++/14.2.1/iomanip
 /usr/include/c++/14.2.1/ios
 /usr/include/c++/14.2.1/iosfwd
 /usr/include/c++/14.2.1/iostream
 /usr/include/c++/14.2.1/istream
 /usr/include/c++/14.2.1/iterator
 /usr/include/c++/14.2.1/limits
 /usr/include/c++/14.2.1/list
 /usr/include/c++/14.2.1/locale
 /usr/include/c++/14.2.1/map
 /usr/include/c++/14.2.1/memory
 /usr/include/c++/14.2.1/mutex
 /usr/include/c++/14.2.1/new
 /usr/include/c++/14.2.1/numbers
 /usr/include/c++/14.2.1/numeric
 /usr/include/c++/14.2.1/optional
 /usr/include/c++/14.2.1/ostream
 /usr/include/c++/14.2.1/pstl/execution_defs.h
 /usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h
 /usr/include/c++/14.2.1/pstl/glue_memory_defs.h
 /usr/include/c++/14.2.1/pstl/glue_numeric_defs.h
 /usr/include/c++/14.2.1/pstl/pstl_config.h
 /usr/include/c++/14.2.1/random
 /usr/include/c++/14.2.1/ratio
 /usr/include/c++/14.2.1/semaphore
 /usr/include/c++/14.2.1/set
 /usr/include/c++/14.2.1/span
 /usr/include/c++/14.2.1/sstream
 /usr/include/c++/14.2.1/stdexcept
 /usr/include/c++/14.2.1/stdlib.h
 /usr/include/c++/14.2.1/stop_token
 /usr/include/c++/14.2.1/streambuf
 /usr/include/c++/14.2.1/string
 /usr/include/c++/14.2.1/string_view
 /usr/include/c++/14.2.1/system_error
 /usr/include/c++/14.2.1/thread
 /usr/include/c++/14.2.1/tr1/bessel_function.tcc
 /usr/include/c++/14.2.1/tr1/beta_function.tcc
 /usr/include/c++/14.2.1/tr1/ell_integral.tcc
 /usr/include/c++/14.2.1/tr1/exp_integral.tcc
 /usr/include/c++/14.2.1/tr1/gamma.tcc
 /usr/include/c++/14.2.1/tr1/hypergeometric.tcc
 /usr/include/c++/14.2.1/tr1/legendre_function.tcc
 /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc
 /usr/include/c++/14.2.1/tr1/poly_hermite.tcc
 /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc
 /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc
 /usr/include/c++/14.2.1/tr1/special_function_util.h
 /usr/include/c++/14.2.1/tuple
 /usr/include/c++/14.2.1/type_traits
 /usr/include/c++/14.2.1/typeinfo
 /usr/include/c++/14.2.1/unordered_map
 /usr/include/c++/14.2.1/unordered_set
 /usr/include/c++/14.2.1/utility
 /usr/include/c++/14.2.1/variant
 /usr/include/c++/14.2.1/vector
 /usr/include/ctype.h
 /usr/include/endian.h
 /usr/include/errno.h
 /usr/include/fcntl.h
 /usr/include/features-time64.h
 /usr/include/features.h
 /usr/include/fenv.h
 /usr/include/gnu/stubs-lp64.h
 /usr/include/gnu/stubs.h
 /usr/include/libintl.h
 /usr/include/limits.h
 /usr/include/linux/close_range.h
 /usr/include/linux/errno.h
 /usr/include/linux/falloc.h
 /usr/include/linux/input-event-codes.h
 /usr/include/linux/input.h
 /usr/include/linux/ioctl.h
 /usr/include/linux/limits.h
 /usr/include/linux/posix_types.h
 /usr/include/linux/sched/types.h
 /usr/include/linux/stddef.h
 /usr/include/linux/types.h
 /usr/include/linux/uinput.h
 /usr/include/locale.h
 /usr/include/math.h
 /usr/include/mysql/ma_list.h
 /usr/include/mysql/mariadb_com.h
 /usr/include/mysql/mariadb_ctype.h
 /usr/include/mysql/mariadb_stmt.h
 /usr/include/mysql/mariadb_version.h
 /usr/include/mysql/mysql.h
 /usr/include/net/if.h
 /usr/include/netdb.h
 /usr/include/netinet/in.h
 /usr/include/netinet/tcp.h
 /usr/include/openssl/bio.h
 /usr/include/openssl/bioerr.h
 /usr/include/openssl/configuration.h
 /usr/include/openssl/core.h
 /usr/include/openssl/crypto.h
 /usr/include/openssl/cryptoerr.h
 /usr/include/openssl/cryptoerr_legacy.h
 /usr/include/openssl/e_os2.h
 /usr/include/openssl/err.h
 /usr/include/openssl/lhash.h
 /usr/include/openssl/macros.h
 /usr/include/openssl/opensslconf.h
 /usr/include/openssl/opensslv.h
 /usr/include/openssl/safestack.h
 /usr/include/openssl/stack.h
 /usr/include/openssl/symhacks.h
 /usr/include/openssl/types.h
 /usr/include/pthread.h
 /usr/include/rpc/netdb.h
 /usr/include/sched.h
 /usr/include/semaphore.h
 /usr/include/setjmp.h
 /usr/include/signal.h
 /usr/include/stdc-predef.h
 /usr/include/stdint.h
 /usr/include/stdio.h
 /usr/include/stdlib.h
 /usr/include/string.h
 /usr/include/strings.h
 /usr/include/sys/cdefs.h
 /usr/include/sys/ioctl.h
 /usr/include/sys/procfs.h
 /usr/include/sys/select.h
 /usr/include/sys/single_threaded.h
 /usr/include/sys/socket.h
 /usr/include/sys/syscall.h
 /usr/include/sys/time.h
 /usr/include/sys/ttydefaults.h
 /usr/include/sys/types.h
 /usr/include/sys/ucontext.h
 /usr/include/sys/uio.h
 /usr/include/sys/un.h
 /usr/include/sys/user.h
 /usr/include/syscall.h
 /usr/include/time.h
 /usr/include/unistd.h
 /usr/include/wchar.h
 /usr/include/wctype.h
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

bin/BL
 /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a
 /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so
 /usr/lib/Scrt1.o
 /usr/lib/crti.o
 /usr/lib/crtn.o
 /usr/lib/libPocoData.so
 /usr/lib/libPocoDataMySQL.so
 /usr/lib/libPocoFoundation.so
 /usr/lib/libPocoJSON.so
 /usr/lib/libPocoNet.so
 /usr/lib/libPocoUtil.so
 /usr/lib/libPocoXML.so
 /usr/lib/libPocoZip.so
 /usr/lib/libc.so
 /usr/lib/libdl.a
 /usr/lib/libgcc_s.so
 /usr/lib/libgcc_s.so.1
 /usr/lib/libm.so
 /usr/lib/libpthread.a
 /usr/lib/librt.a
 /usr/lib/libstdc++.so
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o
 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/libgcc.a
 /usr/lib/ld-linux-aarch64.so.1
 /usr/lib/libEGL.so.1
 /usr/lib/libGL.so.1
 /usr/lib/libGLX.so.0
 /usr/lib/libGLdispatch.so.0
 /usr/lib/libIex-3_3.so.32
 /usr/lib/libIlmThread-3_3.so.32
 /usr/lib/libImath-3_1.so.29
 /usr/lib/libOpenCL.so.1
 /usr/lib/libOpenEXR-3_3.so.32
 /usr/lib/libOpenEXRCore-3_3.so.32
 /usr/lib/libOpenGL.so.0
 /usr/lib/libPocoCrypto.so.111
 /usr/lib/libPocoFoundation.so.111
 /usr/lib/libPocoJSON.so.111
 /usr/lib/libPocoNet.so.111
 /usr/lib/libPocoNetSSL.so.111
 /usr/lib/libPocoUtil.so.111
 /usr/lib/libPocoXML.so.111
 /usr/lib/libQt6Core.so.6
 /usr/lib/libQt6DBus.so.6
 /usr/lib/libQt6Gui.so.6
 /usr/lib/libQt6OpenGL.so.6
 /usr/lib/libQt6OpenGLWidgets.so.6
 /usr/lib/libQt6Test.so.6
 /usr/lib/libQt6Widgets.so.6
 /usr/lib/libX11-xcb.so.1
 /usr/lib/libX11.so.6
 /usr/lib/libXau.so.6
 /usr/lib/libXdmcp.so.6
 /usr/lib/libXext.so.6
 /usr/lib/libXfixes.so.3
 /usr/lib/libXrender.so.1
 /usr/lib/libatomic.so
 /usr/lib/libavcodec.so.61
 /usr/lib/libavformat.so.61
 /usr/lib/libavutil.so.59
 /usr/lib/libb2.so.1
 /usr/lib/libblas.so.3
 /usr/lib/libblkid.so.1
 /usr/lib/libbluray.so.2
 /usr/lib/libbrotlicommon.so.1
 /usr/lib/libbrotlidec.so.1
 /usr/lib/libbrotlienc.so.1
 /usr/lib/libbz2.so.1.0
 /usr/lib/libc.so.6
 /usr/lib/libc_nonshared.a
 /usr/lib/libcairo.so.2
 /usr/lib/libcap.so.2
 /usr/lib/libcblas.so.3
 /usr/lib/libcrypto.so
 /usr/lib/libdatrie.so.1
 /usr/lib/libdav1d.so.7
 /usr/lib/libdbus-1.so.3
 /usr/lib/libdeflate.so.0
 /usr/lib/libdl.so.2
 /usr/lib/libdouble-conversion.so.3
 /usr/lib/libdrm.so.2
 /usr/lib/libdvdnav.so.4
 /usr/lib/libdvdread.so.8
 /usr/lib/libdw.so.1
 /usr/lib/libelf.so.1
 /usr/lib/libexpat.so.1
 /usr/lib/libffi.so.8
 /usr/lib/libfontconfig.so.1
 /usr/lib/libfreetype.so.6
 /usr/lib/libfribidi.so.0
 /usr/lib/libgdk_pixbuf-2.0.so.0
 /usr/lib/libgfortran.so.5
 /usr/lib/libgio-2.0.so.0
 /usr/lib/libglib-2.0.so.0
 /usr/lib/libgmodule-2.0.so.0
 /usr/lib/libgmp.so.10
 /usr/lib/libgnutls.so.30
 /usr/lib/libgobject-2.0.so.0
 /usr/lib/libgomp.so.1
 /usr/lib/libgraphite2.so.3
 /usr/lib/libgsm.so.1
 /usr/lib/libgstapp-1.0.so.0
 /usr/lib/libgstaudio-1.0.so.0
 /usr/lib/libgstbase-1.0.so.0
 /usr/lib/libgstpbutils-1.0.so.0
 /usr/lib/libgstreamer-1.0.so.0
 /usr/lib/libgstriff-1.0.so.0
 /usr/lib/libgsttag-1.0.so.0
 /usr/lib/libgstvideo-1.0.so.0
 /usr/lib/libharfbuzz.so.0
 /usr/lib/libhogweed.so.6
 /usr/lib/libhwy.so.1
 /usr/lib/libicudata.so.76
 /usr/lib/libicui18n.so.76
 /usr/lib/libicuuc.so.76
 /usr/lib/libidn2.so.0
 /usr/lib/libjbig.so.2.1
 /usr/lib/libjpeg.so.8
 /usr/lib/libjxl.so.0.11
 /usr/lib/libjxl_cms.so.0.11
 /usr/lib/libjxl_threads.so.0.11
 /usr/lib/liblapack.so.3
 /usr/lib/libleancrypto.so.1
 /usr/lib/liblzma.so.5
 /usr/lib/libm.so.6
 /usr/lib/libmd4c.so.0
 /usr/lib/libmodplug.so.1
 /usr/lib/libmount.so.1
 /usr/lib/libmp3lame.so.0
 /usr/lib/libmpg123.so.0
 /usr/lib/libmvec.so.1
 /usr/lib/libmysqlclient.so
 /usr/lib/libnettle.so.8
 /usr/lib/libogg.so.0
 /usr/lib/libopencore-amrnb.so.0
 /usr/lib/libopencore-amrwb.so.0
 /usr/lib/libopencv_core.so.4.11.0
 /usr/lib/libopencv_highgui.so.4.11.0
 /usr/lib/libopencv_imgcodecs.so.4.11.0
 /usr/lib/libopencv_imgproc.so.4.11.0
 /usr/lib/libopencv_videoio.so.4.11.0
 /usr/lib/libopenjp2.so.7
 /usr/lib/libopenmpt.so.0
 /usr/lib/libopus.so.0
 /usr/lib/liborc-0.4.so.0
 /usr/lib/libp11-kit.so.0
 /usr/lib/libpango-1.0.so.0
 /usr/lib/libpangocairo-1.0.so.0
 /usr/lib/libpangoft2-1.0.so.0
 /usr/lib/libpcre2-16.so.0
 /usr/lib/libpcre2-8.so.0
 /usr/lib/libpgm-5.3.so.0
 /usr/lib/libpixman-1.so.0
 /usr/lib/libpng16.so.16
 /usr/lib/libpthread.so.0
 /usr/lib/librsvg-2.so.2
 /usr/lib/libsharpyuv.so.0
 /usr/lib/libsnappy.so.1
 /usr/lib/libsodium.so.26
 /usr/lib/libsoxr.so.0
 /usr/lib/libspeex.so.1
 /usr/lib/libsrt.so.1.5
 /usr/lib/libssh.so.4
 /usr/lib/libssl.so
 /usr/lib/libswresample.so.5
 /usr/lib/libswscale.so.8
 /usr/lib/libsystemd.so.0
 /usr/lib/libtasn1.so.6
 /usr/lib/libtbb.so.12
 /usr/lib/libthai.so.0
 /usr/lib/libtheoradec.so.2
 /usr/lib/libtheoraenc.so.2
 /usr/lib/libtiff.so.6
 /usr/lib/libunistring.so.5
 /usr/lib/libunwind.so.8
 /usr/lib/libva-drm.so.2
 /usr/lib/libva-x11.so.2
 /usr/lib/libva.so.2
 /usr/lib/libvdpau.so.1
 /usr/lib/libvorbis.so.0
 /usr/lib/libvorbisenc.so.2
 /usr/lib/libvorbisfile.so.3
 /usr/lib/libvpx.so.9
 /usr/lib/libwebp.so.7
 /usr/lib/libwebpdemux.so.2
 /usr/lib/libwebpmux.so.3
 /usr/lib/libx264.so.164
 /usr/lib/libx265.so.212
 /usr/lib/libxcb-dri3.so.0
 /usr/lib/libxcb-render.so.0
 /usr/lib/libxcb-shm.so.0
 /usr/lib/libxcb.so.1
 /usr/lib/libxkbcommon.so.0
 /usr/lib/libxml2.so.2
 /usr/lib/libxvidcore.so.4
 /usr/lib/libz.so.1
 /usr/lib/libzmq.so.5
 /usr/lib/libzstd.so.1
 /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/BL.dir/main.cpp.o
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libalgorithm_lib.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libbase.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libblkmapi.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libblweb_backend.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libcore.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libcserialport.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libfmt.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/liblkmapi.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libspdlog.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libthread_manager.a
 /home/<USER>/mywork/poco_serverdemo/build_new/bin/lib/libutils.a

