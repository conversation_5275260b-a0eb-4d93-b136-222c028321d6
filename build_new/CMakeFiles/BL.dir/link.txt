/usr/bin/c++  -O3 -O3 -DNDEBUG -Wl,--dependency-file=CMakeFiles/BL.dir/link.d CMakeFiles/BL.dir/main.cpp.o -o bin/BL  -Wl,-rpath,/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux: bin/lib/libutils.a bin/lib/libthread_manager.a bin/lib/libblweb_backend.a bin/lib/libcore.a bin/lib/libalgorithm_lib.a bin/lib/liblkmapi.a bin/lib/libblkmapi.a bin/lib/libspdlog.a /usr/lib/libatomic.so bin/lib/libbase.a /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a /usr/lib/libopencv_highgui.so.4.11.0 /usr/lib/libopencv_videoio.so.4.11.0 /usr/lib/libopencv_imgcodecs.so.4.11.0 /usr/lib/libopencv_imgproc.so.4.11.0 /usr/lib/libopencv_core.so.4.11.0 -lpthread bin/lib/libcserialport.a bin/lib/libalgorithm_lib.a bin/lib/libutils.a /usr/lib/libPocoNetSSL.so.111 /usr/lib/libPocoNet.so.111 /usr/lib/libPocoUtil.so.111 /usr/lib/libPocoJSON.so.111 /usr/lib/libPocoXML.so.111 /usr/lib/libPocoCrypto.so.111 /usr/lib/libPocoFoundation.so.111 -lpthread -ldl -lrt /usr/lib/libssl.so /usr/lib/libcrypto.so bin/lib/libspdlog.a bin/lib/libfmt.a -lPocoFoundation -lPocoNet -lPocoJSON -lPocoUtil -lPocoXML -lPocoZip /usr/lib/libatomic.so -lPocoData -lPocoDataMySQL /usr/lib/libmysqlclient.so
