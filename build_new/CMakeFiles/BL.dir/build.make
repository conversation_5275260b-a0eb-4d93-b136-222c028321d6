# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include CMakeFiles/BL.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/BL.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/BL.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/BL.dir/flags.make

CMakeFiles/BL.dir/codegen:
.PHONY : CMakeFiles/BL.dir/codegen

CMakeFiles/BL.dir/main.cpp.o: CMakeFiles/BL.dir/flags.make
CMakeFiles/BL.dir/main.cpp.o: /home/<USER>/mywork/poco_serverdemo/main.cpp
CMakeFiles/BL.dir/main.cpp.o: CMakeFiles/BL.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/BL.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/BL.dir/main.cpp.o -MF CMakeFiles/BL.dir/main.cpp.o.d -o CMakeFiles/BL.dir/main.cpp.o -c /home/<USER>/mywork/poco_serverdemo/main.cpp

CMakeFiles/BL.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/BL.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/main.cpp > CMakeFiles/BL.dir/main.cpp.i

CMakeFiles/BL.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/BL.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/main.cpp -o CMakeFiles/BL.dir/main.cpp.s

# Object files for target BL
BL_OBJECTS = \
"CMakeFiles/BL.dir/main.cpp.o"

# External object files for target BL
BL_EXTERNAL_OBJECTS =

bin/BL: CMakeFiles/BL.dir/main.cpp.o
bin/BL: CMakeFiles/BL.dir/build.make
bin/BL: CMakeFiles/BL.dir/compiler_depend.ts
bin/BL: bin/lib/libutils.a
bin/BL: bin/lib/libthread_manager.a
bin/BL: bin/lib/libblweb_backend.a
bin/BL: bin/lib/libcore.a
bin/BL: bin/lib/libalgorithm_lib.a
bin/BL: bin/lib/liblkmapi.a
bin/BL: bin/lib/libblkmapi.a
bin/BL: bin/lib/libspdlog.a
bin/BL: /usr/lib/libatomic.so
bin/BL: bin/lib/libbase.a
bin/BL: /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so
bin/BL: /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a
bin/BL: /usr/lib/libopencv_highgui.so.4.11.0
bin/BL: /usr/lib/libopencv_videoio.so.4.11.0
bin/BL: /usr/lib/libopencv_imgcodecs.so.4.11.0
bin/BL: /usr/lib/libopencv_imgproc.so.4.11.0
bin/BL: /usr/lib/libopencv_core.so.4.11.0
bin/BL: bin/lib/libcserialport.a
bin/BL: bin/lib/libalgorithm_lib.a
bin/BL: bin/lib/libutils.a
bin/BL: /usr/lib/libPocoNetSSL.so.111
bin/BL: /usr/lib/libPocoNet.so.111
bin/BL: /usr/lib/libPocoUtil.so.111
bin/BL: /usr/lib/libPocoJSON.so.111
bin/BL: /usr/lib/libPocoXML.so.111
bin/BL: /usr/lib/libPocoCrypto.so.111
bin/BL: /usr/lib/libPocoFoundation.so.111
bin/BL: /usr/lib/libssl.so
bin/BL: /usr/lib/libcrypto.so
bin/BL: bin/lib/libspdlog.a
bin/BL: bin/lib/libfmt.a
bin/BL: /usr/lib/libatomic.so
bin/BL: /usr/lib/libmysqlclient.so
bin/BL: CMakeFiles/BL.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/BL"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/BL.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/BL.dir/build: bin/BL
.PHONY : CMakeFiles/BL.dir/build

CMakeFiles/BL.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/BL.dir/cmake_clean.cmake
.PHONY : CMakeFiles/BL.dir/clean

CMakeFiles/BL.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/BL.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/BL.dir/depend

