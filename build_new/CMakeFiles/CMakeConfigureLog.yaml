
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Linux - 6.1.75-rkr3 - aarch64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/4.0.1/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/4.0.1/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-cfh415"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-cfh415"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-cfh415'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c4a8d/fast
        /usr/bin/make  -f CMakeFiles/cmTC_c4a8d.dir/build.make CMakeFiles/cmTC_c4a8d.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-cfh415'
        Building C object CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -o CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_c4a8d.dir/'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1 -quiet -v /usr/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_c4a8d.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccbmugAW.s
        GNU C17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)
        	compiled by GNU C version 14.2.1 20250207, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        warning: MPFR header version 4.2.1 differs from library version 4.2.2.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include
         /usr/local/include
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed
         /usr/include
        End of search list.
        Compiler executable checksum: f2b56bb48c11a63d8426cff87deef4f7
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_c4a8d.dir/'
         as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o /tmp/ccbmugAW.s
        GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.'
        Linking C executable cmTC_c4a8d
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c4a8d.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c4a8d' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_c4a8d.'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc0lGSjf.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_c4a8d /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        collect2 version 14.2.1 20250207
        /usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc0lGSjf.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_c4a8d /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c4a8d' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_c4a8d.'
        /usr/bin/cc  -v -Wl,-v CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o -o cmTC_c4a8d
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-cfh415'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
          add: [/usr/local/include]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include;/usr/local/include;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-cfh415']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c4a8d/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_c4a8d.dir/build.make CMakeFiles/cmTC_c4a8d.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-cfh415']
        ignore line: [Building C object CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_c4a8d.dir/']
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1 -quiet -v /usr/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_c4a8d.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccbmugAW.s]
        ignore line: [GNU C17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)]
        ignore line: [	compiled by GNU C version 14.2.1 20250207  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [warning: MPFR header version 4.2.1 differs from library version 4.2.2.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: f2b56bb48c11a63d8426cff87deef4f7]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_c4a8d.dir/']
        ignore line: [ as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o /tmp/ccbmugAW.s]
        ignore line: [GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_c4a8d]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c4a8d.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c4a8d' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_c4a8d.']
        link line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc0lGSjf.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_c4a8d /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cc0lGSjf.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [--fix-cortex-a53-835769] ==> ignore
          arg [--fix-cortex-a53-843419] ==> ignore
          arg [-pie] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_c4a8d] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        ignore line: [collect2 version 14.2.1 20250207]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc0lGSjf.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_c4a8d /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_c4a8d.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        linker tool for 'C': /usr/bin/ld
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> [/usr/lib/Scrt1.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> [/usr/lib/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> [/usr/lib/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/usr/lib/Scrt1.o;/usr/lib/crti.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o;/usr/lib/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1;/usr/lib;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils) 2.44
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zXz0iJ"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zXz0iJ"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zXz0iJ'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2281b/fast
        /usr/bin/make  -f CMakeFiles/cmTC_2281b.dir/build.make CMakeFiles/cmTC_2281b.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zXz0iJ'
        Building CXX object CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2281b.dir/'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1plus -quiet -v -D_GNU_SOURCE /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_2281b.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccADg2kN.s
        GNU C++17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)
        	compiled by GNU C version 14.2.1 20250207, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        warning: MPFR header version 4.2.1 differs from library version 4.2.2.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include
         /usr/local/include
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed
         /usr/include
        End of search list.
        Compiler executable checksum: f3b3ed6123a07cfdf28e9e3ba61a9363
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2281b.dir/'
         as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccADg2kN.s
        GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_2281b
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2281b.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_2281b' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_2281b.'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc559Ky5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2281b /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        collect2 version 14.2.1 20250207
        /usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc559Ky5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2281b /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_2281b' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_2281b.'
        /usr/bin/c++  -v -Wl,-v CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_2281b
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zXz0iJ'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
          add: [/usr/local/include]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1] ==> [/usr/include/c++/14.2.1]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu] ==> [/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward] ==> [/usr/include/c++/14.2.1/backward]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/14.2.1;/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu;/usr/include/c++/14.2.1/backward;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include;/usr/local/include;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zXz0iJ']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2281b/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_2281b.dir/build.make CMakeFiles/cmTC_2281b.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zXz0iJ']
        ignore line: [Building CXX object CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2281b.dir/']
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1plus -quiet -v -D_GNU_SOURCE /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_2281b.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccADg2kN.s]
        ignore line: [GNU C++17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)]
        ignore line: [	compiled by GNU C version 14.2.1 20250207  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [warning: MPFR header version 4.2.1 differs from library version 4.2.2.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: f3b3ed6123a07cfdf28e9e3ba61a9363]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2281b.dir/']
        ignore line: [ as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccADg2kN.s]
        ignore line: [GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_2281b]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2281b.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_2281b' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_2281b.']
        link line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc559Ky5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2281b /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cc559Ky5.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [--fix-cortex-a53-835769] ==> ignore
          arg [--fix-cortex-a53-843419] ==> ignore
          arg [-pie] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_2281b] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        ignore line: [collect2 version 14.2.1 20250207]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc559Ky5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2281b /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2281b.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        linker tool for 'CXX': /usr/bin/ld
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> [/usr/lib/Scrt1.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> [/usr/lib/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> [/usr/lib/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib/Scrt1.o;/usr/lib/crti.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o;/usr/lib/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1;/usr/lib;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils) 2.44
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "/usr/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "3rdparty/spdlog/CMakeLists.txt:151 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-irZ3we"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-irZ3we"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-irZ3we'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b517a/fast
        /usr/bin/make  -f CMakeFiles/cmTC_b517a.dir/build.make CMakeFiles/cmTC_b517a.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-irZ3we'
        Building C object CMakeFiles/cmTC_b517a.dir/src.c.o
        /usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_b517a.dir/src.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-irZ3we/src.c
        Linking C executable cmTC_b517a
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b517a.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_b517a.dir/src.c.o -o cmTC_b517a
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-irZ3we'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "3rdparty/spdlog/CMakeLists.txt:250 (check_symbol_exists)"
    checks:
      - "Looking for fwrite_unlocked"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-QqVJI1"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-QqVJI1"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "HAVE_FWRITE_UNLOCKED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-QqVJI1'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_88108/fast
        /usr/bin/make  -f CMakeFiles/cmTC_88108.dir/build.make CMakeFiles/cmTC_88108.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-QqVJI1'
        Building C object CMakeFiles/cmTC_88108.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_88108.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-QqVJI1/CheckSymbolExists.c
        Linking C executable cmTC_88108
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_88108.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_88108.dir/CheckSymbolExists.c.o -o cmTC_88108
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-QqVJI1'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "3rdparty/Catch2/CMake/CatchMiscFunctions.cmake:12 (check_cxx_compiler_flag)"
      - "3rdparty/Catch2/CMake/CatchMiscFunctions.cmake:119 (add_cxx_flag_if_supported_to_targets)"
      - "3rdparty/Catch2/src/CMakeLists.txt:351 (add_build_reproducibility_settings)"
    checks:
      - "Performing Test HAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-HDjo9c"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-HDjo9c"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -O3"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras;/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/CMake"
    buildResult:
      variable: "HAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-HDjo9c'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_830b4/fast
        /usr/bin/make  -f CMakeFiles/cmTC_830b4.dir/build.make CMakeFiles/cmTC_830b4.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-HDjo9c'
        Building CXX object CMakeFiles/cmTC_830b4.dir/src.cxx.o
        /usr/bin/c++ -DHAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__  -O3  -std=c++20   -ffile-prefix-map=/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/= -o CMakeFiles/cmTC_830b4.dir/src.cxx.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-HDjo9c/src.cxx
        Linking CXX executable cmTC_830b4
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_830b4.dir/link.txt --verbose=1
        /usr/bin/c++  -O3  CMakeFiles/cmTC_830b4.dir/src.cxx.o -o cmTC_830b4
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-HDjo9c'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:35 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for clock_gettime"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-G4S4ET"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-G4S4ET"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_CLOCK_GETTIME"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-G4S4ET'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_bb277/fast
        /usr/bin/make  -f CMakeFiles/cmTC_bb277.dir/build.make CMakeFiles/cmTC_bb277.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-G4S4ET'
        Building C object CMakeFiles/cmTC_bb277.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=clock_gettime -o CMakeFiles/cmTC_bb277.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-G4S4ET/CheckFunctionExists.c
        Linking C executable cmTC_bb277
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bb277.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=clock_gettime CMakeFiles/cmTC_bb277.dir/CheckFunctionExists.c.o -o cmTC_bb277
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-G4S4ET'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:36 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pthread_condattr_setclock"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-iTO2ca"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-iTO2ca"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PTHREAD_CONDATTR_SETCLOCK"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-iTO2ca'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2fd66/fast
        /usr/bin/make  -f CMakeFiles/cmTC_2fd66.dir/build.make CMakeFiles/cmTC_2fd66.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-iTO2ca'
        Building C object CMakeFiles/cmTC_2fd66.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_condattr_setclock -o CMakeFiles/cmTC_2fd66.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-iTO2ca/CheckFunctionExists.c
        Linking C executable cmTC_2fd66
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2fd66.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_condattr_setclock CMakeFiles/cmTC_2fd66.dir/CheckFunctionExists.c.o -o cmTC_2fd66
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-iTO2ca'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:37 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pthread_setname_np"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-1gjxX1"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-1gjxX1"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PTHREAD_SETNAME_NP"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-1gjxX1'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_48428/fast
        /usr/bin/make  -f CMakeFiles/cmTC_48428.dir/build.make CMakeFiles/cmTC_48428.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-1gjxX1'
        Building C object CMakeFiles/cmTC_48428.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_setname_np -o CMakeFiles/cmTC_48428.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-1gjxX1/CheckFunctionExists.c
        Linking C executable cmTC_48428
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_48428.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_setname_np CMakeFiles/cmTC_48428.dir/CheckFunctionExists.c.o -o cmTC_48428
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-1gjxX1'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:38 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pthread_threadid_np"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-grXU8X"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-grXU8X"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PTHREAD_THREADID_NP"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-grXU8X'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f9384/fast
        /usr/bin/make  -f CMakeFiles/cmTC_f9384.dir/build.make CMakeFiles/cmTC_f9384.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-grXU8X'
        Building C object CMakeFiles/cmTC_f9384.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_threadid_np -o CMakeFiles/cmTC_f9384.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-grXU8X/CheckFunctionExists.c
        Linking C executable cmTC_f9384
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f9384.dir/link.txt --verbose=1
        /usr/bin/ld: CMakeFiles/cmTC_f9384.dir/CheckFunctionExists.c.o: in function `main':
        CheckFunctionExists.c:(.text+0x10): undefined reference to `pthread_threadid_np'
        collect2: error: ld returned 1 exit status
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_threadid_np CMakeFiles/cmTC_f9384.dir/CheckFunctionExists.c.o -o cmTC_f9384
        make[1]: *** [CMakeFiles/cmTC_f9384.dir/build.make:102: cmTC_f9384] Error 1
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-grXU8X'
        make: *** [Makefile:134: cmTC_f9384/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:39 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for eventfd"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zV82lm"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zV82lm"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_EVENTFD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zV82lm'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_54bb2/fast
        /usr/bin/make  -f CMakeFiles/cmTC_54bb2.dir/build.make CMakeFiles/cmTC_54bb2.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zV82lm'
        Building C object CMakeFiles/cmTC_54bb2.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=eventfd -o CMakeFiles/cmTC_54bb2.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zV82lm/CheckFunctionExists.c
        Linking C executable cmTC_54bb2
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_54bb2.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=eventfd CMakeFiles/cmTC_54bb2.dir/CheckFunctionExists.c.o -o cmTC_54bb2
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zV82lm'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:40 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pipe2"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MIPVKh"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MIPVKh"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PIPE2"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MIPVKh'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_d3ce0/fast
        /usr/bin/make  -f CMakeFiles/cmTC_d3ce0.dir/build.make CMakeFiles/cmTC_d3ce0.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MIPVKh'
        Building C object CMakeFiles/cmTC_d3ce0.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pipe2 -o CMakeFiles/cmTC_d3ce0.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MIPVKh/CheckFunctionExists.c
        Linking C executable cmTC_d3ce0
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d3ce0.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pipe2 CMakeFiles/cmTC_d3ce0.dir/CheckFunctionExists.c.o -o cmTC_d3ce0
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MIPVKh'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:41 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for syslog"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-vZ58oc"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-vZ58oc"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_SYSLOG"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-vZ58oc'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_68642/fast
        /usr/bin/make  -f CMakeFiles/cmTC_68642.dir/build.make CMakeFiles/cmTC_68642.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-vZ58oc'
        Building C object CMakeFiles/cmTC_68642.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=syslog -o CMakeFiles/cmTC_68642.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-vZ58oc/CheckFunctionExists.c
        Linking C executable cmTC_68642
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_68642.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=syslog CMakeFiles/cmTC_68642.dir/CheckFunctionExists.c.o -o cmTC_68642
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-vZ58oc'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFiles.cmake:141 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:43 (check_include_files)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for include file asm/types.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-pQSleD"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-pQSleD"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_ASM_TYPES_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-pQSleD'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_98959/fast
        /usr/bin/make  -f CMakeFiles/cmTC_98959.dir/build.make CMakeFiles/cmTC_98959.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-pQSleD'
        Building C object CMakeFiles/cmTC_98959.dir/HAVE_ASM_TYPES_H.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_98959.dir/HAVE_ASM_TYPES_H.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-pQSleD/HAVE_ASM_TYPES_H.c
        Linking C executable cmTC_98959
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_98959.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_98959.dir/HAVE_ASM_TYPES_H.c.o -o cmTC_98959
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-pQSleD'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFiles.cmake:141 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:45 (check_include_files)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for include file string.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MMN5Wj"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MMN5Wj"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_STRING_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MMN5Wj'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_5ec28/fast
        /usr/bin/make  -f CMakeFiles/cmTC_5ec28.dir/build.make CMakeFiles/cmTC_5ec28.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MMN5Wj'
        Building C object CMakeFiles/cmTC_5ec28.dir/HAVE_STRING_H.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_5ec28.dir/HAVE_STRING_H.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MMN5Wj/HAVE_STRING_H.c
        Linking C executable cmTC_5ec28
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5ec28.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_5ec28.dir/HAVE_STRING_H.c.o -o cmTC_5ec28
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-MMN5Wj'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFiles.cmake:141 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:46 (check_include_files)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for include file sys/time.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-bPpfvJ"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-bPpfvJ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_SYS_TIME_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-bPpfvJ'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_0fa4a/fast
        /usr/bin/make  -f CMakeFiles/cmTC_0fa4a.dir/build.make CMakeFiles/cmTC_0fa4a.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-bPpfvJ'
        Building C object CMakeFiles/cmTC_0fa4a.dir/HAVE_SYS_TIME_H.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_0fa4a.dir/HAVE_SYS_TIME_H.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-bPpfvJ/HAVE_SYS_TIME_H.c
        Linking C executable cmTC_0fa4a
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0fa4a.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_0fa4a.dir/HAVE_SYS_TIME_H.c.o -o cmTC_0fa4a
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-bPpfvJ'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:48 (check_symbol_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for timerfd_create"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zPYkfZ"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zPYkfZ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_TIMERFD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zPYkfZ'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_d5e91/fast
        /usr/bin/make  -f CMakeFiles/cmTC_d5e91.dir/build.make CMakeFiles/cmTC_d5e91.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zPYkfZ'
        Building C object CMakeFiles/cmTC_d5e91.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_d5e91.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zPYkfZ/CheckSymbolExists.c
        Linking C executable cmTC_d5e91
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d5e91.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_d5e91.dir/CheckSymbolExists.c.o -o cmTC_d5e91
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-zPYkfZ'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:49 (check_symbol_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for nfds_t"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_NFDS_T"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_d53f5/fast
        /usr/bin/make  -f CMakeFiles/cmTC_d53f5.dir/build.make CMakeFiles/cmTC_d53f5.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT'
        Building C object CMakeFiles/cmTC_d53f5.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_d53f5.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT/CheckSymbolExists.c
        /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT/CheckSymbolExists.c: In function ‘main’:
        /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT/CheckSymbolExists.c:8:19: error: expected expression before ‘nfds_t’
            8 |   return ((int*)(&nfds_t))[argc];
              |                   ^~~~~~
        make[1]: *** [CMakeFiles/cmTC_d53f5.dir/build.make:81: CMakeFiles/cmTC_d53f5.dir/CheckSymbolExists.c.o] Error 1
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-PgHVOT'
        make: *** [Makefile:134: cmTC_d53f5/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:51 (check_struct_has_member)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Performing Test HAVE_STRUCT_TIMESPEC"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-0tE16F"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-0tE16F"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_STRUCT_TIMESPEC"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-0tE16F'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_941fa/fast
        /usr/bin/make  -f CMakeFiles/cmTC_941fa.dir/build.make CMakeFiles/cmTC_941fa.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-0tE16F'
        Building C object CMakeFiles/cmTC_941fa.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_TIMESPEC   -o CMakeFiles/cmTC_941fa.dir/src.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-0tE16F/src.c
        Linking C executable cmTC_941fa
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_941fa.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_941fa.dir/src.c.o -o cmTC_941fa
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-0tE16F'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:74 (check_c_compiler_flag)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Performing Test HAVE_VISIBILITY"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-JT72pS"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-JT72pS"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_VISIBILITY"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-JT72pS'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_13122/fast
        /usr/bin/make  -f CMakeFiles/cmTC_13122.dir/build.make CMakeFiles/cmTC_13122.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-JT72pS'
        Building C object CMakeFiles/cmTC_13122.dir/src.c.o
        /usr/bin/cc -DHAVE_VISIBILITY  -fvisibility=hidden -o CMakeFiles/cmTC_13122.dir/src.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-JT72pS/src.c
        Linking C executable cmTC_13122
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_13122.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_13122.dir/src.c.o -o cmTC_13122
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-JT72pS'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "backend/CMakeLists.txt:17 (CHECK_CXX_COMPILER_FLAG)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_MARCH_NATIVE"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-5C439j"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-5C439j"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -O3"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_MARCH_NATIVE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-5C439j'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_51051/fast
        /usr/bin/make  -f CMakeFiles/cmTC_51051.dir/build.make CMakeFiles/cmTC_51051.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-5C439j'
        Building CXX object CMakeFiles/cmTC_51051.dir/src.cxx.o
        /usr/bin/c++ -DCOMPILER_SUPPORTS_MARCH_NATIVE  -O3  -std=c++17   -march=native -o CMakeFiles/cmTC_51051.dir/src.cxx.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-5C439j/src.cxx
        Linking CXX executable cmTC_51051
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_51051.dir/link.txt --verbose=1
        /usr/bin/c++  -O3  CMakeFiles/cmTC_51051.dir/src.cxx.o -o cmTC_51051
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-5C439j'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "backend/CMakeLists.txt:119 (CHECK_INCLUDE_FILE)"
    checks:
      - "Looking for netinet/in.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-YYQ79m"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-YYQ79m"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "HAVE_NETINET_IN_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-YYQ79m'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_8fb92/fast
        /usr/bin/make  -f CMakeFiles/cmTC_8fb92.dir/build.make CMakeFiles/cmTC_8fb92.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-YYQ79m'
        Building C object CMakeFiles/cmTC_8fb92.dir/CheckIncludeFile.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_8fb92.dir/CheckIncludeFile.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-YYQ79m/CheckIncludeFile.c
        Linking C executable cmTC_8fb92
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8fb92.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_8fb92.dir/CheckIncludeFile.c.o -o cmTC_8fb92
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-YYQ79m'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "backend/CMakeLists.txt:120 (CHECK_SYMBOL_EXISTS)"
    checks:
      - "Looking for SO_REUSEPORT"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-sQL5vn"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-sQL5vn"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "HAVE_SO_REUSEPORT"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-sQL5vn'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b64b6/fast
        /usr/bin/make  -f CMakeFiles/cmTC_b64b6.dir/build.make CMakeFiles/cmTC_b64b6.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-sQL5vn'
        Building C object CMakeFiles/cmTC_b64b6.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_b64b6.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-sQL5vn/CheckSymbolExists.c
        Linking C executable cmTC_b64b6
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b64b6.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_b64b6.dir/CheckSymbolExists.c.o -o cmTC_b64b6
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/CMakeScratch/TryCompile-sQL5vn'
        
      exitCode: 0
...
