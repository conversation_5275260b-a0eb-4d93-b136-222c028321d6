# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

CMakeFiles/lkm_test.dir/lkm/lkm_test.cpp.o: /home/<USER>/mywork/poco_serverdemo/lkm/lkm_test.cpp \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortInfo.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortListener.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort_global.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/types.h \
  /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.h \
  /home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.h \
  /home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h \
  /home/<USER>/mywork/poco_serverdemo/utils/gmodels.h \
  /home/<USER>/mywork/poco_serverdemo/utils/logger.h \
  /home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.h \
  /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.h \
  /home/<USER>/mywork/poco_serverdemo/lkm/keymouse.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/ioctl.h \
  /usr/include/asm-generic/ioctls.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/ioctl.h \
  /usr/include/asm/ioctls.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/sigcontext.h \
  /usr/include/asm/sve_context.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/ioctl-types.h \
  /usr/include/bits/ioctls.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/procfs-extra.h \
  /usr/include/bits/procfs-id.h \
  /usr/include/bits/procfs-prregset.h \
  /usr/include/bits/procfs.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/sigaction.h \
  /usr/include/bits/sigcontext.h \
  /usr/include/bits/sigevent-consts.h \
  /usr/include/bits/siginfo-arch.h \
  /usr/include/bits/siginfo-consts-arch.h \
  /usr/include/bits/siginfo-consts.h \
  /usr/include/bits/signal_ext.h \
  /usr/include/bits/signum-arch.h \
  /usr/include/bits/signum-generic.h \
  /usr/include/bits/sigstack.h \
  /usr/include/bits/sigstksz.h \
  /usr/include/bits/sigthread.h \
  /usr/include/bits/ss_flags.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/__sigval_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sig_atomic_t.h \
  /usr/include/bits/types/sigevent_t.h \
  /usr/include/bits/types/siginfo_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/sigval_t.h \
  /usr/include/bits/types/stack_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_sigstack.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/opt_random.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/algorithm \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/random.h \
  /usr/include/c++/14.2.1/bits/random.tcc \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algo.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_numeric.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/iostream \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/numeric \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_numeric_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/random \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/input-event-codes.h \
  /usr/include/linux/input.h \
  /usr/include/linux/ioctl.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/linux/uinput.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/signal.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/ioctl.h \
  /usr/include/sys/procfs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/ttydefaults.h \
  /usr/include/sys/types.h \
  /usr/include/sys/ucontext.h \
  /usr/include/sys/user.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

bin/lkm_test: /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so \
  /usr/lib/Scrt1.o \
  /usr/lib/crti.o \
  /usr/lib/crtn.o \
  /usr/lib/libc.so \
  /usr/lib/libdl.a \
  /usr/lib/libgcc_s.so \
  /usr/lib/libgcc_s.so.1 \
  /usr/lib/libm.so \
  /usr/lib/libpthread.a \
  /usr/lib/librt.a \
  /usr/lib/libstdc++.so \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/libgcc.a \
  /usr/lib/ld-linux-aarch64.so.1 \
  /usr/lib/libEGL.so.1 \
  /usr/lib/libGL.so.1 \
  /usr/lib/libGLX.so.0 \
  /usr/lib/libGLdispatch.so.0 \
  /usr/lib/libIex-3_3.so.32 \
  /usr/lib/libIlmThread-3_3.so.32 \
  /usr/lib/libImath-3_1.so.29 \
  /usr/lib/libOpenCL.so.1 \
  /usr/lib/libOpenEXR-3_3.so.32 \
  /usr/lib/libOpenEXRCore-3_3.so.32 \
  /usr/lib/libOpenGL.so.0 \
  /usr/lib/libPocoCrypto.so.111 \
  /usr/lib/libPocoFoundation.so.111 \
  /usr/lib/libPocoJSON.so.111 \
  /usr/lib/libPocoNet.so.111 \
  /usr/lib/libPocoNetSSL.so.111 \
  /usr/lib/libPocoUtil.so.111 \
  /usr/lib/libPocoXML.so.111 \
  /usr/lib/libQt6Core.so.6 \
  /usr/lib/libQt6DBus.so.6 \
  /usr/lib/libQt6Gui.so.6 \
  /usr/lib/libQt6OpenGL.so.6 \
  /usr/lib/libQt6OpenGLWidgets.so.6 \
  /usr/lib/libQt6Test.so.6 \
  /usr/lib/libQt6Widgets.so.6 \
  /usr/lib/libX11-xcb.so.1 \
  /usr/lib/libX11.so.6 \
  /usr/lib/libXau.so.6 \
  /usr/lib/libXdmcp.so.6 \
  /usr/lib/libXext.so.6 \
  /usr/lib/libXfixes.so.3 \
  /usr/lib/libXrender.so.1 \
  /usr/lib/libatomic.so \
  /usr/lib/libavcodec.so.61 \
  /usr/lib/libavformat.so.61 \
  /usr/lib/libavutil.so.59 \
  /usr/lib/libb2.so.1 \
  /usr/lib/libblas.so.3 \
  /usr/lib/libblkid.so.1 \
  /usr/lib/libbluray.so.2 \
  /usr/lib/libbrotlicommon.so.1 \
  /usr/lib/libbrotlidec.so.1 \
  /usr/lib/libbrotlienc.so.1 \
  /usr/lib/libbz2.so.1.0 \
  /usr/lib/libc.so.6 \
  /usr/lib/libc_nonshared.a \
  /usr/lib/libcairo.so.2 \
  /usr/lib/libcap.so.2 \
  /usr/lib/libcblas.so.3 \
  /usr/lib/libcrypto.so \
  /usr/lib/libdatrie.so.1 \
  /usr/lib/libdav1d.so.7 \
  /usr/lib/libdbus-1.so.3 \
  /usr/lib/libdeflate.so.0 \
  /usr/lib/libdl.so.2 \
  /usr/lib/libdouble-conversion.so.3 \
  /usr/lib/libdrm.so.2 \
  /usr/lib/libdvdnav.so.4 \
  /usr/lib/libdvdread.so.8 \
  /usr/lib/libdw.so.1 \
  /usr/lib/libelf.so.1 \
  /usr/lib/libexpat.so.1 \
  /usr/lib/libffi.so.8 \
  /usr/lib/libfontconfig.so.1 \
  /usr/lib/libfreetype.so.6 \
  /usr/lib/libfribidi.so.0 \
  /usr/lib/libgdk_pixbuf-2.0.so.0 \
  /usr/lib/libgfortran.so.5 \
  /usr/lib/libgio-2.0.so.0 \
  /usr/lib/libglib-2.0.so.0 \
  /usr/lib/libgmodule-2.0.so.0 \
  /usr/lib/libgmp.so.10 \
  /usr/lib/libgnutls.so.30 \
  /usr/lib/libgobject-2.0.so.0 \
  /usr/lib/libgomp.so.1 \
  /usr/lib/libgraphite2.so.3 \
  /usr/lib/libgsm.so.1 \
  /usr/lib/libgstapp-1.0.so.0 \
  /usr/lib/libgstaudio-1.0.so.0 \
  /usr/lib/libgstbase-1.0.so.0 \
  /usr/lib/libgstpbutils-1.0.so.0 \
  /usr/lib/libgstreamer-1.0.so.0 \
  /usr/lib/libgstriff-1.0.so.0 \
  /usr/lib/libgsttag-1.0.so.0 \
  /usr/lib/libgstvideo-1.0.so.0 \
  /usr/lib/libharfbuzz.so.0 \
  /usr/lib/libhogweed.so.6 \
  /usr/lib/libhwy.so.1 \
  /usr/lib/libicudata.so.76 \
  /usr/lib/libicui18n.so.76 \
  /usr/lib/libicuuc.so.76 \
  /usr/lib/libidn2.so.0 \
  /usr/lib/libjbig.so.2.1 \
  /usr/lib/libjpeg.so.8 \
  /usr/lib/libjxl.so.0.11 \
  /usr/lib/libjxl_cms.so.0.11 \
  /usr/lib/libjxl_threads.so.0.11 \
  /usr/lib/liblapack.so.3 \
  /usr/lib/libleancrypto.so.1 \
  /usr/lib/liblzma.so.5 \
  /usr/lib/libm.so.6 \
  /usr/lib/libmd4c.so.0 \
  /usr/lib/libmodplug.so.1 \
  /usr/lib/libmount.so.1 \
  /usr/lib/libmp3lame.so.0 \
  /usr/lib/libmpg123.so.0 \
  /usr/lib/libmvec.so.1 \
  /usr/lib/libnettle.so.8 \
  /usr/lib/libogg.so.0 \
  /usr/lib/libopencore-amrnb.so.0 \
  /usr/lib/libopencore-amrwb.so.0 \
  /usr/lib/libopencv_core.so.4.11.0 \
  /usr/lib/libopencv_highgui.so.4.11.0 \
  /usr/lib/libopencv_imgcodecs.so.4.11.0 \
  /usr/lib/libopencv_imgproc.so.4.11.0 \
  /usr/lib/libopencv_videoio.so.4.11.0 \
  /usr/lib/libopenjp2.so.7 \
  /usr/lib/libopenmpt.so.0 \
  /usr/lib/libopus.so.0 \
  /usr/lib/liborc-0.4.so.0 \
  /usr/lib/libp11-kit.so.0 \
  /usr/lib/libpango-1.0.so.0 \
  /usr/lib/libpangocairo-1.0.so.0 \
  /usr/lib/libpangoft2-1.0.so.0 \
  /usr/lib/libpcre2-16.so.0 \
  /usr/lib/libpcre2-8.so.0 \
  /usr/lib/libpgm-5.3.so.0 \
  /usr/lib/libpixman-1.so.0 \
  /usr/lib/libpng16.so.16 \
  /usr/lib/libpthread.so.0 \
  /usr/lib/librsvg-2.so.2 \
  /usr/lib/libsharpyuv.so.0 \
  /usr/lib/libsnappy.so.1 \
  /usr/lib/libsodium.so.26 \
  /usr/lib/libsoxr.so.0 \
  /usr/lib/libspeex.so.1 \
  /usr/lib/libsrt.so.1.5 \
  /usr/lib/libssh.so.4 \
  /usr/lib/libssl.so \
  /usr/lib/libswresample.so.5 \
  /usr/lib/libswscale.so.8 \
  /usr/lib/libsystemd.so.0 \
  /usr/lib/libtasn1.so.6 \
  /usr/lib/libtbb.so.12 \
  /usr/lib/libthai.so.0 \
  /usr/lib/libtheoradec.so.2 \
  /usr/lib/libtheoraenc.so.2 \
  /usr/lib/libtiff.so.6 \
  /usr/lib/libunistring.so.5 \
  /usr/lib/libunwind.so.8 \
  /usr/lib/libva-drm.so.2 \
  /usr/lib/libva-x11.so.2 \
  /usr/lib/libva.so.2 \
  /usr/lib/libvdpau.so.1 \
  /usr/lib/libvorbis.so.0 \
  /usr/lib/libvorbisenc.so.2 \
  /usr/lib/libvorbisfile.so.3 \
  /usr/lib/libvpx.so.9 \
  /usr/lib/libwebp.so.7 \
  /usr/lib/libwebpdemux.so.2 \
  /usr/lib/libwebpmux.so.3 \
  /usr/lib/libx264.so.164 \
  /usr/lib/libx265.so.212 \
  /usr/lib/libxcb-dri3.so.0 \
  /usr/lib/libxcb-render.so.0 \
  /usr/lib/libxcb-shm.so.0 \
  /usr/lib/libxcb.so.1 \
  /usr/lib/libxkbcommon.so.0 \
  /usr/lib/libxml2.so.2 \
  /usr/lib/libxvidcore.so.4 \
  /usr/lib/libz.so.1 \
  /usr/lib/libzmq.so.5 \
  /usr/lib/libzstd.so.1 \
  CMakeFiles/lkm_test.dir/lkm/lkm_test.cpp.o \
  bin/lib/libalgorithm_lib.a \
  bin/lib/libbase.a \
  bin/lib/libblkmapi.a \
  bin/lib/libcore.a \
  bin/lib/libcserialport.a \
  bin/lib/libfmt.a \
  bin/lib/liblkmapi.a \
  bin/lib/libspdlog.a \
  bin/lib/libthread_manager.a \
  bin/lib/libutils.a


bin/lib/libutils.a:

bin/lib/libthread_manager.a:

bin/lib/libcserialport.a:

bin/lib/libbase.a:

/usr/lib/libzmq.so.5:

/usr/lib/libz.so.1:

/usr/lib/libxcb-shm.so.0:

/usr/lib/libxcb-render.so.0:

/usr/lib/libxcb-dri3.so.0:

/usr/lib/libx264.so.164:

/usr/lib/libvpx.so.9:

/usr/lib/libvorbisfile.so.3:

/usr/lib/libvorbisenc.so.2:

/usr/lib/libvorbis.so.0:

/usr/lib/libvdpau.so.1:

/usr/lib/libva.so.2:

/usr/lib/libunwind.so.8:

/usr/lib/libunistring.so.5:

/usr/lib/libtiff.so.6:

/usr/lib/libtheoradec.so.2:

/usr/lib/libtasn1.so.6:

/usr/lib/libsystemd.so.0:

/usr/lib/libswresample.so.5:

/usr/lib/libssl.so:

/usr/lib/libssh.so.4:

/usr/lib/libspeex.so.1:

/usr/lib/libsodium.so.26:

/usr/lib/libsnappy.so.1:

/usr/lib/libsharpyuv.so.0:

/usr/lib/libpng16.so.16:

/usr/lib/libpixman-1.so.0:

/usr/lib/libpgm-5.3.so.0:

/usr/lib/libpangoft2-1.0.so.0:

/usr/lib/libpangocairo-1.0.so.0:

/usr/lib/libp11-kit.so.0:

/usr/lib/libopenmpt.so.0:

/usr/lib/libopencv_videoio.so.4.11.0:

/usr/lib/libopencv_imgproc.so.4.11.0:

/usr/lib/libopencv_highgui.so.4.11.0:

/usr/lib/libopencv_core.so.4.11.0:

/usr/lib/libopencore-amrwb.so.0:

/usr/lib/libogg.so.0:

/usr/lib/libnettle.so.8:

/usr/lib/libmpg123.so.0:

/usr/lib/libmp3lame.so.0:

/usr/include/c++/14.2.1/bits/stl_bvector.h:

/usr/include/c++/14.2.1/bits/std_abs.h:

/usr/include/c++/14.2.1/bits/stl_algobase.h:

/usr/include/c++/14.2.1/cstdlib:

/usr/include/sys/procfs.h:

/usr/include/asm/unistd.h:

/usr/include/c++/14.2.1/tr1/bessel_function.tcc:

/usr/include/c++/14.2.1/bits/semaphore_base.h:

/usr/include/c++/14.2.1/bits/ranges_util.h:

/usr/include/c++/14.2.1/bits/ranges_uninitialized.h:

/usr/include/linux/sched/types.h:

/usr/include/c++/14.2.1/bits/ranges_cmp.h:

/usr/lib/libPocoUtil.so.111:

/usr/include/c++/14.2.1/bits/shared_ptr.h:

/usr/include/c++/14.2.1/bits/ranges_algo.h:

/usr/include/bits/sigstack.h:

/usr/lib/libPocoNet.so.111:

/usr/include/c++/14.2.1/bits/refwrap.h:

/usr/include/c++/14.2.1/bits/quoted_string.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h:

/usr/include/c++/14.2.1/bits/stl_numeric.h:

/usr/include/c++/14.2.1/bits/ostream.tcc:

/usr/include/endian.h:

/usr/include/stdint.h:

/usr/include/c++/14.2.1/bits/nested_exception.h:

/usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc:

/usr/include/c++/14.2.1/bits/locale_facets_nonio.h:

/usr/include/c++/14.2.1/bits/locale_classes.h:

/usr/include/c++/14.2.1/bits/iterator_concepts.h:

/usr/include/bits/types/siginfo_t.h:

/usr/include/c++/14.2.1/bits/ios_base.h:

/usr/include/c++/14.2.1/bits/invoke.h:

/usr/include/c++/14.2.1/bits/hashtable.h:

/usr/include/c++/14.2.1/bits/hash_bytes.h:

/usr/lib/libblkid.so.1:

/usr/include/c++/14.2.1/bits/functional_hash.h:

/usr/include/bits/types/__locale_t.h:

/usr/include/c++/14.2.1/bits/functexcept.h:

/usr/include/c++/14.2.1/bits/exception_defines.h:

/usr/include/c++/14.2.1/bits/specfun.h:

/usr/lib/libOpenEXRCore-3_3.so.32:

/usr/include/c++/14.2.1/bits/exception.h:

/usr/include/c++/14.2.1/bits/stl_construct.h:

/usr/include/c++/14.2.1/bits/utility.h:

/usr/include/c++/14.2.1/ext/atomicity.h:

/usr/include/asm/bitsperlong.h:

/usr/include/c++/14.2.1/bits/cpp_type_traits.h:

/usr/lib/liborc-0.4.so.0:

/usr/lib/libjxl.so.0.11:

/usr/include/c++/14.2.1/bits/chrono.h:

/usr/include/c++/14.2.1/bits/charconv.h:

/usr/lib/libwebpdemux.so.2:

/home/<USER>/mywork/poco_serverdemo/lkm/lkm_test.cpp:

/usr/include/bits/setjmp.h:

/usr/include/sys/syscall.h:

/usr/include/c++/14.2.1/bits/atomic_wait.h:

/usr/include/c++/14.2.1/bits/atomic_timed_wait.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h:

/usr/include/c++/14.2.1/bits/locale_facets.h:

/usr/include/c++/14.2.1/pstl/execution_defs.h:

/usr/include/c++/14.2.1/bits/atomic_base.h:

/usr/lib/libgcc_s.so:

/usr/include/c++/14.2.1/bits/allocated_ptr.h:

/usr/include/linux/input-event-codes.h:

/usr/lib/libgnutls.so.30:

/usr/include/c++/14.2.1/bits/random.h:

/usr/include/c++/14.2.1/bits/basic_string.h:

/usr/include/c++/14.2.1/bits/alloc_traits.h:

/usr/include/c++/14.2.1/bits/sstream.tcc:

/usr/include/c++/14.2.1/bits/align.h:

/usr/lib/libcap.so.2:

/usr/include/bits/select.h:

/usr/include/c++/14.2.1/bits/requires_hosted.h:

/usr/include/c++/14.2.1/bits/memoryfwd.h:

/usr/include/c++/14.2.1/bits/vector.tcc:

/usr/include/c++/14.2.1/backward/binders.h:

/usr/include/c++/14.2.1/limits:

/usr/include/sys/time.h:

/usr/include/c++/14.2.1/pstl/glue_numeric_defs.h:

/usr/lib/libdav1d.so.7:

/usr/include/c++/14.2.1/bits/postypes.h:

/usr/include/c++/14.2.1/bits/stl_algo.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h:

/usr/include/bits/types/time_t.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h:

/usr/include/c++/14.2.1/bits/node_handle.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h:

/usr/include/c++/14.2.1/tr1/legendre_function.tcc:

/usr/include/unistd.h:

/usr/include/bits/wordsize.h:

/usr/include/stdio.h:

/usr/lib/libopencore-amrnb.so.0:

/usr/include/bits/waitflags.h:

/usr/include/c++/14.2.1/bits/shared_ptr_atomic.h:

/usr/include/bits/unistd_ext.h:

/usr/include/bits/uio_lim.h:

/usr/include/bits/math-vector.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h:

/usr/lib/libopenjp2.so.7:

/usr/include/bits/uintn-identity.h:

bin/lib/libalgorithm_lib.a:

/usr/lib/libdl.a:

/usr/include/gnu/stubs.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/opt_random.h:

/usr/include/bits/types/timer_t.h:

/usr/include/bits/types/struct_tm.h:

/usr/include/bits/types/struct_timeval.h:

/usr/include/c++/14.2.1/thread:

/usr/include/bits/types/struct_timespec.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/procfs.h:

/usr/include/c++/14.2.1/bits/exception_ptr.h:

/usr/include/bits/types/sigval_t.h:

/usr/include/bits/types/sigset_t.h:

/usr/include/c++/14.2.1/ext/aligned_buffer.h:

/usr/lib/libzstd.so.1:

/usr/include/sys/cdefs.h:

/usr/include/bits/siginfo-consts.h:

/usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h:

/usr/include/wctype.h:

/usr/include/c++/14.2.1/clocale:

/usr/include/wchar.h:

/usr/include/bits/ioctl-types.h:

/usr/lib/libgmodule-2.0.so.0:

/usr/include/bits/thread-shared-types.h:

/usr/include/c++/14.2.1/bits/cxxabi_init_exception.h:

/usr/lib/libdvdnav.so.4:

/usr/include/bits/floatn.h:

/usr/lib/libpcre2-8.so.0:

/home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a:

/usr/include/c++/14.2.1/bits/parse_numbers.h:

/usr/include/sys/ttydefaults.h:

/usr/lib/libm.so.6:

/usr/include/bits/floatn-common.h:

bin/lib/libspdlog.a:

/usr/include/bits/stdio.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort.h:

/usr/include/bits/environments.h:

/usr/include/bits/types/struct_FILE.h:

/usr/include/c++/14.2.1/iomanip:

/usr/include/bits/fcntl.h:

/usr/lib/libicudata.so.76:

/usr/include/bits/types/struct_sched_param.h:

/usr/include/asm/sigcontext.h:

/usr/include/c++/14.2.1/charconv:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h:

/usr/lib/libQt6OpenGLWidgets.so.6:

/usr/include/asm/posix_types.h:

/usr/include/bits/time.h:

/usr/include/c++/14.2.1/debug/assertions.h:

/usr/include/c++/14.2.1/bits/max_size_type.h:

/usr/lib/libicuuc.so.76:

/usr/lib/libbz2.so.1.0:

/usr/include/c++/14.2.1/bits/std_mutex.h:

/usr/include/bits/types/sigevent_t.h:

/usr/include/bits/ss_flags.h:

/usr/include/c++/14.2.1/bits/concept_check.h:

/usr/include/syscall.h:

/usr/lib/libva-x11.so.2:

/usr/include/assert.h:

/usr/include/c++/14.2.1/bits/ptr_traits.h:

/usr/include/c++/14.2.1/istream:

/usr/include/c++/14.2.1/chrono:

/usr/include/linux/ioctl.h:

/usr/include/bits/getopt_posix.h:

/usr/include/errno.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h:

/usr/lib/libsrt.so.1.5:

/usr/lib/libopus.so.0:

/usr/include/c++/14.2.1/bits/localefwd.h:

/usr/include/bits/siginfo-arch.h:

/usr/include/bits/types.h:

/usr/include/c++/14.2.1/numeric:

/usr/include/bits/struct_mutex.h:

/usr/lib/libPocoXML.so.111:

/usr/include/bits/types/clockid_t.h:

/usr/include/bits/wctype-wchar.h:

/usr/include/bits/stdint-intn.h:

/usr/include/bits/fcntl-linux.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h:

/usr/lib/libdw.so.1:

/home/<USER>/mywork/poco_serverdemo/infer/base/types.h:

/usr/lib/libGLX.so.0:

/home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.h:

/usr/include/c++/14.2.1/bits/ranges_base.h:

/usr/include/linux/uinput.h:

/usr/include/bits/types/__sigval_t.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/libgcc.a:

/usr/include/bits/sigaction.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/bits/libc-header-start.h:

/usr/include/c++/14.2.1/bits/random.tcc:

/usr/lib/libPocoJSON.so.111:

/usr/include/bits/types/clock_t.h:

/usr/include/c++/14.2.1/bits/basic_string.tcc:

/usr/include/c++/14.2.1/bits/char_traits.h:

/usr/include/bits/byteswap.h:

/usr/lib/libOpenCL.so.1:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPort_global.h:

/usr/include/c++/14.2.1/bits/chrono_io.h:

/usr/include/bits/confname.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h:

/home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.h:

/usr/include/c++/14.2.1/cerrno:

/usr/include/c++/14.2.1/bits/istream.tcc:

/usr/lib/libfribidi.so.0:

/home/<USER>/mywork/poco_serverdemo/utils/gmodels.h:

/usr/include/c++/14.2.1/semaphore:

/usr/lib/libcrypto.so:

/usr/lib/libpthread.so.0:

/usr/include/bits/sigthread.h:

/usr/include/bits/endian.h:

/usr/include/bits/procfs-prregset.h:

/usr/include/c++/14.2.1/bits/locale_facets.tcc:

/usr/include/asm/errno.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/c++/14.2.1/iostream:

/usr/lib/libxkbcommon.so.0:

/usr/include/bits/timesize.h:

/usr/lib/libdeflate.so.0:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h:

/usr/include/alloca.h:

/usr/include/bits/sigstksz.h:

/usr/include/bits/types/error_t.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/c++/14.2.1/condition_variable:

/usr/include/c++/14.2.1/bit:

/usr/include/bits/types/__sigset_t.h:

/usr/lib/libQt6Gui.so.6:

/usr/include/bits/types/struct_sigstack.h:

/usr/include/c++/14.2.1/bits/locale_classes.tcc:

/home/<USER>/mywork/poco_serverdemo/lkm/keymouse.h:

/usr/include/c++/14.2.1/array:

/usr/include/c++/14.2.1/type_traits:

/usr/lib/Scrt1.o:

/usr/lib/libmodplug.so.1:

/usr/include/c++/14.2.1/bits/std_thread.h:

/usr/include/c++/14.2.1/bits/basic_ios.h:

/usr/lib/libexpat.so.1:

/usr/include/bits/types/wint_t.h:

/usr/include/c++/14.2.1/atomic:

/usr/include/bits/sigcontext.h:

/usr/include/c++/14.2.1/bits/hashtable_policy.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortListener.h:

/usr/include/c++/14.2.1/bits/version.h:

/usr/lib/libva-drm.so.2:

/usr/include/bits/cpu-set.h:

/usr/include/bits/libm-simd-decl-stubs.h:

/usr/lib/libgraphite2.so.3:

/usr/include/bits/endianness.h:

/usr/include/c++/14.2.1/typeinfo:

/usr/include/libintl.h:

/usr/include/asm/ioctls.h:

/usr/include/c++/14.2.1/bits/memory_resource.h:

/usr/include/asm-generic/ioctls.h:

/usr/include/c++/14.2.1/tr1/special_function_util.h:

/usr/include/asm/ioctl.h:

/home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.h:

/usr/include/c++/14.2.1/bits/stl_relops.h:

/usr/lib/librt.a:

/usr/include/bits/local_lim.h:

/usr/include/bits/getopt_core.h:

/usr/include/c++/14.2.1/locale:

/usr/include/bits/siginfo-consts-arch.h:

/usr/include/bits/locale.h:

/usr/lib/libx265.so.212:

/home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.h:

/usr/include/bits/iscanonical.h:

/usr/include/asm/unistd_64.h:

/usr/include/bits/types/__fpos_t.h:

/usr/include/bits/types/sig_atomic_t.h:

/usr/include/c++/14.2.1/bits/uses_allocator.h:

/usr/include/c++/14.2.1/mutex:

/usr/lib/librsvg-2.so.2:

/usr/lib/libopencv_imgcodecs.so.4.11.0:

/usr/include/c++/14.2.1/stop_token:

/usr/include/bits/long-double.h:

/usr/include/bits/mathcalls-helper-functions.h:

/usr/include/bits/types/struct_itimerspec.h:

/usr/lib/libgsttag-1.0.so.0:

/usr/include/c++/14.2.1/bits/locale_conv.h:

/usr/lib/libgstvideo-1.0.so.0:

/usr/include/asm-generic/int-ll64.h:

/usr/include/bits/mathcalls-macros.h:

/usr/include/bits/mathcalls-narrow.h:

/usr/lib/libbrotlicommon.so.1:

/usr/include/bits/posix_opt.h:

/usr/include/bits/procfs-extra.h:

/usr/include/bits/procfs-id.h:

/usr/include/c++/14.2.1/bits/algorithmfwd.h:

/usr/lib/libPocoFoundation.so.111:

/usr/include/bits/pthreadtypes.h:

/usr/include/asm-generic/types.h:

/usr/lib/libc.so:

/usr/include/bits/pthread_stack_min-dynamic.h:

/usr/include/c++/14.2.1/tr1/ell_integral.tcc:

/usr/include/bits/pthreadtypes-arch.h:

/usr/lib/libsoxr.so.0:

/usr/include/asm-generic/ioctl.h:

/usr/include/bits/struct_rwlock.h:

/usr/include/bits/sched.h:

/usr/include/bits/types/__FILE.h:

/usr/include/features.h:

/usr/include/bits/types/cookie_io_functions_t.h:

/usr/include/bits/types/stack_t.h:

/usr/include/bits/posix2_lim.h:

/usr/include/bits/sigevent-consts.h:

bin/lib/libcore.a:

/usr/include/c++/14.2.1/bits/basic_ios.tcc:

/usr/include/bits/stdlib-bsearch.h:

/usr/include/bits/signal_ext.h:

/usr/include/bits/types/mbstate_t.h:

/usr/include/c++/14.2.1/vector:

/usr/lib/libXrender.so.1:

/usr/include/c++/14.2.1/bits/range_access.h:

/usr/include/bits/signum-generic.h:

/usr/include/c++/14.2.1/sstream:

/usr/include/c++/14.2.1/bits/predefined_ops.h:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/bits/stdio_lim.h:

/home/<USER>/mywork/poco_serverdemo/infer/base/wsq.h:

/usr/lib/libharfbuzz.so.0:

/usr/include/bits/struct_stat.h:

/usr/include/bits/time64.h:

/usr/include/bits/errno.h:

/usr/include/c++/14.2.1/bits/stl_vector.h:

/usr/include/bits/timex.h:

/usr/include/c++/14.2.1/tuple:

/usr/include/bits/flt-eval-method.h:

/usr/include/bits/types/FILE.h:

/usr/include/c++/14.2.1/cctype:

/usr/lib/libjxl_cms.so.0.11:

/usr/include/linux/posix_types.h:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/c++/14.2.1/bits/stl_uninitialized.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h:

/usr/include/bits/ioctls.h:

/usr/lib/libswscale.so.8:

/usr/include/bits/mathcalls.h:

/usr/include/bits/types/locale_t.h:

/usr/include/c++/14.2.1/bits/cxxabi_forced.h:

/usr/include/bits/semaphore.h:

/usr/lib/libxml2.so.2:

/usr/include/bits/typesizes.h:

/usr/include/features-time64.h:

/usr/include/c++/14.2.1/bits/stl_heap.h:

/usr/include/c++/14.2.1/bits/stl_iterator.h:

/usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h:

/usr/include/bits/stdint-least.h:

/usr/include/c++/14.2.1/bits/stl_iterator_base_types.h:

/usr/include/c++/14.2.1/bits/stl_pair.h:

/usr/lib/libpango-1.0.so.0:

/usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h:

/usr/include/bits/stdlib-float.h:

/usr/include/c++/14.2.1/bits/stl_tempbuf.h:

/usr/lib/libdl.so.2:

/usr/include/c++/14.2.1/variant:

/usr/lib/libgcc_s.so.1:

/usr/include/c++/14.2.1/bits/string_view.tcc:

/usr/include/c++/14.2.1/bits/stringfwd.h:

/usr/include/c++/14.2.1/bits/move.h:

/usr/include/c++/14.2.1/bits/this_thread_sleep.h:

/usr/lib/libm.so:

/usr/include/c++/14.2.1/bits/unicode-data.h:

/usr/include/c++/14.2.1/bits/unicode.h:

/usr/include/linux/falloc.h:

/usr/include/c++/14.2.1/bits/uniform_int_dist.h:

/usr/lib/libpcre2-16.so.0:

/usr/include/locale.h:

/usr/lib/libmd4c.so.0:

/usr/lib/libstdc++.so:

/usr/include/c++/14.2.1/bits/unique_lock.h:

/usr/include/c++/14.2.1/bits/unique_ptr.h:

/usr/include/c++/14.2.1/algorithm:

/usr/include/c++/14.2.1/bits/unordered_map.h:

/usr/include/c++/14.2.1/bits/uses_allocator_args.h:

/usr/include/c++/14.2.1/cassert:

/usr/include/c++/14.2.1/climits:

/usr/include/c++/14.2.1/bits/ostream_insert.h:

/usr/include/c++/14.2.1/cmath:

/usr/include/c++/14.2.1/compare:

/usr/include/c++/14.2.1/concepts:

/usr/include/bits/wchar.h:

/usr/include/c++/14.2.1/numbers:

/usr/include/c++/14.2.1/cstddef:

/usr/lib/libhwy.so.1:

/usr/lib/libtbb.so.12:

/usr/include/c++/14.2.1/cstdint:

/usr/lib/libwebp.so.7:

/usr/include/c++/14.2.1/cstdio:

bin/lib/liblkmapi.a:

/usr/include/c++/14.2.1/cstring:

/usr/include/c++/14.2.1/ctime:

/usr/include/c++/14.2.1/cwchar:

/usr/include/c++/14.2.1/tr1/poly_laguerre.tcc:

/usr/include/c++/14.2.1/cwctype:

/usr/include/c++/14.2.1/bits/allocator.h:

/usr/include/c++/14.2.1/debug/debug.h:

/usr/include/c++/14.2.1/exception:

/usr/include/sys/types.h:

/usr/include/c++/14.2.1/ext/concurrence.h:

/usr/include/asm/sve_context.h:

/usr/include/c++/14.2.1/ext/numeric_traits.h:

/usr/include/c++/14.2.1/bits/codecvt.h:

/usr/lib/libQt6Widgets.so.6:

/usr/include/c++/14.2.1/ext/string_conversions.h:

/usr/lib/libgstaudio-1.0.so.0:

/usr/include/bits/stat.h:

/usr/include/c++/14.2.1/ext/type_traits.h:

/usr/include/c++/14.2.1/initializer_list:

/usr/lib/libbrotlidec.so.1:

/usr/include/asm/types.h:

/usr/include/c++/14.2.1/ios:

/usr/include/math.h:

/usr/include/c++/14.2.1/bits/streambuf.tcc:

/usr/include/c++/14.2.1/iosfwd:

/usr/include/c++/14.2.1/memory:

/usr/lib/libgsm.so.1:

/usr/lib/libdbus-1.so.3:

/usr/include/c++/14.2.1/new:

/usr/include/c++/14.2.1/pstl/glue_memory_defs.h:

CMakeFiles/lkm_test.dir/lkm/lkm_test.cpp.o:

/usr/include/bits/syscall.h:

/usr/include/c++/14.2.1/pstl/pstl_config.h:

/usr/include/c++/14.2.1/random:

/usr/lib/libtheoraenc.so.2:

/usr/include/c++/14.2.1/unordered_map:

/usr/include/c++/14.2.1/stdexcept:

/usr/include/c++/14.2.1/streambuf:

/usr/include/c++/14.2.1/backward/auto_ptr.h:

/usr/include/c++/14.2.1/string:

/usr/include/pthread.h:

/usr/include/bits/xopen_lim.h:

/usr/lib/libgstriff-1.0.so.0:

/usr/lib/libjpeg.so.8:

/usr/include/linux/errno.h:

/usr/include/c++/14.2.1/string_view:

/usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h:

/usr/lib/libdouble-conversion.so.3:

/usr/include/c++/14.2.1/system_error:

/usr/include/c++/14.2.1/tr1/beta_function.tcc:

/usr/include/c++/14.2.1/tr1/exp_integral.tcc:

/usr/include/c++/14.2.1/tr1/gamma.tcc:

/usr/include/c++/14.2.1/format:

/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so:

/usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h:

/usr/lib/libOpenEXR-3_3.so.32:

/usr/include/c++/14.2.1/bits/ranges_algobase.h:

/usr/lib/libb2.so.1:

/usr/include/c++/14.2.1/tr1/poly_hermite.tcc:

/usr/include/c++/14.2.1/tr1/riemann_zeta.tcc:

/usr/include/c++/14.2.1/utility:

/usr/include/c++/14.2.1/bits/stl_function.h:

/usr/include/ctype.h:

/usr/include/gnu/stubs-lp64.h:

/usr/include/fcntl.h:

/usr/include/limits.h:

/usr/include/c++/14.2.1/ostream:

/usr/include/linux/close_range.h:

/usr/include/c++/14.2.1/ext/alloc_traits.h:

/usr/include/c++/14.2.1/tr1/hypergeometric.tcc:

/usr/lib/libleancrypto.so.1:

/usr/lib/crtn.o:

/usr/include/linux/limits.h:

/usr/lib/libmvec.so.1:

/usr/include/bits/fp-logb.h:

/usr/include/linux/stddef.h:

/usr/include/linux/types.h:

/usr/include/semaphore.h:

/usr/include/bits/atomic_wide_counter.h:

/usr/include/c++/14.2.1/span:

/usr/include/signal.h:

/usr/lib/libxvidcore.so.4:

/usr/include/strings.h:

/usr/include/stdc-predef.h:

/usr/include/stdlib.h:

/usr/include/string.h:

/usr/lib/libQt6Core.so.6:

/usr/include/sys/ioctl.h:

/usr/lib/libffi.so.8:

/usr/lib/libGL.so.1:

/usr/include/sys/select.h:

/usr/include/sys/user.h:

/usr/include/time.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h:

/usr/lib/libc_nonshared.a:

/usr/lib/libfontconfig.so.1:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h:

/usr/lib/libQt6OpenGL.so.6:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h:

/usr/include/bits/signum-arch.h:

/usr/include/c++/14.2.1/optional:

/usr/lib/crti.o:

/usr/lib/libpthread.a:

/usr/include/asm-generic/errno.h:

/usr/include/sys/single_threaded.h:

/usr/lib/libgstreamer-1.0.so.0:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o:

/usr/lib/ld-linux-aarch64.so.1:

/usr/include/c++/14.2.1/bits/streambuf_iterator.h:

/usr/lib/libgstbase-1.0.so.0:

/usr/include/bits/posix1_lim.h:

/usr/lib/libEGL.so.1:

/usr/lib/libIex-3_3.so.32:

/usr/lib/libgfortran.so.5:

/usr/lib/libImath-3_1.so.29:

/usr/lib/libthai.so.0:

/usr/lib/libjxl_threads.so.0.11:

/usr/lib/libwebpmux.so.3:

/usr/lib/libOpenGL.so.0:

/usr/lib/libPocoCrypto.so.111:

/usr/lib/libPocoNetSSL.so.111:

/usr/include/sched.h:

/usr/lib/libQt6DBus.so.6:

/usr/lib/libxcb.so.1:

/usr/lib/libQt6Test.so.6:

/usr/lib/libX11-xcb.so.1:

/usr/lib/libX11.so.6:

/usr/lib/libXau.so.6:

bin/lib/libfmt.a:

bin/lib/libblkmapi.a:

/usr/lib/libXdmcp.so.6:

/usr/lib/libXext.so.6:

/usr/lib/libXfixes.so.3:

/usr/include/c++/14.2.1/bits/new_allocator.h:

/usr/lib/libatomic.so:

/usr/lib/libbrotlienc.so.1:

/usr/lib/libavcodec.so.61:

/usr/include/sys/ucontext.h:

/usr/lib/libavformat.so.61:

/usr/lib/libavutil.so.59:

/usr/include/c++/14.2.1/ratio:

/usr/lib/libblas.so.3:

/usr/include/bits/types/struct___jmp_buf_tag.h:

/usr/lib/libbluray.so.2:

/usr/include/bits/fp-fast.h:

/usr/lib/libc.so.6:

/usr/lib/libcairo.so.2:

/usr/lib/libcblas.so.3:

/usr/lib/libdatrie.so.1:

/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include/CSerialPort/SerialPortInfo.h:

/usr/lib/libdrm.so.2:

/usr/lib/libdvdread.so.8:

/usr/lib/libelf.so.1:

/usr/include/c++/14.2.1/bits/enable_special_members.h:

/usr/lib/libfreetype.so.6:

/usr/lib/libIlmThread-3_3.so.32:

/usr/lib/libgdk_pixbuf-2.0.so.0:

/usr/lib/libgio-2.0.so.0:

/usr/lib/libglib-2.0.so.0:

/usr/lib/libgmp.so.10:

/usr/include/bits/types/struct_iovec.h:

/usr/include/c++/14.2.1/bits/shared_ptr_base.h:

/usr/include/linux/input.h:

/usr/lib/libgobject-2.0.so.0:

/usr/lib/libgomp.so.1:

/home/<USER>/mywork/poco_serverdemo/utils/logger.h:

/usr/lib/libgstapp-1.0.so.0:

/usr/lib/libgstpbutils-1.0.so.0:

/usr/lib/libhogweed.so.6:

/usr/lib/libicui18n.so.76:

/usr/lib/libidn2.so.0:

/usr/lib/libjbig.so.2.1:

/usr/lib/liblapack.so.3:

/usr/lib/liblzma.so.5:

/usr/include/c++/14.2.1/bits/erase_if.h:

/usr/lib/libGLdispatch.so.0:

/usr/lib/libmount.so.1:
