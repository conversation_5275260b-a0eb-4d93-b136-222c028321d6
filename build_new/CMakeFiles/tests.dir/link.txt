/usr/bin/c++  -O3 -O3 -DNDEBUG -Wl,--dependency-file=CMakeFiles/tests.dir/link.d CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o -o bin/tests  -Wl,-rpath,/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux bin/lib/libcore.a bin/lib/libthread_manager.a bin/lib/libutils.a bin/lib/libfmt.a bin/lib/libCatch2Main.a bin/lib/libcore.a bin/lib/libbase.a /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a /usr/lib/libopencv_highgui.so.4.11.0 /usr/lib/libopencv_videoio.so.4.11.0 /usr/lib/libopencv_imgcodecs.so.4.11.0 /usr/lib/libopencv_imgproc.so.4.11.0 /usr/lib/libopencv_core.so.4.11.0 -lpthread bin/lib/libblkmapi.a /usr/lib/libPocoNetSSL.so.111 /usr/lib/libPocoNet.so.111 /usr/lib/libPocoUtil.so.111 /usr/lib/libPocoJSON.so.111 /usr/lib/libPocoXML.so.111 /usr/lib/libPocoCrypto.so.111 /usr/lib/libPocoFoundation.so.111 -ldl -lrt /usr/lib/libssl.so /usr/lib/libcrypto.so bin/lib/liblkmapi.a bin/lib/libutils.a bin/lib/libspdlog.a bin/lib/libfmt.a bin/lib/libcserialport.a -lpthread bin/lib/libalgorithm_lib.a /usr/lib/libatomic.so bin/lib/libCatch2.a
