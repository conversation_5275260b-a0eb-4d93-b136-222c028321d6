
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/mywork/poco_serverdemo/infer/tests/core/video_capture.cpp" "CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o" "gcc" "CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/infer/tests/external/librga.cpp" "CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o" "gcc" "CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o.d"
  "" "bin/tests" "gcc" "CMakeFiles/tests.dir/link.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
