
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.cpp" "CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o" "gcc" "CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/blkm/cloud_update.cpp" "CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o" "gcc" "CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/blkm/crypto_utils.cpp" "CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o" "gcc" "CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
