# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_new

# Include any dependencies generated for this target.
include CMakeFiles/algorithm_lib.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/algorithm_lib.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/algorithm_lib.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/algorithm_lib.dir/flags.make

CMakeFiles/algorithm_lib.dir/codegen:
.PHONY : CMakeFiles/algorithm_lib.dir/codegen

CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o: CMakeFiles/algorithm_lib.dir/flags.make
CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o: /home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.cpp
CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o: CMakeFiles/algorithm_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o -MF CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o.d -o CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o -c /home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.cpp

CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.cpp > CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.i

CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/algorithm/pid_controller.cpp -o CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.s

CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o: CMakeFiles/algorithm_lib.dir/flags.make
CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o: /home/<USER>/mywork/poco_serverdemo/algorithm/MouseFov.cpp
CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o: CMakeFiles/algorithm_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o -MF CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o.d -o CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o -c /home/<USER>/mywork/poco_serverdemo/algorithm/MouseFov.cpp

CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/algorithm/MouseFov.cpp > CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.i

CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/algorithm/MouseFov.cpp -o CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.s

# Object files for target algorithm_lib
algorithm_lib_OBJECTS = \
"CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o" \
"CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o"

# External object files for target algorithm_lib
algorithm_lib_EXTERNAL_OBJECTS =

bin/lib/libalgorithm_lib.a: CMakeFiles/algorithm_lib.dir/algorithm/pid_controller.cpp.o
bin/lib/libalgorithm_lib.a: CMakeFiles/algorithm_lib.dir/algorithm/MouseFov.cpp.o
bin/lib/libalgorithm_lib.a: CMakeFiles/algorithm_lib.dir/build.make
bin/lib/libalgorithm_lib.a: CMakeFiles/algorithm_lib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library bin/lib/libalgorithm_lib.a"
	$(CMAKE_COMMAND) -P CMakeFiles/algorithm_lib.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/algorithm_lib.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/algorithm_lib.dir/build: bin/lib/libalgorithm_lib.a
.PHONY : CMakeFiles/algorithm_lib.dir/build

CMakeFiles/algorithm_lib.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/algorithm_lib.dir/cmake_clean.cmake
.PHONY : CMakeFiles/algorithm_lib.dir/clean

CMakeFiles/algorithm_lib.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_new && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new /home/<USER>/mywork/poco_serverdemo/build_new/CMakeFiles/algorithm_lib.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/algorithm_lib.dir/depend

