// 建立连接
Poco::Net::SocketAddress address(serverIP, serverPort);
Poco::Net::StreamSocket socket;
socket.connect(address);

// 确认文件是否存在且可读
if (!std::filesystem::exists(zipFilePath)) {
    throw std::runtime_error("ZIP文件不存在: " + zipFilePath);
}

if (!std::filesystem::is_regular_file(zipFilePath)) {
    throw std::runtime_error("指定的路径不是一个常规文件: " + zipFilePath);
}

// 获取文件名和大小
std::string fileName = Poco::Path(zipFilePath).getFileName();
size_t fileSize = std::filesystem::file_size(zipFilePath);

if (fileSize == 0) {
    LOG_WARNING("警告: 文件大小为0字节: {}", zipFilePath);
}

// 发送文件名（以null字符结束）
LOG_INFO("发送文件名: {}", fileName);
socket.sendBytes(fileName.c_str(), fileName.length() + 1);

// 然后发送文件内容
std::ifstream file(zipFilePath, std::ios::binary);
if (!file.is_open()) {
    throw std::runtime_error("无法打开ZIP文件进行读取: " + zipFilePath);
}

LOG_INFO("开始传输文件内容，文件大小: {} 字节", fileSize);

// 读取并发送文件数据
char buffer[BUFFER_SIZE];
size_t totalSent = 0;
int progressPercent = 0;

while (!file.eof()) {
    file.read(buffer, BUFFER_SIZE);
    std::streamsize bytesRead = file.gcount();
    
    if (bytesRead > 0) {
        socket.sendBytes(buffer, bytesRead);
        totalSent += bytesRead;
        
        // 打印上传进度
        int newProgressPercent = (int)((totalSent * 100) / fileSize);
        if (newProgressPercent > progressPercent) {
            progressPercent = newProgressPercent;
            LOG_INFO("上传进度: {}% ({}/{} 字节)", progressPercent, totalSent, fileSize);
        }
    }
}

file.close();
socket.close();

LOG_INFO("文件上传完成，总共发送 {} 字节", totalSent);