# 阵营过滤功能设计文档

## 📋 功能概述

阵营过滤功能允许用户根据选择的阵营来过滤推理目标，确保只攻击敌对阵营的目标，避免误伤友军。

## 🎮 游戏配置

### wwqy游戏（特殊配置）
```
索引0: BS (保安/其他)  - 中性目标
索引1: FF (匪方)       - 匪方目标  
索引2: M  (其他)       - 中性目标
索引3: JF (警方)       - 警方目标
```

### 其他游戏（标准配置）
```
索引0: FF (匪方)       - 匪方目标
索引1: JF (警方)       - 警方目标
```

## 🎯 阵营选择逻辑

| 用户选择阵营 | 攻击目标 | 说明 |
|-------------|----------|------|
| **警方** | 匪方目标 | 选择警方阵营时，只攻击匪方(FF)目标 |
| **匪方** | 警方目标 | 选择匪方阵营时，只攻击警方(JF)目标 |
| **无** | 所有目标 | 无阵营限制，攻击所有检测到的目标 |

## 🔧 技术实现

### 核心函数

```cpp
bool InferCollector::ShouldAttackTarget(
    int class_id, 
    const std::string& game_name, 
    const std::string& selected_faction
);
```

### 实现位置

- **头文件**: `infer/core/aimbot/infer_collector.h`
- **实现文件**: `infer/core/aimbot/infer_collector.cpp`
- **调用位置**: `FindNearestTarget` 函数中的目标过滤阶段

### 数据流程

```mermaid
graph TD
    A[检测到目标] --> B[获取class_id]
    B --> C[获取当前游戏名称]
    C --> D[获取用户阵营选择]
    D --> E{调用ShouldAttackTarget}
    E -->|true| F[处理该目标]
    E -->|false| G[跳过该目标]
    F --> H[计算距离和瞄准点]
    G --> I[继续下一个目标]
```

## 📊 测试用例

### wwqy游戏测试
- ✅ 警方阵营：攻击FF(索引1)，不攻击JF(索引3)
- ✅ 匪方阵营：攻击JF(索引3)，不攻击FF(索引1)  
- ✅ 无阵营：攻击所有目标(索引0,1,2,3)

### 其他游戏测试
- ✅ 警方阵营：攻击FF(索引0)，不攻击JF(索引1)
- ✅ 匪方阵营：攻击JF(索引1)，不攻击FF(索引0)
- ✅ 无阵营：攻击所有目标(索引0,1)

## 🔄 配置更新流程

1. **前端选择**: 用户在WebUI中选择阵营
2. **API传输**: 通过function_modify API传输到后端
3. **数据库存储**: 保存到function_configs表的selected_faction字段
4. **全局变量**: 更新webui::function::PRESET_CONFIGS中的selected_faction
5. **推理应用**: 在FindNearestTarget中实时应用过滤逻辑

## 📈 性能影响

- **额外开销**: 每个目标增加一次阵营检查（O(1)时间复杂度）
- **性能优化**: 不符合阵营要求的目标直接跳过，减少后续计算
- **内存占用**: 增加一个字符串字段（约8-16字节）

## 🛠️ 使用示例

### API请求示例
```json
{
  "action": "function_modify",
  "content": {
    "username": "player1",
    "gameName": "wwqy",
    "configs": [{
      "presetName": "配置1",
      "selectedFaction": "警方",
      "aiMode": "FOV",
      "lockPosition": "头部",
      "enabled": true
    }]
  }
}
```

### C++代码示例
```cpp
// 获取当前配置的阵营选择
int active_index = webui::function::g_active_config_index;
std::string selected_faction = webui::function::PRESET_CONFIGS[active_index].selected_faction;

// 在推理循环中应用过滤
for (const auto& box : detected_boxes) {
    if (!ShouldAttackTarget(box.class_id, current_game, selected_faction)) {
        continue; // 跳过不符合阵营要求的目标
    }
    // 处理符合要求的目标...
}
```

## 🔍 调试信息

可以通过启用调试日志来观察阵营过滤的工作情况：

```cpp
// 在infer_collector.cpp中取消注释以下行
LOG_DEBUG("阵营过滤：跳过目标 class_id={}, 游戏={}, 阵营={}", 
          box.class_id, current_game, selected_faction);
```

## 🚀 后续优化

1. **配置缓存**: 缓存阵营配置以减少字符串比较
2. **动态配置**: 支持运行时动态修改游戏标签配置
3. **扩展支持**: 支持更多游戏的自定义标签配置
4. **性能监控**: 添加阵营过滤的性能统计信息

---

**版本**: v1.0  
**更新日期**: 2025-01-03  
**作者**: C++ RK3588 YOLOV8后端专家 