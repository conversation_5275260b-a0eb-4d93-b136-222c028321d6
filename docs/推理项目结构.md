# 推理项目结构

## 主要目录结构
```
infer/
├── base/                # 基础工具和类型定义
│   ├── alignment.h      # 内存对齐相关
│   ├── file_utils.cpp   # 文件操作工具
│   ├── file_utils.h     
│   ├── fps_counter.h    # FPS计数器
│   ├── macros.h         # 宏定义
│   ├── static_vector.h  # 静态向量实现
│   ├── types.h          # 类型定义
│   └── wsq.h            # WSQ相关工具
├── core/                # 核心功能模块
│   ├── aimbot/          # 自瞄相关功能
│   ├── model/           # 模型处理相关
│   └── video/           # 视频处理相关
├── model/               # 模型文件
│   └── csgo416.rknn     # RKNN模型文件
├── tests/               # 测试代码
├── infer.cmake          # 编译配置
├── infer_test.cpp       # 测试入口
└── wolf240hz_patch.cpp  # 特定补丁实现

## 相关构建脚本
- infer_build.sh         # 推理模块构建脚本
```

## 项目功能概述
推理项目主要负责游戏内目标检测和识别，包含模型推理、图像处理和自瞄逻辑等核心功能。

## 主要技术组件
- RKNN模型推理
- 视频帧处理
- 目标检测与识别
- 自瞄算法实现