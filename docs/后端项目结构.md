# 后端项目结构

## 主要目录结构
```
backend/
├── blserver/               # 服务器核心模块
│   ├── poco_websocket_server.cpp  # WebSocket服务器实现
│   ├── message_handlers.cpp       # 消息处理器
│   ├── update_models.cpp          # 模型更新服务
│   ├── login_service.cpp          # 登录服务
│   ├── register_service.cpp       # 注册服务
│   ├── aim_service.cpp            # 自瞄服务
│   ├── fov_service.cpp            # 视野服务
│   ├── fire_service.cpp           # 射击服务
│   ├── function_service.cpp       # 功能服务
│   ├── pid_service.cpp            # PID控制服务
│   ├── data_collection_service.cpp # 数据收集服务
├── blsql/                 # 数据库操作模块
│   ├── mysql_db.cpp              # MySQL数据库连接
│   ├── login_db.cpp              # 登录数据库操作
│   ├── registerdb.cpp            # 注册数据库操作
│   ├── userinfodb.cpp            # 用户信息数据库操作
│   ├── aim_configs_db.cpp        # 自瞄配置数据库操作
│   ├── fovconfgidb.cpp           # 视野配置数据库操作
│   ├── fire_configs_db.cpp       # 射击配置数据库操作
│   ├── function_configs_db.cpp   # 功能配置数据库操作
│   ├── pid_configs_db.cpp        # PID配置数据库操作
├── webapi/                # Web API接口定义
│   ├── aim_api.md              # 自瞄API文档
│   ├── fov_api.md              # 视野API文档
│   ├── fun_api.md              # 功能API文档
│   ├── header_api.md           # 头部API文档
│   ├── home_api.md             # 主页API文档
│   ├── pid_api.md              # PID控制API文档
│   ├── sidebar_api.md          # 侧边栏API文档
├── handlers/              # 处理器模块
├── main.cpp               # 主程序入口
└── CMakeLists.txt         # 编译配置

## 相关构建脚本
- backend_build.sh        # 后端构建脚本
```

## 项目功能概述
后端项目负责提供WebSocket服务器、用户验证、数据库操作以及各种游戏功能配置的管理接口。

## 主要技术组件
- POCO库WebSocket服务器
- MySQL数据库连接与操作
- RESTful API接口设计
- 用户认证与授权
- 游戏参数配置管理 