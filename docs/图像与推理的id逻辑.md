# 视频流处理优化：帧ID替代时间戳方案

## 核心思想

使用帧ID替代时间戳作为视频帧的唯一标识符，以优化多线程环境下的视频采集、处理和显示流程。帧ID简单递增，可以精确追踪每一帧的处理状态。

## 数据结构优化

```cpp
// 优化后的共享帧结构体
struct SharedFrame {
    cv::Mat image;                    // 图像数据
    std::atomic<int64_t> frame_id{0}; // 帧ID，从0开始递增
};

// 全局变量
SharedFrame g_shared_frame;                       // 共享帧缓冲区
std::atomic<int64_t> g_last_processed_frame_id{-1}; // 记录最后处理的帧ID
```

## 线程职责与实现

### 1. 图像捕获线程

```cpp
void RgaCapture::captureThread()
{
    LOG_INFO("进入图像捕获线程");
    
    if (!initialize()) {
        LOG_ERROR("RGA初始化失败");
        thread_control::g_thread_running = false;
        return;
    }
    
    // 初始化
    m_frameCount = 0;
    static int64_t next_frame_id = 0;  // 下一个要分配的帧ID
    const int printInterval = 100;     // 性能日志打印间隔
    
    // 处理循环
    while (thread_control::g_thread_running) {
        // 记录开始时间
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // 捕获并处理图像
        cv::Mat processed_frame = captureAndCrop(rga_crop::g_capture);
        
        // 检查捕获结果
        if (processed_frame.empty()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }
        
        // 将处理后的图像复制到共享内存（如果直接返回的不是共享内存引用）
        if (&processed_frame != &rga_crop::g_shared_frame.image) {
            processed_frame.copyTo(rga_crop::g_shared_frame.image);
        }
        
        // 更新帧ID（最后更新以确保图像已准备好）
        rga_crop::g_shared_frame.frame_id.store(next_frame_id++);
        
        // 计算处理时间
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration_capture = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        core_vars::g_frame_time = duration_capture.count();
        
        // 计数帧
        m_frameCount++;
        
        // 定期打印性能信息
        if (m_frameCount % printInterval == 0) {
            LOG_INFO("捕获性能: 帧率={} fps, 处理时间={} ms, 当前帧ID={}", 
                     1000 / std::max(1, duration_capture.count()), 
                     duration_capture.count(),
                     next_frame_id - 1);
        }
        
        // 适当休眠以控制采集速率
        if (duration_capture.count() < 5) {  // 如果处理时间少于5ms，则短暂休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    LOG_INFO("退出图像捕获线程");
}
```

### 2. 推理处理线程

```cpp
int run_inference_loop()
{
    // 初始化模型及环境
    std::string model_path = get_model_path(yolo_config::g_model_path);
    LOG_INFO("使用模型路径: {}", model_path);
    
    rknn_app_context_t yolo_ctx;
    if (init_inference_env(model_path, &yolo_ctx) != 0) {
        LOG_ERROR("初始化推理环境失败，程序退出");
        return -1;
    }
    
    LOG_INFO("开始推理循环...");
    int frameCount = 0;
    int64_t last_processed_id = -1;  // 上次处理的帧ID
    static int total_frames = 0;
    static int skipped_frames = 0;
    
    // 主循环
    while (thread_control::g_thread_running) {
        // 验证卡密状态（保留原有逻辑）
        if (jfkm_config::g_auth_status != 1) {
            LOG_ERROR("卡密验证失败，请检查卡密是否正确");
            std::this_thread::sleep_for(std::chrono::seconds(5));
            continue;
        }
        
        // 获取当前帧ID
        int64_t current_frame_id = rga_crop::g_shared_frame.frame_id.load();
        
        // 如果当前帧已经处理过，则跳过
        if (current_frame_id <= last_processed_id) {
            // 帧ID没有更新，短暂休眠并继续
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }
        
        // 记录推理开始时间
        auto start_total = std::chrono::high_resolution_clock::now();
        
        // 执行推理 - 使用共享帧中的图像
        bool inference_success = detect_objects_in_frame(&yolo_ctx, rga_crop::g_shared_frame.image);
        
        // 记录推理结束时间
        auto end_total = std::chrono::high_resolution_clock::now();
        
        // 计算并记录跳过的帧数
        int64_t frames_skipped = current_frame_id - last_processed_id - 1;
        if (frames_skipped > 0) {
            skipped_frames += frames_skipped;
            LOG_DEBUG("跳过了 {} 帧，当前处理帧ID: {}", frames_skipped, current_frame_id);
        }
        
        // 更新最后处理的帧ID
        last_processed_id = current_frame_id;
        
        // 计算推理耗时（毫秒）
        auto duration_total = std::chrono::duration_cast<std::chrono::milliseconds>(end_total - start_total);
        core_vars::g_infer_time = duration_total.count();
        
        // 性能统计
        frameCount++;
        total_frames++;
        if (frameCount % 100 == 0) {
            LOG_INFO("推理性能: 总帧数={}, 跳过帧数={}, 跳过率={:.2f}%, 推理耗时={}ms, 当前帧ID={}",
                     total_frames, skipped_frames, 
                     (skipped_frames * 100.0) / (total_frames + skipped_frames),
                     duration_total.count(),
                     current_frame_id);
        }
    }
    
    // 释放资源
    LOG_INFO("释放推理资源...");
    deinit_post_process();
    m_release_model_impl(&yolo_ctx);
    
    LOG_INFO("推理循环已退出");
    return 0;
}
```

### 3. 显示线程

```cpp
void RgaCapture::displayThread()
{
    LOG_INFO("进入图像显示线程");
    
    // 创建窗口
    cv::namedWindow(rga_crop::g_window_name, cv::WINDOW_NORMAL);
    cv::resizeWindow(rga_crop::g_window_name, rga_crop::g_crop_width, rga_crop::g_crop_height);
    
    int64_t last_displayed_id = -1;  // 上次显示的帧ID
    
    // 显示循环
    while (thread_control::g_thread_running && rga_crop::g_show_window) {
        // 获取当前帧ID
        int64_t current_frame_id = rga_crop::g_shared_frame.frame_id.load();
        
        // 只有当有新帧时才更新显示
        if (current_frame_id > last_displayed_id && !rga_crop::g_shared_frame.image.empty()) {
            cv::imshow(rga_crop::g_window_name, rga_crop::g_shared_frame.image);
            last_displayed_id = current_frame_id;
            
            int key = cv::waitKey(1);
            if (key == 27) { // ESC键
                LOG_INFO("用户按下ESC键，停止处理");
                thread_control::g_thread_running = false;
                break;
            }
        }
        
        // 短暂休眠以降低CPU占用
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 关闭窗口
    cv::destroyWindow(rga_crop::g_window_name);
    
    LOG_INFO("退出图像显示线程");
}
```

## 方案优势

1. **无歧义性**：每个帧都有唯一的帧ID，不会有两帧共享相同ID
2. **简单高效**：无需复杂的时间戳计算和比较
3. **精确追踪**：可以准确知道处理了哪些帧，跳过了哪些帧
4. **易于调试**：帧ID为简单递增整数，比时间戳更直观
5. **无时钟依赖**：不受系统时钟调整影响

## 实现细节说明

1. 捕获线程负责分配新的帧ID，确保帧ID与图像一致性
2. 推理线程通过比较帧ID确保只处理新帧，避免重复处理
3. 显示线程也使用帧ID确保只显示新帧，提高显示流畅度
4. 所有线程共享同一个图像缓冲区，但通过帧ID机制确保数据一致性
5. 各线程使用适当的休眠策略，避免过度占用CPU资源

## 对比时间戳方案的优势

时间戳方案的问题：
1. 时间戳可能不够精确，多个帧可能共享相同或非常接近的时间戳
2. 时间戳比较逻辑容易过于严格，导致大量有效帧被跳过
3. 依赖系统时钟，可能受时钟调整影响
4. 时间戳计算和比较相对复杂，不够直观

帧ID方案的优势：
1. 每帧拥有唯一ID，避免歧义
2. 比较逻辑简单清晰：current_id > last_id 即表示新帧
3. 不依赖系统时钟，更加可靠
4. 实现简单，调试方便，直观理解 