# 项目整体结构

## 主要组件

```
poco_serverdemo/
├── infer/              # 推理模块 - 目标检测与识别
├── backend/            # 后端模块 - WebSocket服务器与数据库
├── lkm/                # 键鼠模块 - 键盘鼠标模拟
├── blkm/               # 卡密模块 - 授权与加密
├── ThreadManager/      # 线程管理
├── 3rdparty/           # 第三方库
├── algorithm/          # 算法实现
├── utils/              # 工具函数
├── docs/               # 文档
├── logs/               # 日志
├── prisma/             # 数据库Schema
├── fpsdemo/            # FPS演示
├── main.cpp            # 主程序入口
├── CMakeLists.txt      # 主构建配置文件
└── *_build.sh          # 各模块构建脚本

## 构建脚本
- infer_build.sh        # 推理模块构建
- backend_build.sh      # 后端模块构建
- lkm_build.sh          # 键鼠模块构建
- blkm_build.sh         # 卡密模块构建
```

## 项目架构说明

本项目采用模块化设计，各子系统功能如下：

1. **推理系统**：负责游戏内目标检测和识别，包含模型推理、图像处理等核心功能
2. **后端系统**：提供WebSocket服务器、用户验证、数据库操作和游戏功能配置管理
3. **键鼠系统**：提供模拟键鼠操作的底层功能，为上层自瞄和游戏控制提供操作接口
4. **卡密系统**：负责软件授权验证、用户身份认证和加密通信等安全功能

## CMakeLists.txt 说明

主CMakeLists.txt文件是整个项目的构建配置核心，主要功能包括：

1. **基础配置**：设置C++17标准，启用O3优化
2. **依赖管理**：自动查找和配置所需的第三方库
   - OpenCV（核心、图像处理、视频IO等组件）
   - POCO库（网络、JSON处理）
   - GStreamer（可选，用于视频捕获）
   - spdlog（日志系统）
3. **子模块集成**：通过include命令包含各子模块的CMake配置
   - algorithm.cmake
   - rga_capture.cmake
   - infer.cmake
   - lkm.make
   - blkm.make
4. **构建目标**：定义多个可执行文件和库
   - BL（主应用程序）
   - bl_server（后端服务器）
   - bl_infer（推理测试程序）
   - lkm_test（键鼠测试程序）
   - blkm_test（卡密测试程序）
5. **资源管理**：配置构建后的模型文件和库文件复制

## 构建脚本说明

项目使用多个Shell脚本管理不同模块的构建过程：

1. **infer_build.sh**：推理模块构建脚本
   - 编译推理相关代码
   - 管理RKNN模型文件
   - 提供测试运行功能
   - 自动处理构建目录和权限

2. **backend_build.sh**：后端服务构建脚本
   - 编译WebSocket服务器
   - 支持启动/停止后端服务
   - 提供日志查看功能
   - 清理和重新构建选项

3. **lkm_build.sh**：键鼠模块构建脚本
   - 编译键鼠模拟功能
   - 配置设备权限
   - 提供测试功能

4. **blkm_build.sh**：卡密模块构建脚本
   - 编译卡密验证系统
   - 管理加密相关资源
   - 提供测试和部署功能

所有脚本均提供交互式菜单，方便开发者快速进行编译、测试和部署操作。