# WSQ流水线架构设计方案 v2.0

## 📋 项目概述

### SkippingRope - RK3588实时游戏辅助系统
基于瑞芯微RK3588平台的高性能实时目标检测与键鼠控制系统，采用YOLOV8算法和RKNN推理引擎。

### 核心功能模块
- **实时推理**：YOLOV8目标检测，支持多游戏模型切换
- **智能瞄准**：PID/FOV/FOVPID多模式算法
- **精确控制**：亚毫秒级键鼠响应，支持多种武器模式
- **后端服务**：WebSocket通信，配置管理，数据收集

## 🏗️ WSQ流水线核心架构

### WSQ流水线目标架构

#### 🔍 **当前架构分析**
```
主线程: video_capture.DequeueBuffer() → npu_work_queue.push(buffer_index) → 通知工作线程
工作线程: 从npu_work_queue获取 → TickFrame(buffer_index) → 设置全局变量 
键鼠线程: 轮询全局变量 → PID/FOV计算 → 发送命令
```

#### 🎯 **WSQ优化架构**  
```
主线程: video_capture.DequeueBuffer() → npu_work_queue.push(buffer_index) → 通知工作线程
工作线程: 从npu_work_queue获取 → TickFrame(buffer_index) → [CommandWSQ.Push] 
键鼠线程: [CommandWSQ.Pop] → 条件执行 → 发送命令
```

### 关键架构澄清

#### ✅ **保持不变的部分**
- **图像捕获与推理集成**: 继续使用现有的`video_capture->DequeueBuffer()`方式
- **工作线程模式**: 保持现有的`npu_work_queue`和多NPU工作线程架构
- **帧资源管理**: 保持现有的`FrameResource`和缓冲区管理

#### 🔄 **需要改动的部分**  
- **推理输出方式**: 从设置全局变量改为推送`CommandWSQ`队列
- **键鼠消费方式**: 从轮询全局变量改为消费`CommandWSQ`队列
- **🎯 关键优化**: **完全消除全局变量依赖**，所有数据通过WSQ命令传递

### WSQ改造涉及的核心模块

#### 🎯 推理模块 (infer/core/aimbot/) - WSQ命令生产者
```
infer/core/aimbot/
├── aimbot_fps.cpp               # 【改造】在TickFrame函数中新增WSQ推送
├── aimbot_fps.h                 # 【改造】添加CommandWSQ队列成员变量
└── infer_collector.h/cpp        # 【保持】继续使用FindNearestTarget()
```

#### 🖱️ 键鼠控制模块 (lkm/) - WSQ命令消费者  
```
lkm/
├── keymouse.cpp                 # 【重构】新增WSQExecutorLoop()函数
├── keymouse.h                   # 【重构】添加WSQExecutorLoop()声明
├── LkmApi.h/cpp                 # 【保持】继续使用sendCommand()等接口
└── KeyMouseConfig.h/cpp         # 【保持】继续使用PID控制器和配置管理
```

#### 🧵 线程管理模块 (ThreadManager/) - WSQ架构协调者
```
ThreadManager/
├── infer_server.cpp             # 【改造】传递CommandWSQ队列引用给AimbotFPS
├── lkm_server.cpp               # 【重构】启动WSQExecutorLoop替代4个键鼠线程
├── lkm_server.h                 # 【更新】更新线程管理接口
└── main.cpp协调                 # 【统一】通过ThreadManager统一管理所有服务
```

#### 🧵 线程管理 (ThreadManager/ + main.cpp)
```
ThreadManager/                   # 【WSQ改造】统一线程管理架构
├── infer_server.h/cpp          # 【保持】推理服务管理
│   └── InferServer::Run()      # video_capture + npu_work_queue + TickFrame
├── lkm_server.h/cpp            # 【重构】键鼠服务管理  
│   ├── 保持: listenThreadLoop  # 热键监听线程(2ms)
│   ├── 新增: WSQExecutorLoop   # WSQ执行器线程(1ms)
│   └── 移除: 4个键鼠线程       # aimLoop, commandServerLoop, autoFireLoop, fovMeasureLoop
├── blkm_server.h/cpp           # 【保持】卡密验证服务
└── web_server.h/cpp            # 【保持】WebSocket服务器

main.cpp                        # 【统一入口】服务启动协调
├── InferServer::Run()          # 推理服务 + WSQ生产者
├── startLkmThreads()           # 键鼠服务 + WSQ消费者
├── startBlkmThreads()          # 卡密验证服务
└── WebServer::start()          # Web服务器
```

### 📊 WSQ改造要点

#### 🎯 **核心改造内容**
| 改造点 | 现有方式 | WSQ改造方式 | 改造文件 |
|-------|---------|------------|----------|
| **数据传递** | 全局变量 `core_vars::mouse_x/y` | WSQ队列 `WSQCommand` | `aimbot_fps.cpp` |
| **瞄准计算** | `aimLoop()` 1ms轮询 | `WSQExecutorLoop()` 队列消费 | `keymouse.cpp` |
| **命令发送** | `commandServerLoop()` 1ms轮询 | 合并到 `WSQExecutorLoop()` | `keymouse.cpp` |
| **自动开火** | `autoFireLoop()` 1ms轮询 | 合并到 `WSQExecutorLoop()` | `keymouse.cpp` |
| **线程管理** | 6线程 (推理+热键+4键鼠) | 4线程 (推理+热键+WSQ执行器) | `ThreadManager/lkm_server.cpp` |
| **服务协调** | 分散在main.cpp中 | 统一ThreadManager管理 | `ThreadManager/*_server.cpp` |
| **队列管理** | 无队列机制 | 全局WSQ队列统一管理 | `utils/gmodels.h/cpp` |
| **变量定义** | 分散在各模块中 | 统一在models中定义 | `utils/gmodels.h` |

#### 🚀 **性能提升预期**
| 指标 | 现有架构 | WSQ架构 | 提升幅度 |
|------|---------|---------|----------|
| **响应延迟** | 3-4ms (轮询+处理) | <1.2ms (队列+处理) | 60-70% ⬇️ |
| **线程数量** | 6线程 | 4线程 | 33% ⬇️ |
| **CPU使用率** | 4线程高频轮询 | 1线程事件驱动 | 20-30% ⬇️ |
| **代码维护性** | 4线程逻辑分散 | 统一执行器集中 | 显著提升 |

## 🚀 WSQ流水线设计

### 1. 当前架构问题
- **时序不一致**：推理线程(60Hz) 与键鼠线程(1000Hz) 频率不匹配
- **数据竞争**：4个线程直接访问全局变量存在竞争
- **响应延迟**：轮询模式存在1-2ms固有延迟
- **逻辑冗余**：重复的目标判断和部位计算

### 2. 目标架构
```
推理线程(60Hz) → [WSQQueue] → 键鼠执行线程(1000Hz)
     │                    │                        │
   YOLO推理          命令缓冲队列              条件执行
   目标查找          (无锁设计)              集成控制
   部位计算          事件驱动                所有功能
```

### 3. WSQ核心数据结构 (统一在models中定义)

#### WSQ命令结构定义
```cpp
// 在utils/gmodels.h中添加WSQ相关定义
namespace wsq_models {

// WSQ命令数据结构
struct WSQCommand {
    // 基础信息
    bool has_target = false;              // 是否检测到目标
    int64_t frame_id = 0;                 // 帧ID，用于去重
    std::chrono::high_resolution_clock::time_point timestamp; // 时间戳
    
    
    // 目标信息
    double target_x = 0, target_y = 0;    // 目标中心坐标
    double aim_y = 0;                     // 锁定部位调整后Y坐标
    double target_distance = 0;           // 目标距离(用于近远端判断)
    
    // 移动控制
    int move_x = 0, move_y = 0;           // 预计算移动量
    bool should_move = false;             // 是否需要移动
    
    // 开火控制
    bool should_fire = false;             // 是否应该开火
    bool in_fire_range = false;           // 是否在开火范围内
    
    // 特殊功能
    bool flash_detected = false;          // 背闪检测
    bool weapon_switch = false;           // 切枪功能
    bool fov_measure = false;             // FOV测量
    
    // 配置快照(避免运行时读取全局变量)
    std::string ai_mode;                  // AI模式快照
    std::string lock_position;            // 锁定部位快照
};

// 使用现有的WorkStealingQueue模板
using CommandWSQ = WorkStealingQueue<WSQCommand>;

// WSQ队列全局实例声明
extern std::unique_ptr<CommandWSQ> g_command_queue;

} // namespace wsq_models
```

### 4. WSQ流水线基本实现思路

#### 推理线程改造要点 (aimbot_fps.cpp)
```cpp
void AimbotFPS::TickFrame(u32 buffer_index) {
    // ... 现有的YOLO推理逻辑保持不变 ...
    // TargetInfo nearest = InferCollector::FindNearestTarget(npu_resource->boxes, out_width, out_height);
    
    // 🆕 新增：生成WSQ命令并推送到队列
    if (nearest.class_id != -1) {
        wsq_models::WSQCommand cmd;
        
        // 基础信息
        cmd.frame_id = frame_count;
        cmd.has_target = true;
        cmd.timestamp = std::chrono::high_resolution_clock::now();
        
        // 目标信息
        cmd.target_x = nearest.x;
        cmd.target_y = nearest.y;
        cmd.target_distance = std::sqrt(dx*dx + dy*dy); // 计算目标距离
        
        // 锁定部位计算 (使用现有逻辑)
        cmd.aim_y = nearest.y; // 默认中心点
        if (webui::function::g_lock_position == "头部") {
            cmd.aim_y = nearest.y1 + nearest.height * (webui::aim::g_head_height / 100.0);
        }
        
        // 移动控制
        double dx = nearest.x - center_x;
        double dy = cmd.aim_y - center_y;
        cmd.move_x = static_cast<int>(dx / webui::fov::g_fov);
        cmd.move_y = static_cast<int>(dy / webui::fov::g_fov);
        cmd.should_move = (std::abs(dx) > 1 || std::abs(dy) > 1);
        
        // 开火控制
        cmd.should_fire = (std::abs(dx) <= range_x && std::abs(dy) <= range_y);
        cmd.in_fire_range = cmd.should_fire;
        
        // 配置快照 (避免WSQ消费者读取全局变量)
        cmd.ai_mode = webui::function::g_ai_mode;
        cmd.lock_position = webui::function::g_lock_position;
        
        // 推送到队列 (替代设置全局变量)
        wsq_models::g_command_queue->push(std::move(cmd));
    }
    
    // 🚀 核心改进：不再设置全局变量
    // ❌ core_vars::mouse_x/y, core_vars::g_is_fire
    // 💡 所有数据通过WSQ队列传递！
}
```

#### 键鼠执行线程改造要点 (keymouse.cpp)
```cpp
// 新增WSQ队列消费循环，替代4线程轮询
void WSQExecutorLoop(LkmAPI& api) {
    while (thread_control::g_thread_running) {
        // 从WSQ队列获取最新命令
        auto cmd_opt = wsq_models::g_command_queue->steal();
        if (!cmd_opt) {
            std::this_thread::sleep_for(std::chrono::microseconds(500)); // 短暂休眠
            continue;
        }
        wsq_models::WSQCommand cmd = *cmd_opt;
        
        // 条件执行 (保留现有逻辑)
        if (cmd.has_target && core_vars::mouse_pressed) {
            if (cmd.ai_mode == "FOV") {
                // FOV模式：直接使用预计算移动量
                api.sendCommand(cmd.move_x, cmd.move_y);
            } else if (cmd.ai_mode == "PID") {
                // PID模式：使用队列数据进行实时PID计算
                // 传递目标位置给PID控制器
                auto [move_x, move_y] = calculatePIDMovement(
                    cmd.target_x, cmd.target_y, cmd.target_distance);
                api.sendCommand(move_x, move_y);
            } else if (cmd.ai_mode == "FOVPID") {
                // FOVPID模式：结合FOV和PID
                // ... 使用现有FOVPID逻辑 ...
            }
        }
        
        // 自动开火 (使用队列开火判断)
        if (cmd.should_fire && core_vars::mouse_pressed && IsTriggerEnabled()) {
            api.sendCommand(0, 0, lkm::MOUSE_LEFT);
            // ... 现有开火逻辑 ...
        }
        
        // 特殊功能处理
        if (cmd.flash_detected) {
            // 背闪躲避逻辑
            handleFlashAvoidance(api);
        }
        
        if (cmd.weapon_switch && webui::function::g_weapon_switch) {
            // 切枪功能
            handleWeaponSwitch(api);
        }
        
        if (cmd.fov_measure) {
            // FOV测量功能
            handleFOVMeasurement(api, cmd);
        }
    }
}
```

## 🎯 实施步骤 (基于ThreadManager统一管理的渐进式改造)

### Step 1: 准备WSQ数据结构 ✅
在 `utils/gmodels.h` 中添加 `wsq_models` 命名空间，定义WSQCommand结构和CommandWSQ类型别名，统一管理WSQ相关变量。

### Step 2: 初始化WSQ全局队列 (utils/gmodels.cpp)
- 在 `gmodels.cpp` 中实现 `wsq_models::g_command_queue` 全局队列实例
- 在程序启动时初始化队列 (main.cpp或InferServer构造函数中)
- 确保队列在推理和键鼠服务中都可访问

### Step 3: 修改推理引擎 (infer/core/aimbot/aimbot_fps.cpp)
- 在 `TickFrame` 函数中使用 `wsq_models::g_command_queue` 全局队列
- 添加WSQ推送逻辑，包含完整的命令数据和配置快照
- 保持所有现有YOLO推理逻辑不变

### Step 4: 重构键鼠服务 (ThreadManager/lkm_server.cpp)
- 修改 `startLkmThreads()` 函数，移除4个键鼠线程的启动代码
- 新增WSQ执行器线程启动: `WSQExecutorLoop`
- 不需要传递队列引用，直接使用全局队列

### Step 5: 实现WSQ执行器 (lkm/keymouse.cpp)  
- 添加 `WSQExecutorLoop` 函数，使用 `wsq_models::g_command_queue` 消费命令
- 整合现有4个线程的功能：瞄准、开火、特殊功能到一个循环中
- 使用命令中的配置快照，避免读取全局配置变量

### Step 6: 调整服务启动和队列初始化
- 在main.cpp中初始化 `wsq_models::g_command_queue`
- 确保推理服务和键鼠服务都能访问同一个队列实例
- 保持其他服务启动顺序不变

### Step 7: 渐进式测试验证
- 可保留原有线程作为编译选项，便于对比测试
- 验证WSQ模式的正确性和性能提升
- 确保所有功能模块正常工作



## 🎯 ThreadManager在WSQ架构中的核心作用

### 统一服务管理架构
```
main.cpp (程序入口)
    ↓
ThreadManager/ (服务管理层)
    ├── InferServer (推理服务) → WSQ生产者
    ├── LkmServer (键鼠服务) → WSQ消费者  
    ├── BlkmServer (卡密服务) → 独立服务
    └── WebServer (Web服务) → 独立服务
```

### ThreadManager的WSQ协调职责

#### 1. **全局队列生命周期管理**
- `wsq_models::g_command_queue` 作为全局队列实例，统一在models中管理
- 在main.cpp程序启动时初始化队列
- 确保队列在程序生命周期内持续存在，避免内存泄漏

#### 2. **统一队列访问模式**
```cpp
// utils/gmodels.h - 统一定义
namespace wsq_models {
    extern std::unique_ptr<CommandWSQ> g_command_queue;
}

// utils/gmodels.cpp - 全局实例
namespace wsq_models {
    std::unique_ptr<CommandWSQ> g_command_queue = nullptr;
}

// main.cpp - 初始化
int main() {
    // 初始化WSQ队列
    wsq_models::g_command_queue = std::make_unique<wsq_models::CommandWSQ>(1024);
    
    // 启动各服务...
}

// aimbot_fps.cpp - 生产者使用
wsq_models::g_command_queue->push(std::move(cmd));

// keymouse.cpp - 消费者使用  
auto cmd_opt = wsq_models::g_command_queue->steal();
```

#### 3. **简化的启动顺序**
```cpp
// main.cpp中的启动顺序
int main() {
    // 1. 初始化WSQ全局队列
    wsq_models::g_command_queue = std::make_unique<wsq_models::CommandWSQ>(1024);
    
    // 2. 启动推理服务(WSQ生产者)
    InferServer inferServer;
    inferServer.Run();
    
    // 3. 启动键鼠服务(WSQ消费者) - 无需传递队列引用
    thread_manager::startLkmThreads();
    
    // 4. 启动其他独立服务
    thread_manager::startBlkmThreads();
    webServer.start();
}
```

#### 4. **线程生命周期管理**
- 统一管理所有服务线程的启动和停止
- 确保WSQ生产者在消费者之前启动
- 优雅关闭时确保消费者先停止，生产者后停止

### WSQ架构的ThreadManager优势

#### 🔧 **架构清晰度**
- 服务职责明确：推理服务专注推理+WSQ生产，键鼠服务专注WSQ消费+执行
- 队列管理统一：全局队列在models中统一管理，避免服务间耦合
- 访问模式简单：所有服务直接使用全局队列，无需复杂的引用传递

#### 🚀 **可维护性**
- 统一的服务管理接口，便于调试和监控
- 清晰的模块边界，便于单独测试和优化
- 标准化的启动/停止流程

#### 📊 **扩展性**
- 可以轻松添加新的WSQ消费者服务
- 支持多个队列的管理 (如果需要多种命令类型)
- 便于添加队列监控和性能统计

## 💡 总结

基于ThreadManager统一管理的WSQ流水线架构专注于优化推理→键鼠的数据传递：

### 🔧 **核心改造方案**
1. **保持现有架构**: 图像捕获与推理集成继续使用现有的`video_capture.DequeueBuffer()`和`npu_work_queue`
2. **使用现有队列**: 基于`infer/base/wsq.h`中的`WorkStealingQueue`模板，无需创建新文件  
3. **保留现有配置**: 继续使用`webui::function::g_ai_mode`等全局配置变量
4. **只改数据传递**: 用WSQ队列传递目标位置和开火状态，替代`core_vars::mouse_x/y`等数据全局变量
5. **线程架构优化**: 从6线程(推理+热键+4键鼠)减少到4线程(推理+热键+WSQ执行器)

### 📊 **架构对比**
**现有方式**:
```
推理线程: TickFrame → 设置全局变量
键鼠线程: 轮询全局变量 → PID/FOV计算 → 发送命令
```

**WSQ优化方式**:
```
推理线程: TickFrame → Push CommandWSQ
键鼠线程: Pop CommandWSQ → 条件执行 → 发送命令  
```

### 🎯 **WSQ队列特性**
- **单生产者-单消费者**: 推理线程生产，键鼠线程消费
- **无锁设计**: 高性能环形缓冲区，支持240Hz推理频率
- **事件驱动**: 消除轮询延迟，提升响应速度60-70%

### 🎯 **WSQ优化优势**
相比现有架构，WSQ改造具有：
- **更低的延迟**: 无锁队列传递，消除1-2ms轮询延迟
- **更少的线程**: 从6线程优化为4线程，减少上下文切换开销
- **更简洁的逻辑**: 统一的命令执行器，集中处理所有键鼠功能
- **保持兼容性**: 继续使用现有AI模式配置、PID控制器、配置管理等
- **🚀 消除数据竞争**: 移除以下数据传递的全局变量依赖：
  - `core_vars::mouse_x/y` - 改为队列传递目标偏移量
  - `core_vars::move_x/y` - 改为队列传递预计算移动量
  - `core_vars::g_is_fire` - 改为队列传递开火判断状态
  - 保留: `webui::*`配置变量、`core_vars::mouse_pressed`等控制变量

---

**文档版本**: v6.0 - WSQ流水线架构(ThreadManager统一管理)  
**更新日期**: 2025-01-07  
**管理方式**: 基于ThreadManager统一服务管理，main.cpp作为统一入口  
**架构模式**: InferServer(WSQ生产者) → [CommandWSQ队列] → LkmServer(WSQ消费者)  
**队列实现**: 基于现有`WorkStealingQueue`模板，使用SPSCQueue优化  
**兼容性**: 保持现有AI模式、PID控制器、配置管理不变  
**预期性能**: 线程数减少33% (6→4)，响应延迟降低60-70%，架构清晰度显著提升