// 共享数据结构
struct Frame {
    cv::Mat image;                   // 图像数据
    std::atomic<long> timestamp{0};  // 捕获时间戳，原子类型
};

// 全局变量
Frame g_shared_frame;                             // 共享帧缓冲区
std::atomic<long> g_last_inference_end_time{0};   // 上次推理完成时间

// 图像捕获线程 - 简单直接
void captureThread() {
    while(running) {
        // 捕获新图像到共享帧
        captureImage(g_shared_frame.image);  // 假设耗时3ms
        
        // 更新时间戳 (最后更新，作为"帧已准备好"的信号)
        g_shared_frame.timestamp.store(getCurrentTimestamp(), std::memory_order_release);
        
        // 捕获频率控制
        std::this_thread::sleep_for(std::chrono::milliseconds(3));
    }
}

// 推理线程
void inferenceThread(int threadId) {
    while(running) {
        // 获取当前帧时间戳
        long current_timestamp = g_shared_frame.timestamp.load(std::memory_order_acquire);
        
        // 核心判断：如果帧时间戳小于等于上次推理完成时间，则跳过
        long last_end_time = g_last_inference_end_time.load(std::memory_order_relaxed);
        if (current_timestamp <= last_end_time) {
            // 这是一个已经过时的帧，跳过
            LOG_INFO("线程 #{}: 跳过过时帧 帧时间={}, 上次推理完成时间={}", 
                     threadId, current_timestamp, last_end_time);
            
            // 短暂休眠避免CPU占用过高
            std::this_thread::sleep_for(std::chrono::microseconds(100));
            continue;
        }
        
        // 直接使用共享图像进行推理
        LOG_INFO("线程 #{}: 开始推理 帧时间={}", threadId, current_timestamp);
        
        // 注意：这里直接使用g_shared_frame.image，可能存在推理过程中图像被更新的风险
        auto result = processImage(g_shared_frame.image);
        
        // 记录推理完成时间
        long end_time = getCurrentTimestamp();
        g_last_inference_end_time.store(end_time, std::memory_order_relaxed);
        
        // 保存结果供移动线程使用
        saveResult(result, current_timestamp, end_time);
        
        LOG_INFO("线程 #{}: 完成推理 帧时间={}, 推理完成时间={}, 耗时={}ms", 
                 threadId, current_timestamp, end_time, end_time - current_timestamp);
    }
}
