#pragma once

#include <string>
#include <cstdint>
#include "../utils/gmodels.h"
#include "../algorithm/pid_controller.h"
// 移除对MouseFov.hpp的直接包含，改为前向声明
// #include "../algorithm/MouseFov.hpp"
#include "LkmApi.h"
#include <chrono>

// 前向声明MouseFov类
class MouseFov;

namespace lkm_tools {

/**
 * @brief 键鼠配置管理类，负责管理键鼠相关的全局配置和状态
 */
class KeyMouseConfig {
public:
    /**
     * @brief 获取单例实例
     * @return KeyMouseConfig& 单例实例引用
     */
    static KeyMouseConfig& getInstance();

    /**
     * @brief 更新PID控制器参数
     */
    void updatePidParameters();

    /**
     * @brief 获取鼠标按键映射并匹配配置
     * @param button_state 鼠标按键状态
     * @return bool 是否成功匹配配置
     */
    bool matchHotkey(uint8_t button_state);

    /**
     * @brief 获取当前鼠标状态
     * @return const lkm::MouseState& 当前鼠标状态的常量引用
     */
    const lkm::MouseState& getCurrentMouseState() const;

    /**
     * @brief 设置当前鼠标状态
     * @param state 新的鼠标状态
     */
    void setCurrentMouseState(const lkm::MouseState& state);

    /**
     * @brief 获取近距离PID控制器
     * @return infer::algorithm::PidController& 近距离PID控制器引用
     */
    infer::algorithm::PidController& getNearPidController();
    
    /**
     * @brief 获取远距离PID控制器
     * @return infer::algorithm::PidController& 远距离PID控制器引用
     */
    infer::algorithm::PidController& getFarPidController();

private:
    // 私有构造函数，防止外部创建实例
    KeyMouseConfig();
    // 禁用拷贝构造和赋值操作
    KeyMouseConfig(const KeyMouseConfig&) = delete;
    KeyMouseConfig& operator=(const KeyMouseConfig&) = delete;

    // 鼠标状态
    lkm::MouseState currentMouseState_;
    
    // 命令轮询间隔
    static const int COMMAND_POLL_INTERVAL_MS = 1;
    
    // PID控制器实例
    infer::algorithm::PidController pidNear_;
    infer::algorithm::PidController pidFar_;
};

} // namespace lkm_tools 