#include "LkmApi.h"
#include <algorithm>  // 为std::fill和std::min添加

// 初始化LKM设备（合并了openPort和configurePort的功能）
bool LkmAPI::initLkm(const std::string& port_name, 
                      int baud_rate, 
                      int data_bits, 
                      char parity, 
                      int stop_bits) {
    // 记录初始化参数
    LOG_INFO("初始化LKM设备: 端口={}, 波特率={}bps, 数据位={}, 校验位={}, 停止位={}", 
             port_name, baud_rate, data_bits, parity, stop_bits);
    
    // 尝试次数计数
    int attempt = 0;
    
    // 无限循环尝试连接，直到成功
    while (true) {
        attempt++;
        try {
            LOG_INFO("尝试连接串口 (第 {} 次)...", attempt);
            
            // 如果已经打开，先关闭
            if (m_serial_port.isOpen()) {
                m_serial_port.close();
            }
            
            // 初始化并打开串口
            m_serial_port.init(port_name.c_str());
            if (!m_serial_port.open()) {
                LOG_ERROR("串口打开失败，将在 {} ms 后重试", RECONNECT_DELAY_MS);
                std::this_thread::sleep_for(std::chrono::milliseconds(RECONNECT_DELAY_MS));
                continue;
            }
            
            // 配置串口参数
            m_serial_port.init(m_serial_port.getPortName(),
                             baud_rate,
                             itas109::ParityNone,
                             itas109::DataBits8,
                             itas109::StopOne);
            
            // 发送测试命令验证连接
            std::vector<uint8_t> testPacket = {0xFF, 0xAA, 0x55, 0xFE, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
            // 计算正确的CRC：前14字节累加和+0xAA
            uint8_t crc = 0xAA;
            for(size_t i = 0; i < 14; i++) {
                crc += testPacket[i];
            }
            testPacket[14] = crc;
            
            int bytes_written = m_serial_port.writeData(testPacket.data(), testPacket.size());
            
            if (bytes_written < 0) {
                LOG_ERROR("连接测试失败，将在 {} ms 后重试", RECONNECT_DELAY_MS);
                std::this_thread::sleep_for(std::chrono::milliseconds(RECONNECT_DELAY_MS));
                continue;
            }
            
            LOG_INFO("LKM设备初始化成功 (第 {} 次尝试)", attempt);
            webui::header::keyMouseStatus = true;
            return true;
            
        } catch (const std::exception& e) {
            m_last_error = e.what();
            LOG_ERROR("LKM设备初始化异常: {}, 将在 {} ms 后重试", e.what(), RECONNECT_DELAY_MS);
            std::this_thread::sleep_for(std::chrono::milliseconds(RECONNECT_DELAY_MS));
        }
    }
    
    // 注意：此处永远不会执行到，因为循环会一直重试直到成功
    return false;
}


// 读取鼠标状态
bool LkmAPI::readMouseState(lkm::MouseState& state) {
    uint8_t buffer[64];
    
    int bytes_read = m_serial_port.readData(buffer, sizeof(buffer));
    
    // 检查是否需要重连
    if (bytes_read < 0) {
        LOG_WARNING("读取数据失败，尝试重连串口");
        return reconnectPort();
    }
    
    if (bytes_read == 0) {
        return false;
    }

    // 解析按键状态
    state.buttons = (bytes_read > 1 && buffer[bytes_read - 1] == 0) ? 0 : buffer[0];
    state.x = 0;
    state.y = 0;
    state.wheel = 0;
    
    return true;
}

// 通用命令发送函数 - 严格按照C#文档的15字节协议格式
bool LkmAPI::sendCommand(int16_t x, int16_t y, uint8_t button, int8_t wheel) {
    // LOG_INFO("发送闪电键鼠命令: X={}, Y={}, 按键={:#x}, 滚轮={}", 
    //          x, y, button, wheel);
    
    // 设置鼠标状态
    m_mouse_state.x = x;
    m_mouse_state.y = y;
    m_mouse_state.buttons = button;
    m_mouse_state.wheel = wheel;
    
    // 发送闪电键鼠数据包
    bool result = sendPacket();
    
    if (!result) {
        LOG_ERROR("闪电键鼠命令发送失败");
        return false;
    }
    
    return true;
}

// 键盘命令发送函数 - 闪电键鼠协议不支持键盘数据，保留接口以兼容现有代码
bool LkmAPI::sendKeyboardCommand(uint8_t modifiers, const std::vector<uint8_t>& keys) {
    LOG_WARNING("闪电键鼠协议不支持键盘数据传输，此函数调用被忽略");
    return false;  // 闪电键鼠不支持键盘操作
}

// 构建15字节闪电键鼠数据包 - 严格按照C#文档格式
std::vector<uint8_t> LkmAPI::buildLightningPacket() const {
    std::vector<uint8_t> packet(PACKET_SIZE, 0);  // 初始化15字节数据包
    
    // 帧头 (4字节) - FF AA 55 FE
    std::copy(std::begin(LIGHTNING_PROTOCOL_HEADER), std::end(LIGHTNING_PROTOCOL_HEADER), packet.begin());
    
    // 按键状态 (5字节) - 按照C#文档定义
    packet[4] = (m_mouse_state.buttons & MOUSE_LEFT) ? 1 : 0;    // 左键状态
    packet[5] = (m_mouse_state.buttons & MOUSE_RIGHT) ? 1 : 0;   // 右键状态
    packet[6] = (m_mouse_state.buttons & MOUSE_MIDDLE) ? 1 : 0;  // 中键状态
    packet[7] = (m_mouse_state.buttons & MOUSE_SIDE1) ? 1 : 0;   // 侧键1状态
    packet[8] = (m_mouse_state.buttons & MOUSE_SIDE2) ? 1 : 0;   // 侧键2状态
    
    // 坐标数据 (4字节) - 小端序，按照C#文档格式
    uint16_t x_coord = static_cast<uint16_t>(m_mouse_state.x);
    uint16_t y_coord = static_cast<uint16_t>(m_mouse_state.y);
    packet[9]  = x_coord & 0xFF;        // X坐标低字节
    packet[10] = (x_coord >> 8) & 0xFF; // X坐标高字节
    packet[11] = y_coord & 0xFF;        // Y坐标低字节
    packet[12] = (y_coord >> 8) & 0xFF; // Y坐标高字节
    
    // 滚轮数据 (1字节) - 有符号字节
    packet[13] = static_cast<uint8_t>(m_mouse_state.wheel);
    
    // 计算CRC校验 (1字节) - 前14字节累加和+0xAA
    packet[14] = calculateCRC(packet);
    
    return packet;
}

// 计算CRC校验和
uint8_t LkmAPI::calculateCRC(const std::vector<uint8_t>& packet) {
    uint8_t crc = CRC_INIT_VALUE;
    for(size_t i = 0; i < PACKET_SIZE - 1; i++) {
        crc += packet[i];
    }
    return crc;
}

// 清除相对值
void LkmAPI::clearRelativeValues() {
    m_mouse_state.x = 0;
    m_mouse_state.y = 0;
    m_mouse_state.wheel = 0;
}

// 发送闪电键鼠数据包
bool LkmAPI::sendPacket() {
    auto packet = buildLightningPacket();
    
    // 发送15字节数据包
    int bytes_written = m_serial_port.writeData(packet.data(), packet.size());
    
    // 检查是否需要重连
    if (bytes_written < 0) {
        LOG_WARNING("发送闪电键鼠数据失败，尝试重连串口");
        if (reconnectPort()) {
            // 重连成功后重试发送
            bytes_written = m_serial_port.writeData(packet.data(), packet.size());
        }
    }
    
    if (static_cast<size_t>(bytes_written) != packet.size()) {
        LOG_ERROR("闪电键鼠数据包发送失败，预期发送 {} 字节，实际发送 {} 字节",
                 packet.size(), bytes_written);
        return false;
    }
    
    return true;
}

// 重连串口
bool LkmAPI::reconnectPort() {
    std::string port_name = m_serial_port.getPortName();
    
    // 先关闭现有连接
    if (m_serial_port.isOpen()) {
        m_serial_port.close();
    }
    
    // 调用初始化函数进行重连，使用闪电键鼠标准波特率
    return initLkm(port_name, STANDARD_BAUD_RATE, 8, 'N', 1);
}
