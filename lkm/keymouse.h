#pragma once

// LkmApi.h 已经包含了 CSerialPort/SerialPortInfo.h，所以我们不需要重复包含
// #include <CSerialPort/SerialPortInfo.h>
#include "LkmApi.h"
#include "KeyMouseConfig.h"
#include <random>  // 添加随机数生成器支持

namespace lkm_tools {
    
    //-----------------------------
    // 监听循环相关函数
    //-----------------------------
    
    // 监听线程循环函数
    void listenThreadLoop(LkmAPI& api);
    
    // 瞄准计算函数 - 可在WSQ工作线程中调用
    std::pair<int, int> calculateAimMovement(double target_offset_x, double target_offset_y, double target_distance, std::mt19937& gen);
    
    //-----------------------------
    // 键鼠服务器相关函数
    //-----------------------------
    
    // FOV测量处理函数 - 独立的循环函数（在监听线程中调用）
    void handleFOVMeasurement(LkmAPI& api);
    
    // 背闪防护处理函数 - 专门处理背闪检测和躲避
    void handleFlashShield(LkmAPI& api, std::mt19937& gen);
    
    // 自动开火处理函数 - 全新非阻塞方式，基于时间间隔控制
    bool handleAutoFire(LkmAPI& api, std::mt19937& gen, bool should_fire, 
                       std::chrono::steady_clock::time_point& last_fire_time);
    
    // WSQ执行器循环函数 - 专门用于瞄准
    void WSQExecutorLoop(LkmAPI& api);
    
    // 辅助功能循环线程 - 处理FOV、开火、背闪
    void auxiliaryFunctionsLoop(LkmAPI& api);
    
    // WSQ多线程架构 - 完全模仿NPU架构
    void CreateWSQWorkerThreads(LkmAPI& api);
    void StopWSQWorkerThreads();
    u32 NextWSQThreadIndex();
     
}

// 以下被移动到ThreadManager/lkm_server.h和lkm_server.cpp中：
// void startListenLoop(LkmAPI& api, MouseStateCallback callback);
// void stopListenLoop();
// bool isListenLoopRunning();
// bool startCommandServer(LkmAPI& api);
// void stopCommandServer();
// bool isCommandServerRunning();

// 备选方案：独立FOV测量线程（与WSQ架构冲突，不推荐）
// void fovMeasureLoop(LkmAPI& api);