#pragma once

#include "../utils/logger.h"
#include "../utils/gmodels.h"
#include <string>
#include <vector>
#include <memory>
#include <CSerialPort/SerialPort.h>
#include <sstream>
#include <iomanip>
#include <thread>
#include <chrono>
#include <linux/input.h>
#include <linux/uinput.h>
#include <fcntl.h>
#include <unistd.h>
#include <cstring>
#include <CSerialPort/SerialPortInfo.h>
#include <iostream>
#include <random>

// 使用全局定义的itas109命名空间和lkm命名空间
using namespace itas109;
using namespace lkm;

// 闪电键鼠使用统一的15字节协议格式，无需区分数据包类型

class LkmAPI {
public:
    LkmAPI() = default;
    ~LkmAPI() {
        if (m_serial_port.isOpen()) {
            m_serial_port.close();
        }
    }

    // 串口实例
    CSerialPort m_serial_port;
    
    // 鼠标和键盘状态
    lkm::MouseState m_mouse_state;
    lkm::KeyboardState m_keyboard_state;

    // 初始化LKM设备（合并了openPort和configurePort的功能）
    bool initLkm(const std::string& port_name, 
                int baud_rate = 230400,  // 修改为闪电键鼠标准波特率
                int data_bits = 8, 
                char parity = 'N', 
                int stop_bits = 1);
    
    // 监听相关
    bool readMouseState(lkm::MouseState& state);
    
    // 通用命令发送函数 - 严格按照C#文档的15字节协议格式
    bool sendCommand(int16_t x = 0, int16_t y = 0, uint8_t button = 0, int8_t wheel = 0);
    
    // 键盘命令发送函数 - 闪电键鼠协议不支持键盘数据，保留接口以兼容现有代码
    bool sendKeyboardCommand(uint8_t modifiers = 0, const std::vector<uint8_t>& keys = {});
    
    // 数据包发送 - 公开以便lkm_tools命名空间访问
    bool sendPacket();

    const std::string& getLastError() const { return m_last_error; }

private:
    // 构建15字节闪电键鼠数据包 - 严格按照C#文档格式
    std::vector<uint8_t> buildLightningPacket() const;
    
    // 计算CRC校验和 - 前14字节累加和+0xAA
    static uint8_t calculateCRC(const std::vector<uint8_t>& packet);
    
    // 清除相对值
    void clearRelativeValues();

    std::string m_last_error;
    
    // 重连方法
    bool reconnectPort();
};