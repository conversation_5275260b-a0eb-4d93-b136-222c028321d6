/*-------------------------------------------
                Includes
-------------------------------------------*/
#include <signal.h>
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <limits>
#include <algorithm>

#include "LkmApi.h"
#include "keymouse.h"
#include "../utils/logger.h"
#include "../utils/gmodels.h"
#include "../ThreadManager/lkm_server.h"

/*-------------------------------------------
                Signal Handler
-------------------------------------------*/
void handle_sig(int signo) {
    LOG_INFO("程序收到信号 {}，准备退出...", signo);
    thread_control::g_thread_running = false;
    
    // 确保停止所有线程
    thread_manager::stopLkmThreads();
    
    // 直接退出程序
    exit(0);
}


/*-------------------------------------------
                  Main Function
-------------------------------------------*/
int main() {
    // 注册信号处理
    signal(SIGINT, handle_sig);
    signal(SIGTERM, handle_sig);
    
    LOG_INFO("LKM测试程序启动");
    
    // 初始化线程运行标志
    thread_control::g_thread_running = true;
    // 启动所有线程（内部会初始化LKM设备）
    if (!thread_manager::startLkmThreads()) {
        LOG_ERROR("启动失败");
        return 1;
    }
    
    // 等待线程完全启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    std::cout << "测试程序已启动，按Ctrl+C退出程序..." << std::endl;
    std::cout << "提示：按下鼠标右键将通过API发送左键+移动命令" << std::endl;
    std::cout << "提示：按下鼠标中键将通过API发送Ctrl+A组合键" << std::endl;
    
    // 使用循环轮询方式等待退出信号
    while (thread_control::g_thread_running) {
        // 每秒检查一次
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        // 输出服务器状态
        LOG_DEBUG("线程运行状态: {}", thread_manager::isLkmThreadsRunning() ? "运行中" : "已停止");
    }
    
    // 停止所有线程
    thread_manager::stopLkmThreads();
    
    LOG_INFO("测试程序正常退出");
    return 0;
}
