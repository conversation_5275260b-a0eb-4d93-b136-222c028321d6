#include "keymouse.h"
#include "../utils/logger.h"
#include "../utils/gmodels.h"
#include "../algorithm/MouseFov.hpp"
#include "KeyMouseConfig.h"
#include <thread>
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <future>
#include <vector>
#include <set>
#include <cmath>
#include "../algorithm/pid_controller.h"
#include <random>

namespace lkm_tools {

//-----------------------------
// 监听线程实现
//-----------------------------

// 监听线程循环函数 - 使用固定的轮询间隔
void listenThreadLoop(LkmAPI& api) {
    // 获取配置单例
    KeyMouseConfig& config = KeyMouseConfig::getInstance();
    
    for (int i = 0; i < 3; i++) {
        // 发送空命令
        api.sendCommand(50, 0, 0x00);
        // 等待1秒
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        api.sendCommand(-50, 0, 0x00);
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    lkm::MouseState current_state;
    
    while (thread_control::g_thread_running) {
        if (jfkm_config::g_auth_status != 1) {
            LOG_ERROR("卡密验证失败，请检查卡密是否正确");
            std::this_thread::sleep_for(std::chrono::milliseconds(800));
            // 初始化键鼠发送的移动像素 move_x move_x
            core_vars::move_x = 0.0;
            core_vars::move_y = 0.0;
            // 原始移动像素-未经过PID处理
            core_vars::mouse_x = 0.0;
            core_vars::mouse_y = 0.0;
            core_vars::target_distance = -1.0;
            core_vars::target_width = 0.0;
            core_vars::target_height = 0.0;
            // 初始化瞄准变量mouse_pressed
            core_vars::mouse_pressed = false;
            continue;
        }
        // 读取鼠标状态
        if (api.readMouseState(current_state)) {
            // 更新全局共享的鼠标状态
            config.setCurrentMouseState(current_state);
            
            if (current_state.buttons != 0) {
                // 匹配热键并设置当前按下状态
                bool matched = config.matchHotkey(current_state.buttons);
                if (matched) {
                    core_vars::mouse_pressed = true;
                    // LOG_INFO("成功匹配按键配置");
                }
                else {
                    LOG_INFO("未匹配到按键配置");
                    core_vars::mouse_pressed = false;
                    // 重置热键状态
                }
            } else {
                core_vars::mouse_pressed = false;
                // 发送空命令
                api.sendCommand(0, 0,lkm::MOUSE_NONE);
                // LOG_INFO("没有按键按下");
                // 初始化数据收集相关
                webui::collect::g_map_hotkey_pressed = false;
                webui::collect::g_target_hotkey_pressed = false;
                // 重置PID控制器
                if (webui::function::g_ai_mode == "PID" || webui::function::g_ai_mode == "FOVPID" || webui::function::g_ai_mode == "PID冲锋狙" || webui::function::g_ai_mode == "FOVPID冲锋狙") {
                    config.getNearPidController().reset();
                    config.getFarPidController().reset();
                    core_vars::mouse_x = 0.0f;
                    core_vars::mouse_y = 0.0f;
                }
            }
        } 
        
        // 🎯 FOV测量功能已移动到辅助功能线程中 

        // 按照固定间隔休眠
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
    
    LOG_INFO("监听线程循环退出");
}

// 瞄准计算函数 - 改为普通函数，可在WSQ工作线程中调用
std::pair<int, int> calculateAimMovement(double target_offset_x, double target_offset_y, double target_distance, std::mt19937& gen) {
    // 获取配置单例
    KeyMouseConfig& config = KeyMouseConfig::getInstance();
    
        // 根据PID随机系数作为偏离幅度：以1.0为中心，上下浮动g_pid_random_factor的幅度
        double min_coeff = 1.0 - webui::pid::g_pid_random_factor;  // 最小系数 = 1.0 - 幅度
        double max_coeff = 1.0 + webui::pid::g_pid_random_factor;  // 最大系数 = 1.0 + 幅度
    
        // 创建动态范围的随机分布
        std::uniform_real_distribution<double> coeff_dist(min_coeff, max_coeff);
    
    // 随机系数更新 - 每次调用更新随机系数
    double near_pid_coeff = coeff_dist(gen);
    double far_pid_coeff = coeff_dist(gen);
    
            // 检查目标距离与目标宽度的关系，使用适当的PID控制器
            infer::algorithm::PidController& pidController = 
        (target_distance < core_vars::target_width) ? 
                config.getNearPidController() : config.getFarPidController();
        
    if (target_distance < core_vars::target_width) {
                // 近距离PID控制器 - 回弹强度使用固定值，不应用随机系数
                pidController.setPidParameters( 
            webui::pid::g_near_move_factor * near_pid_coeff,        // kp: 比例系数(移动速度)
            webui::pid::g_near_stabilizer * near_pid_coeff,         // ki: 积分系数(跟踪速度)
            webui::pid::g_near_response_rate * near_pid_coeff,      // kd: 微分系数(抖动力度)
            webui::pid::g_near_assist_zone * near_pid_coeff,        // deadzone: 死区大小
            webui::pid::g_near_max_adjustment * near_pid_coeff,     // integralLimit: 积分限制
            webui::pid::g_near_response_delay,                      // reboundStrength: 回弹强度(使用固定值，不应用随机系数)
            core_vars::target_width                                 // 最大移动距离限制 使用目标宽度
                );
            } else {
                // 远距离PID控制器 - 回弹强度使用固定值，不应用随机系数
                pidController.setPidParameters( 
            webui::pid::g_near_move_factor * webui::pid::g_far_factor * far_pid_coeff,      // kp: 远端比例系数
            webui::pid::g_near_stabilizer * webui::pid::g_far_factor * far_pid_coeff,       // ki: 远端积分系数
            webui::pid::g_near_response_rate * webui::pid::g_far_factor * far_pid_coeff,    // kd: 远端微分系数
            webui::pid::g_near_assist_zone * webui::pid::g_far_factor * far_pid_coeff,      // deadzone: 远端死区
            webui::pid::g_near_max_adjustment * webui::pid::g_far_factor * far_pid_coeff,   // integralLimit: 远端积分限制
            webui::pid::g_near_response_delay,                                              // reboundStrength: 回弹强度(使用固定值，不应用随机系数)
            core_vars::target_width                                                         // 最大移动距离限制 使用目标宽度
                );
            }
    
            if (webui::function::g_ai_mode == "PID" || webui::function::g_ai_mode == "PID冲锋狙") {
        // 纯PID模式 - 直接使用目标偏移量
        auto [moveX, moveY] = pidController.update(target_offset_x, target_offset_y, 0.004);
        return {moveX, moveY};
            } else if (webui::function::g_ai_mode == "FOVPID" || webui::function::g_ai_mode == "FOVPID冲锋狙") {
                // FOVPID混合模式 - 应用FOV比例后再使用PID控制
        double fov_move_x = target_offset_x / webui::fov::g_fov;
        double fov_move_y = target_offset_y / webui::fov::g_fov;
                auto [moveX, moveY] = pidController.update(fov_move_x, fov_move_y, 0.004);
        return {moveX, moveY};
            } else if (webui::function::g_ai_mode == "FOV" || webui::function::g_ai_mode == "FOV冲锋狙") {   
        // 纯FOV模式 - 应用FOV比例，Y轴应用Y轴系数
        int move_x = static_cast<int>(target_offset_x / webui::fov::g_fov);
        int move_y = static_cast<int>(target_offset_y / webui::fov::g_fov);
        // 等待时间，使用fov时间
        std::this_thread::sleep_for(std::chrono::milliseconds(webui::fov::g_fov_time));
        return {move_x, move_y};
            } else {
                // 未知或不受支持的模式，不进行任何移动
                LOG_WARNING("未知的AI模式: {}, 不执行移动", webui::function::g_ai_mode);
        return {0, 0};
    }
}

//-----------------------------
// 命令服务器实现
//-----------------------------
// 背闪防护处理函数 - 专门处理背闪检测和躲避
void handleFlashShield(LkmAPI& api, std::mt19937& gen) {
        // 🎯 背闪检测：检查当前激活配置是否启用背闪防护功能
        bool flash_shield_enabled = false;
        int active_index = webui::function::g_active_config_index;
        if (active_index >= 0 && active_index < webui::function::PRESET_CONFIGS.size()) {
            flash_shield_enabled = (webui::function::PRESET_CONFIGS[active_index].flash_shield == "true");
        }
        
        // 只有在启用背闪防护且检测到背闪时，才执行躲避移动
        if (flash_shield_enabled && core_vars::g_flash_detected) {
            // 随机选择移动方向：左(-1)或右(1)
            std::uniform_int_distribution<int> direction_dist(0, 1);
            int direction = direction_dist(gen) == 0 ? -1 : 1;
            
            // 随机X轴移动距离：2000-2500像素
            std::uniform_int_distribution<int> x_dist(2000, 2500);
            int move_x = x_dist(gen) * direction;
            
            // 随机Y轴移动距离：-200到200像素（轻微的垂直随机）
            std::uniform_int_distribution<int> y_dist(-50, 80);
            int move_y = y_dist(gen);
            
            LOG_INFO("背闪防护已启用，检测到背闪，执行随机躲避移动: X={}, Y={} (配置索引: {})", 
                     move_x, move_y, active_index);
            // 执行躲避移动
            api.sendCommand(move_x, move_y);
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            
            // 执行回归移动（回到原位附近，带±15%随机偏移）
            std::uniform_real_distribution<double> offset_dist(0.85, 1.15); // ±15%随机系数
            double x_offset_coeff = offset_dist(gen);
            double y_offset_coeff = offset_dist(gen);
            
            int return_x = static_cast<int>(-move_x * x_offset_coeff);
            int return_y = static_cast<int>(-move_y * y_offset_coeff);
            
            api.sendCommand(return_x, return_y);
            
            // 重置背闪状态，避免重复执行
            core_vars::g_flash_detected = false;
            
            LOG_INFO("背闪躲避完成，随机回归: X={}, Y={} (偏移系数: {:.2f}, {:.2f})", 
                     return_x, return_y, x_offset_coeff, y_offset_coeff);
        } else if (core_vars::g_flash_detected && !flash_shield_enabled) {
            // 检测到背闪但防护功能未启用，重置背闪状态并记录
            core_vars::g_flash_detected = false;
            LOG_DEBUG("检测到背闪但背闪防护功能未启用 (配置索引: {})", active_index);
        }
}

// 测量FOV循环
void handleFOVMeasurement(LkmAPI& api) {
    // 检查是否需要启动FOV测量
    if (!webui::fov::g_start_measure) {
        return; // 未启用FOV测量，直接返回
    }
    
    LOG_INFO("开始FOV测量循环");
    // 初始化MouseFov实例
    MouseFov& mouseFov = MouseFov::getInstance();
    bool data = false;
    
    // 获取瞄准范围，用于限制测量过程中的移动
    int aim_range = webui::aim::g_aim_range;
    if (aim_range <= 0) {
        aim_range = 200; // 默认瞄准范围
        LOG_INFO("使用默认瞄准范围: {}", aim_range);
    } else {
        LOG_INFO("使用当前瞄准范围: {}", aim_range);
    }
    
    // 测量用的测试移动距离，根据瞄准范围调整
    int test_move_distance = std::min(aim_range / 2, 100); // 最大移动100像素，但不超过瞄准范围的一半
    
    while (thread_control::g_thread_running && webui::fov::g_start_measure) {
        // 检查是否有有效目标进行FOV测量
        if (core_vars::target_center_x != 0 && core_vars::target_center_y != 0) {
            data = mouseFov.measureFovWithTarget(core_vars::target_distance, core_vars::target_center_x, core_vars::target_center_y);
            if (data) {
                // 目标距离已足够近，可以开始测量
                LOG_INFO("目标距离已适合FOV测量，距离: {}", core_vars::target_distance);
                
                // 记录初始屏幕中心和目标位置
                double screen_center_x = core_vars::screen_center_x;
                double screen_center_y = core_vars::screen_center_y;
                double initial_target_x = core_vars::target_center_x;
                double initial_target_y = core_vars::target_center_y;
                
                // 步骤1: 向右移动固定像素，用于建立参考点
                LOG_INFO("FOV测量步骤1: 向右移动 {} 像素", test_move_distance);
                api.sendCommand(test_move_distance, 0);
                std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待目标更新
                
                // 步骤2: 计算移动后目标偏移量，并据此计算FOV比例
                double x_offset = screen_center_x - core_vars::target_center_x;
                // 计算FOV比例 = 实际偏移/命令偏移
                double new_fov = x_offset / test_move_distance;
                
                // 验证FOV值是否合理 (应该在0.1到10之间)
                if (new_fov < 0.1 || new_fov > 10) {
                    LOG_WARNING("计算的FOV值异常: {}, 使用默认值 1.0", new_fov);
                    new_fov = 1.0; // 使用安全默认值
                } else {
                    LOG_INFO("FOV测量步骤2: 计算FOV比例 = {:.4f}", new_fov);
                    // 更新全局FOV值
                    webui::fov::g_fov = new_fov;
                }
                
                // 步骤3: 使用计算出的FOV比例进行修正移动，回到目标位置
                LOG_INFO("FOV测量步骤3: 使用新FOV进行修正移动");
                
                // 计算当前目标与屏幕中心的偏差
                double current_offset_x = core_vars::target_center_x - screen_center_x;
                double current_offset_y = core_vars::target_center_y - screen_center_y;
                
                // 使用FOV比例计算需要的移动量，Y轴应用Y轴系数
                int correction_x = static_cast<int>(current_offset_x / new_fov);
                int correction_y = static_cast<int>((current_offset_y / new_fov) * webui::pid::g_y_axis_factor);
                
                // 限制修正移动量不超过瞄准范围
                correction_x = std::max(-aim_range, std::min(correction_x, aim_range));
                correction_y = std::max(-aim_range, std::min(correction_y, aim_range));
                
                // 执行修正移动
                LOG_INFO("执行修正移动: x={}, y={}", correction_x, correction_y);
                api.sendCommand(correction_x, correction_y);
                
                // 等待最终位置稳定
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                
                // 完成测量，禁用自动测量标志
                webui::fov::g_start_measure = false;
                LOG_INFO("FOV测量完成: 最终FOV值 = {:.4f}", webui::fov::g_fov);
                
                // 计算测量精度
                double final_offset_x = core_vars::target_center_x - screen_center_x;
                double final_offset_y = core_vars::target_center_y - screen_center_y;
                double precision = std::sqrt(final_offset_x * final_offset_x + final_offset_y * final_offset_y);
                LOG_INFO("FOV测量精度: {:.2f} 像素偏差", precision);
                break;
            } else {
                // 目标距离太远，需要先移动到目标附近
                LOG_INFO("目标距离过远: {}, 正在接近目标", core_vars::target_distance);
                
                // 计算到目标的向量
                double delta_x = core_vars::target_center_x - core_vars::screen_center_x;
                double delta_y = core_vars::target_center_y - core_vars::screen_center_y;
                
                // 限制移动量不超过瞄准范围，Y轴应用Y轴系数
                int move_x = std::max(-aim_range, std::min(static_cast<int>(delta_x), aim_range));
                int move_y = std::max(-aim_range, std::min(static_cast<int>(delta_y * webui::pid::g_y_axis_factor), aim_range));
                
                // 执行移动
                LOG_INFO("向目标移动: x={}, y={}", move_x, move_y);
                api.sendCommand(move_x, move_y);
                
                // 等待位置更新
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                continue;
            }
        } else {
            // 没有有效目标，等待目标出现
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        } 
    }
    
    LOG_INFO("FOV测量循环结束");
}

//-----------------------------
// 自动开火循环
//-----------------------------
// 自动开火处理函数 - 全新非阻塞方式，基于时间间隔控制
bool handleAutoFire(LkmAPI& api, std::mt19937& gen, bool should_fire, 
                   std::chrono::steady_clock::time_point& last_fire_time) {
    // 检查扳机开关是否开启
    bool trigger_enabled = webui::function::g_trigger_switch;
    // 检查是否需要开火：必须同时满足以下条件
    // 1. 扳机开关开启
    // 2. WSQ命令中的开火判断 (should_fire为true)
    if (!trigger_enabled || !should_fire) {
        return false; // 不满足开火条件
    }
    auto now = std::chrono::steady_clock::now();
    // 计算开火间隔时间（应用随机系数）
    std::uniform_real_distribution<double> coeff_dist(0.85, 1.15); // ±15%随机系数
    double fire_interval_coeff = coeff_dist(gen);
    // 步枪开火随机时间
    int random_fire_interval = static_cast<int>(webui::fire::g_rifle_sleep * fire_interval_coeff);
    // 步枪间隔时间
    int rifle_interval = static_cast<int>(webui::fire::g_rifle_interval * fire_interval_coeff);
    // 检查是否到了开火时间
    auto time_since_last_fire = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_fire_time);
    if (time_since_last_fire.count() < random_fire_interval) {
        return false; // 还未到开火时间
    }
    
    // 🔥 执行开火（非阻塞方式）
    if (webui::function::g_ai_mode == "FOVPID冲锋狙" || 
        webui::function::g_ai_mode == "FOV冲锋狙" || 
        webui::function::g_ai_mode == "PID冲锋狙") {
        
        // 冲锋狙模式：快速连击（开镜+开火），无睡眠阻塞
        api.sendCommand(0, 0, lkm::MOUSE_RIGHT);  // 开镜
        // 使用随机间隔 睡眠 rifle_interval
        std::this_thread::sleep_for(std::chrono::milliseconds(rifle_interval));
        api.sendCommand(0, 0, lkm::MOUSE_LEFT);   // 开火
        // LOG_DEBUG("冲锋狙模式非阻塞开火");
        
    } else {
        // 普通模式：直接开火
        api.sendCommand(0, 0, lkm::MOUSE_LEFT);
        LOG_DEBUG("普通模式非阻塞开火");
    }
    
    // 更新上次开火时间
    last_fire_time = now;
    
    return true; // 已执行开火
}

//-----------------------------
// 辅助功能循环线程实现 - 处理FOV、开火、背闪
//-----------------------------

// 辅助功能循环线程 - 处理除瞄准外的其他功能
void auxiliaryFunctionsLoop(LkmAPI& api) {
    LOG_INFO("辅助功能线程启动 - 处理FOV、开火、背闪");
    
    // 获取配置单例
    KeyMouseConfig& config = KeyMouseConfig::getInstance();
    
    // 初始化随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());
    
    // 🔥 开火时间记录
    auto last_fire_time = std::chrono::steady_clock::now();
    
    while (thread_control::g_thread_running) {
        try {
            // ===== 1. FOV测量处理 =====
            if (webui::fov::g_start_measure) {
                handleFOVMeasurement(api);
                LOG_INFO("辅助线程: FOV测量处理完成");
            }
            
            // ===== 2. 自动开火处理 =====
            if (core_vars::mouse_pressed && webui::function::g_trigger_switch) {
                // 检查是否有开火信号（从全局变量获取）
                bool should_fire = false;
                
                // 从WSQ队列中获取最新的开火状态，如果队列为空则使用默认值
                if (wsq_models::g_command_queue && !wsq_models::g_command_queue->empty()) {
                    // 这里我们可以peek最新的命令来获取开火状态
                    // 但为了简单起见，我们使用全局变量
                    should_fire = core_vars::target_distance > 0; // 有目标时考虑开火
                }
                
                if (should_fire) {
                    bool fired = handleAutoFire(api, gen, true, last_fire_time);
                    if (fired) {
                        LOG_INFO("辅助线程: 自动开火执行成功");
                        // 重置PID控制器，避免开火时的移动干扰
                        config.getNearPidController().reset();
                        config.getFarPidController().reset();
                    }
                }
            }
            
            // ===== 3. 背闪防护处理 =====
            if (core_vars::g_flash_detected) {
                handleFlashShield(api, gen);
                LOG_INFO("辅助线程: 背闪防护处理完成");
            }
            
            // 适当休眠，避免过度消耗CPU
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
            
        } catch (const std::exception& e) {
            LOG_ERROR("辅助功能线程出错: {}", e.what());
        } catch (...) {
            LOG_ERROR("辅助功能线程出现未知错误");
        }
    }
    
    LOG_INFO("辅助功能线程正常退出");
}

//-----------------------------
// WSQ多线程执行器实现 - 完全模仿NPU架构
//-----------------------------

// WSQ工作线程向量（全局静态变量）
static std::vector<std::thread> s_wsq_worker_threads;

// 原子轮询分配函数 - 完全模仿NPU的NextNPUCoreIndex()
u32 NextWSQThreadIndex() {
    return wsq_models::g_next_wsq_thread_index.fetch_add(1, std::memory_order_relaxed) % wsq_models::g_wsq_thread_count;
}

// 创建WSQ工作线程 - 完全模仿NPU的CreateWorkerThreads()
void CreateWSQWorkerThreads(LkmAPI& api) {
    LOG_INFO("创建WSQ瞄准架构，线程数: {}", wsq_models::g_wsq_thread_count);
    LOG_INFO("WSQ专门用于瞄准处理");
    
    s_wsq_worker_threads.resize(wsq_models::g_wsq_thread_count);
    
    for (u32 i = 0; i < wsq_models::g_wsq_thread_count; ++i) {
        s_wsq_worker_threads[i] = std::thread([&api, i]() {
            LOG_INFO("WSQ瞄准线程 {} 已启动", i);
            
            // 获取配置单例
            KeyMouseConfig& config = KeyMouseConfig::getInstance();
            
    // 初始化随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());
    // 🔥 每个线程独立的开火时间记录
    auto last_fire_time = std::chrono::steady_clock::now();
    // 📊 线程工作统计
    uint64_t thread_task_count = 0;
    while (thread_control::g_thread_running) {
                // 使用与NPU完全相同的等待机制
                if (wsq_models::g_command_queue->empty()) {
                    std::unique_lock<std::mutex> lock(wsq_models::g_wsq_mutex);
                    wsq_models::g_wsq_cv.wait(lock, []() {
                        return !thread_control::g_thread_running || !wsq_models::g_command_queue->empty();
                    });
                }
                
                // 再次检查停止标志
                if (!thread_control::g_thread_running) {
                    LOG_INFO("WSQ工作线程 {} 收到退出信号", i);
                    break;
                }
                
                // 使用WorkStealingQueue的steal方法获取任务
                const auto cmd_opt = wsq_models::g_command_queue->steal();
                if (!cmd_opt) {
                    continue;
                }
                wsq_models::WSQCommand cmd = *cmd_opt;
                
                // 📊 统计任务处理数量
                thread_task_count++;
                
                // 🎯 WSQ只处理瞄准任务
                if (core_vars::mouse_pressed && cmd.has_target) {
                    // 计算目标偏移量（使用WSQ队列中的数据）
                    double target_offset_x = cmd.target_x - core_vars::screen_center_x;
                    double target_offset_y = cmd.aim_y - core_vars::screen_center_y;
                    // 🎯 使用复用的瞄准计算函数（支持所有AI模式）
                    auto [moveX, moveY] = calculateAimMovement(target_offset_x, target_offset_y, cmd.target_distance, gen);
                    api.sendCommand(moveX, moveY);
                    LOG_INFO("WSQ瞄准线程 {} : 目标偏移({:.2f}, {:.2f}) -> 移动命令({}, {}) [模式: {}]", 
                             i, target_offset_x, target_offset_y, moveX, moveY, webui::function::g_ai_mode);
                }
                // 短暂休眠，避免过度消耗CPU
                // std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
            
            LOG_INFO("WSQ瞄准线程 {} 正常退出，共处理任务: {}", i, thread_task_count);
        });
    }
    
    LOG_INFO("WSQ瞄准架构创建完成：专门用于瞄准处理");
}

// 停止WSQ工作线程 - 完全模仿NPU架构
void StopWSQWorkerThreads() {
    LOG_INFO("正在停止WSQ工作线程...");
    
    // 通知所有WSQ工作线程停止
    {
        std::scoped_lock lock(wsq_models::g_wsq_mutex);
        wsq_models::g_wsq_cv.notify_all();
    }
    
    // 等待所有线程结束
    for (u32 i = 0; i < s_wsq_worker_threads.size(); ++i) {
        if (s_wsq_worker_threads[i].joinable()) {
            s_wsq_worker_threads[i].join();
            LOG_INFO("WSQ工作线程 {} 已停止", i);
        }
    }
    
    s_wsq_worker_threads.clear();
    LOG_INFO("所有WSQ工作线程已停止");
}

// WSQ执行器循环函数 - 使用与NPU工作线程完全相同的高性能队列机制
void WSQExecutorLoop(LkmAPI& api) {
    LOG_INFO("WSQ瞄准执行器启动 - 专门用于瞄准处理");
    
    // 创建WSQ工作线程
    CreateWSQWorkerThreads(api);
    
    // 主循环只负责监控线程状态
    while (thread_control::g_thread_running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 停止所有工作线程
    StopWSQWorkerThreads();
    
    LOG_INFO("WSQ瞄准执行器循环退出");
}

} // namespace lkm_tools