#include "KeyMouseConfig.h"
#include "../utils/logger.h"

namespace lkm_tools {

// 单例实例获取函数
KeyMouseConfig& KeyMouseConfig::getInstance() {
    static KeyMouseConfig instance;
    return instance;
}

// 构造函数 - 初始化PID控制器
KeyMouseConfig::KeyMouseConfig()
    : pidNear_(
        webui::pid::g_near_move_factor,       // 移动速度
        webui::pid::g_near_stabilizer,        // 跟踪速度
        webui::pid::g_near_response_rate,     // 抖动速度
        webui::pid::g_near_assist_zone,       // 死区
        webui::pid::g_near_max_adjustment,    // 积分上限
        webui::pid::g_near_response_delay,    // 回弹强度
        core_vars::target_width               // 最大移动距离限制
    ),
    pidFar_(
        webui::pid::g_near_move_factor * webui::pid::g_far_factor,       // 远端移动速度
        webui::pid::g_near_stabilizer * webui::pid::g_far_factor,        // 远端跟踪速度
        webui::pid::g_near_response_rate * webui::pid::g_far_factor,     // 远端抖动速度
        webui::pid::g_near_assist_zone * webui::pid::g_far_factor,       // 远端死区
        webui::pid::g_near_max_adjustment * webui::pid::g_far_factor,    // 远端积分上限
        webui::pid::g_near_response_delay * webui::pid::g_far_factor,    // 远端回弹强度
        core_vars::target_width                                          // 最大移动距离限制
    ) {
    // 构造函数初始化完成
    LOG_INFO("KeyMouseConfig 初始化完成");
    
}

// 更新PID参数
void KeyMouseConfig::updatePidParameters() {
    LOG_INFO("更新PID参数");
    
    // 更新近距离PID参数
    pidNear_.setPidParameters(
        webui::pid::g_near_move_factor,       // 移动速度
        webui::pid::g_near_stabilizer,        // 跟踪速度
        webui::pid::g_near_response_rate,     // 抖动速度
        webui::pid::g_near_assist_zone,       // 死区
        webui::pid::g_near_max_adjustment,    // 积分上限
        webui::pid::g_near_response_delay,    // 回弹强度
        core_vars::target_width               // 最大移动距离限制
    );
    
    // 更新远距离PID参数
    pidFar_.setPidParameters(
        webui::pid::g_near_move_factor * webui::pid::g_far_factor,       // 远端移动速度
        webui::pid::g_near_stabilizer * webui::pid::g_far_factor,        // 远端跟踪速度
        webui::pid::g_near_response_rate * webui::pid::g_far_factor,     // 远端抖动速度
        webui::pid::g_near_assist_zone * webui::pid::g_far_factor,       // 远端死区
        webui::pid::g_near_max_adjustment * webui::pid::g_far_factor,    // 远端积分上限
        webui::pid::g_near_response_delay * webui::pid::g_far_factor,    // 远端回弹强度
        core_vars::target_width                                          // 最大移动距离限制
    );
    
    // 打印近端和远端参数
    LOG_INFO("更新PID参数 - 近端参数: {}, {}, {}, {}, {}, {}, {}", 
        webui::pid::g_near_move_factor,
        webui::pid::g_near_stabilizer,
        webui::pid::g_near_response_rate,
        webui::pid::g_near_assist_zone,
        webui::pid::g_near_max_adjustment,
        webui::pid::g_near_response_delay,
        core_vars::target_width);
    
    LOG_INFO("更新PID参数 - 远端参数: {}, {}, {}, {}, {}, {}, {}", 
        webui::pid::g_near_move_factor * webui::pid::g_far_factor,
        webui::pid::g_near_stabilizer * webui::pid::g_far_factor,
        webui::pid::g_near_response_rate * webui::pid::g_far_factor,
        webui::pid::g_near_assist_zone * webui::pid::g_far_factor,
        webui::pid::g_near_max_adjustment * webui::pid::g_far_factor,
        webui::pid::g_near_response_delay * webui::pid::g_far_factor,
        core_vars::target_width);
}

// 热键匹配函数
bool KeyMouseConfig::matchHotkey(uint8_t button_state) {
    // LOG_INFO("========== 触发热键匹配 ==========");
    // LOG_INFO("检查按键状态: {:#x}", button_state);
    // 转换按键状态为字符串
    std::string pressed_key;
    
    // 按键状态映射逻辑
    if (button_state == lkm::MOUSE_LEFT || button_state == lkm::MOUSE_LEFT_SIDE1) {
        pressed_key = "左键";
    } else if (button_state == lkm::MOUSE_RIGHT || button_state == lkm::MOUSE_LEFT_RIGHT || 
               button_state == lkm::MOUSE_RIGHT_SIDE2) {
        pressed_key = "右键";
    } else if (button_state == lkm::MOUSE_MIDDLE) {
        pressed_key = "中键";
    } else if (button_state == lkm::MOUSE_SIDE1 || button_state == lkm::MOUSE_LEFT_SIDE1) {
        pressed_key = "前侧";// 
    } else if (button_state == lkm::MOUSE_SIDE2 || button_state == lkm::MOUSE_RIGHT_SIDE2 || button_state == lkm::MOUSE_LEFT_RIGHT || button_state == lkm::MOUSE_LEFT_SIDE2) {
        pressed_key = "后侧";
    } else {
        LOG_INFO("未知按键状态: {:#x}", button_state);
        LOG_INFO("====================================");
        return false;
    }
    
    // LOG_INFO("按键映射为: {}", pressed_key);
    
    // 1. 数据收集热键检测 - 优先级最高
    if (webui::collect::g_is_enabled) {
        // 检查按键是否匹配
        if (pressed_key == webui::collect::g_map_hotkey) {
            // 直接设置热键状态为true
            webui::collect::g_map_hotkey_pressed = true;
            // 强制另一个热键状态为false，确保互斥
            webui::collect::g_target_hotkey_pressed = false;
            LOG_INFO("数据收集 - 地图热键已激活");
            return true;
        } 
        else if (pressed_key == webui::collect::g_target_hotkey) {
            // 直接设置热键状态为true
            webui::collect::g_target_hotkey_pressed = true;
            // 强制另一个热键状态为false，确保互斥
            webui::collect::g_map_hotkey_pressed = false;
            LOG_INFO("数据收集 - 目标热键已激活");
            return true;
        }
    }
    
    // 2. 自瞄配置热键检测
    for (size_t i = 0; i < webui::function::PRESET_CONFIGS.size(); i++) {
        const auto& config = webui::function::PRESET_CONFIGS[i];
        
        if (config.enabled == "true" && config.hotkey == pressed_key) {
            // 更新当前配置的所有参数
            webui::function::g_active_config_index = i;
            webui::function::g_ai_mode = config.ai_mode;
            webui::function::g_lock_position = config.lock_position;
            webui::function::g_hotkey = config.hotkey;
            webui::function::g_trigger_switch = (config.trigger_switch == "true");  // 同步扳机开关状态（string转bool）
            
            // LOG_INFO("切换到配置 {}: AI模式={}, 锁定位置={}, 热键={}",
            //     config.name, webui::function::g_ai_mode,
            //     webui::function::g_lock_position, webui::function::g_hotkey);
                
            // LOG_INFO("====================================");
            return true;  // 找到并切换配置成功
        }
    }
    
    LOG_INFO("未找到按键 {} 对应的配置", pressed_key);
    LOG_INFO("====================================");
    return false;  // 未找到匹配的配置
}

// 获取当前鼠标状态
const lkm::MouseState& KeyMouseConfig::getCurrentMouseState() const {
    return currentMouseState_;
}

// 设置当前鼠标状态
void KeyMouseConfig::setCurrentMouseState(const lkm::MouseState& state) {
    currentMouseState_ = state;
}

// 获取近距离PID控制器
infer::algorithm::PidController& KeyMouseConfig::getNearPidController() {
    return pidNear_;
}

// 获取远距离PID控制器
infer::algorithm::PidController& KeyMouseConfig::getFarPidController() {
    return pidFar_;
}

} // namespace lkm_tools 