# LKM模块定义
# 注意：此文件设计为被主项目的CMakeLists.txt包含，不应独立使用

# 查找系统上安装的CSerialPort库
find_package(CSerialPort QUIET)

# 如果找到了系统安装的CSerialPort库，则使用它
if(CSerialPort_FOUND)
    message(STATUS "使用系统安装的CSerialPort库")
    set(CSERIALPORT_LIBRARIES CSerialPort::CSerialPort)
else()
    message(STATUS "未找到系统安装的CSerialPort库，尝试从源码编译")
    
    # 确认CSerialPort源码目录是否存在
    set(CSERIALPORT_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/CSerialPort-master)
    if(NOT EXISTS ${CSERIALPORT_ROOT_DIR})
        message(FATAL_ERROR "CSerialPort源码目录不存在: ${CSERIALPORT_ROOT_DIR}")
    endif()
    
    # 添加CSerialPort库源文件
    set(CSERIALPORT_SOURCES
        ${CSERIALPORT_ROOT_DIR}/src/SerialPort.cpp
        ${CSERIALPORT_ROOT_DIR}/src/SerialPortBase.cpp
        ${CSERIALPORT_ROOT_DIR}/src/SerialPortInfo.cpp
        ${CSERIALPORT_ROOT_DIR}/src/SerialPortInfoBase.cpp
    )
    
    # 根据操作系统选择平台特定的源文件
    if(CMAKE_SYSTEM_NAME MATCHES "Linux|Unix|Darwin")
        list(APPEND CSERIALPORT_SOURCES
            ${CSERIALPORT_ROOT_DIR}/src/SerialPortInfoUnixBase.cpp
            ${CSERIALPORT_ROOT_DIR}/src/SerialPortUnixBase.cpp
        )
    elseif(CMAKE_SYSTEM_NAME MATCHES "Windows")
        list(APPEND CSERIALPORT_SOURCES
            ${CSERIALPORT_ROOT_DIR}/src/SerialPortInfoWinBase.cpp
            ${CSERIALPORT_ROOT_DIR}/src/SerialPortWinBase.cpp
        )
    endif()
    
    # 创建CSerialPort静态库
    add_library(cserialport STATIC ${CSERIALPORT_SOURCES})
    target_include_directories(cserialport PUBLIC 
        ${CSERIALPORT_ROOT_DIR}/include
    )
    set(CSERIALPORT_LIBRARIES cserialport)
    
    # 将CSerialPort的包含目录添加到全局包含目录 - 确保这一行生效
    include_directories(${CSERIALPORT_ROOT_DIR}/include)
    
    # 确保编译选项正确
    target_compile_definitions(cserialport PRIVATE BUILDING_LIBCSERIALPORT)
    if(CMAKE_SYSTEM_NAME MATCHES "Linux|Unix")
        target_link_libraries(cserialport PRIVATE pthread)
    elseif(CMAKE_SYSTEM_NAME MATCHES "Windows")
        target_link_libraries(cserialport PRIVATE user32 advapi32 setupapi)
    endif()
endif()

# LKM API源文件 - 只包含LKM目录下的源文件
set(LKM_SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/lkm/LkmApi.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/lkm/keymouse.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/lkm/KeyMouseConfig.cpp
)

# ThreadManager源文件单独处理 - 现在改为只依赖而不包含
# set(THREAD_MANAGER_SOURCES
#     ${CMAKE_CURRENT_SOURCE_DIR}/ThreadManager/lkm_server.cpp
# )

# 创建LKM API静态库
add_library(lkmapi STATIC ${LKM_SOURCES})

# 添加所有必要的包含目录
target_include_directories(lkmapi PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/lkm
    ${CSERIALPORT_ROOT_DIR}/include
)

# 链接必要的依赖库
target_link_libraries(lkmapi PUBLIC 
    ${CSERIALPORT_LIBRARIES}
    utils
    Threads::Threads
    algorithm_lib
    ${ATOMIC_LIBRARY}
)

message(STATUS "LKM模块定义已加载") 