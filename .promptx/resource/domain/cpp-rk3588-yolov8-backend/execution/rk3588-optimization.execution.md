<execution>
  <constraint>
    ## RK3588硬件约束
    - **NPU限制**：6TOPS算力，仅支持INT8/INT16量化，不支持FP32
    - **内存带宽**：LPDDR4X-4266，理论带宽68.2GB/s，实际可用约50GB/s
    - **CPU架构**：4×A76@2.4GHz + 4×A55@1.8GHz，大小核调度复杂
    - **温度墙**：85°C硬件保护，75°C开始降频，需要主动温控
    - **功耗限制**：TDP 10W，峰值功耗12W，需要功耗管理
    - **RKNN Runtime**：版本兼容性严格，API变更频繁
  </constraint>

  <rule>
    ## RK3588优化强制规则
    - **NPU独占**：同时只能运行一个RKNN模型实例，不支持并发
    - **内存对齐**：NPU输入必须32字节对齐，输出16字节对齐
    - **数据格式**：输入必须是NHWC格式，RGB或BGR顺序
    - **量化精度**：模型量化必须验证精度损失<2%
    - **温度监控**：必须实时监控CPU/NPU温度，超过70°C降频
    - **大小核绑定**：推理任务绑定A76大核，预处理绑定A55小核
    - **DMA使用**：大数据传输必须使用DMA，避免CPU拷贝
  </rule>

  <guideline>
    ## RK3588优化指导
    - **预热策略**：模型加载后执行dummy推理，预热NPU管道
    - **批处理优化**：虽然NPU不支持batch，但可以流水线并行
    - **内存复用**：输入输出buffer复用，减少内存分配
    - **CPU亲和性**：设置线程CPU亲和性，避免核间迁移
    - **频率调节**：根据负载动态调整CPU/NPU频率
    - **缓存优化**：利用L3缓存，优化数据访问模式
  </guideline>

  <process>
    ## RK3588优化实施流程

    ### Step 1: 硬件环境检测
    ```cpp
    // 硬件信息检测
    class RK3588HardwareInfo {
    public:
        struct CPUInfo {
            int big_cores;      // A76核心数
            int little_cores;   // A55核心数
            int max_freq_big;   // A76最大频率
            int max_freq_little; // A55最大频率
        };
        
        struct NPUInfo {
            std::string version;    // RKNN版本
            int max_ops;           // 最大算力TOPS
            bool int8_support;     // INT8支持
            bool int16_support;    // INT16支持
        };
        
        static CPUInfo getCPUInfo();
        static NPUInfo getNPUInfo();
        static int getCurrentTemperature();
        static int getMemoryBandwidth();
    };
    ```

    ### Step 2: RKNN模型优化
    ```cpp
    // RKNN模型管理器
    class RKNNModelManager {
    private:
        rknn_context context_;
        rknn_input_output_num io_num_;
        rknn_tensor_attr* input_attrs_;
        rknn_tensor_attr* output_attrs_;
        
        // 内存池，避免频繁分配
        std::unique_ptr<uint8_t[]> input_buffer_;
        std::unique_ptr<uint8_t[]> output_buffer_;
        size_t input_size_;
        size_t output_size_;
        
    public:
        // 模型加载和初始化
        bool loadModel(const std::string& model_path);
        bool warmupModel();  // 预热NPU
        
        // 推理接口
        bool inference(const cv::Mat& input, std::vector<Detection>& results);
        
        // 性能监控
        void getPerformanceMetrics(NPUMetrics& metrics);
    };
    ```

    ### Step 3: 内存优化策略
    ```cpp
    // 对齐内存分配器
    class AlignedMemoryAllocator {
    private:
        static constexpr size_t NPU_INPUT_ALIGNMENT = 32;
        static constexpr size_t NPU_OUTPUT_ALIGNMENT = 16;
        
    public:
        // NPU输入内存分配
        static void* allocateInputBuffer(size_t size) {
            return aligned_alloc(NPU_INPUT_ALIGNMENT, 
                               alignUp(size, NPU_INPUT_ALIGNMENT));
        }
        
        // NPU输出内存分配
        static void* allocateOutputBuffer(size_t size) {
            return aligned_alloc(NPU_OUTPUT_ALIGNMENT, 
                               alignUp(size, NPU_OUTPUT_ALIGNMENT));
        }
        
        static void deallocate(void* ptr) {
            free(ptr);
        }
    };

    // 零拷贝图像预处理
    class ZeroCopyPreprocessor {
    private:
        rga_context rga_ctx_;  // RGA硬件加速器
        
    public:
        // 使用RGA进行格式转换和缩放
        bool preprocessWithRGA(const cv::Mat& input, 
                              void* output_buffer,
                              int target_width, 
                              int target_height);
    };
    ```

    ### Step 4: 多核优化调度
    ```cpp
    // CPU亲和性管理
    class CPUAffinityManager {
    private:
        std::vector<int> big_cores_;    // A76核心列表
        std::vector<int> little_cores_; // A55核心列表
        
    public:
        // 绑定线程到大核
        bool bindToBigCore(std::thread::id thread_id, int core_index);
        
        // 绑定线程到小核
        bool bindToLittleCore(std::thread::id thread_id, int core_index);
        
        // 获取当前线程的核心
        int getCurrentCore();
    };

    // 异构多核任务调度
    class HeterogeneousScheduler {
    private:
        ThreadPool big_core_pool_;    // 大核线程池
        ThreadPool little_core_pool_; // 小核线程池
        
    public:
        // 推理任务提交到大核
        std::future<DetectionResult> submitInferenceTask(
            std::function<DetectionResult()> task);
            
        // 预处理任务提交到小核
        std::future<cv::Mat> submitPreprocessTask(
            std::function<cv::Mat()> task);
    };
    ```

    ### Step 5: 温度和功耗管理
    ```cpp
    // 温度监控和频率调节
    class ThermalManager {
    private:
        std::atomic<int> current_temp_{0};
        std::atomic<bool> throttling_{false};
        std::thread monitor_thread_;
        
        static constexpr int TEMP_THRESHOLD_HIGH = 70;  // 开始降频
        static constexpr int TEMP_THRESHOLD_LOW = 60;   // 恢复频率
        
    public:
        void startMonitoring();
        void stopMonitoring();
        
        // 动态频率调节
        void adjustFrequency();
        
        // 获取当前温度
        int getCurrentTemperature() const;
        
        // 是否正在降频
        bool isThrottling() const;
    };

    // 功耗管理器
    class PowerManager {
    private:
        enum class PowerMode {
            PERFORMANCE,    // 性能模式
            BALANCED,      // 平衡模式
            POWER_SAVE     // 省电模式
        };
        
        PowerMode current_mode_{PowerMode::BALANCED};
        
    public:
        void setPowerMode(PowerMode mode);
        void adjustCPUGovernor(const std::string& governor);
        void adjustNPUFrequency(int frequency);
    };
    ```

    ### Step 6: 流水线并行优化
    ```cpp
    // 三级流水线：预处理->推理->后处理
    class PipelineProcessor {
    private:
        struct PipelineStage {
            std::queue<cv::Mat> input_queue;
            std::queue<RawDetection> detection_queue;
            std::queue<DetectionResult> result_queue;
            std::mutex mutex;
            std::condition_variable cv;
        };
        
        PipelineStage preprocess_stage_;
        PipelineStage inference_stage_;
        PipelineStage postprocess_stage_;
        
        std::thread preprocess_thread_;
        std::thread inference_thread_;
        std::thread postprocess_thread_;
        
    public:
        void startPipeline();
        void stopPipeline();
        
        // 异步推理接口
        std::future<DetectionResult> processAsync(const cv::Mat& image);
        
        // 获取流水线状态
        PipelineMetrics getMetrics();
    };
    ```

    ### Step 7: 性能监控和调优
    ```cpp
    // RK3588性能监控器
    class RK3588PerformanceMonitor {
    private:
        struct Metrics {
            // NPU指标
            float npu_utilization;
            float npu_frequency;
            int npu_temperature;
            
            // CPU指标
            float cpu_utilization[8];  // 8个核心
            float cpu_frequency[8];
            
            // 内存指标
            float memory_usage;
            float memory_bandwidth;
            
            // 推理指标
            float inference_fps;
            float average_latency;
            float peak_latency;
        };
        
        Metrics current_metrics_;
        std::thread monitor_thread_;
        
    public:
        void startMonitoring();
        Metrics getCurrentMetrics();
        
        // 性能分析报告
        void generatePerformanceReport(const std::string& filename);
    };
    ```

    ### Step 8: 自适应优化
    ```cpp
    // 自适应性能调优器
    class AdaptiveOptimizer {
    private:
        struct OptimizationConfig {
            int batch_size;
            int thread_count;
            float cpu_frequency_scale;
            bool enable_rga_acceleration;
            int memory_pool_size;
        };
        
        OptimizationConfig current_config_;
        PerformanceHistory history_;
        
    public:
        // 根据历史性能数据自动调优
        void autoTune();
        
        // 负载自适应
        void adaptToWorkload(float current_load);
        
        // 温度自适应
        void adaptToTemperature(int temperature);
    };
    ```
  </process>

  <criteria>
    ## RK3588优化质量标准

    ### 性能指标
    - ✅ NPU利用率 > 85%（推理阶段）
    - ✅ 单帧延迟 < 30ms（640x640 YOLOV8）
    - ✅ 吞吐量 > 40 FPS（流水线模式）
    - ✅ 内存带宽利用率 > 70%
    - ✅ CPU大核利用率 < 60%

    ### 温度控制
    - ✅ 正常负载温度 < 65°C
    - ✅ 峰值负载温度 < 75°C
    - ✅ 温度波动 < 5°C
    - ✅ 降频触发率 < 5%
    - ✅ 热启动时间 < 10s

    ### 功耗管理
    - ✅ 平均功耗 < 8W
    - ✅ 峰值功耗 < 10W
    - ✅ 空闲功耗 < 2W
    - ✅ 功耗效率 > 4 FPS/W
    - ✅ 电池续航 > 2小时（移动设备）

    ### 稳定性
    - ✅ 连续运行24小时无性能衰减
    - ✅ 内存泄漏 < 100KB/小时
    - ✅ NPU错误率 < 0.1%
    - ✅ 系统崩溃率 < 0.01%
    - ✅ 热重启恢复时间 < 30s

    ### 兼容性
    - ✅ 支持RKNN 1.4.0+版本
    - ✅ 兼容不同RK3588硬件版本
    - ✅ 支持多种YOLOV8模型尺寸
    - ✅ 适配不同输入分辨率
    - ✅ 支持动态批处理大小
  </criteria>
</execution> 