<execution>
  <constraint>
    ## YOLOV8技术约束
    - **模型格式**：必须从PyTorch导出为ONNX，再转换为RKNN格式
    - **输入尺寸**：支持640x640、416x416、320x320等固定尺寸
    - **量化限制**：RK3588仅支持INT8量化，精度损失不可避免
    - **输出格式**：YOLOV8输出为[1, 84, 8400]格式，需要解析
    - **NMS处理**：必须在CPU端实现NMS，NPU不支持
    - **类别数量**：COCO数据集80类，自定义数据集需重新训练
  </constraint>

  <rule>
    ## YOLOV8集成强制规则
    - **模型验证**：转换后模型必须验证精度，mAP损失<2%
    - **预处理标准化**：输入必须归一化到[0,1]，RGB格式
    - **后处理一致性**：置信度阈值0.25，IoU阈值0.45
    - **内存管理**：输入输出tensor必须使用对齐内存
    - **线程安全**：多线程环境下模型推理必须串行化
    - **错误处理**：推理失败必须返回空结果，不能崩溃
    - **性能监控**：每次推理必须记录延迟和吞吐量
  </rule>

  <guideline>
    ## YOLOV8集成指导
    - **模型选择**：根据精度要求选择YOLOv8n/s/m/l/x版本
    - **数据增强**：训练时考虑目标设备的图像特征
    - **后处理优化**：使用SIMD指令加速NMS计算
    - **批处理策略**：虽然NPU不支持batch，可以异步流水线
    - **缓存策略**：频繁访问的检测结果可以缓存
    - **动态调整**：根据场景动态调整置信度阈值
  </guideline>

  <process>
    ## YOLOV8集成实施流程

    ### Step 1: 模型准备和转换
    ```python
    # YOLOV8模型转换流程
    from ultralytics import YOLO
    import torch
    
    # 1. 加载预训练模型
    model = YOLO('yolov8n.pt')  # n/s/m/l/x
    
    # 2. 导出ONNX格式
    model.export(
        format='onnx',
        imgsz=640,
        optimize=True,
        simplify=True
    )
    
    # 3. 使用RKNN-Toolkit2转换
    from rknn.api import RKNN
    
    rknn = RKNN(verbose=True)
    rknn.config(
        mean_values=[[0, 0, 0]],
        std_values=[[255, 255, 255]],
        target_platform='rk3588'
    )
    rknn.load_onnx(model='yolov8n.onnx')
    rknn.build(do_quantization=True, dataset='./dataset.txt')
    rknn.export_rknn('./yolov8n_rk3588.rknn')
    ```

    ### Step 2: C++推理引擎实现
    ```cpp
    // YOLOV8推理引擎
    class YOLOV8Engine {
    private:
        // RKNN上下文
        rknn_context ctx_;
        rknn_input_output_num io_num_;
        rknn_tensor_attr* input_attrs_;
        rknn_tensor_attr* output_attrs_;
        
        // 模型参数
        int input_width_ = 640;
        int input_height_ = 640;
        int num_classes_ = 80;
        float conf_threshold_ = 0.25f;
        float iou_threshold_ = 0.45f;
        
        // 内存缓冲区
        std::unique_ptr<uint8_t[]> input_buffer_;
        std::unique_ptr<float[]> output_buffer_;
        
    public:
        // 初始化模型
        bool initialize(const std::string& model_path);
        
        // 推理接口
        std::vector<Detection> detect(const cv::Mat& image);
        
        // 参数设置
        void setConfidenceThreshold(float threshold);
        void setIoUThreshold(float threshold);
        
    private:
        // 预处理
        bool preprocess(const cv::Mat& image, uint8_t* input_buffer);
        
        // 后处理
        std::vector<Detection> postprocess(const float* output, 
                                         int output_size);
        
        // NMS实现
        std::vector<Detection> applyNMS(const std::vector<Detection>& detections);
    };
    ```

    ### Step 3: 高效预处理实现
    ```cpp
    // YOLOV8预处理器
    class YOLOV8Preprocessor {
    private:
        int target_width_;
        int target_height_;
        bool use_rga_acceleration_;
        rga_context rga_ctx_;
        
    public:
        YOLOV8Preprocessor(int width, int height, bool use_rga = true)
            : target_width_(width), target_height_(height), 
              use_rga_acceleration_(use_rga) {}
        
        // 图像预处理主函数
        bool process(const cv::Mat& input, uint8_t* output_buffer) {
            cv::Mat resized, normalized;
            
            // 1. 保持宽高比的resize
            cv::Mat padded = letterboxResize(input, target_width_, target_height_);
            
            // 2. 颜色空间转换 BGR->RGB
            cv::cvtColor(padded, resized, cv::COLOR_BGR2RGB);
            
            // 3. 归一化到[0,1]
            resized.convertTo(normalized, CV_32F, 1.0/255.0);
            
            // 4. HWC->CHW格式转换
            return hwcToCHW(normalized, output_buffer);
        }
        
    private:
        // Letterbox resize保持宽高比
        cv::Mat letterboxResize(const cv::Mat& image, int target_w, int target_h);
        
        // HWC到CHW格式转换
        bool hwcToCHW(const cv::Mat& hwc_image, uint8_t* chw_buffer);
    };
    ```

    ### Step 4: 高性能后处理实现
    ```cpp
    // YOLOV8后处理器
    class YOLOV8Postprocessor {
    private:
        int num_classes_;
        float conf_threshold_;
        float iou_threshold_;
        
        // SIMD优化的向量化操作
        void vectorizedSigmoid(const float* input, float* output, int size);
        void vectorizedMultiply(const float* a, const float* b, float* result, int size);
        
    public:
        YOLOV8Postprocessor(int num_classes = 80, 
                           float conf_thresh = 0.25f, 
                           float iou_thresh = 0.45f)
            : num_classes_(num_classes), 
              conf_threshold_(conf_thresh), 
              iou_threshold_(iou_thresh) {}
        
        // 主后处理函数
        std::vector<Detection> process(const float* output, 
                                     int output_size,
                                     int orig_width, 
                                     int orig_height);
        
    private:
        // 高效NMS实现
        std::vector<Detection> applyNMS(const std::vector<Detection>& detections);
        
        // IoU计算
        float calculateIoU(const BBox& box1, const BBox& box2);
    };
    ```

    ### Step 5: 完整推理流水线
    ```cpp
    // YOLOV8完整推理流水线
    class YOLOV8Pipeline {
    private:
        std::unique_ptr<YOLOV8Engine> engine_;
        std::unique_ptr<YOLOV8Preprocessor> preprocessor_;
        std::unique_ptr<YOLOV8Postprocessor> postprocessor_;
        
        // 性能统计
        std::atomic<uint64_t> total_frames_{0};
        std::atomic<uint64_t> total_inference_time_{0};
        
    public:
        bool initialize(const std::string& model_path, 
                       int input_width = 640, 
                       int input_height = 640);
        
        // 端到端检测
        std::vector<Detection> detect(const cv::Mat& image);
        
        // 获取性能指标
        PerformanceMetrics getPerformanceMetrics();
    };
    ```

    ### Step 6: 多线程异步处理
    ```cpp
    // 异步YOLOV8处理器
    class AsyncYOLOV8Processor {
    private:
        std::unique_ptr<YOLOV8Pipeline> pipeline_;
        ThreadSafeQueue<ProcessingTask> task_queue_;
        std::vector<std::thread> worker_threads_;
        std::atomic<bool> stop_flag_{false};
        
        struct ProcessingTask {
            cv::Mat image;
            std::promise<std::vector<Detection>> promise;
        };
        
    public:
        bool start(const std::string& model_path, int num_workers = 1);
        
        // 异步检测接口
        std::future<std::vector<Detection>> detectAsync(const cv::Mat& image);
        
    private:
        void workerLoop();
    };
    ```
  </process>

  <criteria>
    ## YOLOV8集成质量标准

    ### 精度指标
    - ✅ mAP50 精度损失 < 2%（相比原始模型）
    - ✅ 小目标检测召回率 > 85%
    - ✅ 误检率 < 5%
    - ✅ 漏检率 < 10%
    - ✅ 多类别平均精度 > 90%

    ### 性能指标
    - ✅ 单帧推理延迟 < 30ms（640x640）
    - ✅ 端到端延迟 < 50ms（包含前后处理）
    - ✅ 吞吐量 > 25 FPS
    - ✅ 内存占用 < 1.5GB
    - ✅ NPU利用率 > 80%

    ### 鲁棒性
    - ✅ 不同光照条件下精度稳定
    - ✅ 不同分辨率输入适应性好
    - ✅ 长时间运行精度无衰减
    - ✅ 异常输入不会导致崩溃
    - ✅ 内存泄漏率 < 10KB/小时

    ### 集成质量
    - ✅ API接口简洁易用
    - ✅ 配置参数可动态调整
    - ✅ 支持多线程并发调用
    - ✅ 错误处理完善
    - ✅ 日志信息详细且有用

    ### 兼容性
    - ✅ 支持YOLOV8n/s/m/l/x所有版本
    - ✅ 支持自定义类别数量
    - ✅ 支持多种输入分辨率
    - ✅ 兼容OpenCV 4.x
    - ✅ 支持RKNN 1.4.0+版本
  </criteria>
</execution> 