<execution>
  <constraint>
    ## 技术约束条件
    - **编译器限制**：必须使用GCC 7.5+或Clang 10+，支持C++17标准
    - **内存限制**：RK3588最大32GB内存，需要合理规划内存使用
    - **实时性要求**：推理延迟必须控制在100ms以内
    - **温度约束**：芯片温度不能超过85°C，需要动态调频策略
    - **功耗限制**：峰值功耗不超过10W，需要功耗优化
    - **线程安全**：多线程环境下必须保证数据一致性
  </constraint>

  <rule>
    ## 强制开发规则
    - **RAII原则**：所有资源管理必须遵循RAII，避免内存泄漏
    - **异常安全**：所有函数必须提供基本异常安全保证
    - **线程安全**：共享资源访问必须使用适当的同步机制
    - **错误处理**：必须使用std::expected或错误码，不允许异常穿透API边界
    - **性能监控**：关键路径必须添加性能计数器和日志
    - **代码规范**：严格遵循Google C++编码规范
    - **单元测试**：核心模块测试覆盖率不低于80%
  </rule>

  <guideline>
    ## 开发指导原则
    - **零拷贝优先**：尽可能使用引用、指针和移动语义减少数据拷贝
    - **缓存友好**：数据结构设计考虑CPU缓存行对齐和访问模式
    - **模块化设计**：使用接口抽象，便于单元测试和模块替换
    - **配置驱动**：关键参数通过配置文件控制，支持运行时调整
    - **渐进优化**：先实现功能正确性，再进行性能优化
    - **文档完整**：API文档使用Doxygen格式，包含使用示例
  </guideline>

  <process>
    ## C++后端开发流程

    ### Step 1: 项目架构设计
    ```cpp
    // 目录结构设计
    project/
    ├── include/           # 头文件
    │   ├── core/         # 核心组件
    │   ├── api/          # API接口
    │   └── utils/        # 工具类
    ├── src/              # 源代码
    │   ├── core/         # 核心实现
    │   ├── api/          # API实现
    │   └── utils/        # 工具实现
    ├── tests/            # 单元测试
    ├── benchmarks/       # 性能测试
    ├── docs/             # 文档
    └── CMakeLists.txt    # 构建配置
    ```

    ### Step 2: 核心类设计
    ```cpp
    // 推理引擎接口设计
    class InferenceEngine {
    public:
        virtual ~InferenceEngine() = default;
        virtual std::expected<void, Error> initialize(const Config& config) = 0;
        virtual std::expected<DetectionResult, Error> 
            infer(const cv::Mat& image) = 0;
        virtual void shutdown() = 0;
    };

    // RKNN推理引擎实现
    class RKNNInferenceEngine : public InferenceEngine {
    private:
        rknn_context ctx_;
        std::unique_ptr<MemoryPool> memory_pool_;
        std::unique_ptr<ThreadPool> thread_pool_;
    public:
        std::expected<void, Error> initialize(const Config& config) override;
        std::expected<DetectionResult, Error> infer(const cv::Mat& image) override;
    };
    ```

    ### Step 3: 内存管理优化
    ```cpp
    // 内存池实现
    class MemoryPool {
    private:
        std::vector<std::unique_ptr<uint8_t[]>> pools_;
        std::queue<uint8_t*> available_;
        std::mutex mutex_;
        
    public:
        uint8_t* allocate(size_t size);
        void deallocate(uint8_t* ptr);
        void preAllocate(size_t count, size_t size);
    };

    // RAII内存管理器
    class MemoryGuard {
        MemoryPool* pool_;
        uint8_t* ptr_;
    public:
        explicit MemoryGuard(MemoryPool* pool, size_t size);
        ~MemoryGuard() { if (ptr_) pool_->deallocate(ptr_); }
        uint8_t* get() const { return ptr_; }
    };
    ```

    ### Step 4: 多线程并发设计
    ```cpp
    // 线程安全的任务队列
    template<typename T>
    class ThreadSafeQueue {
    private:
        mutable std::mutex mutex_;
        std::queue<T> queue_;
        std::condition_variable condition_;
        
    public:
        void push(const T& item);
        bool tryPop(T& item);
        void waitAndPop(T& item);
        bool empty() const;
    };

    // 推理任务调度器
    class InferenceScheduler {
    private:
        std::vector<std::thread> workers_;
        ThreadSafeQueue<InferenceTask> task_queue_;
        std::atomic<bool> stop_flag_{false};
        
    public:
        void start(size_t worker_count);
        void submit(InferenceTask task);
        void stop();
    };
    ```

    ### Step 5: 性能监控集成
    ```cpp
    // 性能计数器
    class PerformanceCounter {
    private:
        std::atomic<uint64_t> total_requests_{0};
        std::atomic<uint64_t> total_latency_us_{0};
        std::atomic<uint64_t> peak_memory_mb_{0};
        
    public:
        void recordRequest(uint64_t latency_us);
        void recordMemoryUsage(uint64_t memory_mb);
        PerformanceMetrics getMetrics() const;
    };

    // RAII性能计时器
    class ScopedTimer {
        std::chrono::high_resolution_clock::time_point start_;
        PerformanceCounter* counter_;
    public:
        explicit ScopedTimer(PerformanceCounter* counter);
        ~ScopedTimer();
    };
    ```

    ### Step 6: API接口实现
    ```cpp
    // HTTP服务器集成
    class DetectionServer {
    private:
        std::unique_ptr<InferenceEngine> engine_;
        std::unique_ptr<httplib::Server> server_;
        std::unique_ptr<PerformanceCounter> perf_counter_;
        
    public:
        void setupRoutes();
        void handleDetection(const httplib::Request& req, 
                           httplib::Response& res);
        void start(const std::string& host, int port);
    };
    ```

    ### Step 7: 错误处理和日志
    ```cpp
    // 错误类型定义
    enum class ErrorCode {
        SUCCESS = 0,
        INITIALIZATION_FAILED,
        MODEL_LOAD_FAILED,
        INFERENCE_FAILED,
        MEMORY_ALLOCATION_FAILED
    };

    // 结构化日志
    class Logger {
    public:
        template<typename... Args>
        void info(const std::string& format, Args&&... args);
        
        template<typename... Args>
        void error(const std::string& format, Args&&... args);
        
        void setLevel(LogLevel level);
    };
    ```

    ### Step 8: 单元测试框架
    ```cpp
    // Google Test集成
    class InferenceEngineTest : public ::testing::Test {
    protected:
        void SetUp() override;
        void TearDown() override;
        std::unique_ptr<InferenceEngine> engine_;
    };

    TEST_F(InferenceEngineTest, InitializationTest) {
        Config config = createTestConfig();
        auto result = engine_->initialize(config);
        EXPECT_TRUE(result.has_value());
    }

    TEST_F(InferenceEngineTest, InferenceAccuracyTest) {
        cv::Mat test_image = loadTestImage();
        auto result = engine_->infer(test_image);
        ASSERT_TRUE(result.has_value());
        validateDetectionResult(result.value());
    }
    ```
  </process>

  <criteria>
    ## 开发质量标准

    ### 代码质量
    - ✅ 编译无警告（-Wall -Wextra -Werror）
    - ✅ 静态分析通过（clang-static-analyzer）
    - ✅ 内存检查通过（Valgrind/AddressSanitizer）
    - ✅ 线程安全检查通过（ThreadSanitizer）
    - ✅ 代码覆盖率达到80%以上

    ### 性能指标
    - ✅ 单帧推理延迟 < 50ms（640x640输入）
    - ✅ 吞吐量 > 30 FPS（单线程）
    - ✅ 内存占用 < 2GB（包含模型）
    - ✅ CPU利用率 < 80%（正常负载）
    - ✅ 温度控制在75°C以下

    ### 稳定性要求
    - ✅ 7x24小时连续运行无崩溃
    - ✅ 内存泄漏率 < 1MB/小时
    - ✅ 错误恢复时间 < 5秒
    - ✅ 并发处理1000个请求无异常
    - ✅ 异常情况下优雅降级

    ### 可维护性
    - ✅ API文档完整（Doxygen）
    - ✅ 代码注释覆盖率 > 60%
    - ✅ 配置参数可动态调整
    - ✅ 日志信息详细且结构化
    - ✅ 单元测试覆盖核心功能
  </criteria>
</execution> 