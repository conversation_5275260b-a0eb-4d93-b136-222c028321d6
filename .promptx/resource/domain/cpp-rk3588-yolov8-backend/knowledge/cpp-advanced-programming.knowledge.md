# C++高级编程知识体系

## 现代C++特性掌握

### C++17/20核心特性
- **结构化绑定**：`auto [x, y] = std::make_pair(1, 2);`
- **if constexpr**：编译期条件判断
- **std::optional**：可选值类型，避免空指针
- **std::variant**：类型安全的联合体
- **std::string_view**：零拷贝字符串视图
- **并行算法**：`std::execution::par`并行执行
- **协程**：`co_await`, `co_yield`, `co_return`

### 内存管理专家级技巧
```cpp
// 自定义内存分配器
template<typename T>
class PoolAllocator {
private:
    static constexpr size_t POOL_SIZE = 1024;
    alignas(T) char pool_[POOL_SIZE * sizeof(T)];
    std::bitset<POOL_SIZE> used_;
    
public:
    T* allocate() {
        for (size_t i = 0; i < POOL_SIZE; ++i) {
            if (!used_[i]) {
                used_[i] = true;
                return reinterpret_cast<T*>(&pool_[i * sizeof(T)]);
            }
        }
        throw std::bad_alloc();
    }
    
    void deallocate(T* ptr) {
        size_t index = (reinterpret_cast<char*>(ptr) - pool_) / sizeof(T);
        used_[index] = false;
    }
};

// RAII智能指针封装
template<typename T, typename Deleter = std::default_delete<T>>
class unique_resource {
private:
    T resource_;
    Deleter deleter_;
    bool valid_;
    
public:
    unique_resource(T&& resource, Deleter deleter = {})
        : resource_(std::move(resource)), deleter_(deleter), valid_(true) {}
    
    ~unique_resource() {
        if (valid_) deleter_(resource_);
    }
    
    T& get() { return resource_; }
    T release() {
        valid_ = false;
        return std::move(resource_);
    }
};
```

### 高性能并发编程
```cpp
// 无锁队列实现
template<typename T>
class LockFreeQueue {
private:
    struct Node {
        std::atomic<T*> data{nullptr};
        std::atomic<Node*> next{nullptr};
    };
    
    std::atomic<Node*> head_{new Node};
    std::atomic<Node*> tail_{head_.load()};
    
public:
    void enqueue(T item) {
        Node* new_node = new Node;
        T* data = new T(std::move(item));
        Node* prev_tail = tail_.exchange(new_node);
        prev_tail->data.store(data);
        prev_tail->next.store(new_node);
    }
    
    bool dequeue(T& result) {
        Node* head = head_.load();
        Node* next = head->next.load();
        
        if (next == nullptr) return false;
        
        T* data = next->data.load();
        if (data == nullptr) return false;
        
        result = *data;
        delete data;
        head_.store(next);
        delete head;
        return true;
    }
};

// 线程池实现
class ThreadPool {
private:
    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> tasks_;
    std::mutex queue_mutex_;
    std::condition_variable condition_;
    bool stop_;
    
public:
    ThreadPool(size_t threads) : stop_(false) {
        for (size_t i = 0; i < threads; ++i) {
            workers_.emplace_back([this] {
                for (;;) {
                    std::function<void()> task;
                    {
                        std::unique_lock<std::mutex> lock(queue_mutex_);
                        condition_.wait(lock, [this] { return stop_ || !tasks_.empty(); });
                        
                        if (stop_ && tasks_.empty()) return;
                        
                        task = std::move(tasks_.front());
                        tasks_.pop();
                    }
                    task();
                }
            });
        }
    }
    
    template<typename F, typename... Args>
    auto enqueue(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        
        using return_type = typename std::result_of<F(Args...)>::type;
        
        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        std::future<return_type> res = task->get_future();
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            if (stop_) throw std::runtime_error("enqueue on stopped ThreadPool");
            tasks_.emplace([task](){ (*task)(); });
        }
        condition_.notify_one();
        return res;
    }
};
```

## 性能优化专业技术

### CPU缓存优化
```cpp
// 缓存友好的数据结构
struct alignas(64) CacheLinePadded {  // 避免false sharing
    std::atomic<int> counter;
    char padding[64 - sizeof(std::atomic<int>)];
};

// 数据预取优化
void prefetch_data(const void* addr) {
    __builtin_prefetch(addr, 0, 3);  // 预取到L1缓存
}

// 内存访问模式优化
void matrix_multiply_optimized(const float* A, const float* B, float* C, 
                              int N, int block_size = 64) {
    for (int i = 0; i < N; i += block_size) {
        for (int j = 0; j < N; j += block_size) {
            for (int k = 0; k < N; k += block_size) {
                // 分块矩阵乘法，提高缓存命中率
                for (int ii = i; ii < std::min(i + block_size, N); ++ii) {
                    for (int jj = j; jj < std::min(j + block_size, N); ++jj) {
                        float sum = 0.0f;
                        for (int kk = k; kk < std::min(k + block_size, N); ++kk) {
                            sum += A[ii * N + kk] * B[kk * N + jj];
                        }
                        C[ii * N + jj] += sum;
                    }
                }
            }
        }
    }
}
```

### SIMD向量化优化
```cpp
#include <immintrin.h>

// AVX2向量化加法
void vectorized_add(const float* a, const float* b, float* result, size_t size) {
    size_t simd_size = size & ~7;  // 8的倍数
    
    for (size_t i = 0; i < simd_size; i += 8) {
        __m256 va = _mm256_load_ps(&a[i]);
        __m256 vb = _mm256_load_ps(&b[i]);
        __m256 vr = _mm256_add_ps(va, vb);
        _mm256_store_ps(&result[i], vr);
    }
    
    // 处理剩余元素
    for (size_t i = simd_size; i < size; ++i) {
        result[i] = a[i] + b[i];
    }
}

// NEON向量化（ARM）
#ifdef __ARM_NEON
#include <arm_neon.h>

void neon_add(const float* a, const float* b, float* result, size_t size) {
    size_t simd_size = size & ~3;  // 4的倍数
    
    for (size_t i = 0; i < simd_size; i += 4) {
        float32x4_t va = vld1q_f32(&a[i]);
        float32x4_t vb = vld1q_f32(&b[i]);
        float32x4_t vr = vaddq_f32(va, vb);
        vst1q_f32(&result[i], vr);
    }
    
    for (size_t i = simd_size; i < size; ++i) {
        result[i] = a[i] + b[i];
    }
}
#endif
```

## 系统级编程技术

### 零拷贝技术
```cpp
// mmap零拷贝文件映射
class MemoryMappedFile {
private:
    void* mapped_memory_;
    size_t file_size_;
    int fd_;
    
public:
    MemoryMappedFile(const std::string& filename) {
        fd_ = open(filename.c_str(), O_RDONLY);
        if (fd_ == -1) throw std::runtime_error("Cannot open file");
        
        struct stat sb;
        if (fstat(fd_, &sb) == -1) throw std::runtime_error("Cannot get file size");
        file_size_ = sb.st_size;
        
        mapped_memory_ = mmap(nullptr, file_size_, PROT_READ, MAP_PRIVATE, fd_, 0);
        if (mapped_memory_ == MAP_FAILED) throw std::runtime_error("mmap failed");
    }
    
    ~MemoryMappedFile() {
        if (mapped_memory_ != MAP_FAILED) {
            munmap(mapped_memory_, file_size_);
        }
        if (fd_ != -1) close(fd_);
    }
    
    const void* data() const { return mapped_memory_; }
    size_t size() const { return file_size_; }
};

// 共享内存通信
class SharedMemoryBuffer {
private:
    void* shm_ptr_;
    size_t size_;
    int shm_fd_;
    
public:
    SharedMemoryBuffer(const std::string& name, size_t size, bool create = false) 
        : size_(size) {
        int flags = create ? (O_CREAT | O_RDWR) : O_RDWR;
        shm_fd_ = shm_open(name.c_str(), flags, 0666);
        
        if (create) ftruncate(shm_fd_, size);
        
        shm_ptr_ = mmap(nullptr, size, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd_, 0);
    }
    
    void* data() { return shm_ptr_; }
    size_t size() const { return size_; }
};
```

### 高精度时间测量
```cpp
// 高精度性能计时器
class HighResolutionTimer {
private:
    std::chrono::high_resolution_clock::time_point start_time_;
    std::chrono::high_resolution_clock::time_point end_time_;
    
public:
    void start() {
        start_time_ = std::chrono::high_resolution_clock::now();
    }
    
    void stop() {
        end_time_ = std::chrono::high_resolution_clock::now();
    }
    
    double elapsed_nanoseconds() const {
        auto duration = end_time_ - start_time_;
        return std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();
    }
    
    double elapsed_microseconds() const {
        return elapsed_nanoseconds() / 1000.0;
    }
    
    double elapsed_milliseconds() const {
        return elapsed_nanoseconds() / 1000000.0;
    }
};

// RAII自动计时器
class ScopedTimer {
private:
    std::chrono::high_resolution_clock::time_point start_;
    std::string name_;
    
public:
    ScopedTimer(const std::string& name) : name_(name) {
        start_ = std::chrono::high_resolution_clock::now();
    }
    
    ~ScopedTimer() {
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start_);
        std::cout << name_ << " took: " << duration.count() << " μs" << std::endl;
    }
};
```

## 错误处理和调试技术

### 现代错误处理
```cpp
// std::expected替代方案（C++23之前）
template<typename T, typename E>
class Expected {
private:
    std::variant<T, E> data_;
    
public:
    Expected(const T& value) : data_(value) {}
    Expected(const E& error) : data_(error) {}
    
    bool has_value() const { return std::holds_alternative<T>(data_); }
    
    const T& value() const { return std::get<T>(data_); }
    const E& error() const { return std::get<E>(data_); }
    
    template<typename F>
    auto and_then(F&& f) -> Expected<std::invoke_result_t<F, T>, E> {
        if (has_value()) {
            return f(value());
        } else {
            return error();
        }
    }
};

// 错误码枚举
enum class ErrorCode {
    SUCCESS = 0,
    INVALID_ARGUMENT,
    OUT_OF_MEMORY,
    FILE_NOT_FOUND,
    NETWORK_ERROR,
    TIMEOUT
};

// 结果类型定义
template<typename T>
using Result = Expected<T, ErrorCode>;
```

### 高级调试技术
```cpp
// 条件断点宏
#ifdef DEBUG
#define DEBUG_BREAK_IF(condition) \
    do { if (condition) __builtin_trap(); } while(0)
#else
#define DEBUG_BREAK_IF(condition) ((void)0)
#endif

// 性能分析宏
#define PROFILE_SCOPE(name) ScopedTimer timer(name)
#define PROFILE_FUNCTION() PROFILE_SCOPE(__FUNCTION__)

// 内存泄漏检测
class MemoryTracker {
private:
    std::unordered_map<void*, std::pair<size_t, std::string>> allocations_;
    std::mutex mutex_;
    
public:
    void record_allocation(void* ptr, size_t size, const std::string& location) {
        std::lock_guard<std::mutex> lock(mutex_);
        allocations_[ptr] = {size, location};
    }
    
    void record_deallocation(void* ptr) {
        std::lock_guard<std::mutex> lock(mutex_);
        allocations_.erase(ptr);
    }
    
    void report_leaks() {
        std::lock_guard<std::mutex> lock(mutex_);
        for (const auto& [ptr, info] : allocations_) {
            std::cout << "Leak: " << info.second << " (" << info.first << " bytes)" << std::endl;
        }
    }
};
```

## 编译优化技术

### 编译器优化指导
```cpp
// 分支预测优化
#define LIKELY(x)   __builtin_expect(!!(x), 1)
#define UNLIKELY(x) __builtin_expect(!!(x), 0)

if (LIKELY(condition)) {
    // 常见分支
} else {
    // 不常见分支
}

// 函数内联控制
__attribute__((always_inline)) inline void critical_function() {
    // 强制内联的关键函数
}

__attribute__((noinline)) void debug_function() {
    // 禁止内联的调试函数
}

// 循环展开优化
#pragma GCC unroll 4
for (int i = 0; i < size; ++i) {
    // 循环体将被展开4次
}

// 向量化提示
#pragma GCC ivdep  // 告诉编译器没有循环依赖
for (int i = 0; i < size; ++i) {
    array[i] = array[i] * 2;
}
```

### 链接时优化（LTO）
```cmake
# CMakeLists.txt中启用LTO
set(CMAKE_INTERPROCEDURAL_OPTIMIZATION TRUE)

# 或者针对特定目标
set_property(TARGET myapp PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
```

这些高级C++编程技术为RK3588-YOLOV8后端开发提供了坚实的技术基础，确保能够构建高性能、可靠的系统。 