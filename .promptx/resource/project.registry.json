{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-09T13:33:11.540Z", "updatedAt": "2025-07-09T13:33:11.553Z", "resourceCount": 6}, "resources": [{"id": "cpp-rk3588-yolov8-backend", "source": "project", "protocol": "role", "name": "Cpp Rk3588 <PERSON>lov8 Backend 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/cpp-rk3588-yolov8-backend/cpp-rk3588-yolov8-backend.role.md", "metadata": {"createdAt": "2025-07-09T13:33:11.544Z", "updatedAt": "2025-07-09T13:33:11.544Z", "scannedAt": "2025-07-09T13:33:11.544Z"}}, {"id": "cpp-rk3588-yolov8-backend", "source": "project", "protocol": "thought", "name": "Cpp Rk3588 <PERSON>lov8 Backend 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/cpp-rk3588-yolov8-backend/thought/cpp-rk3588-yolov8-backend.thought.md", "metadata": {"createdAt": "2025-07-09T13:33:11.550Z", "updatedAt": "2025-07-09T13:33:11.551Z", "scannedAt": "2025-07-09T13:33:11.550Z"}}, {"id": "cpp-backend-development", "source": "project", "protocol": "execution", "name": "Cpp Backend Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/cpp-rk3588-yolov8-backend/execution/cpp-backend-development.execution.md", "metadata": {"createdAt": "2025-07-09T13:33:11.552Z", "updatedAt": "2025-07-09T13:33:11.552Z", "scannedAt": "2025-07-09T13:33:11.552Z"}}, {"id": "rk3588-optimization", "source": "project", "protocol": "execution", "name": "Rk3588 Optimization 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/cpp-rk3588-yolov8-backend/execution/rk3588-optimization.execution.md", "metadata": {"createdAt": "2025-07-09T13:33:11.552Z", "updatedAt": "2025-07-09T13:33:11.552Z", "scannedAt": "2025-07-09T13:33:11.552Z"}}, {"id": "yolov8-integration", "source": "project", "protocol": "execution", "name": "Yolov8 Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/cpp-rk3588-yolov8-backend/execution/yolov8-integration.execution.md", "metadata": {"createdAt": "2025-07-09T13:33:11.552Z", "updatedAt": "2025-07-09T13:33:11.552Z", "scannedAt": "2025-07-09T13:33:11.552Z"}}, {"id": "cpp-advanced-programming", "source": "project", "protocol": "knowledge", "name": "Cpp Advanced Programming 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/cpp-rk3588-yolov8-backend/knowledge/cpp-advanced-programming.knowledge.md", "metadata": {"createdAt": "2025-07-09T13:33:11.552Z", "updatedAt": "2025-07-09T13:33:11.552Z", "scannedAt": "2025-07-09T13:33:11.552Z"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 1, "thought": 1, "execution": 3, "knowledge": 1}, "bySource": {"project": 6}}}