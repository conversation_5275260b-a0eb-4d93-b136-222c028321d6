{"测试说明": "验证注册系统创建的默认配置是否包含阵营选择功能", "测试用例": {"register_request": {"action": "register_modify", "content": {"gameList": ["csgo2", "apex", "pubg"], "defaultGame": "csgo2", "username": "test_faction_user", "password": "test123456", "isPro": false, "createdAt": "2025-01-03T12:00:00Z", "updatedAt": "2025-01-03T12:00:00Z"}}, "expected_response": {"action": "register_modify_response", "status": "success", "message": "用户注册成功", "data": {"userInfo": {"username": "test_faction_user", "token": "blweb_token_test_faction_user", "isPro": false}, "homeConfig": {"gameName": "csgo2", "cardKey": ""}}}, "验证步骤": ["1. 发送注册请求", "2. 验证注册成功响应", "3. 发送function_read请求读取默认配置", "4. 验证每个配置都包含selectedFaction字段", "5. 验证阵营选择值符合预期：警方、匪方、无"]}, "后续验证请求": {"function_read_request": {"action": "function_read", "content": {"username": "test_faction_user", "gameName": "csgo2", "cardKey": "", "updatedAt": "2025-01-03T12:00:00Z"}}, "expected_configs": [{"presetName": "配置1", "aiMode": "PID", "lockPosition": "头部", "selectedFaction": "警方", "hotkey": "左键", "triggerSwitch": false, "enabled": true}, {"presetName": "配置2", "aiMode": "FOV", "lockPosition": "胸部", "selectedFaction": "匪方", "hotkey": "右键", "triggerSwitch": true, "enabled": true}, {"presetName": "配置3", "aiMode": "FOVPID", "lockPosition": "颈部", "selectedFaction": "无", "hotkey": "中键", "triggerSwitch": false, "enabled": false}, {"presetName": "配置4", "aiMode": "PID", "lockPosition": "颈部", "selectedFaction": "警方", "hotkey": "右键", "triggerSwitch": false, "enabled": false}]}, "测试目标": ["确保注册时创建的默认功能配置包含阵营选择", "验证不同配置有不同的阵营选择（警方、匪方、无）", "确保阵营选择字段在API响应中正确返回", "验证注册后的用户可以正常读取包含阵营的配置"], "预期结果": {"注册成功": "用户成功注册，系统创建默认配置", "配置完整": "每个默认配置都包含selectedFaction字段", "阵营正确": "阵营选择值为：警方、匪方、无", "API兼容": "所有API响应都包含完整的阵营信息"}}