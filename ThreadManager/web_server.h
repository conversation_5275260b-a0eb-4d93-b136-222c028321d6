#pragma once

#include <string>
#include <memory>
#include <mutex>

#include "Poco/Net/HTTPServer.h"
#include "Poco/Net/HTTPRequestHandler.h"
#include "Poco/Net/HTTPRequestHandlerFactory.h"
#include "Poco/Net/HTTPServerRequest.h"
#include "Poco/Net/HTTPServerResponse.h"
#include "Poco/Net/WebSocket.h"
#include "utils/logger.h"

// WebSocket连接处理器类
class WebSocketRequestHandler : public Poco::Net::HTTPRequestHandler
{
public:
    WebSocketRequestHandler();
    virtual void handleRequest(Poco::Net::HTTPServerRequest& request, Poco::Net::HTTPServerResponse& response) override;

private:
    void handleWebSocketConnection(Poco::Net::WebSocket& ws);
};

// 请求处理器工厂类 - 创建WebSocket处理器实例
class WebSocketRequestHandlerFactory : public Poco::Net::HTTPRequestHandlerFactory
{
public:
    WebSocketRequestHandlerFactory();
    virtual Poco::Net::HTTPRequestHandler* createRequestHandler(const Poco::Net::HTTPServerRequest& request) override;
};

// WebSocket服务器管理类
class WebServer {
public:
    // 获取单例实例
    static WebServer& getInstance();

    // 禁止拷贝和赋值
    WebServer(const WebServer&) = delete;
    WebServer& operator=(const WebServer&) = delete;

    // 初始化服务器
    bool initialize(const std::string& host = "0.0.0.0", int port = 8080, const std::string& path = "/ws");

    // 启动服务器
    bool start();

    // 停止服务器
    void stop();

    // 是否正在运行
    bool isRunning() const;

private:
    // 私有构造函数
    WebServer();
    
    // 私有析构函数
    ~WebServer();

    std::string m_host;                                // 服务器主机地址
    int m_port;                                        // 服务器端口
    std::string m_path;                                // WebSocket路径
    std::unique_ptr<Poco::Net::HTTPServer> m_server;   // HTTP服务器实例
    bool m_running;                                    // 服务器运行状态
    mutable std::mutex m_mutex;                        // 互斥锁
};
