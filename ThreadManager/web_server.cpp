#include "web_server.h"
#include "utils/gmodels.h"
#include "backend/blserver/poco_websocket_server.h"

#include "Poco/Net/HTTPServer.h"
#include "Poco/Net/HTTPServerParams.h"
#include "Poco/Net/SocketAddress.h"
#include "Poco/Net/WebSocket.h"
#include "Poco/Net/NetException.h"
#include "Poco/Timespan.h"
#include "Poco/URI.h"
#include "Poco/JSON/Parser.h"
#include "Poco/JSON/Object.h"
#include "Poco/Dynamic/Var.h"
#include "Poco/Exception.h"

#include <iostream>
#include <thread>
#include <chrono>

//============================== WebSocketRequestHandler 实现 ==============================

WebSocketRequestHandler::WebSocketRequestHandler()
{
    // 白名单管理已集中到whitelist::initializeWhitelist()中
}

void WebSocketRequestHandler::handleRequest(Poco::Net::HTTPServerRequest& request, Poco::Net::HTTPServerResponse& response)
{
    LOG_INFO("收到新的连接请求: {}", request.clientAddress().toString());

    try
    {
        // 检查是否是WebSocket握手请求
        if (request.find("Upgrade") != request.end() && 
            Poco::icompare(request["Upgrade"], "websocket") == 0)
        {
            // 设置WebSocket参数
            Poco::Net::WebSocket ws(request, response);
            ws.setReceiveTimeout(Poco::Timespan(60, 0)); // 60秒超时
            ws.setSendTimeout(Poco::Timespan(10, 0));    // 10秒超时
            
            LOG_INFO("WebSocket连接已建立，客户端: {}", request.clientAddress().toString());
            
            // 增加活跃连接计数
            websocket::g_client_count++;
            LOG_INFO("当前活跃连接数: {}", websocket::g_client_count.load());
            
            // 处理WebSocket连接
            handleWebSocketConnection(ws);
        }
        else
        {
            // 不是WebSocket请求，返回400错误
            LOG_WARNING("收到非WebSocket请求: {}", request.clientAddress().toString());
            response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
            response.setContentLength(0);
            response.send();
        }
    }
    catch (Poco::Net::WebSocketException& exc)
    {
        LOG_ERROR("WebSocket异常: {}", exc.displayText());
        switch (exc.code())
        {
        case Poco::Net::WebSocket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
            response.set("Sec-WebSocket-Version", Poco::Net::WebSocket::WEBSOCKET_VERSION);
            response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
            break;
        case Poco::Net::WebSocket::WS_ERR_NO_HANDSHAKE:
            response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
            break;
        case Poco::Net::WebSocket::WS_ERR_HANDSHAKE_NO_VERSION:
            response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
            break;
        case Poco::Net::WebSocket::WS_ERR_HANDSHAKE_NO_KEY:
            response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
            break;
        }
        response.setContentLength(0);
        response.send();
    }
    catch (std::exception& exc)
    {
        LOG_ERROR("处理WebSocket请求时发生异常: {}", exc.what());
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_INTERNAL_SERVER_ERROR);
        response.setContentLength(0);
        response.send();
    }
}

void WebSocketRequestHandler::handleWebSocketConnection(Poco::Net::WebSocket& ws)
{
    try
    {
        // 使用WebSocketMessageProcessor处理WebSocket连接
        WebSocketMessageProcessor::getInstance().processConnection(ws);
    }
    catch (std::exception& exc)
    {
        LOG_ERROR("处理WebSocket连接时发生异常: {}", exc.what());
        
        // 减少活跃连接计数
        websocket::g_client_count--;
        LOG_INFO("WebSocket连接已关闭（异常），当前活跃连接数: {}", websocket::g_client_count.load());
    }
}

//============================== WebSocketRequestHandlerFactory 实现 ==============================

WebSocketRequestHandlerFactory::WebSocketRequestHandlerFactory()
{
    // 白名单管理已集中到whitelist::initializeWhitelist()中
}

Poco::Net::HTTPRequestHandler* WebSocketRequestHandlerFactory::createRequestHandler(const Poco::Net::HTTPServerRequest& request)
{
    LOG_DEBUG("收到请求: {} {}", request.getMethod(), request.getURI());
    
    try
    {
        // 解析URI
        Poco::URI uri(request.getURI());
        
        // 检查请求路径是否为WebSocket路径(/ws)
        if (uri.getPath() == "/ws")
        {
            // 创建WebSocket处理器
            return new WebSocketRequestHandler();
        }
        else
        {
            // 非WebSocket路径的请求，返回404
            LOG_WARNING("路径不支持: {}", uri.getPath());
            return nullptr;
        }
    }
    catch (std::exception& exc)
    {
        LOG_ERROR("解析请求URI时发生异常: {}", exc.what());
        return nullptr;
    }
}

//============================== WebServer 实现 ==============================

WebServer::WebServer()
    : m_host("0.0.0.0")
    , m_port(8080)
    , m_path("/ws")
    , m_server(nullptr)
    , m_running(false)
{
    // 白名单管理已集中到whitelist::initializeWhitelist()中
    LOG_INFO("WebSocket服务器管理器已创建");
}

WebServer::~WebServer()
{
    stop();
    LOG_INFO("WebSocket服务器管理器已销毁");
}

WebServer& WebServer::getInstance()
{
    static WebServer instance;
    return instance;
}

bool WebServer::initialize(const std::string& host, int port, const std::string& path)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_running)
    {
        LOG_WARNING("服务器已在运行中，无法重新初始化");
        return false;
    }
    
    // 记录原始传入的主机地址，但始终使用0.0.0.0作为绑定地址
    LOG_INFO("原始请求的主机地址: {}, 将使用0.0.0.0确保服务正常运行", host);
    m_host = "0.0.0.0";  // 强制使用0.0.0.0作为绑定地址
    m_port = port;
    m_path = path;
    
    LOG_INFO("WebSocket服务器配置: {}:{}{}", m_host, m_port, m_path);
    
    // 更新全局配置
    websocket::g_ws_ip = m_host;
    websocket::g_ws_port = m_port;
    
    return true;
}

bool WebServer::start()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_running)
    {
        LOG_WARNING("服务器已在运行中，无需重复启动");
        return false;
    }
    
    try
    {
        LOG_INFO("正在启动WebSocket服务器: {}:{}{}", m_host, m_port, m_path);
        
        // 创建服务器套接字 - 使用SocketAddress
        Poco::Net::SocketAddress address(m_host, m_port);
        Poco::Net::ServerSocket serverSocket(address, 64);
        
        // 设置服务器参数
        Poco::Net::HTTPServerParams::Ptr params = new Poco::Net::HTTPServerParams;
        params->setKeepAlive(true);
        params->setMaxKeepAliveRequests(100);
        params->setKeepAliveTimeout(Poco::Timespan(60, 0));
        params->setMaxThreads(websocket::g_max_connections);
        params->setThreadIdleTime(Poco::Timespan(10, 0));
        
        // 创建HTTP服务器
        m_server = std::make_unique<Poco::Net::HTTPServer>(
            new WebSocketRequestHandlerFactory(),
            serverSocket,
            params
        );
        
        // 启动服务器
        m_server->start();
        m_running = true;
        websocket::g_ws_server_running = true;
        
        LOG_INFO("WebSocket服务器已启动，监听在 ws://{}:{}{}", m_host, m_port, m_path);
        
        return true;
    }
    catch (Poco::Exception& exc)
    {
        LOG_ERROR("启动WebSocket服务器时发生异常: {}", exc.displayText());
        m_running = false;
        websocket::g_ws_server_running = false;
        return false;
    }
    catch (std::exception& exc)
    {
        LOG_ERROR("启动WebSocket服务器时发生异常: {}", exc.what());
        m_running = false;
        websocket::g_ws_server_running = false;
        return false;
    }
}

void WebServer::stop()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_running)
    {
        return;
    }
    
    LOG_INFO("正在停止WebSocket服务器");
    
    if (m_server)
    {
        m_server->stop();
        m_server.reset();
    }
    
    m_running = false;
    websocket::g_ws_server_running = false;
    
    LOG_INFO("WebSocket服务器已停止");
}

bool WebServer::isRunning() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_running;
}
