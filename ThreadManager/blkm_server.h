#pragma once

#include <string>
#include <memory>
#include "../blkm/blkmsdk.h"

namespace thread_manager {
    // 初始化BLKM服务
    bool initBlkmServer();
    
    // 启动BLKM卡密验证线程
    bool startBlkmThreads();
    
    // 停止BLKM卡密验证线程
    void stopBlkmThreads();
    
    // 返回BLKM线程是否在运行
    bool isBlkmThreadsRunning();
    
    // 卡密验证线程函数
    void cardVerifyThreadFunc();
    
    // 云更新功能检查
    void perform_cloud_update_check();

}
