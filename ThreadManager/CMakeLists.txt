# ThreadManager CMakeLists.txt
# 创建源文件列表
set(THREAD_MANAGER_SOURCES
    web_server.cpp
    web_server.h
    infer_server.cpp
    infer_server.h
    blkm_server.cpp
    blkm_server.h
    lkm_server.cpp
    lkm_server.h
)

# 创建 ThreadManager 库
add_library(thread_manager STATIC ${THREAD_MANAGER_SOURCES})

# 设置包含目录
target_include_directories(thread_manager PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty/CSerialPort-master/include
)

# 链接依赖库
target_link_libraries(thread_manager PUBLIC 
    utils
    Threads::Threads
    blkmapi
    inference_lib
    spdlog::spdlog
    lkmapi
    ${ATOMIC_LIBRARY}
) 