#include "blkm_server.h"
#include <thread>
#include <chrono>
#include <memory>
#include "../utils/logger.h"
#include "../utils/gmodels.h"
#include "../blkm/blkmsdk.h"
#include "../blkm/crypto_utils.h"
#include "../blkm/cloud_update.h"
#include <sstream>

namespace thread_manager {

// 卡密验证线程
std::unique_ptr<std::thread> g_verify_thread = nullptr;

// 初始化BLKM服务
bool initBlkmServer() {
    try {
        LOG_INFO("正在初始化卡密系统...");
        
        // 验证配置是否有效
        if (jfkm_config::g_access_key.empty() || jfkm_config::g_api_host_standard.empty() || jfkm_config::g_api_host_pro.empty()) {
            LOG_ERROR("卡密系统配置无效: access_key或api_host为空");
            return false;
        }
        
        LOG_INFO("卡密系统初始化完成");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("初始化卡密系统异常: {}", e.what());
        return false;
    }
}

/**
 * 卡密验证线程函数
 * 
 * 该函数在独立线程中运行，负责周期性验证卡密状态并更新全局状态变量
 * 功能：
 * 1. 周期性验证当前设置的卡密
 * 2. 支持强制验证模式，可随时触发立即验证
 * 3. 处理设备绑定问题
 * 4. 更新验证状态到全局变量
 */
void cardVerifyThreadFunc() {
    LOG_INFO("卡密验证线程启动");
    
    try {
        // 常量定义
        const int MAX_RETRIES = 8;              // 最大重试次数
        const int SLEEP_SECONDS = 30;           // 达到最大重试次数后的休眠时间
        const int CHECK_INTERVAL = 3;           // 检查周期(秒)
        const int SUCCESS_LOOP_COUNT = 10;      // 验证成功后的循环次数
        int retry_count = 0;                    // 当前重试计数
        
        // 创建KmSdk实例 - 初始使用标准版API地址
        std::string api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
        jfkm::KmSdk km_sdk(jfkm_config::g_access_key, api_host);
        
        // LOG_INFO("当前使用的卡密API地址: {}", api_host);
        
        // 获取设备标识符
        std::string device_id = jfkm::get_device_identifier();
        // LOG_INFO("设备标识: {}", device_id);
        
        // 主循环 - 一直运行直到程序退出
        while (thread_control::g_thread_running) {
            try {
                // 检查是否需要切换API地址（根据Pro状态）
                std::string current_api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
                if (api_host != current_api_host) {
                    LOG_INFO("检测到Pro状态变更，切换卡密API地址: {} -> {}", 
                             api_host, current_api_host);
                    km_sdk.change_api_host(current_api_host);
                    api_host = current_api_host;
                }
                
                if (jfkm_config::g_card_key.empty()) {
                    LOG_WARNING("卡密为空，跳过验证");
                    jfkm_config::g_auth_status = 2; // 验证失败
                    webui::header::cardKeyStatus = false;
                    // 如果卡密为空，休眠一段时间
                    for (int i = 0; i < SUCCESS_LOOP_COUNT; i++) {
                        if (!thread_control::g_thread_running) break;
                        std::this_thread::sleep_for(std::chrono::seconds(CHECK_INTERVAL));
                    }
                    continue;
                }
                
                // 执行卡密验证
                LOG_INFO("开始验证卡密: {}", jfkm_config::g_card_key);
                auto [data, error] = km_sdk.verify_card(
                    jfkm_config::g_card_key, 
                    jfkm_config::g_app_flag, 
                    device_id
                );
                
                // 验证成功
                if (error.empty() && data) {
                    LOG_INFO("卡密验证成功");
                    retry_count = 0;
                    jfkm_config::g_auth_status = 1; // 验证成功
                    webui::header::cardKeyStatus = true;
                    // 打印验证信息
                    std::ostringstream dataStr;
                    data->stringify(dataStr);
                    LOG_INFO("卡密详细信息: {}", dataStr.str());
                    
                    // 提取过期时间信息
                    if (data->has("expire_time")) {
                        try {
                            std::string expire_time = data->getValue<std::string>("expire_time");
                            LOG_INFO("卡密有效期至: {}", expire_time);
                        } catch (...) {
                            LOG_WARNING("无法解析卡密有效期信息");
                        }
                    }
                    
                    // 执行版本检查（仅在卡密验证成功后执行一次）
                    static bool version_checked = false;
                    if (!version_checked) {
                        try {
                            LOG_INFO("开始执行云更新功能检查...");
                            
                            // 调用完整的云更新检查功能
                            perform_cloud_update_check();
                            
                            version_checked = true; // 标记已检查过版本
                        } catch (const std::exception& e) {
                            LOG_ERROR("云更新功能检查异常: {}", e.what());
                        }
                    }
                    
                    // 验证成功后进入休眠循环，同时检测强制验证标志和版本更新请求
                    for (int i = 0; i < SUCCESS_LOOP_COUNT && thread_control::g_thread_running; i++) {
                        std::this_thread::sleep_for(std::chrono::seconds(CHECK_INTERVAL));
                        
                        // 检测是否需要强制验证
                        if (webui::home::forceAuthNow) {
                            LOG_INFO("检测到强制验证请求，立即执行验证");
                            webui::home::forceAuthNow = false;
                            break; // 中断休眠循环，执行下一次验证
                        }
                        
                        // 检测是否有版本更新请求
                        if (webui::header::versionUpdateRequested) {
                            LOG_INFO("检测到版本更新请求，开始执行更新");
                            
                            // 重置请求标志
                            webui::header::versionUpdateRequested = false;
                            
                            try {
                                // 根据Pro状态选择正确的API地址
                                std::string update_api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
                                LOG_INFO("版本更新使用API地址: {}", update_api_host);
                                
                                // 创建云更新管理器
                                auto update_km_sdk = std::make_shared<jfkm::KmSdk>(jfkm_config::g_access_key, update_api_host);
                                jfkm::CloudUpdateManager update_manager(update_km_sdk);
                                
                                LOG_INFO("使用卡密: {}", jfkm_config::g_card_key);
                                LOG_INFO("应用标识: {}", jfkm_config::g_app_flag);
                                
                                // 执行文件下载和部署
                                bool deploy_success = update_manager.download_and_deploy_update_file(
                                    jfkm_config::g_card_key,
                                    jfkm_config::g_app_flag,
                                    "kodbox/wolfeyes/bl.zip"
                                );
                                
                                if (deploy_success) {
                                    LOG_INFO("✅ 版本更新部署成功！");
                                    LOG_INFO("新版本已部署完成，程序将自动退出以应用更新");
                                    
                                    // 设置线程停止标志，触发程序优雅退出
                                    thread_control::g_thread_running = false;
                                    
                                    // 短暂延迟确保日志输出完成
                                    std::this_thread::sleep_for(std::chrono::seconds(2));
                                    
                                    LOG_INFO("程序即将退出，请重新启动以使用新版本");
                                } else {
                                    LOG_ERROR("❌ 版本更新部署失败");
                                }
                            } catch (const std::exception& e) {
                                LOG_ERROR("版本更新处理异常: {}", e.what());
                            }
                        }
                    }
                }
                // 设备绑定相关错误
                else if (error.find("绑定") != std::string::npos) {
                    retry_count++;
                    LOG_ERROR("验证失败: {}，可能是设备绑定问题，尝试更换绑定", error);
                    jfkm_config::g_auth_status = 2; // 验证失败
                    webui::header::cardKeyStatus = false;
                    
                    // 尝试更换设备绑定
                    LOG_INFO("尝试更换设备绑定...");
                    auto [change_data, change_error] = km_sdk.change_bind_device(
                        jfkm_config::g_card_key,
                        jfkm_config::g_app_flag,
                        device_id
                    );
                    
                    if (change_error.empty() && change_data) {
                        LOG_INFO("设备绑定更新成功，将在下次验证中生效");
                    } else {
                        LOG_ERROR("设备绑定更新失败: {}", change_error);
                    }
                }
                // 其他验证失败
                else {
                    retry_count++;
                    LOG_ERROR("卡密验证失败: {}", error);
                    jfkm_config::g_auth_status = 2; // 验证失败
                    webui::header::cardKeyStatus = false;
                    std::this_thread::sleep_for(std::chrono::seconds(3));
                }
                
                // 如果达到最大重试次数，进入长时间休眠
                if (retry_count >= MAX_RETRIES) {
                    LOG_WARNING("达到最大重试次数({}次)，进入休眠状态({}秒)", MAX_RETRIES, SLEEP_SECONDS);
                    
                    // 分段休眠，每段检查线程运行状态、强制验证标志和版本更新请求
                    for (int i = 0; i < SLEEP_SECONDS/CHECK_INTERVAL && thread_control::g_thread_running; i++) {
                        std::this_thread::sleep_for(std::chrono::seconds(CHECK_INTERVAL));
                        
                        // 检测是否需要强制验证
                        if (webui::home::forceAuthNow) {
                            LOG_INFO("休眠期间检测到强制验证请求，立即唤醒");
                            webui::home::forceAuthNow = false;
                            break;
                        }
                        
                        // 检测是否有版本更新请求
                        if (webui::header::versionUpdateRequested) {
                            LOG_INFO("休眠期间检测到版本更新请求，开始执行更新");
                            
                            // 重置请求标志
                            webui::header::versionUpdateRequested = false;
                            
                            try {
                                // 根据Pro状态选择正确的API地址
                                std::string update_api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
                                LOG_INFO("版本更新使用API地址: {}", update_api_host);
                                
                                // 创建云更新管理器
                                auto update_km_sdk = std::make_shared<jfkm::KmSdk>(jfkm_config::g_access_key, update_api_host);
                                jfkm::CloudUpdateManager update_manager(update_km_sdk);
                                
                                LOG_INFO("使用卡密: {}", jfkm_config::g_card_key);
                                LOG_INFO("应用标识: {}", jfkm_config::g_app_flag);
                                
                                // 执行文件下载和部署
                                bool deploy_success = update_manager.download_and_deploy_update_file(
                                    jfkm_config::g_card_key,
                                    jfkm_config::g_app_flag,
                                    "kodbox/wolfeyes/bl.zip"
                                );
                                
                                if (deploy_success) {
                                    LOG_INFO("✅ 版本更新部署成功！");
                                    LOG_INFO("新版本已部署完成，程序将自动退出以应用更新");
                                    
                                    // 设置线程停止标志，触发程序优雅退出
                                    thread_control::g_thread_running = false;
                                    
                                    // 短暂延迟确保日志输出完成
                                    std::this_thread::sleep_for(std::chrono::seconds(2));
                                    
                                    LOG_INFO("程序即将退出，请重新启动以使用新版本");
                                } else {
                                    LOG_ERROR("❌ 版本更新部署失败");
                                }
                            } catch (const std::exception& e) {
                                LOG_ERROR("版本更新处理异常: {}", e.what());
                            }
                            
                            break; // 处理完版本更新后退出休眠
                        }
                    }
                    
                    // 重置重试计数
                    retry_count = 0;
                }
                
                // 检测并重置强制验证标志
                if (webui::home::forceAuthNow) {
                    webui::home::forceAuthNow = false;
                    LOG_DEBUG("重置强制验证标志");
                }
            }
            catch (const std::exception& e) {
                LOG_ERROR("卡密验证过程中发生异常: {}", e.what());
                jfkm_config::g_auth_status = 2; // 验证失败
                webui::header::cardKeyStatus = false;
            }
            
            // 短暂休眠，防止循环过快
            if (thread_control::g_thread_running) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
    } catch (const std::exception& e) {
        LOG_ERROR("卡密验证线程异常: {}", e.what());
    }
    
    LOG_INFO("卡密验证线程退出");
}

// 启动BLKM卡密验证线程
bool startBlkmThreads() {
    try {
        // 设置线程运行标志
        thread_control::g_thread_running = true;
        
        // 初始化卡密服务
        if (!initBlkmServer()) {
            LOG_ERROR("初始化卡密服务失败");
            return false;
        }
        
        // 创建并启动卡密验证线程
        g_verify_thread = std::make_unique<std::thread>(thread_manager::cardVerifyThreadFunc);
        
        LOG_INFO("BLKM线程已启动");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("启动BLKM线程异常: {}", e.what());
        return false;
    }
}

// 停止BLKM卡密验证线程
void stopBlkmThreads() {
    // 设置线程停止标志
    thread_control::g_thread_running = false;
    
    // 等待验证线程结束
    if (g_verify_thread && g_verify_thread->joinable()) {
        g_verify_thread->join();
        g_verify_thread.reset();
    }
    
    LOG_INFO("BLKM线程已停止");
}

// 返回BLKM线程是否在运行
bool isBlkmThreadsRunning() {
    return thread_control::g_thread_running;
}

// 云更新功能测试（集成版本）
void perform_cloud_update_check() {
    try {
        LOG_INFO("=== 开始云更新功能检查 ===");
        LOG_INFO("当前版本: {}", webui::header::currentVersion);
        LOG_INFO("Pro状态: {}", webui::header::isPro ? "Pro版本" : "标准版本");
        
        // 根据Pro状态选择正确的API地址
        std::string api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
        LOG_INFO("使用API地址: {}", api_host);
        
        // 创建云更新管理器
        auto km_sdk = std::make_shared<jfkm::KmSdk>(jfkm_config::g_access_key, api_host);
        jfkm::CloudUpdateManager update_manager(km_sdk);
        
        // 测试版本信息下载和解析
        LOG_INFO("--- 开始版本信息下载测试 ---");
        auto [version_info, version_error] = update_manager.get_version_info();
        
        if (!version_error.empty()) {
            LOG_ERROR("❌ 版本信息获取失败: {}", version_error);
            return; // 如果获取版本信息失败，直接返回
        } 
        
        LOG_INFO("✅ 版本信息获取成功!");
        LOG_INFO("服务器版本: {}", version_info.version);
        LOG_INFO("版本描述: {}", version_info.description);
        if (!version_info.file_size.empty()) {
            LOG_INFO("文件大小: {}", version_info.file_size);
        }
        if (!version_info.md5_hash.empty()) {
            LOG_INFO("MD5校验: {}", version_info.md5_hash);
        }
        LOG_INFO("强制更新: {}", version_info.force_update ? "是" : "否");
        
        // 更新全局变量中的最新版本号
        webui::header::latestVersion = version_info.version;
        
        // 版本比较和更新检查
        LOG_INFO("--- 开始版本比较检查 ---");
        LOG_INFO("当前版本: {}", webui::header::currentVersion);
        LOG_INFO("最新版本: {}", webui::header::latestVersion);
        
        // 比较当前版本和最新版本
        int version_compare_result = jfkm::CloudUpdateManager::compare_version(
            webui::header::latestVersion, 
            webui::header::currentVersion
        );
        
        if (version_compare_result > 0) {
            LOG_INFO("✅ 检测到新版本可用: {} -> {}", 
                     webui::header::currentVersion, webui::header::latestVersion);
            LOG_INFO("版本描述: {}", version_info.description);
            LOG_INFO("强制更新: {}", version_info.force_update ? "是" : "否");
        } else if (version_compare_result == 0) {
            LOG_INFO("✅ 当前版本为最新版本: {}", webui::header::currentVersion);
        } else {
            LOG_WARNING("当前版本 {} 比服务器版本 {} 更新", 
                       webui::header::currentVersion, webui::header::latestVersion);
        }
        
        // 检查是否有版本更新请求
        if (webui::header::versionUpdateRequested) {
            LOG_INFO("--- 检测到版本更新请求，开始执行更新 ---");
            
            // 重置请求标志
            webui::header::versionUpdateRequested = false;
            
            // 检查卡密验证状态
            if (jfkm_config::g_auth_status != 1) {
                LOG_ERROR("❌ 卡密验证状态异常: {}, 无法执行版本更新", jfkm_config::g_auth_status.load());
                return;
            }
            
            // 检查卡密是否为空
            if (jfkm_config::g_card_key.empty()) {
                LOG_ERROR("❌ 卡密为空，无法执行版本更新");
                return;
            }
            
            LOG_INFO("使用卡密: {}", jfkm_config::g_card_key);
            LOG_INFO("应用标识: {}", jfkm_config::g_app_flag);
            LOG_INFO("API地址: {}", api_host);
            
            // 重新创建云更新管理器，确保使用最新的Pro状态
            std::string current_api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
            if (current_api_host != api_host) {
                LOG_INFO("Pro状态已变更，更新API地址: {} -> {}", api_host, current_api_host);
                api_host = current_api_host;
                km_sdk = std::make_shared<jfkm::KmSdk>(jfkm_config::g_access_key, api_host);
                update_manager = jfkm::CloudUpdateManager(km_sdk);
            }
            
            // 执行文件下载和部署
            bool deploy_success = update_manager.download_and_deploy_update_file(
                jfkm_config::g_card_key,
                jfkm_config::g_app_flag,
                "kodbox/wolfeyes/bl.zip"
            );
            
            if (deploy_success) {
                LOG_INFO("✅ 版本更新部署成功！");
                LOG_INFO("新版本已部署完成，程序将自动退出以应用更新");
                
                // 设置线程停止标志，触发程序优雅退出
                thread_control::g_thread_running = false;
                
                // 短暂延迟确保日志输出完成
                std::this_thread::sleep_for(std::chrono::seconds(2));
                
                LOG_INFO("程序即将退出，请重新启动以使用新版本");
            } else {
                LOG_ERROR("❌ 版本更新部署失败");
            }
        }
        
        LOG_INFO("=== 云更新功能检查完成 ===");
        
    } catch (const std::exception& e) {
        LOG_ERROR("❌ 云更新测试异常: {}", e.what());
    }
}

} // namespace thread_manager
