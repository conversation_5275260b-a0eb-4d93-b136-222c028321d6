#include "lkm_server.h"
#include "../utils/logger.h"
#include "../utils/gmodels.h"  // 添加WSQ相关定义
#include <thread>
#include <atomic>

namespace thread_manager {

// 线程实例
static std::thread s_listen_thread;
static std::thread s_wsq_executor_thread;     // WSQ执行器线程，专门用于瞄准
static std::thread s_auxiliary_thread;        // 辅助功能线程，处理FOV、开火、背闪

// 原有的4个线程已被新架构替代：
// static std::thread s_command_server_thread;  // 已移除
// static std::thread s_aim_thread;             // 已移除  
// static std::thread s_auto_fire_thread;       // 已移除

// 全局共享的API实例
static LkmAPI s_api_instance;

// 线程运行状态
static bool s_threads_initialized = false;

// 启动所有LKM线程(监听循环和命令服务器)，内部初始化LKM设备
bool startLkmThreads() {
    // 确保先停止已有的线程
    stopLkmThreads();
    LOG_INFO("正在初始化LKM设备...");
    // 硬编码串口参数
    const std::string port_name = "/dev/ttyACM0";  // 默认串口
    const int baud_rate = 115200;
    const int data_bits = 8;
    const char parity = 'N';
    const int stop_bits = 1;
    // 初始化LKM设备
    if (!s_api_instance.initLkm(port_name, baud_rate, data_bits, parity, stop_bits)) {
        LOG_ERROR("LKM设备初始化失败: {}", s_api_instance.getLastError());
        return false;
    }
    
    LOG_INFO("LKM设备初始化成功（串口: {}）", port_name);
    LOG_INFO("正在启动新架构线程...");
    
    // 启动监听线程（保持鼠标状态监听）
    s_listen_thread = std::thread(lkm_tools::listenThreadLoop, std::ref(s_api_instance));
    // 启动WSQ执行器线程 - 专门用于瞄准
    s_wsq_executor_thread = std::thread(lkm_tools::WSQExecutorLoop, std::ref(s_api_instance));
    // 启动辅助功能线程 - 处理FOV、开火、背闪
    s_auxiliary_thread = std::thread(lkm_tools::auxiliaryFunctionsLoop, std::ref(s_api_instance));

    // 原有的4个线程已被新架构替代：
    // s_command_server_thread = std::thread(lkm_tools::commandServerLoop, std::ref(s_api_instance));  // 已移除
    // s_aim_thread = std::thread(lkm_tools::aimLoop);                                                  // 已移除
    // s_auto_fire_thread = std::thread(lkm_tools::autoFireLoop, std::ref(s_api_instance));           // 已移除
    
    // 设置初始化标志
    s_threads_initialized = true;
    
    LOG_INFO("新架构线程已启动（监听+WSQ瞄准+辅助功能）");
    return true;
}

// 停止所有LKM线程
void stopLkmThreads() {
    if (!s_threads_initialized) {
        return;
    }
    LOG_INFO("正在停止新架构线程...");
    
    // 通知WSQ队列停止 - 与NPU工作线程完全相同的机制
    {
        std::scoped_lock lock(wsq_models::g_wsq_mutex);
        wsq_models::g_wsq_cv.notify_all();
    }
    
    // WSQ多线程架构会在WSQExecutorLoop内部自动停止所有工作线程
    
    // 等待线程终止
    if (s_listen_thread.joinable()) {
        s_listen_thread.join();
        LOG_INFO("监听循环已停止");
    }
    if (s_wsq_executor_thread.joinable()) {
        s_wsq_executor_thread.join();
        LOG_INFO("WSQ瞄准执行器已停止");
    }
    if (s_auxiliary_thread.joinable()) {
        s_auxiliary_thread.join();
        LOG_INFO("辅助功能线程已停止");
    }
    
    // 原有的4个线程已被新架构替代，无需单独停止：
    // s_command_server_thread, s_aim_thread, s_auto_fire_thread
    
    // 清理资源
    s_threads_initialized = false;
    LOG_INFO("新架构线程已全部停止");
}

// LKM线程是否运行中
bool isLkmThreadsRunning() {
    return s_threads_initialized && 
           thread_control::g_thread_running;
}

} // namespace thread_manager
