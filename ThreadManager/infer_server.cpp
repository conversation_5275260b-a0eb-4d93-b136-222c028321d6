#include "core/aimbot/aimbot_fps.h"
#include "ThreadManager/infer_server.h"
#include "utils/logger.h"
#include "utils/gmodels.h"
#include "core/model/yolo/yolo.h"
#include <thread>  // 添加线程支持

namespace aibox {

InferServer::InferServer() {
    aimbot = std::make_unique<aimbot::AimbotFPS>();
}

InferServer::~InferServer() {
    // 确保aimbot实例被正确销毁，它会停止内部线程
    LOG_INFO("开始关闭推理服务...");
    
    if (aimbot) {
        LOG_INFO("停止推理引擎");
        aimbot->Stop(); // 注意，这个方法现在会设置全局thread_control::g_thread_running = false
    }
    
    // 等待推理线程结束
    if (infer_thread.joinable()) {
        LOG_INFO("等待推理线程退出...");
        infer_thread.join();
        LOG_INFO("推理线程已退出");
    }
    
    LOG_INFO("推理服务关闭完成");
}

void InferServer::Setup() {
    // 执行必要的设置，但不再处理鼠标设备
}

void InferServer::Run() {
    if (is_started) {
        throw std::runtime_error("System already started");
    }
    is_started = true;
    Setup();
    
    // 设置推理状态为false
    webui::header::inferenceStatus = false;
    LOG_INFO("设置推理状态为false");
    
    // 初始模型加载逻辑
    const std::string& current_game = webui::home::g_game_name;
    LOG_INFO("初始加载游戏 {} 的模型...", current_game);

    // 根据当前游戏配置加载模型
    if (yolo_config::g_game_configs.count(current_game)) {
        const auto& game_config = yolo_config::g_game_configs[current_game];
        
        // 初始化时更新YOLO类别数量
        yolo_config::UpdateYoloClassNum(current_game);
        
        try {
            // 调用 AimbotFPS 的 LoadModel 方法进行初始加载
            aimbot->LoadModel(game_config.weight_path, model::YoloVersion::YoloV8);
            LOG_INFO("初始模型加载成功: {}", game_config.weight_path);
             // 设置初始阈值等参数
            for (auto& resource : aimbot->GetNPUResources()) {
                if (resource && resource->model) {
                    resource->model->SetThreshold(game_config.box_thresh);
                    resource->model->SetNMSThreshold(game_config.nms_thresh);
                }
            }
            LOG_INFO("已为初始模型设置阈值");
            
            // 模型初始化成功，设置推理状态为true
            webui::header::inferenceStatus = true;
            LOG_INFO("设置推理状态为true");

        } catch (const std::exception& e) {
             LOG_ERROR("初始加载模型失败: {}", e.what());
             is_started = false;
             thread_control::g_thread_running = false;
             LOG_ERROR("由于初始模型加载失败，程序将强制退出");
             exit(1); // 强制退出程序
        }
    } else {
        LOG_ERROR("找不到游戏 {} 的初始模型配置，程序将强制退出", current_game);
        is_started = false;
        thread_control::g_thread_running = false;
        exit(1); // 找不到配置，强制退出
    }

    // 创建推理线程，运行 AimbotFPS 的主循环
    infer_thread = std::thread([this]() {
        LOG_INFO("启动推理主线程...");
        // 运行推理引擎并监控线程运行标志
        bool result = aimbot->Run(); // AimbotFPS::Run 包含重载逻辑和帧处理循环
        if (!result) {
            LOG_ERROR("推理引擎运行失败");
            thread_control::g_thread_running = false;
        }
        LOG_INFO("推理主线程正常退出");
    });
    
    // 不再使用detach，而是在析构函数中join线程
    // infer_thread.detach();
}

}  // namespace aibox
