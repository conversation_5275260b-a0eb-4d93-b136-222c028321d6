#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目目录和目标目录
PROJECT_DIR=$(pwd)
BUILD_DIR="${PROJECT_DIR}/build"
TARGET_LIB_DIR="${BUILD_DIR}/bin/lib"
DEPS_LIST_FILE="${TARGET_LIB_DIR}/dependencies.txt"

# 创建目标目录
mkdir -p "${TARGET_LIB_DIR}"

echo -e "${YELLOW}开始打包环境依赖到 ${TARGET_LIB_DIR}...${NC}"

# 获取可执行文件的依赖库
collect_dependencies() {
    local executable=$1
    local lib_dir=$2
    
    if [ ! -f "$executable" ]; then
        echo -e "${RED}错误: 可执行文件 $executable 不存在${NC}"
        return 1
    fi
    
    echo -e "${GREEN}收集 $(basename $executable) 的依赖...${NC}"
    
    # 使用ldd获取依赖库列表
    ldd "$executable" | grep "=>" | grep -v "not found" | awk '{print $3}' | while read -r lib; do
        # 排除系统库以及OpenCV和Poco库
        if [ -f "$lib" ] && [[ "$lib" != /lib/* ]] && [[ "$lib" != /usr/lib/* ]] && 
           [[ ! "$lib" =~ libopencv ]] && [[ ! "$lib" =~ libPoco ]]; then
            # 仅复制非系统库且非OpenCV与Poco库
            local lib_name=$(basename "$lib")
            echo "复制库: $lib_name"
            cp "$lib" "$lib_dir/"
            
            # 记录依赖项
            echo "$lib_name: $lib" >> "$DEPS_LIST_FILE"
            
            # 递归收集这个库的依赖
            ldd "$lib" | grep "=>" | grep -v "not found" | awk '{print $3}' | while read -r sublib; do
                if [ -f "$sublib" ] && [[ "$sublib" != /lib/* ]] && [[ "$sublib" != /usr/lib/* ]] && 
                   [[ ! "$sublib" =~ libopencv ]] && [[ ! "$sublib" =~ libPoco ]]; then
                    local sublib_name=$(basename "$sublib")
                    if [ ! -f "$lib_dir/$sublib_name" ]; then
                        echo "复制子依赖库: $sublib_name"
                        cp "$sublib" "$lib_dir/"
                        echo "$sublib_name: $sublib" >> "$DEPS_LIST_FILE"
                    fi
                fi
            done
        fi
    done
}

# 复制第三方库
copy_thirdparty_libs() {
    echo -e "${YELLOW}复制第三方库...${NC}"
    
    # 复制RKNN运行时库
    RKNN_LIB="${PROJECT_DIR}/3rdparty/rknn/linux/librknnrt.so"
    if [ -f "$RKNN_LIB" ]; then
        echo "复制 RKNN 库: librknnrt.so"
        cp "$RKNN_LIB" "$TARGET_LIB_DIR/"
        echo "librknnrt.so: $RKNN_LIB" >> "$DEPS_LIST_FILE"
    else
        echo -e "${RED}警告: RKNN 库不存在: $RKNN_LIB${NC}"
    fi
    
    # 复制模型文件
    MODEL_DIR="${PROJECT_DIR}/infer/model"
    if [ -d "$MODEL_DIR" ]; then
        echo "复制模型文件夹..."
        mkdir -p "${BUILD_DIR}/bin/model"
        cp -r "$MODEL_DIR"/* "${BUILD_DIR}/bin/model/"
        echo "模型目录: ${BUILD_DIR}/bin/model/" >> "$DEPS_LIST_FILE"
    else
        echo -e "${RED}警告: 模型目录不存在: $MODEL_DIR${NC}"
    fi
    
    # 如果有其他特定的第三方库，可以在这里添加
}

# 复制RGA库
copy_rga_libs() {
    echo -e "${YELLOW}复制RGA库...${NC}"
    
    # 使用实际的RGA库路径
    RGA_DIR="${PROJECT_DIR}/3rdparty/librga/Linux/aarch64"
    if [ -d "$RGA_DIR" ]; then
        echo "从指定目录复制RGA库..."
        # 复制.so文件
        if [ -f "${RGA_DIR}/librga.so" ]; then
            echo "复制RGA库: librga.so"
            cp "${RGA_DIR}/librga.so" "$TARGET_LIB_DIR/"
            echo "librga.so: ${RGA_DIR}/librga.so" >> "$DEPS_LIST_FILE"
        else
            echo -e "${RED}警告: 未找到 librga.so 在 ${RGA_DIR}${NC}"
        fi
        
        # 如果有静态库且需要，也可以复制
        if [ -f "${RGA_DIR}/librga.a" ]; then
            echo "复制RGA静态库: librga.a"
            cp "${RGA_DIR}/librga.a" "$TARGET_LIB_DIR/"
            echo "librga.a: ${RGA_DIR}/librga.a" >> "$DEPS_LIST_FILE"
        fi
    else
        echo -e "${RED}警告: 指定的RGA库目录不存在: ${RGA_DIR}${NC}"
    fi
    
    # 仍然尝试查找其他需要的库
    MPP_LIBS=("librockchip_mpp.so" "libdrm.so")
    
    for lib_name in "${MPP_LIBS[@]}"; do
        # 尝试在系统库路径中查找
        lib_path=$(find /usr/lib /usr/local/lib -name "${lib_name}*" -type f | head -n 1)
        
        if [ -n "$lib_path" ]; then
            echo "复制库: $(basename $lib_path)"
            cp "$lib_path" "$TARGET_LIB_DIR/"
            echo "$(basename $lib_path): $lib_path" >> "$DEPS_LIST_FILE"
        else
            echo -e "${YELLOW}警告: 未在系统路径中找到库: $lib_name${NC}"
            
            # 尝试在项目的其他目录中查找
            project_lib_path=$(find "${PROJECT_DIR}/3rdparty" -name "${lib_name}*" -type f | head -n 1)
            if [ -n "$project_lib_path" ]; then
                echo "从项目目录复制库: $(basename $project_lib_path)"
                cp "$project_lib_path" "$TARGET_LIB_DIR/"
                echo "$(basename $project_lib_path): $project_lib_path" >> "$DEPS_LIST_FILE"
            else
                echo -e "${RED}警告: 未找到库: $lib_name${NC}"
            fi
        fi
    done
}

# 主程序流程
main() {
    # 检查构建目录是否存在
    if [ ! -d "$BUILD_DIR" ]; then
        echo -e "${RED}错误: 构建目录不存在，请先编译项目${NC}"
        exit 1
    fi
    
    # 检查bin目录是否存在
    if [ ! -d "${BUILD_DIR}/bin" ]; then
        echo -e "${RED}错误: bin目录不存在，请先编译项目${NC}"
        exit 1
    fi
    
    # 创建依赖项列表文件
    > "$DEPS_LIST_FILE"
    
    # 收集主程序的依赖
    BL_EXEC="${BUILD_DIR}/bin/BL"
    if [ -f "$BL_EXEC" ]; then
        collect_dependencies "$BL_EXEC" "$TARGET_LIB_DIR"
    else
        echo -e "${RED}警告: BL可执行文件不存在${NC}"
    fi
    
    # 检查并收集其他可执行文件的依赖
    OTHER_EXECS=("img_capture" "bl_infer" "lkm_test" "blkm_test")
    for exec_name in "${OTHER_EXECS[@]}"; do
        EXEC_PATH="${BUILD_DIR}/bin/${exec_name}"
        if [ -f "$EXEC_PATH" ]; then
            collect_dependencies "$EXEC_PATH" "$TARGET_LIB_DIR"
        fi
    done
    
    # 复制第三方库
    copy_thirdparty_libs
    
    # 复制RGA库
    copy_rga_libs
    
    echo -e "${GREEN}环境依赖打包完成!${NC}"
    echo -e "${GREEN}所有依赖库已复制到: ${TARGET_LIB_DIR}${NC}"
    echo -e "${GREEN}依赖列表保存到: ${DEPS_LIST_FILE}${NC}"
}

# 执行主程序
main
