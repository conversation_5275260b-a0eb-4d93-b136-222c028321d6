#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 恢复默认颜色

# 显示标题
echo -e "${BLUE}===========================================${NC}"
echo -e "${GREEN}      Prisma 数据库管理脚本      ${NC}"
echo -e "${BLUE}===========================================${NC}"

# 检查schema.prisma文件是否存在
if [ ! -f "./prisma/schema.prisma" ]; then
  echo -e "${RED}错误: 未找到schema.prisma文件!${NC}"
  echo -e "${YELLOW}请确保脚本在项目根目录下运行，且prisma/schema.prisma文件存在${NC}"
  exit 1
fi

# 显示菜单
echo -e "\n请选择要执行的操作:"
echo -e "${YELLOW}1.${NC} 生成prisma客户端"
echo -e "${YELLOW}2.${NC} 重置数据库"
echo -e "${YELLOW}3.${NC} 创建示例数据"
echo -e "${YELLOW}4.${NC} 启动prisma studio"
echo -e "${YELLOW}0.${NC} 退出\n"

# 读取用户输入
read -p "请输入选项 (0-4): " choice

# 处理用户选择
case $choice in
  1)
    echo -e "\n${YELLOW}正在生成prisma客户端...${NC}"
    npx prisma generate
    
    if [ $? -eq 0 ]; then
      echo -e "\n${GREEN}prisma客户端已成功生成!${NC}"
    else
      echo -e "\n${RED}生成失败。${NC}"
    fi
    ;;
    
  2)
    echo -e "\n${YELLOW}正在重置数据库...${NC}"
    echo -e "${RED}警告: 此操作将删除数据库中的所有数据!${NC}"
    read -p "确定要继续吗? (y/n): " confirm
    
    if [ "$confirm" == "y" ] || [ "$confirm" == "Y" ]; then
      npx prisma db push --force-reset
      
      if [ $? -eq 0 ]; then
        echo -e "\n${GREEN}数据库已成功重置!${NC}"
      else
        echo -e "\n${RED}数据库重置失败。${NC}"
      fi
    else
      echo -e "\n${YELLOW}操作已取消${NC}"
    fi
    ;;
    
  3)
    echo -e "\n${YELLOW}正在创建示例数据...${NC}"
    # 创建示例数据
    node << EOF
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()

    async function main() {
        try {
            // 创建用户 - 包含isPro字段
            const user1 = await prisma.superAdmin.create({
                data: {
                    username: "user1",
                    password: "password123",
                    isPro: false,
                    lastLogin: new Date(),
                    token: "token_user1_sample"
                }
            })

            const user2 = await prisma.superAdmin.create({
                data: {
                    username: "user2",
                    password: "password456",
                    isPro: true,
                    lastLogin: new Date(),
                    token: "token_user2_sample"
                }
            })

            console.log("✅ 用户创建成功")

            // 创建HomeConfig示例 - 一对一关系
            await prisma.homeConfig.create({
                data: {
                    username: "user1",
                    gameName: "CS:GO",
                    cardKey: "AAAA-BBBB-CCCC-DDDD"
                }
            })

            await prisma.homeConfig.create({
                data: {
                    username: "user2",
                    gameName: "APEX",
                    cardKey: "EEEE-FFFF-1111-2222"
                }
            })

            console.log("✅ 首页配置创建成功")

            // 创建FunctionConfig示例 - 一对多关系，联合主键[username, gameName, presetName]
            const functionConfigs = [
                { 
                    username: "user1", 
                    gameName: "CS:GO", 
                    presetName: "头部瞄准", 
                    aiMode: "自动", 
                    lockPosition: "头部",
                    hotkey: "F1",
                    triggerSwitch: true,
                    weaponSwitch: false,
                    flashShield: false,
                    enabled: true,
                    selectedFaction: "警方"
                },
                { 
                    username: "user1", 
                    gameName: "CS:GO", 
                    presetName: "胸部瞄准", 
                    aiMode: "手动", 
                    lockPosition: "胸部",
                    hotkey: "F2", 
                    triggerSwitch: false,
                    weaponSwitch: true,
                    flashShield: true,
                    enabled: true,
                    selectedFaction: "匪方"
                },
                { 
                    username: "user1", 
                    gameName: "APEX", 
                    presetName: "通用设置", 
                    aiMode: "自动", 
                    lockPosition: "头部",
                    hotkey: "F3",
                    triggerSwitch: true,
                    weaponSwitch: false,
                    flashShield: false,
                    enabled: true,
                    selectedFaction: "无"
                },
                { 
                    username: "user2", 
                    gameName: "CS:GO", 
                    presetName: "专业设置", 
                    aiMode: "自动", 
                    lockPosition: "头部",
                    hotkey: "F4",
                    triggerSwitch: true,
                    weaponSwitch: true,
                    flashShield: true,
                    enabled: true,
                    selectedFaction: "警方"
                }
            ]

            for (const config of functionConfigs) {
                await prisma.functionConfig.create({ data: config })
            }

            console.log("✅ 功能配置创建成功")

            // 创建PidConfig示例 - 根据API文档和schema.prisma的字段结构
            const pidConfigs = [
                { 
                    username: "user1", 
                    gameName: "CS:GO", 
                    nearMoveFactor: 1.0,
                    nearStabilizer: 0.5,
                    nearResponseRate: 0.3,
                    nearAssistZone: 3.0,
                    nearResponseDelay: 1.0,
                    nearMaxAdjustment: 2.0,
                    farFactor: 1.0,
                    yAxisFactor: 1.0,
                    pidRandomFactor: 0.5
                },
                { 
                    username: "user1", 
                    gameName: "APEX", 
                    nearMoveFactor: 1.2,
                    nearStabilizer: 0.6,
                    nearResponseRate: 0.25,
                    nearAssistZone: 2.5,
                    nearResponseDelay: 0.8,
                    nearMaxAdjustment: 2.5,
                    farFactor: 1.2,
                    yAxisFactor: 1.1,
                    pidRandomFactor: 0.6
                },
                { 
                    username: "user2", 
                    gameName: "CS:GO", 
                    nearMoveFactor: 0.9,
                    nearStabilizer: 0.4,
                    nearResponseRate: 0.35,
                    nearAssistZone: 3.5,
                    nearResponseDelay: 1.2,
                    nearMaxAdjustment: 1.8,
                    farFactor: 0.9,
                    yAxisFactor: 0.95,
                    pidRandomFactor: 0.4
                }
            ]

            for (const config of pidConfigs) {
                await prisma.pidConfig.create({ data: config })
            }

            console.log("✅ PID配置创建成功")

            // 创建FovConfig示例
            const fovConfigs = [
                { username: "user1", gameName: "CS:GO", fov: 90.0, fovTime: 1000 },
                { username: "user1", gameName: "APEX", fov: 95.0, fovTime: 800 },
                { username: "user2", gameName: "CS:GO", fov: 88.0, fovTime: 1200 }
            ]

            for (const config of fovConfigs) {
                await prisma.fovConfig.create({ data: config })
            }

            console.log("✅ 视野配置创建成功")

            // 创建AimConfig示例 - 根据schema.prisma的完整字段
            const aimConfigs = [
                { 
                    username: "user1", 
                    gameName: "CS:GO", 
                    aimRange: 100.0, 
                    trackRange: 50.0, 
                    headHeight: 1.8, 
                    neckHeight: 1.6, 
                    chestHeight: 1.4,
                    headRangeX: 0.2,
                    headRangeY: 0.2,
                    neckRangeX: 0.3,
                    neckRangeY: 0.3,
                    chestRangeX: 0.4,
                    chestRangeY: 0.4
                },
                { 
                    username: "user1", 
                    gameName: "APEX", 
                    aimRange: 120.0, 
                    trackRange: 60.0, 
                    headHeight: 1.7, 
                    neckHeight: 1.5, 
                    chestHeight: 1.3,
                    headRangeX: 0.22,
                    headRangeY: 0.22,
                    neckRangeX: 0.32,
                    neckRangeY: 0.32,
                    chestRangeX: 0.42,
                    chestRangeY: 0.42
                },
                { 
                    username: "user2", 
                    gameName: "CS:GO", 
                    aimRange: 110.0, 
                    trackRange: 55.0, 
                    headHeight: 1.75, 
                    neckHeight: 1.55, 
                    chestHeight: 1.35,
                    headRangeX: 0.21,
                    headRangeY: 0.21,
                    neckRangeX: 0.31,
                    neckRangeY: 0.31,
                    chestRangeX: 0.41,
                    chestRangeY: 0.41
                }
            ]

            for (const config of aimConfigs) {
                await prisma.aimConfig.create({ data: config })
            }

            console.log("✅ 瞄准配置创建成功")

            // 创建FireConfig示例
            const fireConfigs = [
                { 
                    username: "user1", 
                    gameName: "CS:GO", 
                    rifleSleep: 100, 
                    rifleInterval: 200, 
                    pistolSleep: 150, 
                    pistolInterval: 250, 
                    sniperSleep: 300, 
                    sniperInterval: 500 
                },
                { 
                    username: "user1", 
                    gameName: "APEX", 
                    rifleSleep: 90, 
                    rifleInterval: 180, 
                    pistolSleep: 140, 
                    pistolInterval: 240, 
                    sniperSleep: 280, 
                    sniperInterval: 480 
                },
                { 
                    username: "user2", 
                    gameName: "CS:GO", 
                    rifleSleep: 95, 
                    rifleInterval: 190, 
                    pistolSleep: 145, 
                    pistolInterval: 245, 
                    sniperSleep: 290, 
                    sniperInterval: 490 
                }
            ]

            for (const config of fireConfigs) {
                await prisma.fireConfig.create({ data: config })
            }

            console.log("✅ 射击配置创建成功")

            // 创建DataCollection示例 - 根据schema.prisma简化的结构
            const dataCollections = [
                { username: "user1", gameName: "CS:GO" },
                { username: "user1", gameName: "APEX" },
                { username: "user2", gameName: "CS:GO" }
            ]

            for (const data of dataCollections) {
                await prisma.dataCollection.create({ data })
            }

            console.log("✅ 数据收集记录创建成功")

            console.log("\n🎉 所有示例数据创建完成！")
            console.log("📊 数据统计:")
            console.log("   • 用户数量: 2")
            console.log("   • 功能配置: 4 (包含阵营选择)")
            console.log("   • PID配置: 3") 
            console.log("   • 视野配置: 3")
            console.log("   • 瞄准配置: 3")
            console.log("   • 射击配置: 3")
            console.log("   • 数据收集: 3")
            console.log("\n💡 用户账号:")
            console.log("   • user1: password123")
            console.log("   • user2: password456")
            console.log("\n🎯 阵营配置示例:")
            console.log("   • CS:GO-头部瞄准: 警方")
            console.log("   • CS:GO-胸部瞄准: 匪方") 
            console.log("   • APEX-通用设置: 无")
            console.log("   • CS:GO-专业设置: 警方")

        } catch (error) {
            console.error("❌ 创建示例数据时出错：", error)
            console.error("详细信息：", error.message)
        } finally {
            await prisma.\$disconnect()
        }
    }

    main()
EOF
    ;;
    
  4)
    echo -e "\n${YELLOW}正在启动prisma studio...${NC}"
    npx prisma studio
    ;;
    
  0)
    echo -e "\n${GREEN}退出脚本${NC}"
    exit 0
    ;;
    
  *)
    echo -e "\n${RED}无效的选项，请重新运行脚本并选择有效的选项。${NC}"
    exit 1
    ;;
esac

echo -e "\n${BLUE}===========================================${NC}"
echo -e "${GREEN}      操作完成      ${NC}"
echo -e "${BLUE}===========================================${NC}" 