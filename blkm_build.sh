#!/bin/bash

# 显示彩色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 设置程序名称
BLKM_EXEC="blkm_test"
BUILD_DIR="build"

# 打印带颜色的信息
function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

function print_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 清空控制台
function clear_console() {
    clear
}

# 检查必要的依赖
function check_dependencies() {
    print_info "检查编译依赖..."
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        print_error "未找到cmake，请安装cmake"
        read -p "按Enter键继续..."
        return 1
    fi
    
    # 检查Poco库
    if ! pkg-config --exists Poco; then
        print_warn "未找到Poco库，请确保已安装Poco开发库"
        print_warn "您可以使用以下命令安装: apt install libpoco-dev"
    fi
    
    # 检查spdlog库
    if [ ! -d "3rdparty/spdlog" ]; then
        print_error "未找到spdlog库，请确保3rdparty/spdlog目录存在"
        read -p "按Enter键继续..."
        return 1
    fi
    
    print_info "依赖检查完成"
    return 0
}

# 编译项目
function compile() {
    print_info "开始编译BLKM模块..."
    
    # 创建构建目录
    if [ ! -d "$BUILD_DIR" ]; then
        mkdir -p "$BUILD_DIR"
    fi
    
    cd "$BUILD_DIR" || return 1
    
    # 运行CMake
    cmake ..
    if [ $? -ne 0 ]; then
        print_error "CMake配置失败"
        cd ..
        read -p "按Enter键继续..."
        return 1
    fi
    
    # 使用系统可用的CPU核心数进行并行构建
    CPU_CORES=$(nproc)
    make -j$CPU_CORES
    if [ $? -ne 0 ]; then
        print_error "编译失败"
        cd ..
        read -p "按Enter键继续..."
        return 1
    fi
    
    # 验证可执行文件是否存在
    if [ -f "bin/${BLKM_EXEC}" ]; then
        print_info "编译成功！"
        print_info "可执行文件位置: bin/${BLKM_EXEC}"
    else
        print_error "编译完成，但找不到可执行文件。查找可能的位置..."
        # 尝试在其他路径查找
        EXEC_PATH=$(find . -name "${BLKM_EXEC}")
        if [ -n "$EXEC_PATH" ]; then
            print_info "找到可执行文件: $EXEC_PATH"
        else
            print_error "无法找到可执行文件"
            cd ..
            read -p "按Enter键继续..."
            return 1
        fi
    fi
    
    cd ..
    read -p "按Enter键继续..."
    return 0
}

# 清理并重新编译
function clean_and_compile() {
    print_info "清理并重新编译BLKM模块..."
    
    # 停止所有运行中的进程
    stop_blkm
    
    # 删除旧的构建文件
    if [ -d "$BUILD_DIR" ]; then
        print_info "删除旧的BLKM模块构建文件..."
        rm -rf "${BUILD_DIR}"
    fi
    
    # 重新编译
    compile
    return $?
}

# 运行BLKM模块
function run_blkm() {
    print_info "运行BLKM测试模块..."
    
    # 检查可执行文件是否存在
    EXEC_PATH="${BUILD_DIR}/bin/${BLKM_EXEC}"
    if [ ! -f "$EXEC_PATH" ]; then
        print_warn "在默认路径未找到可执行文件，尝试查找..."
        EXEC_PATH=$(find "${BUILD_DIR}" -name "${BLKM_EXEC}")
        if [ -n "$EXEC_PATH" ]; then
            print_info "找到可执行文件: $EXEC_PATH"
        else
            print_error "错误: ${BLKM_EXEC}可执行文件不存在，请先编译项目"
            read -p "按Enter键继续..."
            return 1
        fi
    fi
    
    # 运行程序
    print_info "启动BLKM测试模块..."
    print_warn "按Ctrl+C退出"
    
    # 执行程序并捕获退出状态
    "$EXEC_PATH"
    RUN_STATUS=$?
    
    if [ $RUN_STATUS -ne 0 ]; then
        print_error "程序异常退出，退出码: $RUN_STATUS"
    fi
    
    # 暂停以便查看输出
    read -p "按Enter键继续..."
    
    return $RUN_STATUS
}

# 停止所有BLKM模块进程
function stop_blkm() {
    print_info "正在停止所有BLKM测试模块进程..."
    
    # 查找所有blkm_test进程
    BLKM_PIDS=$(ps -ef | grep "${BLKM_EXEC}" | grep -v grep | awk '{print $2}')
    
    if [ -n "$BLKM_PIDS" ]; then
        print_info "找到以下BLKM测试模块进程:"
        for pid in $BLKM_PIDS; do
            echo "  - PID: $pid"
            kill -15 $pid
            sleep 1
            
            # 检查进程是否仍在运行，如果是则强制终止
            if ps -p $pid > /dev/null 2>&1; then
                print_warn "进程 $pid 未响应正常终止，尝试强制终止..."
                kill -9 $pid
                sleep 1
            fi
        done
        print_info "所有BLKM测试模块进程已停止"
    else
        print_warn "未找到运行中的BLKM测试模块进程"
    fi
    
    read -p "按Enter键继续..."
    return 0
}

# 显示菜单
function show_menu() {
    clear_console
    echo -e "\n${GREEN}===== BLKM模块工具 =====${NC}"
    echo "1. 编译项目"
    echo "2. 清理并重新编译"
    echo "3. 运行BLKM"
    echo "4. 停止BLKM"
    echo "5. 退出"
    echo -e "${YELLOW}请选择操作 (1-5): ${NC}"
}

# 主循环
function main() {
    # 启动时清空控制台
    clear_console
    
    # 检查依赖
    check_dependencies
    
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                compile
                ;;
            2)
                clean_and_compile
                ;;
            3)
                run_blkm
                ;;
            4)
                stop_blkm
                ;;
            5)
                echo -e "${GREEN}退出程序${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                read -p "按Enter键继续..."
                ;;
        esac
    done
}

# 运行主函数
main 