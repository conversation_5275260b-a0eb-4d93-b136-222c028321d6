#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目名称和路径
IMG_EXEC="rga_capture_test"
BUILD_DIR="build"
BIN_DIR="${BUILD_DIR}/bin"

# 清空控制台
clear_console() {
    clear
}

# 编译项目
compile() {
    echo -e "${YELLOW}开始编译RGA捕获模块...${NC}"
    
    # 创建构建目录
    if [ ! -d "$BUILD_DIR" ]; then
        mkdir -p "$BUILD_DIR"
    fi
    
    cd "$BUILD_DIR" || exit 1
    
    # 运行CMake
    echo -e "${YELLOW}配置CMake...${NC}"
    cmake -DCMAKE_BUILD_TYPE=Release ..
    if [ $? -ne 0 ]; then
        echo -e "${RED}CMake配置失败${NC}"
        cd ..
        return 1
    fi
    
    # 编译
    echo -e "${YELLOW}编译项目...${NC}"
    make -j$(nproc)
    if [ $? -ne 0 ]; then
        echo -e "${RED}编译失败${NC}"
        cd ..
        return 1
    fi
    
    # 验证可执行文件是否存在
    if [ -f "${BIN_DIR}/${IMG_EXEC}" ]; then
        echo -e "${GREEN}编译成功！${NC}"
        echo -e "${GREEN}可执行文件位置: ${BIN_DIR}/${IMG_EXEC}${NC}"
    else
        echo -e "${RED}编译完成，但找不到可执行文件。查找可能的位置...${NC}"
        # 尝试在其他路径查找
        EXEC_PATH=$(find . -name "${IMG_EXEC}" 2>/dev/null)
        if [ -n "$EXEC_PATH" ]; then
            echo -e "${GREEN}找到可执行文件: $EXEC_PATH${NC}"
        else
            echo -e "${RED}无法找到可执行文件${NC}"
            cd ..
            return 1
        fi
    fi
    
    cd ..
    return 0
}

# 清理构建目录
clean() {
    echo -e "${YELLOW}清理构建目录...${NC}"
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        echo -e "${GREEN}已清理构建目录${NC}"
    else
        echo -e "${YELLOW}构建目录不存在，无需清理${NC}"
    fi
}

# 停止所有RGA测试进程
stop_img() {
    echo -e "${YELLOW}正在停止所有RGA测试进程...${NC}"
    
    # 查找所有RGA测试相关进程
    IMG_PIDS=$(ps -ef | grep "$IMG_EXEC" | grep -v grep | awk '{print $2}')
    
    if [ -n "$IMG_PIDS" ]; then
        echo -e "${GREEN}找到以下RGA测试进程:${NC}"
        for pid in $IMG_PIDS; do
            echo "  - PID: $pid"
            kill -15 $pid
            sleep 1
            
            # 检查进程是否仍在运行，如果是则强制终止
            if ps -p $pid > /dev/null 2>&1; then
                echo -e "${YELLOW}进程 $pid 未响应正常终止，尝试强制终止...${NC}"
                kill -9 $pid
                sleep 1
            fi
        done
    else
        echo -e "${YELLOW}未找到正在运行的RGA测试进程${NC}"
    fi
    
    echo -e "${GREEN}所有RGA测试进程已停止${NC}"
}

# 检查X11连接
check_x11_connection() {
    echo -e "${YELLOW}检查X11连接...${NC}"
    if ! xset q &>/dev/null; then
        echo -e "${RED}X11连接失败，无法访问显示器${NC}"
        echo -e "${YELLOW}请检查以下事项:${NC}"
        echo -e " - 确保已在远程服务器(************)上启用X11服务"
        echo -e " - 确保已设置正确的防火墙规则允许X11连接"
        echo -e " - 如果通过SSH连接，确保启用了X11转发(-X或-Y选项)"
        echo -e " - 尝试运行 'export XAUTHORITY=~/.Xauthority' 并重新运行"
        return 1
    else
        echo -e "${GREEN}X11连接成功${NC}"
        return 0
    fi
}

# 运行RGA测试程序
run_img() {
    echo -e "${YELLOW}运行RGA测试程序...${NC}"
    
    # 设置X11显示到远程服务器
    export DISPLAY=************:0.0
    echo -e "${YELLOW}设置DISPLAY=$DISPLAY${NC}"
    
    # 添加RGA库路径到LD_LIBRARY_PATH
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    RGA_LIB_PATH="$SCRIPT_DIR/3rdparty/librga/libs/Linux/gcc-aarch64"
    export LD_LIBRARY_PATH="$RGA_LIB_PATH:$LD_LIBRARY_PATH"
    echo -e "${YELLOW}设置LD_LIBRARY_PATH=$LD_LIBRARY_PATH${NC}"
    
    # 检查X11连接
    check_x11_connection
    if [ $? -ne 0 ]; then
        read -p "是否继续运行程序？(y/n): " continue_choice
        if [[ "$continue_choice" != "y" && "$continue_choice" != "Y" ]]; then
            read -p "按Enter键返回主菜单..."
            return 1
        fi
        echo -e "${YELLOW}警告: 继续运行，但可能无法显示图形界面${NC}"
    fi
    
    # 检查可执行文件是否存在
    EXEC_PATH="${BUILD_DIR}/${BIN_DIR}/${IMG_EXEC}"
    if [ ! -f "$EXEC_PATH" ]; then
        echo -e "${YELLOW}在默认路径未找到可执行文件，尝试查找...${NC}"
        EXEC_PATH=$(find ${BUILD_DIR} -name "${IMG_EXEC}" 2>/dev/null)
        if [ -n "$EXEC_PATH" ]; then
            echo -e "${GREEN}找到可执行文件: $EXEC_PATH${NC}"
        else
            echo -e "${RED}错误: ${IMG_EXEC}可执行文件不存在，请先编译项目${NC}"
            read -p "是否现在编译项目？(y/n): " choice
            if [[ "$choice" == "y" || "$choice" == "Y" ]]; then
                compile
                if [ $? -ne 0 ]; then
                    read -p "按Enter键继续..."
                    return 1
                fi
                # 重新检查可执行文件
                EXEC_PATH="${BUILD_DIR}/${BIN_DIR}/${IMG_EXEC}"
                if [ ! -f "$EXEC_PATH" ]; then
                    EXEC_PATH=$(find ${BUILD_DIR} -name "${IMG_EXEC}" 2>/dev/null)
                    if [ -z "$EXEC_PATH" ]; then
                        echo -e "${RED}编译后仍然找不到可执行文件${NC}"
                        read -p "按Enter键继续..."
                        return 1
                    fi
                fi
            else
                read -p "按Enter键继续..."
                return 1
            fi
        fi
    fi
    
    # 运行程序
    echo -e "${GREEN}启动RGA测试程序...${NC}"
    echo -e "${YELLOW}按Ctrl+C或ESC键退出${NC}"
    
    # 执行程序并捕获退出状态
    "$EXEC_PATH"
    RUN_STATUS=$?
    
    if [ $RUN_STATUS -ne 0 ]; then
        echo -e "${RED}程序异常退出，退出码: $RUN_STATUS${NC}"
    fi
    
    read -p "按Enter键继续..."
    return 0
}

# 主菜单
show_menu() {
    clear_console
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}        RGA捕获程序构建与运行工具        ${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    echo -e "${YELLOW}1. 编译项目${NC}"
    echo -e "${YELLOW}2. 运行RGA测试程序${NC}"
    echo -e "${YELLOW}3. 停止所有RGA测试进程${NC}"
    echo -e "${YELLOW}4. 清理构建目录${NC}"
    echo -e "${YELLOW}5. 退出${NC}"
    echo ""
    echo -e "${GREEN}========================================${NC}"
    echo -e "${YELLOW}请选择操作 (1-5): ${NC}"
}

# 主函数
main() {
    while true; do
        show_menu
        read choice
        
        case $choice in
            1)
                compile
                read -p "按Enter键继续..."
                ;;
            2)
                run_img
                ;;
            3)
                stop_img
                read -p "按Enter键继续..."
                ;;
            4)
                clean
                read -p "按Enter键继续..."
                ;;
            5)
                echo -e "${GREEN}感谢使用，再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效的选项，请重新选择${NC}"
                read -p "按Enter键继续..."
                ;;
        esac
    done
}

# 运行主函数
main