{"测试说明": "验证功能配置API中的阵营选择功能", "测试用例": {"function_modify_request": {"action": "function_modify", "content": {"username": "testuser", "gameName": "csgo2", "cardKey": "test123", "configs": [{"presetName": "配置1", "hotkey": "右键", "aiMode": "FOV", "lockPosition": "头部", "selectedFaction": "警方", "triggerSwitch": true, "enabled": true}, {"presetName": "配置2", "hotkey": "左键", "aiMode": "PID", "lockPosition": "胸部", "selectedFaction": "匪方", "triggerSwitch": false, "enabled": true}, {"presetName": "配置3", "hotkey": "中键", "aiMode": "FOVPID", "lockPosition": "颈部", "selectedFaction": "无", "triggerSwitch": false, "enabled": false}, {"presetName": "配置4", "hotkey": "前侧", "aiMode": "PID", "lockPosition": "头部", "selectedFaction": "警方", "triggerSwitch": true, "enabled": true}], "createdAt": "2023-06-01T12:00:00Z", "updatedAt": "2023-06-01T14:00:00Z"}}, "expected_response": {"action": "function_modify_response", "status": "ok", "data": {"username": "testuser", "gameName": "csgo2", "configs": [{"presetName": "配置1", "hotkey": "右键", "aiMode": "FOV", "lockPosition": "头部", "selectedFaction": "警方", "triggerSwitch": true, "enabled": true}, {"presetName": "配置2", "hotkey": "左键", "aiMode": "PID", "lockPosition": "胸部", "selectedFaction": "匪方", "triggerSwitch": false, "enabled": true}, {"presetName": "配置3", "hotkey": "中键", "aiMode": "FOVPID", "lockPosition": "颈部", "selectedFaction": "无", "triggerSwitch": false, "enabled": false}, {"presetName": "配置4", "hotkey": "前侧", "aiMode": "PID", "lockPosition": "头部", "selectedFaction": "警方", "triggerSwitch": true, "enabled": true}], "createdAt": "2023-06-01T12:00:00Z", "updatedAt": "2023-06-01T14:00:00Z"}}}, "验证要点": ["1. 每个配置都包含selectedFaction字段", "2. 阵营选择支持：警方、匪方、无三种选项", "3. 不同配置可以选择不同的阵营", "4. 阵营选择与其他配置项独立存在", "5. 数据库正确存储和读取selected_faction字段", "6. API响应包含完整的阵营信息"], "数据库验证": {"table": "function_configs", "fields": ["username", "game_name", "preset_name", "ai_mode", "lock_position", "selected_faction", "hotkey", "trigger_switch", "enabled", "created_at", "updated_at"], "sample_data": ["('testuser', 'csgo2', '配置1', 'FOV', '头部', '警方', '右键', '1', '1', NOW(), NOW())", "('testuser', 'csgo2', '配置2', 'PID', '胸部', '匪方', '左键', '0', '1', NOW(), NOW())", "('testuser', 'csgo2', '配置3', 'FOVPID', '颈部', '无', '中键', '0', '0', NOW(), NOW())", "('testuser', 'csgo2', '配置4', 'PID', '头部', '警方', '前侧', '1', '1', NOW(), NOW())"]}}