#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目名称
BACKEND_EXEC="bl_server"
BUILD_DIR="build"
BIN_DIR="${BUILD_DIR}/bin"

# 清空控制台
clear_console() {
    clear
}

# 检查脚本是否具有执行权限
check_permissions() {
    if [ ! -x "$0" ]; then
        echo -e "${YELLOW}添加执行权限到脚本...${NC}"
        chmod +x "$0"
        echo -e "${GREEN}执行权限已添加，请重新运行脚本${NC}"
        exit 0
    fi
}

# 检查是否安装了必要的工具
check_dependencies() {
    if ! command -v cmake &> /dev/null; then
        echo -e "${RED}错误: 未安装CMake${NC}"
        exit 1
    fi
    if ! command -v make &> /dev/null; then
        echo -e "${RED}错误: 未安装Make${NC}"
        exit 1
    fi
}

# 编译项目
compile() {
    echo -e "${YELLOW}开始编译后端项目...${NC}"
    
    # 创建构建目录
    if [ ! -d "$BUILD_DIR" ]; then
        mkdir -p "$BUILD_DIR"
    fi
    
    cd "$BUILD_DIR" || exit 1
    
    # 运行CMake
    cmake ..
    if [ $? -ne 0 ]; then
        echo -e "${RED}CMake配置失败${NC}"
        cd ..
        return 1
    fi
    
    # 编译
    make -j$(nproc)
    if [ $? -ne 0 ]; then
        echo -e "${RED}编译失败${NC}"
        cd ..
        return 1
    fi
    
    cd ..
    echo -e "${GREEN}编译成功！${NC}"
    return 0
}

# 运行后端服务器
run_backend() {
    echo -e "${YELLOW}启动后端服务器...${NC}"
    
    # 检查可执行文件是否存在
    if [ ! -f "${BIN_DIR}/${BACKEND_EXEC}" ]; then
        echo -e "${RED}错误: 后端服务器可执行文件不存在，请先编译项目${NC}"
        return 1
    fi
    
    # 检查是否已经在运行
    BACKEND_PID=$(ps -ef | grep "${BIN_DIR}/${BACKEND_EXEC}" | grep -v grep | awk '{print $2}')
    if [ -n "$BACKEND_PID" ]; then
        echo -e "${RED}错误: 后端服务器已经在运行 (PID: $BACKEND_PID)${NC}"
        return 1
    fi
    
    # 运行后端服务器
    "${BIN_DIR}/${BACKEND_EXEC}"
    echo -e "${GREEN}后端服务器已执行完毕！${NC}"
    return 0
}

# 结束运行后端服务器
stop_backend() {
    echo -e "${YELLOW}正在停止后端服务器...${NC}"
    
    # 使用更精确的方式查找进程
    BACKEND_PID=$(ps -ef | grep "${BIN_DIR}/${BACKEND_EXEC}" | grep -v grep | awk '{print $2}')
    if [ -n "$BACKEND_PID" ]; then
        echo -e "${GREEN}找到后端服务器进程 (PID: $BACKEND_PID)，正在终止...${NC}"
        kill -15 $BACKEND_PID
        sleep 1
        
        # 检查进程是否仍在运行，如果是则强制终止
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            echo -e "${YELLOW}进程未响应正常终止，尝试强制终止...${NC}"
            kill -9 $BACKEND_PID
            sleep 1
        fi
        
        echo -e "${GREEN}后端服务器已停止${NC}"
    else
        echo -e "${YELLOW}未找到运行中的后端服务器进程${NC}"
    fi
}

# 清理并重新编译
clean_and_compile() {
    echo -e "${YELLOW}清理并重新编译...${NC}"
    
    # 停止运行中的进程
    stop_backend
    
    # 清理构建目录
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    # 重新编译
    compile
}

# 查看后端服务器状态
view_backend_status() {
    echo -e "${YELLOW}查看后端服务器状态...${NC}"
    
    BACKEND_PID=$(ps -ef | grep "${BIN_DIR}/${BACKEND_EXEC}" | grep -v grep | awk '{print $2}')
    if [ -n "$BACKEND_PID" ]; then
        echo -e "${GREEN}后端服务器正在运行 (PID: $BACKEND_PID)${NC}"
        echo -e "${GREEN}内存使用情况:${NC}"
        ps -o pid,ppid,rss,vsz,comm -p $BACKEND_PID
    else
        echo -e "${YELLOW}后端服务器未运行${NC}"
    fi
}

# 显示菜单
show_menu() {
    clear_console
    echo -e "\n${GREEN}===== 后端服务工具 =====${NC}"
    echo "1. 编译后端项目"
    echo "2. 启动后端服务器"
    echo "3. 停止后端服务器"
    echo "4. 清理并重新编译"
    echo "5. 查看后端服务器状态"
    echo "6. 退出"
    echo -e "${YELLOW}请选择操作 (1-6): ${NC}"
}

# 主循环
main() {
    # 启动时清空控制台
    clear_console
    check_permissions
    check_dependencies
    
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                compile
                ;;
            2)
                run_backend
                ;;
            3)
                stop_backend
                ;;
            4)
                clean_and_compile
                ;;
            5)
                view_backend_status
                ;;
            6)
                echo -e "${GREEN}退出程序${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效的选择，请重新输入${NC}"
                ;;
        esac
        
        echo -e "\n按回车键继续..."
        read -r
    done
}

# 启动主程序
main 