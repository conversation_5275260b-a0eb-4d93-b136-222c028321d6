#include <algorithm>
#include <numeric>
#include <optional>
#include <cmath>
#include "core/model/yolo/yolo_v8.h"
#include "utils/gmodels.h"

namespace aibox::model {

// 辅助函数声明在类成员函数之前
inline static s32 Clamp(float val, float min = -128.f, float max = 127.f) {
    float res = val <= min ? min : (val >= max ? max : val);
    return (s32)res;
}

inline static int8_t F32ToAffine(float f32, s32 zp, float scale) {
    float dst_val = (f32 / scale) + (float)zp;
    s8 res = (s8)Clamp(dst_val);
    return res;
}

inline static uint8_t F32ToAffineU8(float f32, s32 zp, float scale) {
    float dst_val = (f32 / scale) + (float)zp;
    uint8_t res = (uint8_t)Clamp(dst_val, 0, 255);
    return res;
}

inline static float AffineToF32(int8_t qnt, int32_t zp, float scale) {
    return ((float)qnt - (float)zp) * scale;
}

inline static float AffineToF32U8(uint8_t qnt, int32_t zp, float scale) {
    return ((float)qnt - (float)zp) * scale;
}

YoloV8::YoloV8(std::span<u8> data) : Yolo(data) {
    LOG_INFO("YoloV8模型初始化成功");
    
    // 检查模型输出层的数量，以确定是否为新版RKNN格式
    is_rknn_format = model->GetOutTensorCount() >= 9; // YOLOv8的RKNN格式有9个输出张量
    
    // 根据模型获取DFL长度
    if (is_rknn_format) {
        const auto& box_shape = model->GetOutTensorInfo(0).shape;
        dfl_len = box_shape[1] / 4; // 按照官方示例获取DFL长度
        LOG_INFO("检测到RKNN格式的YOLOv8模型，DFL长度: {}", dfl_len);
    }
    
    LOG_INFO("YOLOv8模型输出层数量: {}, 使用{}格式", 
             model->GetOutTensorCount(), 
             is_rknn_format ? "RKNN多输出" : "单输出");
}

void YoloV8::PostProcess() {
    if (is_rknn_format) {
        ProcessRknnFormat();
    } else {
        ProcessStandardFormat();
    }
}

void YoloV8::ComputeDFL(float* tensor, int dfl_len, float* box) {
    for (int b = 0; b < 4; b++) {
        float exp_t[dfl_len];
        float exp_sum = 0;
        float acc_sum = 0;
        
        // 计算指数和
        for (int i = 0; i < dfl_len; i++) {
            exp_t[i] = exp(tensor[i + b * dfl_len]);
            exp_sum += exp_t[i];
        }
        
        // 计算加权和
        for (int i = 0; i < dfl_len; i++) {
            acc_sum += exp_t[i] / exp_sum * i;
        }
        
        box[b] = acc_sum;
    }
}

void YoloV8::ProcessRknnFormat() {
    // RKNN格式的YOLOv8有3个检测层，每个检测层有3个输出张量
    // 分别是边界框(box)、类别置信度(cls)和置信度总和(score_sum)
    boxes.clear();
    
    // 处理三个检测头
    for (size_t i = 0; i < 3; ++i) {
        size_t box_index = i * 3;        // 边界框输出索引
        size_t cls_index = i * 3 + 1;    // 类别输出索引
        size_t sum_index = i * 3 + 2;    // 置信度总和索引
        
        // 获取网格大小
        const auto& box_shape = model->GetOutTensorInfo(box_index).shape;
        u32 grid_h = box_shape[2];
        u32 grid_w = box_shape[3];
        
        // 获取输入图像大小来计算步长
        const auto& image_shape = model->GetInTensorInfo(0).shape;
        u32 stride = image_shape[1] / grid_h;
        
        // 处理当前检测头的输出
        ProcessRknnData(
            &model->GetOutTensorInfo(box_index),
            &model->GetOutTensorInfo(cls_index),
            &model->GetOutTensorInfo(sum_index),
            grid_h, grid_w, static_cast<float>(stride)
        );
    }
}

void YoloV8::ProcessRknnData(
    const TensorInfo* tensor_box,
    const TensorInfo* tensor_cls,
    const TensorInfo* tensor_sum,
    u32 grid_h, u32 grid_w,
    float stride) {
    
    const u32 grid_len = grid_h * grid_w;
    const u32 num_classes = yolo_config::g_yolo_class_num;
    const float sum_noise = yolo_config::g_yolo_class_num * 1.f / 256;
    
    try {
        // 根据张量类型选择不同的处理方法
        if (tensor_box->type == TensorType::Int8) {
            // INT8量化模型处理
    const s8 zero_i8 = F32ToAffine(0.f, tensor_cls->zp, tensor_cls->scale);
    const s8 threshold_i8 = F32ToAffine(threshold, tensor_cls->zp, tensor_cls->scale);
    const s8 sum_noise_i8 = F32ToAffine(sum_noise, tensor_sum->zp, tensor_sum->scale);
    
        // 遍历所有网格点
        for (u32 i = 0; i < grid_h; i++) {
            for (u32 j = 0; j < grid_w; j++) {
                const u32 grid_idx = i * grid_w + j;
                
                // 使用置信度总和快速过滤
                if (tensor_sum->Data()[grid_idx] < sum_noise_i8) {
                    continue;
                }
                
                // 查找最高置信度的类别
                s32 best_class = -1;
                s8 max_score = zero_i8;
                u32 cls_offset = grid_idx;
                
                for (s32 c = 0; c < num_classes; c++) {
                    const s8 score = tensor_cls->Data()[cls_offset];
                    if (score > threshold_i8 && score > max_score) {
                        max_score = score;
                        best_class = c;
                    }
                    cls_offset += grid_len; // 移动到下一个类别的得分
                }
                
                // 如果找到有效的类别
                if (max_score > threshold_i8) {
                        // 为DFL计算准备数据
                        float before_dfl[dfl_len * 4];
                    u32 box_offset = grid_idx;
                    
                        for (int k = 0; k < dfl_len * 4; k++) {
                            before_dfl[k] = AffineToF32(
                            tensor_box->Data()[box_offset], 
                            tensor_box->zp, 
                            tensor_box->scale
                        );
                        box_offset += grid_len;
                    }
                    
                        // 执行DFL计算
                        float box[4];
                        ComputeDFL(before_dfl, dfl_len, box);
                        
                        // 计算边界框坐标 (使用官方示例的坐标计算方式)
                        const float x1 = (-box[0] + static_cast<float>(j) + 0.5f) * stride;
                        const float y1 = (-box[1] + static_cast<float>(i) + 0.5f) * stride;
                        const float x2 = (box[2] + static_cast<float>(j) + 0.5f) * stride;
                        const float y2 = (box[3] + static_cast<float>(i) + 0.5f) * stride;
                    const float score = AffineToF32(max_score, tensor_cls->zp, tensor_cls->scale);
                    
                    // 添加到检测结果
                    boxes.push_back({
                        best_class,
                        score,
                        x1, y1, x2, y2
                    });
                    }
                }
            }
        } else if (tensor_box->type == TensorType::Uint8) {
            // UINT8量化模型处理
            const uint8_t zero_u8 = F32ToAffineU8(0.f, tensor_cls->zp, tensor_cls->scale);
            const uint8_t threshold_u8 = F32ToAffineU8(threshold, tensor_cls->zp, tensor_cls->scale);
            const uint8_t sum_noise_u8 = F32ToAffineU8(sum_noise, tensor_sum->zp, tensor_sum->scale);
            
            // 遍历所有网格点
            for (u32 i = 0; i < grid_h; i++) {
                for (u32 j = 0; j < grid_w; j++) {
                    const u32 grid_idx = i * grid_w + j;
                    const uint8_t* box_data = reinterpret_cast<const uint8_t*>(tensor_box->data.data());
                    const uint8_t* cls_data = reinterpret_cast<const uint8_t*>(tensor_cls->data.data());
                    const uint8_t* sum_data = reinterpret_cast<const uint8_t*>(tensor_sum->data.data());
                    
                    // 使用置信度总和快速过滤
                    if (sum_data[grid_idx] < sum_noise_u8) {
                        continue;
                    }
                    
                    // 查找最高置信度的类别
                    s32 best_class = -1;
                    uint8_t max_score = zero_u8;
                    u32 cls_offset = grid_idx;
                    
                    for (s32 c = 0; c < num_classes; c++) {
                        const uint8_t score = cls_data[cls_offset];
                        if (score > threshold_u8 && score > max_score) {
                            max_score = score;
                            best_class = c;
                        }
                        cls_offset += grid_len; // 移动到下一个类别的得分
                    }
                    
                    // 如果找到有效的类别
                    if (max_score > threshold_u8) {
                        // 为DFL计算准备数据
                        float before_dfl[dfl_len * 4];
                        u32 box_offset = grid_idx;
                        
                        for (int k = 0; k < dfl_len * 4; k++) {
                            before_dfl[k] = AffineToF32U8(
                                box_data[box_offset], 
                                tensor_box->zp, 
                                tensor_box->scale
                            );
                            box_offset += grid_len;
                        }
                        
                        // 执行DFL计算
                        float box[4];
                        ComputeDFL(before_dfl, dfl_len, box);
                        
                        // 计算边界框坐标
                        const float x1 = (-box[0] + static_cast<float>(j) + 0.5f) * stride;
                        const float y1 = (-box[1] + static_cast<float>(i) + 0.5f) * stride;
                        const float x2 = (box[2] + static_cast<float>(j) + 0.5f) * stride;
                        const float y2 = (box[3] + static_cast<float>(i) + 0.5f) * stride;
                        const float score = AffineToF32U8(max_score, tensor_cls->zp, tensor_cls->scale);
                        
                        // 添加到检测结果
                        boxes.push_back({
                            best_class,
                            score,
                            x1, y1, x2, y2
                        });
                    }
                }
            }
        } else if (tensor_box->type == TensorType::Float32) {
            // 浮点模型处理
            const float* box_data = reinterpret_cast<const float*>(tensor_box->data.data());
            const float* cls_data = reinterpret_cast<const float*>(tensor_cls->data.data());
            const float* sum_data = reinterpret_cast<const float*>(tensor_sum->data.data());
            
            // 遍历所有网格点
            for (u32 i = 0; i < grid_h; i++) {
                for (u32 j = 0; j < grid_w; j++) {
                    const u32 grid_idx = i * grid_w + j;
                    
                    // 使用置信度总和快速过滤
                    if (sum_data && sum_data[grid_idx] < sum_noise) {
                        continue;
                    }
                    
                    // 查找最高置信度的类别
                    s32 best_class = -1;
                    float max_score = 0.0f;
                    u32 cls_offset = grid_idx;
                    
                    for (s32 c = 0; c < num_classes; c++) {
                        const float score = cls_data[cls_offset];
                        if (score > threshold && score > max_score) {
                            max_score = score;
                            best_class = c;
                        }
                        cls_offset += grid_len; // 移动到下一个类别的得分
                    }
                    
                    // 如果找到有效的类别
                    if (max_score > threshold) {
                        // 为DFL计算准备数据
                        float before_dfl[dfl_len * 4];
                        u32 box_offset = grid_idx;
                        
                        for (int k = 0; k < dfl_len * 4; k++) {
                            before_dfl[k] = box_data[box_offset];
                            box_offset += grid_len;
                        }
                        
                        // 执行DFL计算
                        float box[4];
                        ComputeDFL(before_dfl, dfl_len, box);
                        
                        // 计算边界框坐标
                        const float x1 = (-box[0] + static_cast<float>(j) + 0.5f) * stride;
                        const float y1 = (-box[1] + static_cast<float>(i) + 0.5f) * stride;
                        const float x2 = (box[2] + static_cast<float>(j) + 0.5f) * stride;
                        const float y2 = (box[3] + static_cast<float>(i) + 0.5f) * stride;
                        
                        // 添加到检测结果
                        boxes.push_back({
                            best_class,
                            max_score,
                            x1, y1, x2, y2
                        });
                }
            }
            }
        } else {
            LOG_ERROR("不支持的张量类型: {}", static_cast<int>(tensor_box->type));
        }
    } catch (const std::exception& e) {
        LOG_ERROR("YOLOv8 RKNN格式后处理出错: {}", e.what());
    }
}

void YoloV8::ProcessStandardFormat() {
    // 原始YOLOv8处理方式，适用于单一输出张量的模型
    // 获取模型的输出，YOLOv8通常只有一个输出张量
    const auto& output_tensor = model->GetOutTensorInfo(0);
    
    // 输出张量的形状应该是 [1, 84, grid_h, grid_w]
    // 其中84 = 4（边界框）+ 1（置信度）+ 79（类别）或类似
    const auto& tensor_shape = output_tensor.shape;
    
    const u32 grid_h = tensor_shape[2];
    const u32 grid_w = tensor_shape[3];
    
    // 获取输入图像大小来计算步长
    const auto& image_shape = model->GetInTensorInfo(0).shape;
    const u32 stride = image_shape[1] / grid_h;
    
    // 处理输出数据
    ProcessStandardData(&output_tensor, grid_h, grid_w, static_cast<float>(stride));
}

void YoloV8::ProcessStandardData(const TensorInfo* tensor_output,
                          u32 grid_h,
                          u32 grid_w,
                          float stride) {
    const u32 grid_len = grid_h * grid_w;
    const u32 num_classes = yolo_config::g_yolo_class_num;
    const u32 box_channels = 4; // x, y, w, h
    const u32 conf_offset = box_channels; // 置信度偏移
    const u32 class_offset = box_channels + 1; // 类别偏移
    const u32 channels_per_grid = box_channels + 1 + num_classes; // 每个网格点的通道数
    
    boxes.clear();
    
    try {
        // 遍历所有网格点
        for (u32 i = 0; i < grid_h; i++) {
            for (u32 j = 0; j < grid_w; j++) {
                const u32 grid_idx = i * grid_w + j;
                
                // 获取置信度
                float confidence = 0.0f;
                if (tensor_output->type == TensorType::Int8) {
                    const s8* conf_ptr = &tensor_output->Data()[(conf_offset * grid_len) + grid_idx];
                    confidence = AffineToF32(*conf_ptr, tensor_output->zp, tensor_output->scale);
                } else {
                    // 其他类型的处理...
                    LOG_ERROR("不支持的张量类型: {}", static_cast<int>(tensor_output->type));
                    continue;
                }
                
                // 如果置信度低于阈值，跳过这个网格点
                if (confidence < threshold) {
                    continue;
                }
                
                // 获取最佳类别
                s32 best_class = -1;
                float best_score = threshold;
                
                for (u32 c = 0; c < num_classes; c++) {
                    float class_score = 0.0f;
                    if (tensor_output->type == TensorType::Int8) {
                        const s8* class_ptr = &tensor_output->Data()[((class_offset + c) * grid_len) + grid_idx];
                        class_score = AffineToF32(*class_ptr, tensor_output->zp, tensor_output->scale);
                    } else {
                        // 其他类型的处理...
                        continue;
                    }
                    
                    // 计算最终得分 (class_score * confidence)
                    float score = class_score * confidence;
                    
                    if (score > best_score) {
                        best_score = score;
                        best_class = c;
                    }
                }
                
                // 如果找到有效的类别
                if (best_class >= 0) {
                    // 获取边界框参数
                    float box_params[4] = {0};
                    
                    for (u32 k = 0; k < box_channels; k++) {
                        if (tensor_output->type == TensorType::Int8) {
                            const s8* box_ptr = &tensor_output->Data()[(k * grid_len) + grid_idx];
                            box_params[k] = AffineToF32(*box_ptr, tensor_output->zp, tensor_output->scale);
                        } else {
                            // 其他类型的处理...
                            continue;
                        }
                    }
                    
                    // YOLOv8使用中心点坐标和宽高表示法
                    // 中心点 (cx, cy)
                    float cx = (box_params[0] + j) * stride;
                    float cy = (box_params[1] + i) * stride;
                    
                    // 宽和高 (w, h)
                    float w = box_params[2] * stride;
                    float h = box_params[3] * stride;
                    
                    // 计算边界框的左上角和右下角坐标
                    float x1 = cx - w / 2.0f;
                    float y1 = cy - h / 2.0f;
                    float x2 = cx + w / 2.0f;
                    float y2 = cy + h / 2.0f;
                    
                    // 添加到检测结果
                    boxes.push_back({
                        best_class,
                        best_score,
                        x1, y1, x2, y2
                    });
                    
                    // LOG_DEBUG("检测到边界框 - 类别: {}, 得分: {:.2f}, 坐标: [{:.1f}, {:.1f}, {:.1f}, {:.1f}]",
                    //          best_class, best_score, x1, y1, x2, y2);
                }
            }
        }
    } catch (const std::exception& e) {
        LOG_ERROR("YOLOv8标准格式后处理出错: {}", e.what());
    }
}

}  // namespace aibox::model 