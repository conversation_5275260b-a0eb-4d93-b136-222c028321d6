#pragma once

#include <vector>
#include "utils/logger.h"
#include "base/macros.h"
#include "base/types.h"
#include "core/model/model.h"
#include "core/model/yolo/yolo.h"

namespace aibox::model {

class YoloV8 : public Yolo {
public:
    explicit YoloV8(std::span<u8> data);

private:
    bool is_rknn_format{false}; // 标识是否为RKNN多输出格式
    int dfl_len{16}; // 分布焦点损失长度，默认为16

    void PostProcess() override;

    // 处理RKNN格式的多输出模型
    void ProcessRknnFormat();
    void ProcessRknnData(
        const TensorInfo* tensor_box,
        const TensorInfo* tensor_cls, 
        const TensorInfo* tensor_sum,
        u32 grid_h, 
        u32 grid_w, 
        float stride
    );

    // 处理标准格式的单输出模型
    void ProcessStandardFormat();
    void ProcessStandardData(
        const TensorInfo* tensor_output, 
        u32 grid_h, 
        u32 grid_w, 
        float stride
    );
    
    // DFL计算函数 - 从官方示例引入
    void ComputeDFL(float* tensor, int dfl_len, float* box);
};

}  // namespace aibox::model 