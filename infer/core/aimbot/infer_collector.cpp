#include <cmath>
#include <limits>
#include "core/aimbot/infer_collector.h"
#include "utils/gmodels.h"

namespace aibox::aimbot {

/**
 * @brief 根据游戏类型和阵营选择判断目标是否应该被攻击
 * 
 * @param class_id 目标的类别ID
 * @param selected_faction 用户选择的阵营
 * @return true 如果应该攻击该目标，false 否则
 */
bool InferCollector::ShouldAttackTarget(int class_id, const std::string& selected_faction) {
    // 如果阵营选择为"无"，攻击所有目标
    if (selected_faction == "无") {
        return true;
    }
    // 根据不同游戏的标签配置进行判断
    if (webui::home::g_game_name == "wwqy") {
        // wwqy游戏的特殊配置：FF(匪方)在索引1，JF(警方)在索引3
        if (selected_faction == "警方") {
            // 选择警方阵营，攻击匪方目标（FF，索引1）
            return class_id == 3;
        } else if (selected_faction == "匪方") {
            // 选择匪方阵营，攻击警方目标（JF，索引3）和其他目标（M，索引2）
            return class_id == 1 || class_id == 2;
        }
    } else if (webui::home::g_game_name == "cf") {
        // cf游戏的特殊配置：FF(匪方)在索引0，JF(警方)在索引1
        if (selected_faction == "警方") {
            // 选择警方阵营，攻击匪方目标（FF，索引0）
            return class_id == 1;
        } else if (selected_faction == "匪方") {
            // 选择匪方阵营，攻击警方目标（JF，索引1）
            return class_id == 0;
        }
    } else {
        return class_id == 0;
    }
    
    // 默认情况下不攻击
    return false;
}

/**
 * @brief 寻找最近的目标（集成锁定部位计算和开火范围判断）
 * 
 * @param boxes 目标检测结果列表，包含所有检测到的目标的边界框信息
 * @param model_width 模型宽度，用于计算中心点
 * @param model_height 模型高度，用于计算中心点
 * @param screen_center_x 屏幕中心X坐标
 * @param screen_center_y 屏幕中心Y坐标
 * @return 返回最接近准星的目标信息，包含瞄准点和开火状态
 */
TargetInfo InferCollector::FindNearestTarget(const std::vector<model::Box>& boxes, 
                                            int model_width, int model_height,
                                            double screen_center_x, double screen_center_y) {
    // 计算实际准星位置（使用模型尺寸的一半作为中心点）
    const double actual_center_x = model_width / 2.0;
    const double actual_center_y = model_height / 2.0;
    
    // 初始化最小距离为最大浮点数，用于寻找最近目标
    double min_distance = std::numeric_limits<double>::max();
    TargetInfo nearest;
    
    // 获取瞄准范围设置，如果未配置则使用默认值200
    const double aim_range = static_cast<double>(webui::aim::g_aim_range > 0 ? webui::aim::g_aim_range : 200);
    
    // 检查激活配置索引是否有效，获取背闪防护设置
    bool flash_shield_enabled = false;
    if (webui::function::g_active_config_index >= 0 && 
        webui::function::g_active_config_index < webui::function::PRESET_CONFIGS.size()) {
        flash_shield_enabled = (webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].flash_shield == "true");
    }
    
    // 遍历所有检测到的目标，同时处理背闪检测和瞄准逻辑
    for (const auto& box : boxes) {
        // 🎯 背闪检测：只有在启用背闪防护功能时才检测背闪
        if (flash_shield_enabled && webui::home::g_game_name == "wwqy" && box.class_id == 0) {
            core_vars::g_flash_detected = true;
            LOG_DEBUG("背闪检测：在wwqy游戏中检测到背闪 (class_id=0)，背闪防护已启用");
        }
        
        // 🎯 阵营过滤：根据游戏类型和阵营选择判断是否应该攻击此目标
        if (!ShouldAttackTarget(box.class_id, webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].selected_faction)) {
            continue;
        }
        
        // 计算目标几何信息
        const double x1 = box.x1;
        const double y1 = box.y1;
        const double x2 = box.x2;
        const double y2 = box.y2;
        const double w = x2 - x1;
        const double h = y2 - y1;
        
        // 计算目标中心点坐标
        const double center_x = (x1 + x2) / 2.0;
        const double center_y = (y1 + y2) / 2.0;
        
        // 🎯 锁定部位计算：根据锁定部位调整瞄准点Y坐标
        double aim_y = center_y; // 默认使用中心点
        
        if (webui::function::g_lock_position == "头部") {
            const double height_ratio = webui::aim::g_head_height / 100.0;
            aim_y = y1 + h * height_ratio;
        } else if (webui::function::g_lock_position == "颈部") {
            const double height_ratio = webui::aim::g_neck_height / 100.0;
            aim_y = y1 + h * height_ratio;
        } else if (webui::function::g_lock_position == "胸部") {
            const double height_ratio = webui::aim::g_chest_height / 100.0;
            aim_y = y1 + h * height_ratio;
        }
        
        // 计算基于调整后瞄准点的距离（用于寻找最近目标）
        const double dx = center_x - actual_center_x;
        const double dy = aim_y - actual_center_y;
        const double distance = std::sqrt(dx * dx + dy * dy);
        
        // 如果距离大于瞄准范围，则跳过这个目标
        if (distance > aim_range) {
            continue;
        }
        
        // 🎯 开火范围判断：计算目标与准星的偏移（使用屏幕中心坐标）
        const double fire_dx = center_x - screen_center_x;
        const double fire_dy = aim_y - screen_center_y;
        
        // 获取当前锁定部位对应的开火范围参数
        double range_x = 50.0; // 默认值
        double range_y = 50.0; // 默认值
        
        if (webui::function::g_lock_position == "头部") {
            range_x = webui::aim::g_head_range_x;
            range_y = webui::aim::g_head_range_y;
        } else if (webui::function::g_lock_position == "颈部") {
            range_x = webui::aim::g_neck_range_x;
            range_y = webui::aim::g_neck_range_y;
        } else if (webui::function::g_lock_position == "胸部") {
            range_x = webui::aim::g_chest_range_x;
            range_y = webui::aim::g_chest_range_y;
        }
        
        // 计算开火状态
        bool is_fire = (std::abs(fire_dx) <= range_x && std::abs(fire_dy) <= range_y);
        
        // 选择距离最近的目标
        if (distance < min_distance) {
            min_distance = distance;
            
            // 更新最近目标信息
            nearest.x = center_x;           // 目标水平中心X坐标
            nearest.y = center_y;           // 目标垂直中心Y坐标（原始中心）
            nearest.aim_y = aim_y;          // 调整后的瞄准点Y坐标
            nearest.width = w;              // 目标宽度
            nearest.height = h;             // 目标高度
            nearest.x1 = x1;                // 左上角x坐标
            nearest.y1 = y1;                // 左上角y坐标
            nearest.x2 = x2;                // 右下角x坐标
            nearest.y2 = y2;                // 右下角y坐标
            nearest.distance = distance;    // 距离（基于调整后的瞄准点）
            nearest.class_id = box.class_id; // 类别ID
            nearest.confidence = box.score;  // 置信度
            nearest.is_fire = is_fire;      // 开火状态
        }
    }
    
    // 返回最近的目标信息
    return nearest;
}

}  // namespace aibox::aimbot
