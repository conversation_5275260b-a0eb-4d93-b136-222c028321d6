#pragma once

#include <string>
#include <vector>
#include "base/types.h"
#include "core/model/yolo/yolo.h"
#include <opencv2/opencv.hpp>
#include <chrono>
#include <unordered_map>

namespace aibox::aimbot {

/**
 * @brief 数据收集类，负责图像采集和保存的函数封装
 */
class DataCollector {
public:
    /**
     * @brief 保存数据收集图像到指定目录
     * 
     * @param boxes 目标检测结果
     * @param img_data 图像数据
     * @param img_width 图像宽度
     * @param img_height 图像高度
     * @param frame_number 帧序号
     * @param center_x 屏幕中心X坐标
     * @param center_y 屏幕中心Y坐标
     * @param collectionName 指定的数据收集名称，默认为空
     * @return true 保存成功
     * @return false 保存失败
     */
    static bool SaveCollectionImage(const std::vector<model::Box>& boxes, 
                            const u8* img_data, 
                            u32 img_width,
                            u32 img_height,
                            int frame_number,
                            double center_x,
                            double center_y,
                            const std::string& collectionName = "");

    /**
     * @brief 保存调试图像到指定目录
     * 
     * @param boxes 目标检测结果
     * @param img_data 图像数据
     * @param img_width 图像宽度
     * @param img_height 图像高度
     * @param target_box_index 目标框索引
     * @param frame_number 帧序号
     * @param center_x 屏幕中心X坐标
     * @param center_y 屏幕中心Y坐标
     * @return true 保存成功
     * @return false 保存失败
     */
    static bool SaveDebugImage(const std::vector<model::Box>& boxes, 
                       const u8* img_data, 
                       u32 img_width,
                       u32 img_height,
                       s32 target_box_index,
                       int frame_number,
                       double center_x,
                       double center_y);

private:
    /**
     * @brief 生成文件名（基于时间戳）
     * 
     * @param prefix 文件名前缀
     * @param extension 文件扩展名
     * @return std::string 生成的文件名
     */
    static std::string GenerateFileName(const std::string& prefix, const std::string& extension);

    /**
     * @brief 准备保存数据收集图像的路径
     * 
     * @param is_map 是否为地图数据（否则为目标数据）
     * @param collectionName 指定的数据收集名称
     * @return std::string 保存路径
     */
    static std::string PrepareCollectionPath(bool is_map, const std::string& collectionName);
    
    /**
     * @brief 生成YOLO格式的标签文件
     * 
     * @param boxes 目标检测结果
     * @param label_path 标签文件保存路径
     * @param img_width 图像宽度
     * @param img_height 图像高度
     * @return true 生成成功
     * @return false 生成失败
     */
    static bool GenerateYoloLabel(const std::vector<model::Box>& boxes,
                           const std::string& label_path,
                           u32 img_width,
                           u32 img_height);

    /**
     * @brief 检查是否可以保存图像（基于时间戳限制）
     * 
     * @param filename 文件名（带时间戳）
     * @param type 收集类型（地图/目标）
     * @return true 可以保存
     * @return false 不能保存（时间间隔太短）
     */
    static bool ShouldSaveImage(const std::string& filename, const std::string& type);

    // 存储上次保存的时间戳（按收集类型分开存储）
    static std::unordered_map<std::string, std::chrono::time_point<std::chrono::system_clock>> last_save_times;
    
    // 图像保存的最小时间间隔（秒）
    static constexpr double MIN_SAVE_INTERVAL = 0.3;
};

} // namespace aibox::aimbot 