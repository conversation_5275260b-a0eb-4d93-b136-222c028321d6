#pragma once

#include <vector>
#include "core/model/yolo/yolo.h"

namespace aibox::aimbot {

// 定义目标信息结构体
struct TargetInfo {
    double x{0.0};         // 目标瞄准点X坐标（通常是水平中心点）
    double y{0.0};         // 目标瞄准点Y坐标（根据瞄准位置变化）
    double aim_y{0.0};     // 调整后的瞄准点Y坐标（根据锁定部位计算）
    double width{0.0};     // 目标宽度
    double height{0.0};    // 目标高度
    double x1{0.0};        // 原始边界框左上角x坐标
    double y1{0.0};        // 原始边界框左上角y坐标
    double x2{0.0};        // 原始边界框右下角x坐标
    double y2{0.0};        // 原始边界框右下角y坐标
    double distance{-1.0}; // 与准星的距离（基于调整后的瞄准点）
    int class_id{-1};      // 目标类别ID
    float confidence{0.0f};// 置信度
    bool is_fire{false};   // 是否在开火范围内
};

class InferCollector {
public:
    /**
     * @brief 寻找最近的目标（集成锁定部位计算和开火范围判断）
     * 
     * @param boxes 目标检测结果列表，包含所有检测到的目标的边界框信息
     * @param model_width 模型宽度，用于计算中心点
     * @param model_height 模型高度，用于计算中心点
     * @param screen_center_x 屏幕中心X坐标
     * @param screen_center_y 屏幕中心Y坐标
     * @return 返回最接近准星的目标信息，包含瞄准点和开火状态
     */
    static TargetInfo FindNearestTarget(const std::vector<model::Box>& boxes, 
                                       int model_width, int model_height,
                                       double screen_center_x, double screen_center_y);

private:
    /**
     * @brief 根据游戏类型和阵营选择判断目标是否应该被攻击
     * 
     * @param class_id 目标的类别ID
     * @param selected_faction 用户选择的阵营
     * @return true 如果应该攻击该目标，false 否则
     */
    static bool ShouldAttackTarget(int class_id, const std::string& selected_faction);
};

}  // namespace aibox::aimbot
