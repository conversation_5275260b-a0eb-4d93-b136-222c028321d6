#include <chrono>
#include <cmath>
#include <iostream>
#include <thread>
#include <im2d.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#include "base/alignment.h"
#include "base/file_utils.h"
#include "core/aimbot/aimbot_debug.h"
#include "core/aimbot/aimbot_fps.h"
#include "core/aimbot/infer_collector.h"
#include "core/model/yolo/yolo.h"
#include "core/video/video_capture.h"
#include "utils/gmodels.h"
#include <filesystem>
#include <limits>
#include <fstream>
#include <cstdlib>
#include <unistd.h>
#include <linux/limits.h>
#include <linux/videodev2.h>
#include "base/fps_counter.h"
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <Poco/File.h>

namespace aibox::aimbot {

struct FrameResource {
    video::VideoBuffer buffer{};
    rga_buffer_handle_t in_handle{};
    rga_buffer_t in_img{};
    rga_buffer_t out_img{};
    std::vector<u8> out_data;

    FrameResource(const video::VideoBuffer& in_buffer,
                  u32 in_width,
                  u32 in_height,
                  u32 out_width,
                  u32 out_height) {
        buffer = in_buffer;
        in_handle = importbuffer_virtualaddr(buffer.buffer, static_cast<int>(buffer.length));
        in_img = wrapbuffer_handle(in_handle,
                                   static_cast<int>(in_width),
                                   static_cast<int>(in_height),
                                   RK_FORMAT_BGR_888);
        
        out_data.resize(out_width * out_height * 3);
        rga_buffer_handle_t out_handle = importbuffer_virtualaddr(out_data.data(), static_cast<int>(out_data.size()));
        out_img = wrapbuffer_handle(out_handle,
                                    static_cast<int>(out_width),
                                    static_cast<int>(out_height),
                                    RK_FORMAT_RGB_888);
    }

    ~FrameResource() {
        if (in_handle) {
            releasebuffer_handle(in_handle);
        }
        if (out_img.handle) {
            releasebuffer_handle(out_img.handle);
        }
    }
};

AimbotFPS::AimbotFPS() {
    video_capture = std::make_unique<video::VideoCapture>();
    CreateWorkerThreads();
    video_capture->Open();
    
    // 创建保存图像的目录
    std::filesystem::create_directories("detected_images");
    LOG_INFO("创建图像保存目录: detected_images");
}

AimbotFPS::~AimbotFPS() {
    // 不需要设置stop_requested，使用全局thread_control::g_thread_running
    std::scoped_lock lock(npu_work_mutex);
    npu_work_cv.notify_all();
    for (int i = 0; i < npu_threads.size(); ++i) {
        if (npu_threads[i].joinable()) {
            npu_threads[i].join();
        }
    }
}

bool AimbotFPS::Run() {
    frame_count = 0;
    try {
        SetupVideoInput();
        video_capture->StartStreaming();
        counter.Reset();
        while (thread_control::g_thread_running) {
            if (yolo_config::g_reload_model) {
                LOG_INFO("检测到模型重载请求，准备重载模型...");
                reloading_model = true; // 通知工作线程暂停
                // 唤醒所有等待任务的工作线程，让它们检查 reloading_model 标志
                { std::scoped_lock lock(npu_work_mutex); npu_work_cv.notify_all(); }

                // 等待所有工作线程准备好重载
                { std::unique_lock<std::mutex> lock(reload_mutex);
                  reload_cv.wait(lock, [this]() {
                      // 等待所有工作线程数量达到 npu_count 并且程序未停止
                      return !thread_control::g_thread_running || workers_ready_for_reload.load() == npu_count;
                  });
                }

                if (!thread_control::g_thread_running) break; // 如果在等待期间收到停止信号，退出循环

                LOG_INFO("所有工作线程已暂停，开始加载新模型...");

                // 获取当前游戏的配置，重新加载模型
                const std::string& current_game = webui::home::g_game_name;
                LOG_INFO("加载游戏 {} 的新模型...", current_game);

                // 清空旧的NPU资源，shared_ptr会自动释放模型
                npu_resources.clear();
                workers_ready_for_reload = 0; // 重置计数器

                // 根据当前游戏配置重新加载模型
                // 这里需要根据实际的游戏配置逻辑来获取新的模型路径和参数
                // 假设我们从 yolo_config::g_game_configs 中获取
                if (yolo_config::g_game_configs.count(current_game)) {
                    const auto& game_config = yolo_config::g_game_configs[current_game];
                    
                    // 更新YOLO类别数量
                    yolo_config::UpdateYoloClassNum(current_game);
                    
                    try {
                        LoadModel(game_config.weight_path, model::YoloVersion::YoloV8); // 重新加载模型
                        LOG_INFO("新模型加载成功: {}", game_config.weight_path);
                        // 重新设置阈值等参数
                        for (auto& resource : npu_resources) {
                            if (resource && resource->model) {
                                resource->model->SetThreshold(game_config.box_thresh);
                                resource->model->SetNMSThreshold(game_config.nms_thresh);
                            }
                        }
                        LOG_INFO("已为新模型设置阈值");
                    } catch (const std::exception& e) {
                         LOG_ERROR("加载新模型失败: {}", e.what());
                         // 处理加载失败的情况，可能需要退出或回滚
                         thread_control::g_thread_running = false; // 加载失败，强制退出
                         break;
                    }
                } else {
                    LOG_ERROR("找不到游戏 {} 的新模型配置，模型重载失败", current_game);
                    thread_control::g_thread_running = false; // 找不到配置，强制退出
                    break;
                }

                yolo_config::g_reload_model = false; // 重置重载标志
                reloading_model = false; // 通知工作线程恢复
                // 通知所有工作线程可以恢复工作
                { std::scoped_lock lock(reload_mutex); reload_cv.notify_all(); }

                LOG_INFO("模型重载完成，工作线程将恢复...");
            }
            u32 buffer_index = video_capture->DequeueBuffer();
            
            npu_work_queue.push(buffer_index);
            {
                std::scoped_lock lock(npu_work_mutex);
                npu_work_cv.notify_one();
            }
            counter.Tick();
            
            // 仅在主线程打印FPS
            // 根据实际帧率调整打印间隔，大约每5秒打印一次FPS信息
            if (frame_count % (frame_rate * 5) == 0) {
                // 打印FPS
                LOG_INFO("FPS: {:.2f}, 显示器刷新率: {}Hz", counter.GetFPS(), frame_rate);
            }
            
            // 每帧计数增加
            frame_count++;
        }
        LOG_INFO("推理主循环退出，停止视频流");
        video_capture->StopStreaming();
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("运行时出错: {}", e.what());
        return false;
    } catch (...) {
        LOG_ERROR("运行时出现未知错误");
        return false;
    }
}

void AimbotFPS::Stop() { 
    LOG_INFO("接收到停止推理的请求");
    // 不再设置stop_requested，而是使用全局的thread_control::g_thread_running
    thread_control::g_thread_running = false;
    // 通知所有工作线程退出，包括可能正在等待任务或重载的线程
    { std::scoped_lock lock(npu_work_mutex); npu_work_cv.notify_all(); }
     { std::scoped_lock lock(reload_mutex); reloading_model = false; reload_cv.notify_all(); }
    LOG_INFO("已通知所有NPU工作线程退出");
}

void AimbotFPS::SetupVideoInput() {
    in_width = video_capture->GetWidth();
    in_height = video_capture->GetHeight();
    // 获取帧率
    frame_rate = video_capture->GetFrameRate();
    // 将帧率同时设置到webui::header中，用于UI显示
    webui::header::frame_rate = frame_rate;
    // 将分辨率同时设置到webui::header中，用于UI显示
    webui::header::width = in_width;
    webui::header::height = in_height;

    const auto& buffers = video_capture->GetBuffers();
    frame_resources.resize(buffers.size());
    for (int i = 0; i < buffers.size(); i++) {
        frame_resources[i] = std::make_shared<FrameResource>(buffers[i],
                                                             in_width,
                                                             in_height,
                                                             out_width,
                                                             out_height);
    }
    
    // 输出更多HDMI信息
    if (video_capture->IsHDMIInput()) {
        LOG_INFO("HDMI输入已连接: 分辨率 {}x{}, 刷新率 {}Hz", 
                in_width, in_height, frame_rate);
        
        // 色彩空间信息
        std::string colorspace_name = "未知";
        switch (video_capture->GetColorSpace()) {
        case V4L2_COLORSPACE_SRGB: colorspace_name = "sRGB"; break;
        case V4L2_COLORSPACE_REC709: colorspace_name = "Rec.709"; break;
        case V4L2_COLORSPACE_BT2020: colorspace_name = "Rec.2020"; break;
        case V4L2_COLORSPACE_DCI_P3: colorspace_name = "DCI-P3"; break;
        case V4L2_COLORSPACE_SMPTE170M: colorspace_name = "SMPTE 170M"; break;
        case V4L2_COLORSPACE_SMPTE240M: colorspace_name = "SMPTE 240M"; break;
        }
        LOG_INFO("HDMI色彩空间: {}", colorspace_name);
    } else {
        LOG_INFO("输入尺寸: {}x{}, 输出尺寸: {}x{}, 帧率: {}Hz", 
                in_width, in_height, out_width, out_height, frame_rate);
    }
}

void AimbotFPS::LoadModel(const std::string& model_path, model::YoloVersion version) {
    try {
        LOG_INFO("开始加载模型: {}", model_path);
        npu_resources.resize(npu_count);
        LOG_INFO("读取模型文件");
        auto model_data = base::ReadFile(model_path);
        LOG_INFO("模型文件大小: {} 字节", model_data.size());
        
        // 检查模型文件是否为空
        if (model_data.empty()) {
            LOG_ERROR("模型文件为空或无法读取");
            thread_control::g_thread_running = false;
            LOG_ERROR("由于模型文件为空，程序将强制退出");
            exit(1); // 强制退出程序
        }
        
        for (int i = 0; i < npu_resources.size(); ++i) {
            LOG_DEBUG("创建NPU资源 {}", i);
            npu_resources[i] = std::make_shared<NPUResource>();
            LOG_DEBUG("为NPU {} 创建YOLO模型", i);
            npu_resources[i]->model = model::Yolo::Create(version, model_data);
            
            // 检查模型是否成功创建
            if (!npu_resources[i]->model) {
                LOG_ERROR("NPU {} 创建YOLO模型失败", i);
                thread_control::g_thread_running = false;
                LOG_ERROR("由于创建YOLO模型失败，程序将强制退出");
                exit(1); // 强制退出程序
            }
            
            LOG_DEBUG("设置核心索引 {}", i);
            npu_resources[i]->model->SetCoreIndex(i);
            
            // 设置默认阈值
            npu_resources[i]->SetConfThreshold(0.4f);
            npu_resources[i]->SetNMSThreshold(0.45f);
            
            LOG_DEBUG("NPU资源 {} 设置完成", i);
        }
        
        LOG_INFO("获取输入尺寸");
        std::tie(out_width, out_height) = npu_resources[0]->model->GetInputSize();
        LOG_INFO("模型输入尺寸: {}x{}", out_width, out_height);
        ASSERT(out_width == out_height);
        
        center_x = static_cast<double>(out_width) / 2.0;
        center_y = static_cast<double>(out_height) / 2.0;
        LOG_INFO("屏幕中心点: ({:.1f}, {:.1f})", center_x, center_y);
        LOG_INFO("模型加载完成");
    } catch (const std::exception& e) {
        LOG_ERROR("加载模型时出错: {}", e.what());
        thread_control::g_thread_running = false;
        // 确保异常能够传递到上层
        LOG_ERROR("由于模型加载异常，程序将强制退出");
        exit(1); // 强制退出程序
    } catch (...) {
        LOG_ERROR("加载模型时出现未知错误");
        thread_control::g_thread_running = false;
        LOG_ERROR("由于未知错误，程序将强制退出");
        exit(1); // 强制退出程序
    }
}

void AimbotFPS::TickFrame(u32 buffer_index) {
    try {
        const u32 core_index = NextNPUCoreIndex();
        
        auto& npu_resource = npu_resources[core_index];
        npu_resource->boxes.clear();
        auto& frame_resource = frame_resources[buffer_index];
        
        PreProcess(frame_resource.get());
        
        video_capture->RequeueBuffer(buffer_index);
        
        npu_resource->model->Detect(npu_resource->boxes, frame_resource->out_data);

        // 使用InferCollector::FindNearestTarget寻找最近的目标（集成锁定部位和开火判断）
        TargetInfo nearest = InferCollector::FindNearestTarget(npu_resource->boxes, out_width, out_height, center_x, center_y);
        
        // 获取当前的帧计数，确保在多线程环境下读取到正确的值
        int current_frame = frame_count;
        
        // 🎯 目标处理逻辑 - 使用集成后的目标信息
        if (nearest.class_id != -1) {
            // ===== 更新全局变量 =====
            // 仅用于日志输出，不影响实际计算
            const double actual_dx = nearest.x - center_x;
            const double actual_dy = nearest.aim_y - center_y;
            
            core_vars::mouse_x = actual_dx;             // 目标中心 - 屏幕中心
            core_vars::mouse_y = actual_dy;             // 调整后的瞄准点 - 屏幕中心
            core_vars::target_distance = nearest.distance;  // 🚀 使用已计算的距离
            core_vars::target_width = nearest.width;
            core_vars::target_height = nearest.height;
            core_vars::target_center_x = nearest.x;
            core_vars::target_center_y = nearest.aim_y;         // 使用调整后的瞄准点Y坐标
            core_vars::screen_center_x = center_x;      // FOV测量屏幕中心X
            core_vars::screen_center_y = center_y;      // FOV测量屏幕中心Y
            
            // ===== 定期日志输出 =====
            // 保存检测结果图像（根据帧率每5秒一次）
            if (current_frame % (frame_rate * 5) == 0) {
                // 声明target_box_index变量，由于我们不再需要寻找特定目标框，设为-1
                s32 target_box_index = -1;
                // 打印目标位置信息
                // LOG_INFO("目标左上角: ({:.1f}, {:.1f}), 右下角: ({:.1f}, {:.1f}), 目标中心点: ({:.1f}, {:.1f})", 
                //         nearest.x1, nearest.y1, nearest.x2, nearest.y2, 
                //         nearest.x, nearest.y);
                // // 打印移动像素、目标宽高、距离和是否在范围内  
                // LOG_INFO("目标移动: x={:.2f} ({}), y={:.2f} ({}), 屏幕中心: ({:.1f}, {:.1f}), 目标中心: ({:.1f}, {:.1f}), 目标宽高: {:.1f}x{:.1f}, 距离: {:.2f}", 
                //         core_vars::mouse_x, 
                //         core_vars::mouse_x > 0 ? "向右" : (core_vars::mouse_x < 0 ? "向左" : "不移动"), 
                //         core_vars::mouse_y, 
                //         core_vars::mouse_y > 0 ? "向下" : (core_vars::mouse_y < 0 ? "向上" : "不移动"), 
                //         center_x, center_y,
                //         nearest.x, nearest.y,
                //         nearest.width, nearest.height, nearest.distance);
            }
        } else {
            // ===== 没有检测到有效目标的处理 =====
            // 初始化键鼠发送的移动像素 move_x move_y
            core_vars::move_x = 0.0;
            core_vars::move_y = 0.0;
            // 原始移动像素-未经过PID处理
            core_vars::mouse_x = 0.0;
            core_vars::mouse_y = 0.0;
            core_vars::target_distance = -1.0;
            core_vars::target_width = 0.0;
            core_vars::target_height = 0.0;
        }
        
        // ===== WSQ流水线推送逻辑 =====
        // 高性能模式：无论是否有目标都生成并推送WSQ命令
        if (wsq_models::g_command_queue) {
            wsq_models::WSQCommand cmd;
            
            // 基础信息
            cmd.frame_id = current_frame;
            cmd.has_target = (nearest.class_id != -1);
            cmd.timestamp = std::chrono::high_resolution_clock::now();
            
            if (cmd.has_target) {
                // 有目标时的处理 - 使用集成后的目标信息
                cmd.target_x = nearest.x;
                cmd.target_y = nearest.y;
                cmd.aim_y = nearest.aim_y;              // 使用已计算的瞄准点Y坐标
                cmd.target_distance = nearest.distance; // 使用已计算的距离
                
                // 移动控制 - 预计算移动量
                double dx = nearest.x - center_x;
                double dy = nearest.aim_y - center_y;
                
                // 根据AI模式预计算移动量
                if (webui::function::g_ai_mode == "FOV" || webui::function::g_ai_mode == "FOV冲锋狙") {
                    cmd.move_x = static_cast<int>(dx / webui::fov::g_fov);
                    cmd.move_y = static_cast<int>((dy / webui::fov::g_fov) * webui::pid::g_y_axis_factor);
                } else {
                    // PID和FOVPID模式的移动量将在WSQ消费者中计算
                    cmd.move_x = static_cast<int>(dx);
                    cmd.move_y = static_cast<int>(dy);
                }
                
                cmd.should_move = (std::abs(dx) > 1 || std::abs(dy) > 1);
                cmd.g_is_fire = nearest.is_fire;  // 使用已计算的开火状态
            } else {
                // 没有目标时的默认值（确保WSQ消费者总是有数据处理）
                cmd.target_x = center_x;
                cmd.target_y = center_y;
                cmd.aim_y = center_y;
                cmd.target_distance = 0;
                cmd.move_x = 0;
                cmd.move_y = 0;
                cmd.should_move = false;
                cmd.g_is_fire = false;  // 没有目标时不开火
            }
            
            // 特殊功能（独立于目标检测）
            cmd.flash_detected = core_vars::g_flash_detected;
            cmd.weapon_switch = false;
            cmd.fov_measure = webui::fov::g_start_measure;
            
            // 注意：直接使用原始字段，不再设置复杂的need_标记
            
            // 高性能推送：使用与npu_work_queue完全相同的机制
            wsq_models::g_command_queue->push(std::move(cmd));
            {
                std::scoped_lock lock(wsq_models::g_wsq_mutex);
                wsq_models::g_wsq_cv.notify_one();
            }
        }
        
        // ===== 数据收集处理逻辑 =====
        // 使用基于文件名时间戳的过滤机制
        if (webui::collect::g_is_enabled) {
            // 使用全局配置的收集名称，而不是硬编码的名称
            std::string collectionName = webui::collect::g_collection_name;
            
            // 如果收集名称为空，则使用游戏名+阵营+用户名的默认格式
            if (collectionName.empty()) {
                std::ostringstream oss;
                oss << webui::home::g_game_name << "_"
                    << webui::collect::g_team_side << "_" 
                    << webui::home::g_username;
                collectionName = oss.str();
                LOG_INFO("使用默认格式生成收集名称: {}", collectionName);
            }
            
            // 地图热键处理
            if (webui::collect::g_map_hotkey_pressed) {
                // 添加地图后缀，但保持基本名称不变
                std::string mapCollectionName = collectionName;
                // 确保名称中不包含重复的map标记
                if (mapCollectionName.find("_map") == std::string::npos) {
                    mapCollectionName += "_map";
                }
                
                bool result = DataCollector::SaveCollectionImage(npu_resource->boxes,
                                        frame_resource->out_data.data(),
                                        out_width,
                                        out_height,
                                        current_frame,
                                        center_x,
                                        center_y,
                                        mapCollectionName);
                
                if (result) {
                    LOG_INFO("地图数据收集请求处理完成");
                    // 收集完成后重置热键状态
                    webui::collect::g_map_hotkey_pressed = false;
                }
            }
            
            // 目标热键处理 - 只有当热键激活且有目标时才收集
            if (webui::collect::g_target_hotkey_pressed) {
                // 仅当检测到有效目标时才收集 - 使用已有的nearest结果
                if (nearest.class_id != -1) {
                    // 添加目标后缀，但保持基本名称不变
                    std::string targetCollectionName = collectionName;
                    // 确保名称中不包含重复的target标记
                    if (targetCollectionName.find("_target") == std::string::npos) {
                        targetCollectionName += "_target";
                    }
                    
                    bool result = DataCollector::SaveCollectionImage(npu_resource->boxes,
                                            frame_resource->out_data.data(),
                                            out_width,
                                            out_height,
                                            current_frame,
                                            center_x,
                                            center_y,
                                            targetCollectionName);
                    
                    if (result) {
                        LOG_INFO("目标数据收集请求处理完成");
                        // 收集完成后重置热键状态
                        webui::collect::g_target_hotkey_pressed = false;
                    }
                } else {
                    LOG_INFO("没有检测到有效目标，跳过目标数据收集");
                    // 即使没有目标，也重置热键状态
                    webui::collect::g_target_hotkey_pressed = false;
                }
            }
        }
    } catch (const std::exception& e) {
        LOG_ERROR("帧处理出错: {}", e.what());
    } catch (...) {
        LOG_ERROR("帧处理出现未知错误");
    }
}

void AimbotFPS::PreProcess(FrameResource* frame_resource) const {
    try {
        // 从输入图像中心裁剪出模型所需的区域
        int crop_x = static_cast<int>((in_width - out_width) / 2);
        int crop_y = static_cast<int>((in_height - out_height) / 2);
        
        // 确保裁剪区域在图像内
        if (crop_x < 0) crop_x = 0;
        if (crop_y < 0) crop_y = 0;
        
        // 如果输入图像小于模型输入尺寸，需要进行缩放而不是裁剪
        if (in_width < out_width || in_height < out_height) {
            LOG_DEBUG("输入图像小于模型输入尺寸，进行缩放");
            int ret = imresize(frame_resource->in_img, frame_resource->out_img);
            if (ret < 0) {
                throw std::runtime_error("imresize failed");
            }
        } else {
            // 直接裁剪中心区域
            im_rect crop_rect = {
                .x = crop_x,
                .y = crop_y,
                .width = static_cast<int>(out_width),
                .height = static_cast<int>(out_height)
            };
            
            int ret = imcrop(frame_resource->in_img, frame_resource->out_img, crop_rect);
            if (ret < 0) {
                throw std::runtime_error("imcrop failed");
            }
        }
    } catch (const std::exception& e) {
        LOG_ERROR("预处理出错: {}", e.what());
        throw; // 重新抛出异常
    }
}

void AimbotFPS::CreateWorkerThreads() {
    npu_threads.resize(npu_count);
    for (int i = 0; i < npu_count; ++i) {
        npu_threads[i] = std::thread([this]() {
            LOG_INFO("NPU工作线程已启动");
            while (thread_control::g_thread_running) {
                // 在尝试获取任务之前检查是否正在重载模型
                if (reloading_model.load()) {
                    LOG_DEBUG("工作线程准备重载，等待信号...");
                    workers_ready_for_reload.fetch_add(1); // 增加准备好重载的工作线程计数

                    // 通知主线程我已经准备好了
                    { std::scoped_lock lock(reload_mutex); reload_cv.notify_one(); }

                    // 等待主线程通知恢复工作
                    std::unique_lock<std::mutex> lock(reload_mutex);
                    reload_cv.wait(lock, [this]() {
                        // 等待 reloading_model 变为 false 或程序停止
                        return !thread_control::g_thread_running || !reloading_model.load();
                    });

                    if (!thread_control::g_thread_running) {
                         LOG_INFO("NPU工作线程收到退出信号");
                         break; // 如果在等待期间收到停止信号，退出循环
                    }
                     LOG_DEBUG("工作线程恢复工作");
                }


                // 原来的等待任务逻辑
                if (npu_work_queue.empty()) {
                    std::unique_lock<std::mutex> lock(npu_work_mutex);
                    npu_work_cv.wait(
                            lock, [this]() {
                                // 在等待任务时，也要检查是否需要停止或重载
                                return !thread_control::g_thread_running || !npu_work_queue.empty() || reloading_model.load();
                            });
                }
                // 再次检查停止标志或重载标志
                if (!thread_control::g_thread_running || reloading_model.load()) {
                     if(!thread_control::g_thread_running) {
                         LOG_INFO("NPU工作线程收到退出信号");
                     } else {
                          LOG_DEBUG("工作线程检测到重载信号，准备暂停");
                     }
                    continue; // 如果停止或重载，跳过获取任务
                }
                const auto buffer_index = npu_work_queue.steal();
                if (!buffer_index) {
                    continue;
                }
                TickFrame(*buffer_index);
            }
            LOG_INFO("NPU工作线程正常退出");
        });
    }
}

}  // namespace aibox::aimbot