#pragma once

#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include "base/fps_counter.h"
#include "base/macros.h"
#include "base/wsq.h"
#include "core/model/yolo/yolo.h"
#include "data_collector.h"
#include "core/aimbot/infer_collector.h"

namespace aibox::video {
class VideoCapture;
}  // namespace aibox::video

namespace aibox::aimbot {

struct FrameResource;

// 完整定义NPUResource结构体，而不是仅做前向声明
struct NPUResource {
    std::shared_ptr<model::Yolo> model;
    std::vector<model::Box> boxes;
    float conf_threshold{0.4f};     // 置信度阈值
    float nms_threshold{0.45f};     // NMS阈值
    int obj_class_num{1};           // 目标类别数量
    
    // 设置阈值
    void SetConfThreshold(float threshold) {
        conf_threshold = threshold;
        if (model) {
            model->SetThreshold(threshold);
        }
    }
    
    // 获取阈值
    float GetConfThreshold() const {
        return conf_threshold;
    }
    
    // 设置NMS阈值
    void SetNMSThreshold(float threshold) {
        nms_threshold = threshold;
        if (model) {
            model->SetNMSThreshold(threshold);
        }
    }
    
    // 获取NMS阈值
    float GetNMSThreshold() const {
        return nms_threshold;
    }
};

using MoveCallback = std::function<void(float x, float y)>;

class AimbotFPS {
public:
    AimbotFPS();

    ~AimbotFPS();

    bool Run();

    void Stop();

    void LoadModel(const std::string& model_path, model::YoloVersion version = model::YoloVersion::YoloV6);

    // 获取NPU资源列表，用于外部设置阈值等参数
    std::vector<std::shared_ptr<NPUResource>>& GetNPUResources() {
        return npu_resources;
    }
    
    // 设置所有NPU资源的置信度阈值
    void SetConfidenceThreshold(float threshold) {
        for (auto& resource : npu_resources) {
            if (resource) {
                resource->SetConfThreshold(threshold);
            }
        }
    }
    
    // 设置所有NPU资源的NMS阈值
    void SetNMSThreshold(float threshold) {
        for (auto& resource : npu_resources) {
            if (resource) {
                resource->SetNMSThreshold(threshold);
            }
        }
    }
    
    // 设置所有NPU资源的类别数量
    void SetClassNumber(int num) {
        for (auto& resource : npu_resources) {
            if (resource) {
                resource->obj_class_num = num;
            }
        }
    }
    
    // 启用或禁用可视化
    void EnableVisualization(bool enable) {
        visualization_enabled = enable;
    }

private:
    u32 npu_count{3};
    base::FPSCounter counter;
    std::unique_ptr<video::VideoCapture> video_capture;
    std::vector<std::shared_ptr<NPUResource>> npu_resources;
    u32 in_width{}, in_height{};
    u32 out_width{640}, out_height{640};
    u32 frame_rate{60}; // 存储视频帧率
    double center_x{}, center_y{};
    std::vector<std::shared_ptr<FrameResource>> frame_resources;
    std::atomic<u32> next_npu_core{};
    bool visualization_enabled{true}; // 默认启用可视化
    std::atomic<int> frame_count{0}; // 帧计数器（用于周期性输出和保存），使用原子类型确保线程安全
    
    std::mutex npu_work_mutex;
    std::condition_variable npu_work_cv;
    std::vector<std::thread> npu_threads;
    WorkStealingQueue<u32> npu_work_queue;

    std::atomic<bool> reloading_model{false}; // 通知工作线程重载模型
    std::atomic<int> workers_ready_for_reload{0}; // 计数器，记录有多少工作线程已暂停并准备好重载
    std::mutex reload_mutex; // 用于保护 reload_cv
    std::condition_variable reload_cv; // 用于主线程等待工作线程准备好，以及通知工作线程恢复

    ALWAYS_INLINE u32 NextNPUCoreIndex() {
        return next_npu_core.fetch_add(1, std::memory_order_relaxed) % npu_resources.size();
    }

    void CreateWorkerThreads();

    void SetupVideoInput();

    void TickFrame(u32 buffer_index);

    void PreProcess(FrameResource* frame_resource) const;

    static std::string StrPixelFormat(u32 pixel_format);
};

}  // namespace aibox::aimbot