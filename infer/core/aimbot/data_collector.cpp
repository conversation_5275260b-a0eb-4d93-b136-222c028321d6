#include "data_collector.h"
#include "utils/gmodels.h"
#include "utils/logger.h"
#include <chrono>
#include <cstring>
#include <ctime>
#include <filesystem>
#include <iomanip>
#include <sstream>
#include <opencv2/opencv.hpp>
#include <fstream>
#include <regex>

// 静态成员变量定义
namespace aibox::aimbot {
    std::unordered_map<std::string, std::chrono::time_point<std::chrono::system_clock>> DataCollector::last_save_times;
}

// 静态初始化：确保基础目录存在
namespace {
    bool EnsureDirectories() {
        try {
            // 创建保存图像的基础目录
            std::filesystem::create_directories("detected_images");
            std::filesystem::create_directories("imgs");
            
            LOG_INFO("数据收集目录已初始化: detected_images, imgs");
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("初始化数据收集目录失败: {}", e.what());
            return false;
        }
    }
    
    // 静态变量，确保目录在程序启动时创建
    static bool dirs_created = EnsureDirectories();
}

namespace aibox::aimbot {

bool DataCollector::ShouldSaveImage(const std::string& filename, const std::string& type) {
    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    
    // 检查是否存在上次保存的时间戳
    if (last_save_times.find(type) != last_save_times.end()) {
        // 计算与上次保存的时间差（秒）
        auto duration = std::chrono::duration_cast<std::chrono::duration<double>>(
            now - last_save_times[type]).count();
        
        // 如果时间差小于最小间隔，则跳过保存
        if (duration < MIN_SAVE_INTERVAL) {
            LOG_INFO("跳过保存 - 时间间隔太短 ({:.2f}秒 < {:.2f}秒): {}", 
                    duration, MIN_SAVE_INTERVAL, filename);
            return false;
        }
    }
    
    // 更新上次保存时间
    last_save_times[type] = now;
    return true;
}

bool DataCollector::SaveCollectionImage(const std::vector<model::Box>& boxes, 
                                      const u8* img_data, 
                                      u32 img_width,
                                      u32 img_height,
                                      int frame_number,
                                      double center_x,
                                      double center_y,
                                      const std::string& collectionName) {
    try {
        // 详细记录数据收集状态
        LOG_INFO("数据收集状态检查: g_is_enabled={}", webui::collect::g_is_enabled);
        
        // 仅检查是否启用了数据收集模式
        if (!webui::collect::g_is_enabled) {
            LOG_WARNING("数据收集模式未启用");
            return false;
        }
        
        // 创建OpenCV图像用于保存
        cv::Mat image(img_height, img_width, CV_8UC3);
        std::memcpy(image.data, img_data, img_width * img_height * 3);
        
        // RGB转BGR (OpenCV默认使用BGR)
        cv::cvtColor(image, image, cv::COLOR_RGB2BGR);
        
        // 获取数据收集类型（通过调用位置传入的参数判断，不再依赖热键状态）
        // 由于我们无法确定调用者的意图，先假设通过collectionName中是否包含"map"来判断
        bool is_map_collection = collectionName.find("map") != std::string::npos || 
                                 webui::collect::g_map_hotkey_pressed;
        std::string collectType = is_map_collection ? "地图" : "目标";
        
        // 记录数据收集请求信息
        std::string finalCollectionName = collectionName;
        
        // 首先检查全局配置中的收集名称
        if (finalCollectionName.empty() && !webui::collect::g_collection_name.empty()) {
            finalCollectionName = webui::collect::g_collection_name;
            LOG_INFO("数据收集[{}] - 使用全局配置的采集名称: {}", collectType, finalCollectionName);
        }
        
        // 如果仍然为空，则根据游戏名、阵营和用户名自动生成
        if (finalCollectionName.empty()) {
            // 使用新格式: 游戏名_阵营_用户名
            std::ostringstream oss;
            oss << webui::home::g_game_name << "_"
                << webui::collect::g_team_side << "_" 
                << webui::home::g_username;
            
            // 根据热键类型添加后缀
            if (is_map_collection) {
                oss << "_map";
            } else {
                oss << "_target";
            }
            
            finalCollectionName = oss.str();
            LOG_INFO("数据收集[{}] - 处理采集请求，使用自动生成的采集名称: {}", collectType, finalCollectionName);
        } else {
            LOG_INFO("数据收集[{}] - 处理采集请求，采集名称：{}", collectType, finalCollectionName);
        }
        
        // 准备保存路径 - 使用绝对路径确保路径正确
        std::string save_path = PrepareCollectionPath(is_map_collection, finalCollectionName);
        if (save_path.empty()) {
            LOG_ERROR("创建数据收集目录失败");
            return false;
        }
        
        // 确保目录存在
        if (!std::filesystem::exists(save_path)) {
            LOG_ERROR("保存路径不存在: {}", save_path);
            return false;
        }
        
        // 生成文件名（不带路径和扩展名）
        std::string filename_base = GenerateFileName("img", "");
        // 去掉末尾的点
        if (filename_base.back() == '.') {
            filename_base.pop_back();
        }
        
        // 使用新的时间戳过滤机制检查是否应该保存该图像
        if (!ShouldSaveImage(filename_base, collectType)) {
            // 不满足保存时间间隔要求，跳过此次保存
            LOG_INFO("基于时间间隔控制，跳过当前图像保存");
            return true; // 返回true，因为这是预期的行为而非错误
        }
        
        // 构建完整的图像和标签路径
        std::string image_filename = save_path + "/images/" + filename_base + ".jpg";
        std::string label_filename = save_path + "/labels/" + filename_base + ".txt";
        
        // 保存图像
        LOG_INFO("正在保存数据收集图像: {}", image_filename);
        bool saved = cv::imwrite(image_filename, image);
        
        if (!saved) {
            LOG_ERROR("保存图像失败: {}", image_filename);
            return false;
        }
        
        // 生成YOLO格式标签文件
        bool label_saved = GenerateYoloLabel(boxes, label_filename, img_width, img_height);
        if (!label_saved) {
            LOG_ERROR("保存标签文件失败: {}", label_filename);
            return false;
        }
        
        LOG_INFO("已成功保存数据收集图像: {}", image_filename);
        LOG_INFO("已成功保存YOLO标签: {}", label_filename);
        return true;
    } catch (const cv::Exception& e) {
        LOG_ERROR("OpenCV错误: {}", e.what());
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR("保存数据收集图像时出错: {}", e.what());
        return false;
    } catch (...) {
        LOG_ERROR("保存数据收集图像时出现未知错误");
        return false;
    }
}

bool DataCollector::SaveDebugImage(const std::vector<model::Box>& boxes, 
                                 const u8* img_data, 
                                 u32 img_width,
                                 u32 img_height,
                                 s32 target_box_index,
                                 int frame_number,
                                 double center_x,
                                 double center_y) {
    try {
        // 确保detected_images目录存在
        std::filesystem::path debug_dir("detected_images");
        if (!std::filesystem::exists(debug_dir)) {
            LOG_INFO("调试图像目录不存在，创建目录: {}", debug_dir.string());
            std::filesystem::create_directories(debug_dir);
        }
        
        // 创建OpenCV图像用于保存
        cv::Mat image(img_height, img_width, CV_8UC3);
        std::memcpy(image.data, img_data, img_width * img_height * 3);
        
        // RGB转BGR (OpenCV默认使用BGR)
        cv::cvtColor(image, image, cv::COLOR_RGB2BGR);
        
        // 绘制所有检测到的边界框
        for (size_t i = 0; i < boxes.size(); i++) {
            const auto& box = boxes[i];
            bool is_target = (static_cast<s32>(i) == target_box_index);
            
            // 目标边界框使用红色，其他边界框使用绿色
            cv::Scalar color = is_target ? cv::Scalar(0, 0, 255) : cv::Scalar(0, 255, 0);
            int thickness = is_target ? 2 : 1;
            
            // 绘制边界框
            cv::rectangle(image, 
                         cv::Point(static_cast<int>(box.x1), static_cast<int>(box.y1)),
                         cv::Point(static_cast<int>(box.x2), static_cast<int>(box.y2)),
                         color, thickness);
            
            // 绘制置信度分数
            std::string label = "Score: " + std::to_string(box.score).substr(0, 4);
            cv::putText(image, label, 
                       cv::Point(static_cast<int>(box.x1), static_cast<int>(box.y1 - 5)),
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, color, 1);
            
            // 计算目标中心点
            double box_center_x = (box.x1 + box.x2) / 2.0;
            double box_center_y = (box.y1 + box.y2) / 2.0;
            
            // 绘制从屏幕中心到目标中心的直线(蓝色)
            cv::line(image, 
                    cv::Point(static_cast<int>(center_x), static_cast<int>(center_y)),
                    cv::Point(static_cast<int>(box_center_x), static_cast<int>(box_center_y)),
                    cv::Scalar(255, 0, 0), 2);
            
            // 为目标中心点绘制小圆圈(黄色)
            cv::circle(image, cv::Point(static_cast<int>(box_center_x), static_cast<int>(box_center_y)), 
                      3, cv::Scalar(0, 255, 255), -1);
            
            // 对于目标边界框，绘制中心点
            if (is_target || i == 0) {  // 如果是指定的目标或第一个检测到的目标
                // 计算锁定点位置（根据锁定部位设置）
                double target_x = (box.x1 + box.x2) / 2.0;  // 目标水平中心
                double target_y = box.y1 + (box.y2 - box.y1) * 0.15;  // 默认瞄准点（身体上部）
                
                // 根据锁定部位设置确定瞄准点
                double h = box.y2 - box.y1;  // 目标高度
                if (webui::function::g_lock_position == "头部") {
                    // 从顶部往下的百分比
                    target_y = box.y1 + h * (webui::aim::g_head_height / 100.0);
                } else if (webui::function::g_lock_position == "颈部") {
                    // 从顶部往下的百分比
                    target_y = box.y1 + h * (webui::aim::g_neck_height / 100.0);
                } else if (webui::function::g_lock_position == "胸部") {
                    // 从顶部往下的百分比
                    target_y = box.y1 + h * (webui::aim::g_chest_height / 100.0);
                }
                
                // 绘制十字准星（洋红色）
                int cross_size = 10;
                cv::line(image, 
                        cv::Point(static_cast<int>(target_x - cross_size), static_cast<int>(target_y)),
                        cv::Point(static_cast<int>(target_x + cross_size), static_cast<int>(target_y)),
                        cv::Scalar(255, 0, 255), 2);
                cv::line(image, 
                        cv::Point(static_cast<int>(target_x), static_cast<int>(target_y - cross_size)),
                        cv::Point(static_cast<int>(target_x), static_cast<int>(target_y + cross_size)),
                        cv::Scalar(255, 0, 255), 2);
                
                // 绘制从屏幕中心到瞄准点的直线(红色)
                cv::line(image, 
                        cv::Point(static_cast<int>(center_x), static_cast<int>(center_y)),
                        cv::Point(static_cast<int>(target_x), static_cast<int>(target_y)),
                        cv::Scalar(0, 0, 255), 1);
                
                // 添加锁定部位标签
                std::string pos_label = webui::function::g_lock_position;
                cv::putText(image, pos_label, 
                          cv::Point(static_cast<int>(target_x + 15), static_cast<int>(target_y)),
                          cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 0, 255), 2);
            }
        }
        
        // 绘制中心点(白色)
        cv::circle(image, cv::Point(static_cast<int>(center_x), static_cast<int>(center_y)), 3, cv::Scalar(255, 255, 255), -1);
        
        // 添加坐标信息文本
        std::string coord_info = "Center: (" + std::to_string(static_cast<int>(center_x)) + 
                               ", " + std::to_string(static_cast<int>(center_y)) + ")";
        cv::putText(image, coord_info, cv::Point(10, 60), cv::FONT_HERSHEY_SIMPLEX, 0.6, 
                   cv::Scalar(255, 255, 255), 1);
        
        // 添加FPS信息
        std::string fps_info = "FPS: " + std::to_string(webui::header::frame_rate).substr(0, 5);
        cv::putText(image, fps_info, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 255, 255), 2);
        
        // 添加锁定部位信息
        std::string lock_info = "Lock: " + webui::function::g_lock_position;
        cv::putText(image, lock_info, cv::Point(10, 90), cv::FONT_HERSHEY_SIMPLEX, 0.6, 
                   cv::Scalar(255, 0, 255), 1);
        
        // 保存图像到文件
        std::string filename = "detected_images/detection_" + std::to_string(frame_number) + ".jpg";
        LOG_INFO("正在保存检测结果图像: {}", filename);
        
        bool saved = cv::imwrite(filename, image);
        if (!saved) {
            LOG_ERROR("保存检测结果图像失败: {}", filename);
            return false;
        }
        
        LOG_INFO("已成功保存检测结果图像: {}", filename);
        
        // 记录检测到的目标数量
        LOG_INFO("检测到 {} 个目标，目标索引: {}", boxes.size(), target_box_index);
        
        if (target_box_index >= 0 && target_box_index < boxes.size()) {
            const auto& box = boxes[target_box_index];
            LOG_DEBUG("目标位置: ({:.1f}, {:.1f}, {:.1f}, {:.1f}), 置信度: {:.4f}", 
                     box.x1, box.y1, box.x2, box.y2, box.score);
        }
        
        return true;
    } catch (const cv::Exception& e) {
        LOG_ERROR("OpenCV错误: {}", e.what());
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR("保存检测结果图像时出错: {}", e.what());
        return false;
    } catch (...) {
        LOG_ERROR("保存检测结果图像时出现未知错误");
        return false;
    }
}

std::string DataCollector::GenerateFileName(const std::string& prefix, const std::string& extension) {
    // 获取当前时间点
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
    
    // 格式化时间字符串
    std::tm now_tm;
    localtime_r(&now_time_t, &now_tm);
    
    std::ostringstream oss;
    oss << prefix << "_"
        << std::put_time(&now_tm, "%Y%m%d_%H%M%S") 
        << "_" << std::setfill('0') << std::setw(3) << now_ms.count()
        << "." << extension;
    
    return oss.str();
}

std::string DataCollector::PrepareCollectionPath(bool is_map, const std::string& collectionName) {
    try {
        // 数据收集模式 - 保存到数据收集目录结构
        std::string basePath = "./imgs";
        
        // 确保基础路径存在
        if (!std::filesystem::exists(basePath)) {
            LOG_INFO("基础路径不存在，创建目录: {}", basePath);
            std::filesystem::create_directories(basePath);
        }
        
        // 获取收集名称
        std::string collectionFolder;
        
        // 首先检查是否有传入的采集名称
        if (!collectionName.empty()) {
            collectionFolder = collectionName;
            LOG_INFO("使用传入的收集名称: {}", collectionFolder);
        }
        // 然后检查全局配置中的收集名称
        else if (!webui::collect::g_collection_name.empty()) {
            collectionFolder = webui::collect::g_collection_name;
            LOG_INFO("使用全局配置的收集名称: {}", collectionFolder);
        }
        // 最后才自动生成
        else {
            // 使用新格式: 游戏名_阵营_用户名
            std::ostringstream oss;
            oss << webui::home::g_game_name << "_"
                << webui::collect::g_team_side << "_" 
                << webui::home::g_username;
            
            // 根据热键类型添加后缀
            if (is_map) {
                oss << "_map";
            } else {
                oss << "_target";
            }
            
            collectionFolder = oss.str();
            LOG_INFO("使用自动生成的收集名称: {}", collectionFolder);
        }
        
        // 构建收集文件夹路径
        std::string collectionPath = basePath + "/" + collectionFolder;
        
        // 确保收集文件夹存在
        if (!std::filesystem::exists(collectionPath)) {
            LOG_INFO("收集文件夹不存在，创建目录: {}", collectionPath);
            std::filesystem::create_directories(collectionPath);
        }
        
        // 确定子文件夹类型 - 地图或目标
        std::string dataType = is_map ? "maps" : "targets";
        
        // 构建完整路径: imgs/收集名称/maps或targets
        std::string save_path = collectionPath + "/" + dataType;
        
        // 确保数据类型子文件夹存在
        if (!std::filesystem::exists(save_path)) {
            LOG_INFO("数据类型文件夹不存在，创建目录: {}", save_path);
            std::filesystem::create_directories(save_path);
        }
        
        // 创建images和labels子目录
        std::string images_path = save_path + "/images";
        std::string labels_path = save_path + "/labels";
        
        if (!std::filesystem::exists(images_path)) {
            LOG_INFO("创建图像目录: {}", images_path);
            std::filesystem::create_directories(images_path);
        }
        
        if (!std::filesystem::exists(labels_path)) {
            LOG_INFO("创建标签目录: {}", labels_path);
            std::filesystem::create_directories(labels_path);
        }
        
        LOG_INFO("准备好的数据收集路径: {}", save_path);
        return save_path;
    } catch (const std::exception& e) {
        LOG_ERROR("准备数据收集路径时出错: {}", e.what());
        return "";
    }
}

// 新增函数：将检测框转换为YOLO格式标签数据
bool DataCollector::GenerateYoloLabel(const std::vector<model::Box>& boxes, 
                                     const std::string& label_path,
                                     u32 img_width,
                                     u32 img_height) {
    try {
        // 创建标签文件
        std::ofstream label_file(label_path);
        if (!label_file.is_open()) {
            LOG_ERROR("无法创建标签文件: {}", label_path);
            return false;
        }

        // 如果没有检测到目标，创建空标签文件
        if (boxes.empty()) {
            LOG_INFO("未检测到目标，创建空标签文件: {}", label_path);
            label_file.close();
            return true;
        }

        // 遍历所有检测框，将其转换为YOLO格式
        for (const auto& box : boxes) {
            // YOLO格式：class_id center_x center_y width height
            // 所有值都需要归一化到0-1之间

            // 计算中心点坐标和宽高
            double center_x = (box.x1 + box.x2) / 2.0 / img_width;
            double center_y = (box.y1 + box.y2) / 2.0 / img_height;
            double width = (box.x2 - box.x1) / img_width;
            double height = (box.y2 - box.y1) / img_height;

            // 写入YOLO格式标签
            label_file << box.class_id << " " 
                      << center_x << " " 
                      << center_y << " " 
                      << width << " " 
                      << height << std::endl;
        }

        label_file.close();
        LOG_INFO("成功生成YOLO格式标签: {}", label_path);
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("生成YOLO标签时出错: {}", e.what());
        return false;
    }
}

} // namespace aibox::aimbot 