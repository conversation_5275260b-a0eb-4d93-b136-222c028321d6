#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <linux/videodev2.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include "base/alignment.h"
#include "utils/logger.h"
#include "core/video/edid.h"
#include "core/video/video_capture.h"

namespace aibox::video {

std::string VideoCapture::StrPixelFormat(u32 pixel_format) {
    std::array<char, 5> fourcc{};
    strncpy(fourcc.data(), reinterpret_cast<char*>(&pixel_format), 4);
    return fourcc.data();
}

VideoCapture::VideoCapture() = default;

VideoCapture::~VideoCapture() { Close(); }

void VideoCapture::Open(s32 video_device_id) {
    if (video_device_id >= 0) {
        fd = open(fmt::format("/dev/video{}", video_device_id).c_str(), O_RDWR);
    } else {
        DIR* dir = opendir("/dev");
        if (dir == nullptr) {
            throw std::runtime_error("Could not open /dev");
        }
        struct dirent* entry;
        while ((entry = readdir(dir)) != nullptr) {
            if (strncmp(entry->d_name, "video", 5) == 0 && isdigit(entry->d_name[5])) {
                fd = open(fmt::format("/dev/{}", entry->d_name).c_str(), O_RDWR);
                if (fd >= 0) {
                    break;
                }
            }
        }
        closedir(dir);
    }
    if (fd < 0) {
        throw std::runtime_error("Could not open video device");
    }
    Setup();
}

void VideoCapture::Setup() {
    v4l2_capability capability{};
    if (ioctl(fd, VIDIOC_QUERYCAP, &capability) < 0) {
        throw std::runtime_error("Could not query video device capabilities");
    }
    if ((capability.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) == 0) {
        throw std::runtime_error("Video device does not support multi-planar API");
    }
    if ((capability.capabilities & V4L2_CAP_STREAMING) == 0) {
        throw std::runtime_error("Video device does not support streaming");
    }
    v4l2_format get_format_command{
            .type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE,
    };
    if (ioctl(fd, VIDIOC_G_FMT, &get_format_command) < 0) {
        throw std::runtime_error("Could not query video device format");
    }
    if (get_format_command.fmt.pix_mp.pixelformat != V4L2_PIX_FMT_BGR24) {
        // 打印中文
        LOG_INFO("当前像素格式不是BGR24 (got {}), 写入EDID...",
                 StrPixelFormat(get_format_command.fmt.pix_mp.pixelformat));
        // 60Hz的EDID配置
        // WriteEDID(EDID_FORCE_GBR24_60HZ, sizeof(EDID_FORCE_GBR24_60HZ));
        // 使用240Hz的EDID配置
        // WriteEDID(EDID_FORCE_GBR24_240HZ, sizeof(EDID_FORCE_GBR24_240HZ));
        // 使用120Hz的EDID配置
        WriteEDID(EDID_FORCE_RGB32_120HZ, sizeof(EDID_FORCE_RGB32_120HZ));
    }
    if (ioctl(fd, VIDIOC_G_FMT, &get_format_command) < 0) {
        throw std::runtime_error("Could not query video device format");
    }
    if (get_format_command.fmt.pix_mp.pixelformat != V4L2_PIX_FMT_BGR24) {
        throw std::runtime_error(
                fmt::format("Video device does not support RGB24 format (got {})",
                            StrPixelFormat(get_format_command.fmt.pix_mp.pixelformat)));
    }
    format = get_format_command.fmt.pix_mp.pixelformat;
    
    // 获取基本宽高信息（如果下面的HDMI状态检测失败，这些将作为默认值）
    width = get_format_command.fmt.pix_mp.width;
    height = get_format_command.fmt.pix_mp.height;
    
    // 获取HDMI状态信息（这会更新width, height, frame_rate等参数）
    GetHDMIStatus();
    
    // 如果HDMI状态检测失败，则使用常规方法获取帧率
    if (!is_hdmi_input) {
        GetFrameRateInfo();
    }
    
    if (is_hdmi_input) {
        LOG_INFO("Video device format: {}x{} {}, 帧率: {}Hz (HDMI输入已连接)",
                width,
                height,
                StrPixelFormat(format),
                frame_rate);
    } else {
        LOG_INFO("Video device format: {}x{} {}, 帧率: {}Hz",
                width,
                height,
                StrPixelFormat(format),
                frame_rate);
    }
    MapBuffers();
}

// 新增获取HDMI输入帧率的方法
void VideoCapture::GetFrameRateInfo() {
    // 首先尝试获取数字视频时序信息（适用于HDMI输入）
    struct v4l2_dv_timings timings;
    memset(&timings, 0, sizeof(timings));
    
    if (ioctl(fd, VIDIOC_G_DV_TIMINGS, &timings) >= 0) {
        // 成功获取DV时序信息
        // 从pixelclock和总行数/列数计算刷新率
        uint64_t pixel_clock = timings.bt.pixelclock; // 像素时钟（Hz）
        uint32_t htotal = timings.bt.width; // 总水平分辨率
        uint32_t vtotal = timings.bt.height; // 总垂直分辨率
        
        if (pixel_clock > 0 && htotal > 0 && vtotal > 0) {
            // 刷新率 = 像素时钟 / (总水平分辨率 * 总垂直分辨率)
            frame_rate = pixel_clock / (htotal * vtotal);
            LOG_INFO("获取到HDMI输入刷新率: {}Hz (通过DV时序)", frame_rate);
            return;
        }
    }
    
    // 如果DV_TIMINGS方法失败，尝试使用VIDIOC_G_PARM方法
    v4l2_streamparm parm{};
    parm.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    
    if (ioctl(fd, VIDIOC_G_PARM, &parm) < 0) {
        LOG_WARNING("无法获取视频设备帧率，使用默认值 {}Hz", frame_rate);
        return;
    }
    
    // 检查是否支持帧率设置
    if (!(parm.parm.capture.capability & V4L2_CAP_TIMEPERFRAME)) {
        LOG_WARNING("视频设备不支持帧率查询，使用默认值 {}Hz", frame_rate);
        return;
    }
    
    // 计算帧率 = 分子 / 分母
    if (parm.parm.capture.timeperframe.denominator > 0) {
        frame_rate = parm.parm.capture.timeperframe.denominator / 
                    parm.parm.capture.timeperframe.numerator;
        LOG_INFO("获取到设备帧率: {}Hz (通过VIDIOC_G_PARM)", frame_rate);
    } else {
        LOG_WARNING("无效的帧率值，使用默认值 {}Hz", frame_rate);
    }
}

void VideoCapture::MapBuffers() {
    v4l2_requestbuffers request_buffers{
            .count = buffer_count,
            .type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE,
            .memory = V4L2_MEMORY_MMAP,
    };
    if (ioctl(fd, VIDIOC_REQBUFS, &request_buffers) < 0) {
        throw std::runtime_error("Could not request buffers");
    }
    if (request_buffers.count < buffer_count) {
        throw std::runtime_error("Could not allocate enough buffers");
    }
    buffers.resize(request_buffers.count);
    for (u32 i = 0; i < request_buffers.count; ++i) {
        v4l2_plane planes[VIDEO_MAX_PLANES]{};
        v4l2_buffer buffer{
                .index = i,
                .type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE,
                .memory = V4L2_MEMORY_MMAP,
                .length = VIDEO_MAX_PLANES,
        };
        buffer.m.planes = planes;

        if (ioctl(fd, VIDIOC_QUERYBUF, &buffer) < 0) {
            throw std::runtime_error("Could not query buffer");
        }
        buffers[i].index = i;
        buffers[i].length = buffer.m.planes[0].length;
        buffers[i].buffer = static_cast<u8*>(mmap(nullptr,
                                                  buffer.m.planes[0].length,
                                                  PROT_READ | PROT_WRITE,
                                                  MAP_SHARED,
                                                  fd,
                                                  buffer.m.planes[0].m.mem_offset));
        if (buffers[i].buffer == MAP_FAILED) {
            throw std::runtime_error("Could not map buffer");
        }
    }
}

void VideoCapture::QueueBuffers() const {
    for (u32 i = 0; i < buffer_count; ++i) {
        v4l2_plane planes[VIDEO_MAX_PLANES]{};
        v4l2_buffer buffer{
                .index = i,
                .type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE,
                .memory = V4L2_MEMORY_MMAP,
                .length = VIDEO_MAX_PLANES,
        };
        buffer.m.planes = planes;
        if (ioctl(fd, VIDIOC_QBUF, &buffer) < 0) {
            throw std::runtime_error("Could not queue buffer");
        }
    }
}

u32 VideoCapture::DequeueBuffer() const {
    v4l2_plane planes[VIDEO_MAX_PLANES]{};
    v4l2_buffer buffer{
            .type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE,
            .memory = V4L2_MEMORY_MMAP,
            .length = VIDEO_MAX_PLANES,
    };
    buffer.m.planes = planes;
    if (ioctl(fd, VIDIOC_DQBUF, &buffer) < 0) {
        throw std::runtime_error("Could not dequeue buffer");
    }
    return buffer.index;
}

void VideoCapture::RequeueBuffer(u32 index) const {
    v4l2_plane planes[VIDEO_MAX_PLANES]{};
    v4l2_buffer buffer{
            .index = index,
            .type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE,
            .memory = V4L2_MEMORY_MMAP,
            .length = VIDEO_MAX_PLANES,
    };
    buffer.m.planes = planes;
    if (ioctl(fd, VIDIOC_QBUF, &buffer) < 0) {
        throw std::runtime_error("Could not queue buffer");
    }
}

void VideoCapture::StartStreaming() const {
    QueueBuffers();
    u32 type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    if (ioctl(fd, VIDIOC_STREAMON, &type) < 0) {
        throw std::runtime_error("Could not start streaming");
    }
}

void VideoCapture::StopStreaming() const {
    u32 type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    if (ioctl(fd, VIDIOC_STREAMOFF, &type) < 0) {
        throw std::runtime_error("Could not stop streaming");
    }
}
void VideoCapture::WriteEDID(const u8* data, u32 len) const {
    const u32 blocks = base::DivUp(len, EDID_BLOCK_SIZE);
    std::vector<u8> aligned_data(EDID_BLOCK_SIZE * blocks);
    memcpy(aligned_data.data(), data, len);
    v4l2_edid edid{.start_block = 0, .blocks = blocks, .edid = aligned_data.data()};
    if (ioctl(fd, VIDIOC_S_EDID, &edid) < 0) {
        throw std::runtime_error("Could not set EDID");
    }
}

void VideoCapture::Close() {
    if (fd >= 0) {
        for (auto& buffer : buffers) {
            munmap(buffer.buffer, buffer.length);
        }
        close(fd);
        fd = -1;
    }
}

// 获取详细的HDMI状态信息
void VideoCapture::GetHDMIStatus() {
    // 尝试获取DV时序来判断是否为HDMI输入
    struct v4l2_dv_timings timings;
    memset(&timings, 0, sizeof(timings));
    
    // 默认设置
    is_hdmi_input = false;
    is_connected = false;
    
    // 尝试获取DV时序信息
    if (ioctl(fd, VIDIOC_G_DV_TIMINGS, &timings) >= 0) {
        // 成功获取，说明是HDMI输入并且已连接
        is_hdmi_input = true;
        is_connected = true;
        
        // 记录分辨率信息（有效宽高）
        width = timings.bt.width;
        height = timings.bt.height;
        
        // 计算刷新率
        // 刷新率 = 像素时钟 / (总水平分辨率 * 总垂直分辨率)
        if (timings.bt.pixelclock > 0 && timings.bt.width > 0 && timings.bt.height > 0) {
            frame_rate = timings.bt.pixelclock / 
                         (timings.bt.width * timings.bt.height);
        }
        
        LOG_INFO("HDMI输入连接正常，分辨率: {}x{}, 刷新率: {}Hz", 
                 width, height, frame_rate);
        
        // 输出更多详细信息
        LOG_INFO("HDMI时序: 水平前沿: {}, 水平同步: {}, 水平后沿: {}, 垂直前沿: {}, 垂直同步: {}, 垂直后沿: {}",
                timings.bt.hfrontporch, timings.bt.hsync, timings.bt.hbackporch,
                timings.bt.vfrontporch, timings.bt.vsync, timings.bt.vbackporch);
    } else {
        LOG_WARNING("未检测到HDMI连接或设备不支持DV时序查询");
    }
    
    // 尝试获取色彩空间信息
    struct v4l2_format fmt;
    memset(&fmt, 0, sizeof(fmt));
    fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    
    if (ioctl(fd, VIDIOC_G_FMT, &fmt) >= 0) {
        colorspace = fmt.fmt.pix_mp.colorspace;
        
        // 输出色彩空间信息
        std::string colorspace_name;
        switch (colorspace) {
            case V4L2_COLORSPACE_SRGB: colorspace_name = "sRGB"; break;
            case V4L2_COLORSPACE_REC709: colorspace_name = "Rec.709"; break;
            case V4L2_COLORSPACE_BT2020: colorspace_name = "Rec.2020"; break;
            case V4L2_COLORSPACE_DCI_P3: colorspace_name = "DCI-P3"; break;
            case V4L2_COLORSPACE_SMPTE170M: colorspace_name = "SMPTE 170M"; break;
            case V4L2_COLORSPACE_SMPTE240M: colorspace_name = "SMPTE 240M"; break;
            default: colorspace_name = "未知"; break;
        }
        
        LOG_INFO("色彩空间: {} (0x{:x})", colorspace_name, colorspace);
    }
}

}  // namespace aibox::video