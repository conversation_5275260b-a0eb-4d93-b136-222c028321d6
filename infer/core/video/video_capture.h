#pragma once

#include <vector>
#include "base/types.h"

namespace aibox::video {

struct VideoBuffer {
    u32 index;
    u8* buffer;
    u32 length;
};

class VideoCapture {
public:
    VideoCapture();

    ~VideoCapture();

    void Open(s32 video_device_id = -1);

    void Close();

    u32 DequeueBuffer() const;

    void RequeueBuffer(u32 index) const;

    void StartStreaming() const;

    void StopStreaming() const;

    [[nodiscard]] u32 GetFormat() const { return format; }

    [[nodiscard]] u32 GetWidth() const { return width; }

    [[nodiscard]] u32 GetHeight() const { return height; }

    [[nodiscard]] u32 GetFrameRate() const { return frame_rate; }

    // 返回是否是HDMI输入
    [[nodiscard]] bool IsHDMIInput() const { return is_hdmi_input; }

    // 获取连接状态
    [[nodiscard]] bool IsConnected() const { return is_connected; }

    // 获取色彩空间
    [[nodiscard]] u32 GetColorSpace() const { return colorspace; }

    [[nodiscard]] const std::vector<VideoBuffer>& GetBuffers() const { return buffers; }

private:
    u32 buffer_count{3};
    int fd{-1};
    u32 format{};
    u32 width{}, height{};
    u32 frame_rate{60}; // 默认帧率为60fps
    bool is_hdmi_input{false}; // 是否是HDMI输入
    bool is_connected{false}; // 是否有设备连接
    u32 colorspace{0}; // 色彩空间
    std::vector<VideoBuffer> buffers;

    static std::string StrPixelFormat(u32 pixel_format);

    void WriteEDID(const u8* data, u32 len) const;

    void Setup();

    void MapBuffers();

    void QueueBuffers() const;
    
    // 获取HDMI输入的帧率信息
    void GetFrameRateInfo();
    
    // 获取详细的HDMI状态信息
    void GetHDMIStatus();
};

}  // namespace aibox::video