add_library(core STATIC
        core.cpp
        core.h
        model/model.cpp
        model/model.h
        model/rknn/model_rknn.cpp
        model/rknn/model_rknn.h
        model/yolo/yolo_v6.cpp
        model/yolo/yolo_v6.h
        model/yolo/yolo_v8.cpp
        model/yolo/yolo_v8.h
        model/object_detector.cpp
        model/object_detector.h
        model/yolo/yolo.cpp
        model/yolo/yolo.h
        video/video_capture.cpp
        video/video_capture.h
        video/edid.h
        aimbot/aimbot_fps.cpp
        aimbot/aimbot_fps.h
        aimbot/data_collector.cpp
        aimbot/data_collector.h
        aimbot/aimbot_debug.cpp
        aimbot/aimbot_debug.h)

target_link_libraries(core base rknn rga CImg -lpthread)