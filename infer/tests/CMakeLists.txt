add_executable(tests
        external/librga.cpp
        core/video_capture.cpp)

# 配置Catch2
set(CATCH2_DIR ${CMAKE_SOURCE_DIR}/external/Catch2)
include_directories(
    ${CATCH2_DIR}/src
    ${CMAKE_BINARY_DIR}/external/Catch2/generated-includes
)

# 链接Catch2库和核心库
if(TARGET Catch2::Catch2WithMain)
    target_link_libraries(tests PRIVATE core Catch2::Catch2WithMain)
else()
    add_subdirectory(${CATCH2_DIR} ${CMAKE_BINARY_DIR}/external/Catch2 EXCLUDE_FROM_ALL)
    target_link_libraries(tests PRIVATE core Catch2::Catch2WithMain)
endif()