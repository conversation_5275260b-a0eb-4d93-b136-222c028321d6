cmake_minimum_required(VERSION 3.10)
project(aibox)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置变量以简化路径引用
set(INFER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/infer)
set(EXTERNAL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty)

# 设置包含路径
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${INFER_DIR})
# 添加 RKNN 头文件路径
include_directories(${EXTERNAL_DIR}/rknn/include)
# 添加 RGA 头文件路径
include_directories(${EXTERNAL_DIR}/librga/include)
# 添加 Catch2 头文件路径
include_directories(${EXTERNAL_DIR}/Catch2/src)
# 添加 CImg 头文件路径
include_directories(${EXTERNAL_DIR}/CImg)

# 寻找第三方库
find_package(fmt REQUIRED)
# 只查找我们需要的OpenCV组件，避免不必要的依赖
find_package(OpenCV 4 REQUIRED COMPONENTS core imgproc highgui imgcodecs)
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
message(STATUS "OpenCV core libraries found at: ${OpenCV_core_LIBRARY}")
find_path(RKNN_INCLUDE_DIR rknn_api.h PATHS ${EXTERNAL_DIR}/rknn/include NO_DEFAULT_PATH)
find_library(RKNN_LIBRARY rknnrt PATHS ${EXTERNAL_DIR}/rknn/linux NO_DEFAULT_PATH)

# 指定 RGA 库的确切路径
set(RGA_LIBRARY ${EXTERNAL_DIR}/librga/linux/librga.a)

# 添加 base 库
add_library(base STATIC
    ${INFER_DIR}/base/alignment.h
    ${INFER_DIR}/base/file_utils.cpp
    ${INFER_DIR}/base/file_utils.h
    ${INFER_DIR}/base/fps_counter.h
    ${INFER_DIR}/base/macros.h
    ${INFER_DIR}/base/static_vector.h
    ${INFER_DIR}/base/types.h
    ${INFER_DIR}/base/wsq.h
)
target_link_libraries(base utils)

# 添加 core 库
add_library(core STATIC
    ${INFER_DIR}/core/model/model.cpp
    ${INFER_DIR}/core/model/model.h
    ${INFER_DIR}/core/model/rknn/model_rknn.cpp
    ${INFER_DIR}/core/model/rknn/model_rknn.h
    ${INFER_DIR}/core/model/yolo/yolo_v6.cpp
    ${INFER_DIR}/core/model/yolo/yolo_v6.h
    ${INFER_DIR}/core/model/yolo/yolo_v8.cpp
    ${INFER_DIR}/core/model/yolo/yolo_v8.h
    ${INFER_DIR}/core/model/yolo/yolo.cpp
    ${INFER_DIR}/core/model/yolo/yolo.h
    ${INFER_DIR}/core/model/object_detector.cpp
    ${INFER_DIR}/core/model/object_detector.h
    ${INFER_DIR}/core/video/video_capture.cpp
    ${INFER_DIR}/core/video/video_capture.h
    ${INFER_DIR}/core/video/edid.h
    ${INFER_DIR}/core/aimbot/aimbot_fps.cpp
    ${INFER_DIR}/core/aimbot/aimbot_fps.h
    ${INFER_DIR}/core/aimbot/aimbot_debug.cpp
    ${INFER_DIR}/core/aimbot/aimbot_debug.h
    ${INFER_DIR}/core/aimbot/data_collector.cpp
    ${INFER_DIR}/core/aimbot/data_collector.h
    ${INFER_DIR}/core/aimbot/infer_collector.cpp
    ${INFER_DIR}/core/aimbot/infer_collector.h
)

# 设置库依赖关系
target_link_libraries(core base ${RKNN_LIBRARY} ${RGA_LIBRARY} CImg 
    opencv_core opencv_imgproc opencv_highgui opencv_imgcodecs
    -lpthread ${ATOMIC_LIBRARY})

# 添加测试库
file(GLOB_RECURSE TEST_SOURCES
    ${INFER_DIR}/tests/*.cpp
    ${INFER_DIR}/tests/*.h
)
if(TEST_SOURCES)
    # 不再添加 Catch2 子目录，直接使用外部已构建的 Catch2
    add_executable(tests ${TEST_SOURCES})
    # 使用 Catch2WithMain 而不是 Catch2，确保正确的目标名称
    target_link_libraries(tests core thread_manager utils fmt::fmt Catch2::Catch2WithMain)
endif()

# 添加主可执行文件
add_executable(infer_test ${INFER_DIR}/infer_test.cpp)

if (ANDROID)
    target_link_libraries(infer_test android log)
endif()

target_link_libraries(infer_test thread_manager core utils fmt::fmt ${ATOMIC_LIBRARY}) 