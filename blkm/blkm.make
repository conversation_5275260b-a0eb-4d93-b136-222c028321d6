# BLKM模块定义
# 注意：此文件设计为被主项目的CMakeLists.txt包含，不应独立使用

# 确认Poco库已在主CMakeLists.txt中正确配置
if(NOT Poco_FOUND)
    message(FATAL_ERROR "未找到Poco库，请确保在主CMakeLists.txt中正确配置")
endif()

# 查找OpenSSL库（用于crypto_utils.cpp）
find_package(OpenSSL REQUIRED)
message(STATUS "OpenSSL库版本: ${OPENSSL_VERSION}")
message(STATUS "OpenSSL库包含目录: ${OPENSSL_INCLUDE_DIR}")
message(STATUS "OpenSSL库: ${OPENSSL_LIBRARIES}")

# 显示Poco库配置信息
message(STATUS "使用Poco库: ${Poco_LIBRARIES}")
message(STATUS "使用OpenSSL库: ${OPENSSL_LIBRARIES}")

# 查找Poco::NetSSL组件 - 用于HTTPS请求
find_package(Poco REQUIRED COMPONENTS NetSSL JSON)
message(STATUS "Poco NetSSL库: ${Poco_NetSSL_LIBRARY}")
message(STATUS "Poco JSON库: ${Poco_JSON_LIBRARY}")

# BLKM API源文件
set(BLKM_SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/blkm/blkmsdk.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/blkm/crypto_utils.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/blkm/cloud_update.cpp
)

# 创建BLKM API静态库
add_library(blkmapi STATIC ${BLKM_SOURCES})

# 设置包含目录 - 只包含BLKM自身目录，其他已在全局配置
target_include_directories(blkmapi PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/blkm
    ${OPENSSL_INCLUDE_DIR}  # 添加OpenSSL包含目录
)

# 链接必要的依赖库
target_link_libraries(blkmapi PUBLIC 
    utils
    ${Poco_LIBRARIES}
    ${POCO_NETSSL_LIBRARY}
    Poco::NetSSL
    Poco::Net
    Poco::Foundation
    Poco::Crypto
    Poco::JSON
    Threads::Threads
    OpenSSL::Crypto
    OpenSSL::SSL
)

# 创建BLKM测试可执行文件
add_executable(blkm_test ${CMAKE_CURRENT_SOURCE_DIR}/blkm/blkm_test.cpp)

# 链接必要的依赖库 - 简化配置
target_link_libraries(blkm_test PRIVATE 
    blkmapi
    utils
    ${Poco_LIBRARIES}
    ${POCO_NETSSL_LIBRARY}
    Poco::NetSSL
    Poco::Net
    Poco::Foundation
    Poco::Crypto
    Poco::JSON
    Threads::Threads
    thread_manager
    OpenSSL::Crypto
    OpenSSL::SSL
    spdlog::spdlog
)

message(STATUS "BLKM模块定义已加载") 