# 云更新功能使用说明

## 功能概述

云更新模块提供了完整的版本检查和文件下载功能，支持通过卡密系统获取版本信息和下载链接。在卡密验证成功后，只需一行代码即可完成版本检查。

## 文件结构

```
blkm/
├── cloud_update.h           # 云更新头文件
├── cloud_update.cpp         # 云更新实现文件
├── blkm_test.cpp            # BLKM测试程序（包含云更新测试）
└── README_云更新.md         # 本说明文档
```

## 核心功能

### 简化版本检查（推荐使用）

在`cardVerifyThreadFunc`函数中，已经集成了版本检查功能，只需要一行代码即可完成：

```cpp
#include "../blkm/cloud_update.h"

// 在卡密验证成功后调用（已集成到blkm_server.cpp中）
bool has_update = jfkm::CloudUpdateManager::check_update_simple(
    jfkm_config::g_access_key,    // 访问密钥
    api_host,                     // API主机地址
    jfkm_config::g_card_key,      // 卡密号码
    jfkm_config::g_app_flag,      // 应用标识
    core_vars::g_version          // 当前版本号（定义在utils/gmodels.cpp中）
);

if (has_update) {
    LOG_INFO("检测到新版本可用");
    // 可以在这里添加UI通知或自动更新逻辑
} else {
    LOG_INFO("当前版本为最新版本");
}
```

### 版本号配置

当前版本号定义在 `utils/gmodels.cpp` 中的 `core_vars` 命名空间：

```cpp
namespace core_vars {
    std::string g_version = "1.0.0";  // 修改这里来更新版本号
}
```

### 自动版本检查流程

1. **卡密验证成功** → 自动触发版本检查
2. **获取服务器版本信息** → 从服务器下载 `version.json` 文件
3. **版本号比较** → 使用语义化版本号比较算法
4. **获取下载链接** → 如果有新版本，自动获取下载链接
5. **日志记录** → 完整的版本检查过程日志

### 高级用法（可选）

如果需要更详细的控制，可以使用完整的API：

```cpp
// 创建KmSdk实例
auto km_sdk = std::make_shared<jfkm::KmSdk>(access_key, api_host);

// 创建云更新管理器
jfkm::CloudUpdateManager update_manager(km_sdk);

// 检查版本更新
auto [version_info, error] = update_manager.check_version_update(
    card_num, app_flag, current_version);

if (!error.empty()) {
    LOG_ERROR("版本检查失败: {}", error);
} else if (!version_info.version.empty()) {
    LOG_INFO("发现新版本: {}", version_info.version);
    LOG_INFO("版本描述: {}", version_info.description);
    LOG_INFO("文件大小: {}", version_info.file_size);
    LOG_INFO("MD5校验: {}", version_info.md5_hash);
    LOG_INFO("强制更新: {}", version_info.force_update);
    
    // 获取下载链接
    auto [download_url, url_error] = update_manager.get_update_file_url(
        card_num, app_flag, version_info.download_url);
    
    if (!url_error.empty()) {
        LOG_ERROR("获取下载链接失败: {}", url_error);
    } else {
        LOG_INFO("下载链接: {}", download_url);
        
        // 下载文件
        auto [success, download_error] = update_manager.download_update_file(
            download_url, "./update_file.zip");
        
        if (success) {
            // 验证文件完整性
            bool is_valid = update_manager.verify_file_integrity(
                "./update_file.zip", version_info.md5_hash);
            
            if (is_valid) {
                LOG_INFO("文件下载并验证成功");
            } else {
                LOG_ERROR("文件完整性验证失败");
            }
        }
    }
}
```

## 服务器端版本文件格式

服务器上的`version.json`文件应该包含以下格式：

```json
{
    "version": "1.0.1",
    "description": "修复了一些bug，增加了新功能",
    "download_url": "update_v1.0.1.zip",
    "file_size": "15.2MB",
    "md5_hash": "d41d8cd98f00b204e9800998ecf8427e",
    "force_update": false
}
```

## 版本号比较规则

支持标准的语义化版本号格式：`主版本.次版本.修订版本[.构建版本]`

例如：
- `1.0.0` < `1.0.1`
- `1.0.1` > `1.0.0`
- `1.0.0` = `1.0.0`
- `2.0.0` > `1.9.9`
- `1.0.0` < `*******`

## 构建配置

云更新模块已经集成到`blkm.make`中，会自动编译到`blkmapi`静态库中。

## 测试

使用现有的BLKM测试程序进行测试：

```bash
# 编译
make blkm_test

# 运行测试
./blkm_test
```

测试程序会验证版本比较功能和基本的API调用。

## 使用说明

### 1. 版本号更新
修改 `utils/gmodels.cpp` 中的版本号：
```cpp
namespace core_vars {
    std::string g_version = "1.0.1";  // 更新到新版本号
}
```

### 2. 自动检查
版本检查已经集成到卡密验证流程中，无需额外配置。当卡密验证成功后会自动执行版本检查。

### 3. 日志监控
通过日志可以监控版本检查过程：
- `开始检查版本更新...` - 版本检查开始
- `发现新版本: x.x.x -> y.y.y` - 发现新版本
- `当前版本为最新版本` - 无需更新
- `版本检查失败: xxx` - 检查失败

## 注意事项

1. **网络连接**：确保程序运行环境可以访问卡密服务器
2. **权限验证**：版本检查需要有效的卡密验证
3. **文件路径**：服务器上需要有 `version.json` 文件
4. **错误处理**：版本检查失败不会影响主程序运行
5. **安全性**：下载的文件会进行MD5校验以确保完整性
6. **执行频率**：版本检查在每次程序启动时只执行一次（卡密验证成功后）

## 集成状态

✅ **已完成集成** - 云更新功能已经完全集成到现有的卡密验证流程中
✅ **一键使用** - 只需一行代码即可完成版本检查
✅ **自动触发** - 卡密验证成功后自动执行版本检查
✅ **完整日志** - 提供详细的版本检查过程日志
✅ **错误处理** - 完善的异常处理，不影响主程序运行 