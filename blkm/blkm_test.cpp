/*-------------------------------------------
                Includes
-------------------------------------------*/
#include <signal.h>
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <limits>
#include <algorithm>
#include <fstream>
#include <filesystem>

#include "blkmsdk.h"
#include "cloud_update.h"
#include "../utils/logger.h"
#include "../utils/gmodels.h"
#include "../ThreadManager/blkm_server.h"

/*-------------------------------------------
                Signal Handler
-------------------------------------------*/
void handle_sig(int signo) {
    LOG_INFO("程序收到信号 {}，准备退出...", signo);
    thread_control::g_thread_running = false;
    
    // 确保停止所有线程
    thread_manager::stopBlkmThreads();
    
    // 直接退出程序
    exit(0);
}

/*-------------------------------------------
                Test Functions
-------------------------------------------*/
void test_card_verification() {
    std::cout << "\n=== 卡密验证测试 ===" << std::endl;
    
    try {
        // 创建KmSdk实例
        std::string api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
        jfkm::KmSdk km_sdk(jfkm_config::g_access_key, api_host);
        
        // 获取设备标识符
        std::string device_id = jfkm::get_device_identifier();
        std::cout << "设备标识: " << device_id << std::endl;
        std::cout << "API地址: " << api_host << std::endl;
        std::cout << "卡密: " << jfkm_config::g_card_key << std::endl;
        
        // 执行卡密验证
        std::cout << "正在验证卡密..." << std::endl;
        auto [data, error] = km_sdk.verify_card(
            jfkm_config::g_card_key, 
            jfkm_config::g_app_flag, 
            device_id
        );
        
        if (!error.empty()) {
            std::cout << "❌ 卡密验证失败: " << error << std::endl;
            return;
        }
        
        if (data) {
            std::cout << "✅ 卡密验证成功!" << std::endl;
            
            // 打印验证信息
            std::ostringstream dataStr;
            data->stringify(dataStr);
            std::cout << "验证详情: " << dataStr.str() << std::endl;
            
            // 提取过期时间信息
            if (data->has("expire_time")) {
                try {
                    std::string expire_time = data->getValue<std::string>("expire_time");
                    std::cout << "有效期至: " << expire_time << std::endl;
                } catch (...) {
                    std::cout << "无法解析有效期信息" << std::endl;
                }
            }
        } else {
            std::cout << "❌ 卡密验证失败: 返回数据为空" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ 卡密验证异常: " << e.what() << std::endl;
    }
}

void test_cloud_update() {
    std::cout << "\n=== 云更新功能测试 ===" << std::endl;
    
    try {
        std::cout << "当前版本: " << webui::header::currentVersion << std::endl;
        
        // 创建云更新管理器
        std::string api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
        auto km_sdk = std::make_shared<jfkm::KmSdk>(jfkm_config::g_access_key, api_host);
        jfkm::CloudUpdateManager update_manager(km_sdk);
        
        // 测试版本信息下载和解析
        std::cout << "\n--- 版本信息下载测试 ---" << std::endl;
        auto [version_info, version_error] = update_manager.get_version_info();
        
        if (!version_error.empty()) {
            std::cout << "❌ 版本信息获取失败: " << version_error << std::endl;
        } else {
            std::cout << "✅ 版本信息获取成功!" << std::endl;
            std::cout << "服务器版本: " << version_info.version << std::endl;
            std::cout << "版本描述: " << version_info.description << std::endl;
            if (!version_info.file_size.empty()) {
                std::cout << "文件大小: " << version_info.file_size << std::endl;
            }
            if (!version_info.md5_hash.empty()) {
                std::cout << "MD5校验: " << version_info.md5_hash << std::endl;
            }
            std::cout << "强制更新: " << (version_info.force_update ? "是" : "否") << std::endl;
        }
        
        // 测试版本比较功能
        std::cout << "\n--- 版本比较测试 ---" << std::endl;
        std::vector<std::pair<std::string, std::string>> test_cases = {
            {webui::header::currentVersion, "1.0.1"},
            {webui::header::currentVersion, "0.9.9"},
            {webui::header::currentVersion, webui::header::currentVersion},
            {"1.0.0", "*******"},
            {"2.0.0", "1.9.9"}
        };
        
        for (const auto& test_case : test_cases) {
            int result = jfkm::CloudUpdateManager::compare_version(test_case.first, test_case.second);
            std::string comparison;
            if (result < 0) comparison = "<";
            else if (result > 0) comparison = ">";
            else comparison = "=";
            
            std::cout << test_case.first << " " << comparison << " " << test_case.second << std::endl;
        }
        
        // 测试版本更新检查
        std::cout << "\n--- 版本更新检查测试 ---" << std::endl;
        
        auto [update_info, update_error] = update_manager.check_version_update(webui::header::currentVersion);
        
        if (!update_error.empty()) {
            std::cout << "❌ 版本检查失败: " << update_error << std::endl;
        } else if (!update_info.version.empty()) {
            std::cout << "✅ 检测到新版本可用" << std::endl;
            std::cout << "新版本: " << update_info.version << std::endl;
            std::cout << "版本描述: " << update_info.description << std::endl;
            std::cout << "强制更新: " << (update_info.force_update ? "是" : "否") << std::endl;
            
            // 可选：测试下载和部署（注释掉以避免实际部署）
            // std::cout << "\n--- 测试下载和部署 ---" << std::endl;
            // bool deploy_success = update_manager.download_and_deploy_update_file(
            //     jfkm_config::g_card_key,
            //     jfkm_config::g_app_flag,
            //     "kodbox/wolfeyes/bl.zip"
            // );
            // std::cout << (deploy_success ? "✅ 部署成功" : "❌ 部署失败") << std::endl;
        } else {
            std::cout << "✅ 当前版本为最新版本" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ 云更新测试异常: " << e.what() << std::endl;
    }
}

/*-------------------------------------------
                  Main Function
-------------------------------------------*/
int main() {
    // 注册信号处理
    signal(SIGINT, handle_sig);
    signal(SIGTERM, handle_sig);
    
    LOG_INFO("BLKM测试程序启动");
    
    // 设置卡密配置（这些值在gmodels.cpp中已有默认值，这里可以覆盖）
    jfkm_config::g_card_key = "300879cb-a9f1-4f8f-b616-3e773b0243aa"; // 如果需要设置不同的卡密
    LOG_INFO("当前卡密: {}", jfkm_config::g_card_key);
    LOG_INFO("应用标识: {}", jfkm_config::g_app_flag);
    LOG_INFO("当前版本: {}", webui::header::currentVersion);
    
    std::cout << "\n========================================" << std::endl;
    std::cout << "         BLKM 功能测试程序" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // 执行各项测试
    // test_card_verification();
    // test_cloud_update();
    
    std::cout << "\n========================================" << std::endl;
    std::cout << "         启动后台验证线程" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // 启动所有线程（内部会初始化BLKM服务和启动卡密验证线程）
    if (!thread_manager::startBlkmThreads()) {
        LOG_ERROR("启动线程失败");
        return 1;
    }
    
    // 等待线程完全启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    std::cout << "卡密验证程序已启动，按Ctrl+C退出程序..." << std::endl;
    
    // 使用循环轮询方式等待退出信号
    while (thread_control::g_thread_running) {
        // 每秒检查一次
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        // 输出服务器状态和验证状态
        LOG_DEBUG("线程运行状态: {}", thread_manager::isBlkmThreadsRunning() ? "运行中" : "已停止");
        LOG_DEBUG("卡密验证状态: {}", jfkm_config::g_auth_status == 1 ? "已验证" : 
                                 (jfkm_config::g_auth_status == 2 ? "验证失败" : "未验证"));
    }
    
    // 停止所有线程
    thread_manager::stopBlkmThreads();
    
    LOG_INFO("测试程序正常退出");
    return 0;
}
