#pragma once

#include <string>
#include <memory>
#include <utility>
#include "Poco/JSON/Object.h"
#include "blkmsdk.h"

namespace jfkm {

/**
 * 版本信息结构体
 */
struct VersionInfo {
    std::string version;        // 版本号
    std::string description;    // 版本描述
    std::string download_url;   // 下载链接
    std::string file_size;      // 文件大小
    std::string md5_hash;       // 文件MD5校验值
    bool force_update;          // 是否强制更新
    
    VersionInfo() : force_update(false) {}
};

/**
 * 云更新管理器类
 * 负责版本检查、文件下载等云更新相关功能
 */
class CloudUpdateManager {
public:
    /**
     * 构造函数
     * @param km_sdk 卡密SDK实例的共享指针
     */
    CloudUpdateManager(std::shared_ptr<KmSdk> km_sdk);
    
    /**
     * 析构函数
     */
    ~CloudUpdateManager();

    // ==================== 核心功能模块 ====================
    
    /**
     * 1. 获取版本信息函数
     * 从服务器下载并解析版本信息文件
     * @return 包含版本信息和错误信息的对
     */
    std::pair<VersionInfo, std::string> get_version_info();

    /**
     * 2. 检查版本是否更新函数
     * 比较当前版本与服务器版本，判断是否需要更新
     * @param current_version 当前版本号
     * @return 包含版本信息和错误信息的对，如果没有更新则版本信息为空
     */
    std::pair<VersionInfo, std::string> check_version_update(const std::string& current_version);

    /**
     * 3. 下载并部署版本文件函数
     * 获取下载链接、下载文件并执行部署
     * @param card_num 卡密号码
     * @param app_flag 应用标识
     * @param file_path 服务器文件路径
     * @param target_user 目标用户（可选，默认自动选择）
     * @return 是否部署成功
     */
    bool download_and_deploy_update_file(
        const std::string& card_num,
        const std::string& app_flag,
        const std::string& file_path = "kodbox/wolfeyes/bl.zip",
        const std::string& target_user = "");

    // ==================== 辅助工具函数 ====================

    /**
     * 比较版本号
     * @param version1 版本号1
     * @param version2 版本号2
     * @return -1: version1 < version2, 0: 相等, 1: version1 > version2
     */
    static int compare_version(const std::string& version1, const std::string& version2);

    /**
     * 验证文件完整性
     * @param file_path 文件路径
     * @param expected_md5 期望的MD5值
     * @return 验证是否成功
     */
    bool verify_file_integrity(const std::string& file_path, const std::string& expected_md5);

private:
    /**
     * 下载更新文件到本地
     * @param download_url 下载链接
     * @param save_path 保存路径
     * @return 包含成功标志和错误信息的对
     */
    std::pair<bool, std::string> download_file(const std::string& download_url, const std::string& save_path);

    /**
     * 获取文件下载链接
     * @param card_num 卡密号码
     * @param app_flag 应用标识
     * @param file_path 文件路径
     * @return 包含下载链接和错误信息的对
     */
    std::pair<std::string, std::string> get_file_download_url(
        const std::string& card_num,
        const std::string& app_flag,
        const std::string& file_path);

    /**
     * 选择目标用户
     * @param preferred_user 首选用户（可选）
     * @return 选择的用户名和用户主目录路径
     */
    std::pair<std::string, std::string> select_target_user(const std::string& preferred_user = "");

    /**
     * 解析版本检查响应
     * @param response_data 响应数据
     * @return 解析后的版本信息
     */
    VersionInfo parse_version_response(const Poco::JSON::Object::Ptr& response_data);

    std::shared_ptr<KmSdk> km_sdk_;  // 卡密SDK实例
};

} // namespace jfkm 