#include "crypto_utils.h"
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/err.h>
#include <openssl/md5.h>
#include "Poco/Base64Decoder.h"
#include "Poco/Base64Encoder.h"
#include "Poco/StreamCopier.h"
#include <sstream>
#include <iomanip>
#include "../utils/logger.h"

namespace jfkm {
namespace crypto {

std::string aes_decrypt(const std::string& ciphertext, const std::string& key, const std::string& iv) {
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        LOG_ERROR("创建OpenSSL解密上下文失败");
        return "";
    }
    
    try {
        // 初始化解密
        if (1 != EVP_DecryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, 
                                  (const unsigned char*)key.c_str(), 
                                  (const unsigned char*)iv.c_str())) {
            LOG_ERROR("初始化解密失败: {}", ERR_error_string(ERR_get_error(), NULL));
            EVP_CIPHER_CTX_free(ctx);
            return "";
        }
        
        // 禁用填充以与PyCryptodome兼容
        EVP_CIPHER_CTX_set_padding(ctx, 0);
        
        // 准备输出缓冲区
        std::string plaintext;
        plaintext.resize(ciphertext.size() + AES_BLOCK_SIZE);
        int len = 0, plaintext_len = 0;
        
        // 执行解密
        if (1 != EVP_DecryptUpdate(ctx, (unsigned char*)&plaintext[0], &len,
                                 (const unsigned char*)ciphertext.c_str(), ciphertext.size())) {
            LOG_ERROR("解密失败: {}", ERR_error_string(ERR_get_error(), NULL));
            EVP_CIPHER_CTX_free(ctx);
            return "";
        }
        plaintext_len = len;
        
        // 完成解密过程 (可能会失败，因为我们禁用了填充)
        EVP_DecryptFinal_ex(ctx, (unsigned char*)&plaintext[plaintext_len], &len);
        plaintext_len += len;
        
        // 调整输出大小
        plaintext.resize(plaintext_len);
        EVP_CIPHER_CTX_free(ctx);
        return plaintext;
    }
    catch (const std::exception& e) {
        LOG_ERROR("AES解密异常: {}", e.what());
        EVP_CIPHER_CTX_free(ctx);
        return "";
    }
}

std::string remove_pkcs7_padding(const std::string& data) {
    if (data.empty()) return data;
    
    unsigned char padding = static_cast<unsigned char>(data.back());
    if (padding == 0 || padding > 16 || data.size() < padding) {
        return data;  // 无效填充，返回原始数据
    }
    
    // 验证所有填充字节
    for (size_t i = data.size() - padding; i < data.size(); ++i) {
        if (data[i] != padding) return data;  // 填充无效，返回原始数据
    }
    
    // 去除填充
    return data.substr(0, data.size() - padding);
}

std::string base64_encode(const std::string& data) {
    try {
        std::ostringstream ostr;
        Poco::Base64Encoder encoder(ostr);
        encoder.write(data.data(), data.size());
        encoder.close();
        return ostr.str();
    } catch (const std::exception& e) {
        LOG_ERROR("Base64编码异常: {}", e.what());
        return "";
    }
}

std::string base64_decode(const std::string& data) {
    try {
        if(data.empty()) return "";
        
        std::istringstream istr(data);
        std::ostringstream ostr;
        Poco::Base64Decoder decoder(istr);
        Poco::StreamCopier::copyStream(decoder, ostr);
        
        std::string result = ostr.str();
        LOG_DEBUG("Base64解码完成, 结果长度: {}", result.length());
        return result;
    } catch (const std::exception& e) {
        LOG_ERROR("Base64解码异常: {}", e.what());
        return "";
    }
}

std::string md5_hash(const std::string& data) {
    // 使用EVP接口替代弃用的MD5函数
    unsigned char md[EVP_MAX_MD_SIZE];
    unsigned int md_len = 0;
    
    // 创建EVP上下文
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        LOG_ERROR("创建MD5上下文失败");
        return "";
    }
    
    // 初始化MD5摘要算法
    if (1 != EVP_DigestInit_ex(mdctx, EVP_md5(), NULL)) {
        LOG_ERROR("初始化MD5算法失败");
        EVP_MD_CTX_free(mdctx);
        return "";
    }
    
    // 添加数据
    if (1 != EVP_DigestUpdate(mdctx, data.c_str(), data.length())) {
        LOG_ERROR("更新MD5数据失败");
        EVP_MD_CTX_free(mdctx);
        return "";
    }
    
    // 计算摘要
    if (1 != EVP_DigestFinal_ex(mdctx, md, &md_len)) {
        LOG_ERROR("完成MD5计算失败");
        EVP_MD_CTX_free(mdctx);
        return "";
    }
    
    // 释放上下文
    EVP_MD_CTX_free(mdctx);
    
    // 将二进制摘要转换为十六进制字符串
    std::ostringstream oss;
    for (unsigned int i = 0; i < md_len; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(md[i]);
    }
    return oss.str();
}

std::string prepare_aes_key(const std::string& key) {
    std::string result = key;
    
    // 确保密钥长度为16字节
    if (result.length() > 16) {
        result = result.substr(0, 16);
    } else if (result.length() < 16) {
        result.append(16 - result.length(), '\0');
    }
    
    return result;
}

} // namespace crypto
} // namespace jfkm 