#include "cloud_update.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <regex>
#include <filesystem>
#include "Poco/MD5Engine.h"
#include "Poco/DigestStream.h"
#include "Poco/File.h"
#include "Poco/Path.h"
#include "Poco/Exception.h"
#include "Poco/JSON/Parser.h"
#include "Poco/JSON/Object.h"
#include "Poco/Dynamic/Var.h"
#include "../utils/logger.h"
#include "crypto_utils.h"

namespace jfkm {

// 构造函数
CloudUpdateManager::CloudUpdateManager(std::shared_ptr<KmSdk> km_sdk)
    : km_sdk_(km_sdk) {
    LOG_INFO("云更新管理器初始化完成");
}

// 析构函数
CloudUpdateManager::~CloudUpdateManager() {
    LOG_INFO("云更新管理器销毁");
}

// 1. 获取版本信息函数
std::pair<VersionInfo, std::string> CloudUpdateManager::get_version_info() {
    try {
        LOG_INFO("开始获取版本信息（使用永久链接）");
        
        // 使用永久链接直接下载版本文件
        std::string version_url = "http://110.42.52.244:4006/?explorer/share/file&hash=bd3fGcG73lxyeVBMIQG0iApILpfazwmcKfqnR0mnvWsqQqPux6sFL_PRzm4T62OeWg";
        
        LOG_INFO("使用永久链接下载版本文件: {}", version_url);
        
        // 下载版本文件内容
        auto [version_content, download_error] = km_sdk_->download_url(version_url);
        if (!download_error.empty()) {
            LOG_ERROR("下载版本文件失败: {}", download_error);
            return {VersionInfo(), download_error};
        }
        
        LOG_INFO("版本文件下载成功，大小: {} 字节", version_content.size());
        LOG_DEBUG("版本文件内容: {}", version_content);
        
        // 解析版本文件JSON
        try {
            Poco::JSON::Parser parser;
            Poco::Dynamic::Var result = parser.parse(version_content);
            Poco::JSON::Object::Ptr version_obj = result.extract<Poco::JSON::Object::Ptr>();
            
            // 解析版本信息
            VersionInfo version_info = parse_version_response(version_obj);
            
            LOG_INFO("版本信息解析成功: 版本={}, 描述={}", 
                     version_info.version, version_info.description);
            
            return {version_info, ""};
            
        } catch (const Poco::Exception& e) {
            LOG_ERROR("解析版本文件JSON失败: {}", e.displayText());
            return {VersionInfo(), "解析版本文件失败: " + e.displayText()};
        } catch (const std::exception& e) {
            LOG_ERROR("解析版本文件异常: {}", e.what());
            return {VersionInfo(), "解析版本文件失败: " + std::string(e.what())};
        }
        
    } catch (const std::exception& e) {
        LOG_ERROR("获取版本信息异常: {}", e.what());
        return {VersionInfo(), "获取版本信息失败: " + std::string(e.what())};
    }
}

// 2. 检查版本是否更新函数
std::pair<VersionInfo, std::string> CloudUpdateManager::check_version_update(
    const std::string& current_version) {
    
    try {
        LOG_INFO("开始检查版本更新，当前版本: {}", current_version);
        
        // 获取服务器版本信息
        auto [version_info, error] = get_version_info();
        if (!error.empty()) {
            LOG_ERROR("获取版本信息失败: {}", error);
            return {VersionInfo(), error};
        }
        
        // 比较版本号
        int version_compare = compare_version(version_info.version, current_version);
        if (version_compare > 0) {
            LOG_INFO("发现新版本: {} -> {}", current_version, version_info.version);
            return {version_info, ""};
        } else {
            LOG_INFO("当前已是最新版本: {}", current_version);
            return {VersionInfo(), ""}; // 返回空版本信息表示无更新
        }
        
    } catch (const std::exception& e) {
        LOG_ERROR("检查版本更新异常: {}", e.what());
        return {VersionInfo(), "检查版本更新失败: " + std::string(e.what())};
    }
}

// 获取更新文件下载链接
std::pair<std::string, std::string> CloudUpdateManager::get_file_download_url(
    const std::string& card_num,
    const std::string& app_flag,
    const std::string& file_path) {
    
    try {
        LOG_INFO("获取更新文件下载链接: {}", file_path);
        
        // 使用KmSdk的get_file_tmp_url方法获取文件链接
        auto [download_url, error] = km_sdk_->get_file_tmp_url(card_num, app_flag, file_path);
        
        if (!error.empty()) {
            LOG_ERROR("获取文件链接失败: {}", error);
            return {"", error};
        }
        
        LOG_INFO("成功获取文件下载链接: {}", download_url);
        return {download_url, ""};
        
    } catch (const std::exception& e) {
        LOG_ERROR("获取更新文件链接异常: {}", e.what());
        return {"", "获取文件链接失败: " + std::string(e.what())};
    }
}

// 下载文件到本地
std::pair<bool, std::string> CloudUpdateManager::download_file(
    const std::string& download_url,
    const std::string& save_path) {
    
    try {
        LOG_INFO("开始下载文件到: {}", save_path);
        
        // 使用KmSdk下载文件
        auto [file_content, error] = km_sdk_->download_url(download_url);
        if (!error.empty()) {
            LOG_ERROR("下载文件失败: {}", error);
            return {false, error};
        }
        
        // 确保保存目录存在
        Poco::Path file_path(save_path);
        Poco::File parent_dir(file_path.parent());
        if (!parent_dir.exists()) {
            parent_dir.createDirectories();
            LOG_INFO("创建目录: {}", parent_dir.path());
        }
        
        // 保存文件到本地
        std::ofstream file(save_path, std::ios::binary);
        if (!file.is_open()) {
            LOG_ERROR("无法创建文件: {}", save_path);
            return {false, "无法创建文件: " + save_path};
        }
        
        file.write(file_content.c_str(), file_content.size());
        file.close();
        
        LOG_INFO("文件下载完成，大小: {} 字节", file_content.size());
        return {true, ""};
        
    } catch (const std::exception& e) {
        LOG_ERROR("下载文件异常: {}", e.what());
        return {false, "下载文件失败: " + std::string(e.what())};
    }
}

// 验证文件完整性
bool CloudUpdateManager::verify_file_integrity(
    const std::string& file_path,
    const std::string& expected_md5) {
    
    try {
        LOG_INFO("验证文件完整性: {}", file_path);
        
        // 检查文件是否存在
        Poco::File file(file_path);
        if (!file.exists()) {
            LOG_ERROR("文件不存在: {}", file_path);
            return false;
        }
        
        // 计算文件MD5
        std::ifstream input_file(file_path, std::ios::binary);
        if (!input_file.is_open()) {
            LOG_ERROR("无法打开文件: {}", file_path);
            return false;
        }
        
        Poco::MD5Engine md5;
        Poco::DigestOutputStream dos(md5);
        
        char buffer[8192];
        while (input_file.read(buffer, sizeof(buffer)) || input_file.gcount() > 0) {
            dos.write(buffer, input_file.gcount());
        }
        input_file.close();
        dos.close();
        
        std::string file_md5 = Poco::DigestEngine::digestToHex(md5.digest());
        
        // 比较MD5值（忽略大小写）
        std::string expected_lower = expected_md5;
        std::string file_lower = file_md5;
        std::transform(expected_lower.begin(), expected_lower.end(), expected_lower.begin(), ::tolower);
        std::transform(file_lower.begin(), file_lower.end(), file_lower.begin(), ::tolower);
        
        bool is_valid = (expected_lower == file_lower);
        
        if (is_valid) {
            LOG_INFO("文件完整性验证成功: {}", file_md5);
        } else {
            LOG_ERROR("文件完整性验证失败，期望: {}，实际: {}", expected_md5, file_md5);
        }
        
        return is_valid;
        
    } catch (const std::exception& e) {
        LOG_ERROR("验证文件完整性异常: {}", e.what());
        return false;
    }
}

// 比较版本号
int CloudUpdateManager::compare_version(
    const std::string& version1,
    const std::string& version2) {
    
    try {
        // 使用正则表达式分割版本号
        std::regex version_regex(R"((\d+)\.(\d+)\.(\d+)(?:\.(\d+))?)");
        std::smatch match1, match2;
        
        if (!std::regex_match(version1, match1, version_regex) ||
            !std::regex_match(version2, match2, version_regex)) {
            LOG_WARNING("版本号格式不正确，使用字符串比较: {} vs {}", version1, version2);
            return version1.compare(version2);
        }
        
        // 提取版本号各部分
        std::vector<int> v1_parts, v2_parts;
        
        for (size_t i = 1; i < match1.size() && !match1[i].str().empty(); ++i) {
            v1_parts.push_back(std::stoi(match1[i].str()));
        }
        for (size_t i = 1; i < match2.size() && !match2[i].str().empty(); ++i) {
            v2_parts.push_back(std::stoi(match2[i].str()));
        }
        
        // 补齐版本号长度
        while (v1_parts.size() < 4) v1_parts.push_back(0);
        while (v2_parts.size() < 4) v2_parts.push_back(0);
        
        // 逐个比较版本号部分
        for (size_t i = 0; i < 4; ++i) {
            if (v1_parts[i] < v2_parts[i]) return -1;
            if (v1_parts[i] > v2_parts[i]) return 1;
        }
        
        return 0; // 版本号相等
        
    } catch (const std::exception& e) {
        LOG_ERROR("比较版本号异常: {}", e.what());
        return version1.compare(version2); // 降级为字符串比较
    }
}

// 解析版本检查响应
VersionInfo CloudUpdateManager::parse_version_response(const Poco::JSON::Object::Ptr& response_data) {
    VersionInfo version_info;
    
    try {
        if (response_data->has("version")) {
            version_info.version = response_data->getValue<std::string>("version");
        }
        
        if (response_data->has("description")) {
            version_info.description = response_data->getValue<std::string>("description");
        }
        
        if (response_data->has("download_url")) {
            version_info.download_url = response_data->getValue<std::string>("download_url");
        }
        
        if (response_data->has("file_size")) {
            version_info.file_size = response_data->getValue<std::string>("file_size");
        }
        
        if (response_data->has("md5_hash")) {
            version_info.md5_hash = response_data->getValue<std::string>("md5_hash");
        }
        
        if (response_data->has("force_update")) {
            version_info.force_update = response_data->getValue<bool>("force_update");
        }
        
        LOG_DEBUG("解析版本信息完成: 版本={}, 强制更新={}", 
                 version_info.version, version_info.force_update);
        
    } catch (const std::exception& e) {
        LOG_ERROR("解析版本响应异常: {}", e.what());
    }
    
    return version_info;
}

// 选择目标用户
std::pair<std::string, std::string> CloudUpdateManager::select_target_user(const std::string& preferred_user) {
    try {
        std::string selected_user;
        std::string user_home_path;
        
        // 如果指定了首选用户，优先使用
        if (!preferred_user.empty() && preferred_user != "root") {
            selected_user = preferred_user;
            user_home_path = "/home/" + selected_user;
            LOG_INFO("使用指定用户: {}", selected_user);
            return {selected_user, user_home_path};
        }
        
        // 读取/etc/passwd文件获取用户列表
        std::ifstream passwd_file("/etc/passwd");
        std::string line;
        std::vector<std::string> normal_users;
        
        if (passwd_file.is_open()) {
            while (std::getline(passwd_file, line)) {
                // 解析passwd文件格式: username:x:uid:gid:comment:home:shell
                size_t first_colon = line.find(':');
                if (first_colon != std::string::npos) {
                    std::string username = line.substr(0, first_colon);
                    
                    // 跳过系统用户和root
                    if (username != "root" && username != "daemon" && username != "bin" && 
                        username != "sys" && username != "sync" && username != "games" && 
                        username != "man" && username != "lp" && username != "mail" && 
                        username != "news" && username != "uucp" && username != "proxy" && 
                        username != "www-data" && username != "backup" && username != "list" && 
                        username != "irc" && username != "gnats" && username != "nobody" && 
                        username != "systemd-network" && username != "systemd-resolve" && 
                        username != "messagebus" && username != "systemd-timesync" && 
                        username != "syslog" && username != "_apt" && username != "tss" && 
                        username != "uuidd" && username != "tcpdump" && username != "sshd" && 
                        username != "landscape" && username != "pollinate" && username != "ec2-instance-connect" && 
                        username != "systemd-coredump" && username != "ubuntu" && username != "lxd" && 
                        username != "fwupd-refresh" && !username.empty() && username[0] != '_') {
                        
                        // 检查用户ID是否大于1000（通常是普通用户）
                        size_t uid_start = line.find(':', first_colon + 1);
                        if (uid_start != std::string::npos) {
                            uid_start++;
                            size_t uid_end = line.find(':', uid_start);
                            if (uid_end != std::string::npos) {
                                std::string uid_str = line.substr(uid_start, uid_end - uid_start);
                                try {
                                    int uid = std::stoi(uid_str);
                                    if (uid >= 1000) {
                                        normal_users.push_back(username);
                                    }
                                } catch (...) {
                                    // 忽略解析错误
                                }
                            }
                        }
                    }
                }
            }
            passwd_file.close();
        }
        
        // 选择用户
        if (!normal_users.empty()) {
            selected_user = normal_users[0]; // 选择第一个普通用户
            user_home_path = "/home/" + selected_user;
        } else {
            // 如果没有找到普通用户，使用当前用户
            selected_user = getenv("USER") ? getenv("USER") : "bred";
            if (selected_user == "root") {
                selected_user = "bred"; // 强制使用bred用户
            }
            user_home_path = "/home/" + selected_user;
        }
        
        LOG_INFO("选择的用户: {}, 用户主目录: {}", selected_user, user_home_path);
        return {selected_user, user_home_path};
        
    } catch (const std::exception& e) {
        LOG_ERROR("选择目标用户异常: {}", e.what());
        return {"bred", "/home/<USER>"}; // 默认返回bred用户
    }
}

// 3. 下载并部署版本文件函数
bool CloudUpdateManager::download_and_deploy_update_file(
    const std::string& card_num,
    const std::string& app_flag,
    const std::string& file_path,
    const std::string& target_user) {
    
    try {
        LOG_INFO("开始下载并部署更新文件: {}", file_path);
        
        // 选择目标用户
        auto [selected_user, user_home_path] = select_target_user(target_user);
        std::string local_file_path = user_home_path + "/bl.zip";
        
        // 获取文件下载链接
        auto [download_url, url_error] = get_file_download_url(card_num, app_flag, file_path);
        if (!url_error.empty()) {
            LOG_ERROR("获取文件链接失败: {}", url_error);
            return false;
        }
        
        LOG_INFO("成功获取文件下载链接: {}", download_url);
        
        // 下载文件到本地
        auto [download_success, download_error] = download_file(download_url, local_file_path);
        if (!download_success) {
            LOG_ERROR("下载文件失败: {}", download_error);
            return false;
        }
        
        LOG_INFO("文件下载成功，保存路径: {}", local_file_path);
        
        // 验证文件是否存在
        std::ifstream file(local_file_path, std::ios::binary | std::ios::ate);
        if (!file.is_open()) {
            LOG_ERROR("无法打开下载的文件: {}", local_file_path);
            return false;
        }
        
        std::streamsize file_size = file.tellg();
        file.close();
        LOG_INFO("下载文件大小: {} 字节", file_size);
        
        // 文件下载完成，不进行解压和删除操作
        LOG_INFO("新版本文件下载完成: {}", local_file_path);
        LOG_INFO("文件已保存，等待程序重启后应用新版本");
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("下载并部署更新文件异常: {}", e.what());
        return false;
    }
}

} // namespace jfkm