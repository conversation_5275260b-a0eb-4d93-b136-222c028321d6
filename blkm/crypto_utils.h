#pragma once

#include <string>

namespace jfkm {
namespace crypto {

/**
 * 使用AES-CBC模式解密数据
 * @param ciphertext 待解密的数据
 * @param key 密钥 (16字节)
 * @param iv 初始化向量 (16字节)
 * @return 解密后的数据
 */
std::string aes_decrypt(const std::string& ciphertext, const std::string& key, const std::string& iv);

/**
 * 移除PKCS#7填充
 * @param data 待处理的数据
 * @return 移除填充后的数据
 */
std::string remove_pkcs7_padding(const std::string& data);

/**
 * Base64编码
 * @param data 原始数据
 * @return Base64编码的字符串
 */
std::string base64_encode(const std::string& data);

/**
 * Base64解码
 * @param data Base64编码的字符串
 * @return 解码后的数据
 */
std::string base64_decode(const std::string& data);

/**
 * 计算MD5哈希值
 * @param data 待计算哈希的数据
 * @return MD5哈希值的十六进制字符串表示
 */
std::string md5_hash(const std::string& data);

/**
 * 准备AES密钥
 * 确保密钥长度为16字节(AES-128)
 * @param key 原始密钥
 * @return 处理后的16字节密钥
 */
std::string prepare_aes_key(const std::string& key);

} // namespace crypto
} // namespace jfkm 