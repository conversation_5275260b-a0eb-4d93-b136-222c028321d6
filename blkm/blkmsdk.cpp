#include "blkmsdk.h"
#include <chrono>
#include <thread>
#include <sstream>
#include <iostream>
#include <iomanip>  // 添加 setw, setfill 支持
#include "Poco/Net/HTTPClientSession.h"
#include "Poco/Net/HTTPRequest.h"
#include "Poco/Net/HTTPResponse.h"
#include "Poco/Net/HTTPSClientSession.h"
#include "Poco/Net/Context.h"
#include "Poco/Net/NetException.h"
#include "Poco/URI.h"
#include "Poco/JSON/Parser.h"
#include "Poco/JSON/Object.h"
#include "Poco/Crypto/Cipher.h"
#include "Poco/Crypto/CipherFactory.h"
#include "Poco/Crypto/CipherKey.h"
#include "Poco/Base64Decoder.h"
#include "Poco/Base64Encoder.h"
#include "Poco/StreamCopier.h"
#include "Poco/MD5Engine.h"
#include "Poco/DigestStream.h"
#include "Poco/Exception.h"
#include "Poco/String.h"
#include "Poco/Environment.h"
#include "../utils/logger.h"
#include "crypto_utils.h"

namespace jfkm {

// 获取设备标识符
std::string get_device_identifier() {
    try {
        Poco::MD5Engine md5;
        md5.update(Poco::Environment::nodeName() + Poco::Environment::osName());
        return Poco::DigestEngine::digestToHex(md5.digest());
    } catch (const std::exception& e) {
        LOG_ERROR("获取设备标识符失败: {}", e.what());
        return "备用设备标识";
    }
}

// 构造函数
KmSdk::KmSdk(const std::string& access_key, const std::string& host)
    : access_key_(access_key), host_(host) {
    try {
        LOG_INFO("初始化卡密SDK, 服务地址: {}", host);
        host_uri_ = Poco::URI(host);
        user_agent_ = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3";
    } catch (const Poco::Exception& e) {
        LOG_ERROR("KmSdk初始化异常: {}", e.what());
    }
}

// 析构函数
KmSdk::~KmSdk() {
}

// 更改API主机地址
void KmSdk::change_api_host(const std::string& new_host) {
    try {
        LOG_INFO("更改卡密API主机地址: {} -> {}", host_, new_host);
        host_ = new_host;
        host_uri_ = Poco::URI(new_host);
    } catch (const Poco::Exception& e) {
        LOG_ERROR("更改API主机地址异常: {}", e.what());
    }
}

// 验证卡密
std::pair<Poco::JSON::Object::Ptr, std::string> KmSdk::verify_card(
    const std::string& card_num, 
    const std::string& app_flag, 
    const std::string& bind_device) {
    
    try {
        // 获取当前时间戳
        auto now_time = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        // 构造请求体
        Poco::JSON::Object body;
        body.set("card_num", card_num);
        body.set("bind_device", bind_device);
        body.set("timestamp", now_time);
        body.set("app_flag", app_flag);
        
        // 生成签名
        std::string raw_str = std::to_string(now_time) + "|" + card_num + "|" + access_key_;
        LOG_DEBUG("生成签名原始字符串: {}", raw_str);
        
        // 使用crypto_utils的MD5函数
        std::string sign = crypto::md5_hash(raw_str);
        LOG_DEBUG("生成签名: {}", sign);
        
        body.set("sign", sign);
        
        // 输出请求信息
        std::ostringstream requestBody;
        body.stringify(requestBody);
        // LOG_INFO("【请求URL】: {}/api/v1/verify/verifyCard", host_);
        // LOG_INFO("【请求体】: {}", requestBody.str());
        
        // 构造请求URL并发送
        std::string url = host_ + "/api/v1/verify/verifyCard";
        std::this_thread::sleep_for(std::chrono::milliseconds(200));  // 防止请求过快
        auto [data, error] = make_request(url, body);
        
        // 处理响应
        if (!error.empty()) {
            LOG_ERROR("验证卡密请求失败: {}", error);
            return {nullptr, error};
        }
        
        // 打印响应数据
        std::ostringstream dataStr;
        data->stringify(dataStr);
        LOG_INFO("验证卡密完成，响应数据: {}", dataStr.str());
        
        return {data, ""};
    } catch (const std::exception& e) {
        LOG_ERROR("验证卡密异常: {}", e.what());
        return {nullptr, "请求失败: " + std::string(e.what())};
    }
}

// 获取文件临时下载链接
std::pair<std::string, std::string> KmSdk::get_file_tmp_url(
    const std::string& card_num, 
    const std::string& app_flag, 
    const std::string& path) {
    
    try {
        // 获取当前时间戳
        auto now_time = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        // 构造请求体
        Poco::JSON::Object body;
        body.set("card_num", card_num);
        body.set("filepath", path);
        body.set("timestamp", now_time);
        body.set("app_flag", app_flag);
        
        // 生成签名
        std::string raw_str = std::to_string(now_time) + "|" + card_num + "|" + access_key_;
        LOG_DEBUG("生成签名原始字符串: {}", raw_str);
        
        // 使用crypto_utils的MD5函数
        std::string sign = crypto::md5_hash(raw_str);
        LOG_DEBUG("生成签名: {}", sign);
        
        body.set("sign", sign);
        
        // 输出请求信息
        std::ostringstream requestBody;
        body.stringify(requestBody);
        LOG_INFO("【请求URL】: {}/api/v1/verify/getFileUrl", host_);
        LOG_INFO("【请求体】: {}", requestBody.str());
        
        // 构造请求URL
        std::string url = host_ + "/api/v1/verify/getFileUrl";
        
        // 发送请求
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        auto [data, error] = make_request(url, body);
        
        if (!error.empty()) {
            LOG_ERROR("获取文件链接失败: {}", error);
            return {"", error};
        }
        
        // 获取URL并处理
        std::ostringstream dataStr;
        data->stringify(dataStr);
        LOG_DEBUG("获取文件链接响应: {}", dataStr.str());
        
        std::string file_url = data->getValue<std::string>("url");
        LOG_DEBUG("原始URL: {}", file_url);
        
        // URL解码
        std::string decodedUrl;
        Poco::URI::decode(file_url, decodedUrl);
        LOG_DEBUG("解码后URL: {}", decodedUrl);
        
        // 移除引号，处理成与Python版本相同的结果
        if (!decodedUrl.empty() && decodedUrl.front() == '"' && decodedUrl.back() == '"') {
            decodedUrl = decodedUrl.substr(1, decodedUrl.length() - 2);
        }
        
        return {decodedUrl, ""};
    } catch (const std::exception& e) {
        LOG_ERROR("获取文件链接异常: {}", e.what());
        return {"", "请求失败: " + std::string(e.what())};
    }
}

// 更换绑定设备
std::pair<Poco::JSON::Object::Ptr, std::string> KmSdk::change_bind_device(
    const std::string& card_num, 
    const std::string& app_flag, 
    const std::string& bind_device) {
    
    try {
        // 获取当前时间戳
        auto now_time = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        // 构造请求体
        Poco::JSON::Object body;
        body.set("card_num", card_num);
        body.set("bind_device", bind_device);
        body.set("timestamp", now_time);
        body.set("app_flag", app_flag);
        
        // 生成签名
        std::string raw_str = std::to_string(now_time) + "|" + card_num + "|" + access_key_;
        LOG_DEBUG("生成签名原始字符串: {}", raw_str);
        
        // 使用crypto_utils的MD5函数
        std::string sign = crypto::md5_hash(raw_str);
        LOG_DEBUG("生成签名: {}", sign);
        
        body.set("sign", sign);
        
        // 输出请求信息
        std::ostringstream requestBody;
        body.stringify(requestBody);
        LOG_INFO("【请求URL】: {}/api/v1/verify/changeBind", host_);
        LOG_INFO("【请求体】: {}", requestBody.str());
        
        // 构造请求URL并发送
        std::string url = host_ + "/api/v1/verify/changeBind";
        std::this_thread::sleep_for(std::chrono::milliseconds(200));  // 防止请求过快
        auto [data, error] = make_request(url, body);
        
        // 处理响应
        if (!error.empty()) {
            LOG_ERROR("更换绑定设备请求失败: {}", error);
            return {nullptr, error};
        }
        
        // 打印响应数据
        std::ostringstream dataStr;
        data->stringify(dataStr);
        LOG_INFO("更换绑定设备完成，响应数据: {}", dataStr.str());
        
        return {data, ""};
    } catch (const std::exception& e) {
        LOG_ERROR("更换绑定设备异常: {}", e.what());
        return {nullptr, "请求失败: " + std::string(e.what())};
    }
}

// 下载指定URL的内容
std::pair<std::string, std::string> KmSdk::download_url(const std::string& url) {
    try {
        LOG_INFO("准备下载URL: {}", url);
        
        std::string current_url = url;
        int redirect_count = 0;
        const int max_redirects = 5; // 最大重定向次数
        
        while (redirect_count < max_redirects) {
            Poco::URI uri(current_url);
        
        // 创建HTTP/HTTPS会话
        std::unique_ptr<Poco::Net::HTTPClientSession> cs;
        if (uri.getScheme() == "https") {
            // 创建SSL上下文
            Poco::Net::Context::Ptr context = new Poco::Net::Context(
                Poco::Net::Context::CLIENT_USE, "", "", "",
                Poco::Net::Context::VERIFY_NONE, 9, false, "ALL:!ADH:!LOW:!EXP:!MD5:@STRENGTH"
            );
            cs.reset(new Poco::Net::HTTPSClientSession(uri.getHost(), uri.getPort(), context));
        } else {
            cs.reset(new Poco::Net::HTTPClientSession(uri.getHost(), uri.getPort()));
        }
        
        // 设置超时
        cs->setTimeout(Poco::Timespan(30, 0));  // 30秒超时
        
        // 创建请求
        std::string path = uri.getPathAndQuery();
        if (path.empty()) path = "/";
        
        Poco::Net::HTTPRequest request(Poco::Net::HTTPRequest::HTTP_GET, path, Poco::Net::HTTPMessage::HTTP_1_1);
        request.setHost(uri.getHost());
        request.set("User-Agent", user_agent_);
        
        LOG_DEBUG("发送HTTP请求: {}", path);
        cs->sendRequest(request);
        
        // 获取响应
        Poco::Net::HTTPResponse response;
        std::istream& rs = cs->receiveResponse(response);
        
        LOG_DEBUG("服务器响应状态: {} {}", static_cast<int>(response.getStatus()), response.getReason());
        
            // 处理重定向
            if (response.getStatus() == Poco::Net::HTTPResponse::HTTP_FOUND ||
                response.getStatus() == Poco::Net::HTTPResponse::HTTP_MOVED_PERMANENTLY ||
                response.getStatus() == Poco::Net::HTTPResponse::HTTP_TEMPORARY_REDIRECT) {
                
                if (response.has("Location")) {
                    std::string location = response.get("Location");
                    LOG_INFO("检测到重定向 ({}): {} -> {}", static_cast<int>(response.getStatus()), current_url, location);
                    
                    // 处理相对URL
                    if (location.find("http") != 0) {
                        if (location[0] == '/') {
                            // 绝对路径
                            current_url = uri.getScheme() + "://" + uri.getHost();
                            if (uri.getPort() != 80 && uri.getPort() != 443) {
                                current_url += ":" + std::to_string(uri.getPort());
                            }
                            current_url += location;
                        } else {
                            // 相对路径
                            std::string base_path = uri.getPath();
                            size_t last_slash = base_path.find_last_of('/');
                            if (last_slash != std::string::npos) {
                                base_path = base_path.substr(0, last_slash + 1);
                            }
                            current_url = uri.getScheme() + "://" + uri.getHost();
                            if (uri.getPort() != 80 && uri.getPort() != 443) {
                                current_url += ":" + std::to_string(uri.getPort());
                            }
                            current_url += base_path + location;
                        }
                    } else {
                        current_url = location;
                    }
                    
                    redirect_count++;
                    continue; // 继续循环处理重定向
                } else {
                    LOG_ERROR("重定向响应缺少Location头");
                    return {"", "重定向响应缺少Location头"};
                }
            }
            
            // 检查是否成功
        if (response.getStatus() != Poco::Net::HTTPResponse::HTTP_OK) {
            LOG_ERROR("HTTP状态码错误: {} {}", static_cast<int>(response.getStatus()), response.getReason());
            return {"", "HTTP错误: " + std::to_string(static_cast<int>(response.getStatus())) + " " + response.getReason()};
        }
        
        // 读取响应内容到字符串
        std::string content;
        Poco::StreamCopier::copyToString(rs, content);
        
        LOG_INFO("下载完成，大小: {} 字节", content.size());
        
        return {content, ""};
        }
        
        LOG_ERROR("重定向次数超过限制: {}", max_redirects);
        return {"", "重定向次数超过限制"};
        
    } catch (const Poco::Exception& e) {
        LOG_ERROR("下载URL异常: {}", e.displayText());
        return {"", "下载失败: " + e.displayText()};
    } catch (const std::exception& e) {
        LOG_ERROR("下载URL异常: {}", e.what());
        return {"", "下载失败: " + std::string(e.what())};
    }
}

// Base64编码
std::string KmSdk::base64_encode(const std::string& data) {
    return crypto::base64_encode(data);
}

// Base64解码
std::string KmSdk::base64_decode(const std::string& data) {
    return crypto::base64_decode(data);
}

// HTTP请求实现
std::pair<Poco::JSON::Object::Ptr, std::string> KmSdk::make_request(
    const std::string& url, const Poco::JSON::Object& body) {
    
    try {
        Poco::URI uri(url);
        // LOG_INFO("发送请求到: {}", url);
        
        // 创建HTTP/HTTPS会话
        std::unique_ptr<Poco::Net::HTTPClientSession> cs;
        if (uri.getScheme() == "https") {
            Poco::Net::Context::Ptr context = new Poco::Net::Context(
                Poco::Net::Context::CLIENT_USE, "", "", "",
                Poco::Net::Context::VERIFY_NONE, 9, false, "ALL:!ADH:!LOW:!EXP:!MD5:@STRENGTH"
            );
            cs.reset(new Poco::Net::HTTPSClientSession(uri.getHost(), uri.getPort(), context));
        } else {
            cs.reset(new Poco::Net::HTTPClientSession(uri.getHost(), uri.getPort()));
        }
        
        // 设置超时 (30秒)
        cs->setTimeout(Poco::Timespan(30, 0));
        
        // 准备请求体和请求头
        std::string path = uri.getPathAndQuery();
        if (path.empty()) path = "/";
        
        Poco::Net::HTTPRequest request(Poco::Net::HTTPRequest::HTTP_POST, path, Poco::Net::HTTPMessage::HTTP_1_1);
        request.setContentType("application/json");
        request.setHost(uri.getHost());
        request.set("User-Agent", user_agent_);
        
        // 将请求体转换为字符串
        std::ostringstream oss;
        body.stringify(oss);
        std::string bodyStr = oss.str();
        
        request.setContentLength(bodyStr.length());
        
        // 发送请求
        // LOG_DEBUG("发送HTTP请求: {}", path);
        std::ostream& os = cs->sendRequest(request);
        os << bodyStr;
        
        // 接收响应
        Poco::Net::HTTPResponse response;
        std::istream& rs = cs->receiveResponse(response);
        
        LOG_DEBUG("服务器响应状态: {} {}", static_cast<int>(response.getStatus()), response.getReason());
        
        if (response.getStatus() != Poco::Net::HTTPResponse::HTTP_OK) {
            LOG_ERROR("HTTP状态码错误: {} {}", static_cast<int>(response.getStatus()), response.getReason());
            return {nullptr, "HTTP错误: " + std::to_string(static_cast<int>(response.getStatus())) + " " + response.getReason()};
        }
        
        // 读取响应数据
        std::string responseStr;
        Poco::StreamCopier::copyToString(rs, responseStr);
        
        // LOG_DEBUG("收到响应数据: {}", responseStr);
        
        // 解析JSON
        Poco::JSON::Parser parser;
        Poco::Dynamic::Var result = parser.parse(responseStr);
        Poco::JSON::Object::Ptr resultObj = result.extract<Poco::JSON::Object::Ptr>();
        
        // 检查code字段
        if (resultObj->has("code")) {
            int code = resultObj->getValue<int>("code");
            if (code != 0) {
                std::string errMsg = (resultObj->has("msg")) 
                    ? resultObj->getValue<std::string>("msg") 
                    : "未知错误";
                    
                LOG_ERROR("API返回错误码: {}, 消息: {}", code, errMsg);
                return {resultObj, errMsg};
            }
        }
        
        // 获取data字段
        if (resultObj->has("data")) {
            Poco::Dynamic::Var dataVar = resultObj->get("data");
            if (dataVar.isString()) {
                // data是加密字符串
                try {
                    std::string encryptedData = dataVar.extract<std::string>();
                    if (!encryptedData.empty()) {
                        std::string decryptedData = decrypt_data_to_string(encryptedData);
                        LOG_DEBUG("解密后的数据: {}", decryptedData);
                        
                        // 检查解密后的数据是否是JSON格式
                        if (decryptedData.front() == '{' && decryptedData.back() == '}') {
                        // 尝试将解密后的数据解析为JSON
                        Poco::JSON::Parser jsonParser;
                        Poco::Dynamic::Var decryptedResult = jsonParser.parse(decryptedData);
                        Poco::JSON::Object::Ptr decryptedObj = decryptedResult.extract<Poco::JSON::Object::Ptr>();
                        
                        return {decryptedObj, ""};
                        } else {
                            // 解密后的数据是URL字符串，创建一个包含url字段的JSON对象
                            Poco::JSON::Object::Ptr urlObj = new Poco::JSON::Object();
                            
                            // 移除可能存在的引号
                            std::string cleanUrl = decryptedData;
                            if (!cleanUrl.empty() && cleanUrl.front() == '"' && cleanUrl.back() == '"') {
                                cleanUrl = cleanUrl.substr(1, cleanUrl.length() - 2);
                            }
                            
                            urlObj->set("url", cleanUrl);
                            return {urlObj, ""};
                        }
                    }
                } catch (const Poco::Exception& e) {
                    LOG_WARNING("解密或解析响应数据失败: {}", e.displayText());
                    // 如果解密失败，保持原样
                } catch (const std::exception& e) {
                    LOG_WARNING("解密或解析响应数据失败: {}", e.what());
                    // 如果解密失败，保持原样
                }
            }
        }
        
        return {resultObj, ""};
        
    } catch (const Poco::Exception& e) {
        LOG_ERROR("HTTP请求异常: {}", e.displayText());
        return {nullptr, "请求失败: " + e.displayText()};
    } catch (const std::exception& e) {
        LOG_ERROR("HTTP请求异常: {}", e.what());
        return {nullptr, "请求失败: " + std::string(e.what())};
    }
}

// 解密数据为字符串
std::string KmSdk::decrypt_data_to_string(const std::string& data) {
    try {
        if (data.empty()) {
            LOG_ERROR("输入数据为空");
            return "";
        }
        
        // Base64解码
        std::string decoded_data = crypto::base64_decode(data);
        if (decoded_data.empty()) {
            LOG_ERROR("Base64解码失败");
            return "";
        }
        
        // 准备密钥和向量 (使用相同密钥作为IV)
        std::string key = crypto::prepare_aes_key(access_key_);
        
        // 输出前32字节数据用于调试
        if (decoded_data.length() >= 32) {
            std::ostringstream hex_output;
            for (size_t i = 0; i < 32; ++i) {
                hex_output << std::hex << std::setw(2) << std::setfill('0') 
                          << static_cast<int>(static_cast<unsigned char>(decoded_data[i])) << " ";
            }
            LOG_DEBUG("加密数据前32字节: {}", hex_output.str());
        }
        
        // AES解密
        std::string decrypted_data = crypto::aes_decrypt(decoded_data, key, key);
        
        // 移除PKCS#7填充
        std::string result = crypto::remove_pkcs7_padding(decrypted_data);
        
        LOG_INFO("解密完成, 结果长度: {}", result.length());
        return result;
    }
    catch (const std::exception& e) {
        LOG_ERROR("解密异常: {}", e.what());
        return "";
    }
}

} // namespace jfkm
