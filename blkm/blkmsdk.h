#pragma once

#include <string>
#include <memory>
#include <utility>
#include "Poco/JSON/Object.h"
#include "Poco/Net/HTTPClientSession.h"
#include "Poco/Net/HTTPRequest.h"
#include "Poco/Net/HTTPResponse.h"
#include "Poco/URI.h"
#include "Poco/Crypto/Cipher.h"

namespace jfkm {

/**
 * 获取设备唯一标识符
 * @return 设备标识符字符串
 */
std::string get_device_identifier();

/**
 * 卡密验证系统SDK类
 * 实现与卡密服务器的通信，包括验证卡密、获取下载链接等功能
 */
class KmSdk {
public:
    /**
     * 构造函数
     * @param access_key 访问密钥
     * @param host 卡密服务器主机地址
     */
    KmSdk(const std::string& access_key, const std::string& host);
    
    /**
     * 析构函数
     */
    ~KmSdk();

    /**
     * 更改API主机地址
     * @param new_host 新的主机地址
     */
    void change_api_host(const std::string& new_host);

    /**
     * 验证卡密
     * @param card_num 卡密号码
     * @param app_flag 应用标识
     * @param bind_device 设备标识(可选)
     * @return 包含验证结果和错误信息的对
     */
    std::pair<Poco::JSON::Object::Ptr, std::string> verify_card(
        const std::string& card_num, 
        const std::string& app_flag, 
        const std::string& bind_device = "");

    /**
     * 获取文件临时下载链接
     * @param card_num 卡密号码
     * @param app_flag 应用标识
     * @param path 文件路径
     * @return 包含下载链接和错误信息的对
     */
    std::pair<std::string, std::string> get_file_tmp_url(
        const std::string& card_num, 
        const std::string& app_flag, 
        const std::string& path);

    /**
     * 更换绑定设备
     * @param card_num 卡密号码
     * @param app_flag 应用标识
     * @param bind_device 新设备标识
     * @return 包含更换结果和错误信息的对
     */
    std::pair<Poco::JSON::Object::Ptr, std::string> change_bind_device(
        const std::string& card_num, 
        const std::string& app_flag, 
        const std::string& bind_device);

    /**
     * 下载指定URL的内容
     * @param url 要下载的URL
     * @return 包含下载内容和错误信息的对
     */
    std::pair<std::string, std::string> download_url(const std::string& url);

    /**
     * Base64编码
     * @param data 原始数据
     * @return Base64编码的字符串
     */
    std::string base64_encode(const std::string& data);
    
    /**
     * Base64解码
     * @param data Base64编码的字符串
     * @return 解码后的数据
     */
    std::string base64_decode(const std::string& data);

private:
    /**
     * 解密数据为字符串
     * @param data 加密数据
     * @return 解密后的字符串
     */
    std::string decrypt_data_to_string(const std::string& data);

    /**
     * HTTP请求封装
     * @param url 请求URL
     * @param body 请求体
     * @return 包含响应数据和错误信息的对
     */
    std::pair<Poco::JSON::Object::Ptr, std::string> make_request(
        const std::string& url, 
        const Poco::JSON::Object& body);

    std::string access_key_;  // 访问密钥
    std::string host_;        // 服务器主机
    Poco::URI host_uri_;      // 解析后的URI
    std::string user_agent_;  // 用户代理
};

} // namespace jfkm
