#include "gmodels.h"
#include "logger.h"

// 白名单文件列表相关变量定义
namespace whitelist {
    std::vector<std::string> g_allowed_files = {
        // algorithm
        // "pid_controller.cpp",
        // "pid_controller.h",
        //img_capture
        // "opencv_imgcrop.cpp",

        // blserver
        // "aim_service.cpp",
        // "fov_service.cpp",
        // "function_service.cpp",
        "home_service.cpp",
        // "login_service.cpp",
        // "message_handlers.cpp",
        // "pid_service.cpp",
        // "poco_websocket_server.cpp",
        // "register_service.cpp",
        // "sidebar_service.cpp",
        "update_models.cpp",
        // blsql
        // "aim_configs_db.cpp",
        // "fovconfgidb.cpp",
        // "function_configs_db.cpp",
        // "login_db.cpp",
        // "mysql_db.cpp",
        // "pid_configs_db.cpp",
        // "registerdb.cpp",
        // "sidebar_db.cpp",
        // "userinfodb.cpp",
        // lkm
        "keymouse.cpp",
        // "LkmApi.cpp",
        // infer
        "infer_tools.cpp",
        "inference.cpp"
        "postprocess.cc",
        // ThreadManager
        // "img_server.cpp",
        "infer_server.cpp",
        // "web_server.cpp",
        // "lkm_server.cpp",
        // "blkm_server.cpp",
        // main
        "main.cpp",
    };  // 初始化允许打印日志的文件列表
}

// 日志系统全局变量定义
namespace logger {
    bool g_log_enabled = true;                       // 默认启用日志打印
    bool g_whitelist_enabled = false;               // 默认不启用白名单
    int g_current_level = 1;                        // 默认日志级别为info (spdlog level)
}

namespace webui {
    // 登录相关-login_read
    namespace login {
        // 用户认证相关常量定义
        const std::string DEFAULT_USERNAME = "admin";    // 默认用户名
        const std::string DEFAULT_PASSWORD = "admin";    // 默认密码
        const std::string DEFAULT_TOKEN = "blweb_token";    // 修改为非空token，匹配前端的AppConfig.fixedAuthToken
    }
    // 注册相关-register_modify
    namespace myregister {
        // 游戏列表
        std::vector<std::string> g_game_list = {
            "apex",
            "cf",
            "cfhd",
            "csgo2",
            "sjz",
            "ssjj2",
            "pubg",
            "wwqy",
        };
        std::string defaultGame = webui::home::g_game_name;
        // 用户认证相关常量定义
        const std::string username = webui::login::DEFAULT_USERNAME;    // 默认用户名
        const std::string password = webui::login::DEFAULT_PASSWORD;    // 默认密码
        const std::string token = webui::login::DEFAULT_TOKEN;    // 修改为非空token，匹配前端的AppConfig.fixedAuthToken
    }

    // 页头相关配置变量
    namespace header {
        // 游戏名字 - 当前选择的游戏
        std::string g_game_name = home::g_game_name;
        bool databaseStatus = false;    // 数据库连接状态：true表示已连接，false表示未连接
        bool inferenceStatus = false;   // AI推理状态：true表示推理引擎正常，false表示异常
        bool cardKeyStatus = false;    // 卡密验证状态：true表示卡密有效，false表示无效
        bool keyMouseStatus = false;   // 键鼠控制状态：true表示正常，false表示异常
        bool isPro = false;           // Pro版本状态：默认为标准版本

        // CPU温度监控 - 单位：摄氏度
        double cpuTemperature = 0.0;
        // CPU使用率监控 - 百分比值（0-100）
        double cpuUsage = 0.0;
        // HDMI输入的刷新率 - 单位：Hz
        uint32_t frame_rate = 60;
        // HDMI输入的分辨率 - 单位：像素
        uint32_t width = 1920;
        uint32_t height = 1080;
        
        // 版本信息
        std::string currentVersion = "1.5.0";  // 当前版本号，直接设置版本号
        std::string latestVersion = "";        // 最新版本号，初始为空，通过网络获取
        bool versionUpdateRequested = false;                // 版本更新请求标志，默认为false
    }

    // Home页面相关配置
    namespace home {
        // 用户名 - 对应数据库中的username字段，用于标识当前选择的用户
        std::string g_username = webui::login::DEFAULT_USERNAME;
        // 游戏名称 - 对应数据库中的game_name字段，用于标识当前选择的游戏
        std::string g_game_name = "wwqy";
        // 卡密 - 用户的许可证密钥，用于验证用户身份和使用权限
        // std::string g_card_key = "8b1cb1b9-65aa-4450-87d2-b56fda23f8a7";
        std::string g_card_key = "035";
        bool forceAuthNow = false;        // 强制验证标志
    }

    // 功能设置相关配置
    namespace function {
        // 预设配置列表 - 包含多种不同的预设配置组合
        // 格式：{AI模式, 锁定位置, 阵营选择, 热键, 扳机开关, 切枪开关, 是否启用, 配置名称}
        std::vector<Config> PRESET_CONFIGS = {
            {"FOV", "头部", "警方", "右键", "true", "false", "false", "true", "配置1"},  // FOV模式头部瞄准，警方阵营，右键触发，扳机开关开启，切枪关闭，背闪关闭
            {"FOV", "颈部", "匪方", "右键", "false", "true", "true", "false", "配置2"}, // FOV模式颈部瞄准，匪方阵营，右键触发，扳机开关关闭，切枪开启，背闪开启
            {"FOV", "胸部", "无", "左键", "true", "false", "false", "false", "配置3"}, // FOV模式胸部瞄准，无阵营，左键触发，扳机开关开启，切枪关闭，背闪关闭
            {"FOV", "颈部", "警方", "左键", "false", "true", "true", "false", "配置4"}, // FOV模式颈部瞄准，警方阵营，左键触发，扳机开关关闭，切枪开启，背闪开启
        };

        // 当前激活的配置索引 - 对应PRESET_CONFIGS中的位置
        int g_active_config_index = 0;
        // 当前AI工作模式 - 可选值：PID（精确跟踪）、FOV（视野扫描）、FOVPID（混合模式）
        std::string g_ai_mode = "FOV";
        // 锁定位置 - 可选值：头部、颈部、胸部
        std::string g_lock_position = "头部";
        // 触发热键 - 可选值：左键、右键、中键等
        std::string g_hotkey = "右键";
        // 当前扳机开关状态 - 与PRESET_CONFIGS[g_active_config_index].trigger_switch保持同步
        bool g_trigger_switch = false;
    }

    // PID控制器配置参数
    namespace pid {
        double g_near_move_factor = 1.0;         // 近端移动速度
        double g_near_stabilizer = 0.5;          // 近端跟踪速度
        double g_near_response_rate = 0.3;       // 近端抖动力度
        double g_near_assist_zone = 3.0;         // 近端死区大小
        double g_near_response_delay = 1.0;      // 近端回弹速度
        double g_near_max_adjustment = 2.0;      // 近端积分限制
        double g_far_factor = 1.0;               // 远端系数
        double g_y_axis_factor = 1.0;            // Y轴系数
        double g_pid_random_factor = 0.5;        // PID随机系数
    }

    // FOV视野范围配置
    namespace fov {
        double g_fov = 0.75;            // FOV值
        int g_fov_time = 1;            // FOV时间 - 
        bool g_start_measure = false;    // FOV测量标志 - 默认为false，表示不在测量状态
    }

    // 瞄准参数配置
    namespace aim {
        int g_aim_range = 200;         // 瞄准范围 - 有效瞄准的最大距离
        float g_track_range = 0.008f;        // 跟踪范围 - 开始跟踪目标的最大距离
        double g_head_height = 80;        // 头部高度 - 瞄准头部时的相对高度
        double g_neck_height = 60;        // 颈部高度 - 瞄准颈部时的相对高度
        double g_chest_height = 40;       // 胸部高度 - 瞄准胸部时的相对高度
        double g_head_range_x = 5;       // 头部X方向范围 - 头部瞄准的水平偏移限制
        double g_head_range_y = 5;       // 头部Y方向范围 - 头部瞄准的垂直偏移限制
        double g_neck_range_x = 5;       // 颈部X方向范围 - 颈部瞄准的水平偏移限制
        double g_neck_range_y = 5;       // 颈部Y方向范围 - 颈部瞄准的垂直偏移限制
        double g_chest_range_x = 50;      // 胸部X方向范围 - 胸部瞄准的水平偏移限制
        double g_chest_range_y = 50;      // 胸部Y方向范围 - 胸部瞄准的垂直偏移限制
    }

    // 射击参数配置
    namespace fire {
        int g_rifle_sleep = 50;        // 步枪开火睡眠时间 - 射击后暂停时间，单位：毫秒
        int g_rifle_interval = 100;    // 步枪开火间隔 - 连续射击的间隔时间，单位：毫秒
        int g_pistol_sleep = 80;       // 手枪开火睡眠时间 - 射击后暂停时间，单位：毫秒
        int g_pistol_interval = 150;   // 手枪开火间隔 - 连续射击的间隔时间，单位：毫秒
        int g_sniper_sleep = 200;      // 狙击开火睡眠时间 - 射击后暂停时间，单位：毫秒
        int g_sniper_interval = 400;   // 狙击开火间隔 - 连续射击的间隔时间，单位：毫秒
    }

    // 数据采集配置
    namespace collect {
        bool g_is_enabled = false;            // 是否启用数据收集 - 默认关闭
        std::string g_map_hotkey = "右键";    // 地图数据收集热键 - 默认使用鼠标右键
        std::string g_target_hotkey = "前侧"; // 目标数据收集热键 - 默认使用鼠标前侧键
        std::string g_team_side = "警方";     // 阵营选择 - 默认警方
        std::string g_collection_name = "";   // 数据收集名称 - 默认为空，由系统自动生成
        bool g_map_hotkey_pressed = false;    // 地图热键状态 - 使用翻转模式控制
        bool g_target_hotkey_pressed = false; // 目标热键状态 - 使用翻转模式控制
    }

    // 数据库连接配置
    namespace database {
        // std::string g_db_host = "blsql.antszy.com"; //正式版
        // int g_db_port = 33065;                        // 正式版 据库端口
        std::string g_db_host = "sql.antszy.com"; //内测 测试版 下次搞中间
        int g_db_port = 33066;                        // 内测 测试数据库端口
        // std::string g_db_host = "***********";       // 本地测试版数据库主机地址
        // int g_db_port = 33065;                        // 本地测试版据库端口
        std::string g_db_name = "wolfeyesdb";         // 数据库名称
        std::string g_db_user = "wolfeyes";           // 数据库普通用户
        std::string g_db_password = "ai.antszy.com";  // 普通用户密码
        std::string g_db_root_user = "root";          // 数据库管理员用户
        std::string g_db_root_password = "rootpassword"; // 管理员密码
        int g_db_timeout = 5000;                      // 数据库连接超时时间，单位：毫秒
        int g_db_pool_size = 10;                      // 数据库连接池大小
    }
}

// WebSocket服务器配置
namespace websocket {
    // WebSocket服务绑定IP - 0.0.0.0表示监听所有网络接口
    std::string g_ws_ip = "0.0.0.0";
    // WebSocket服务端口 - 默认8080
    uint16_t g_ws_port = 8080;
    // 客户端连接计数器 - 当前已连接的客户端数量
    std::atomic<int> g_client_count(0);
    // 消息计数器 - 已处理的WebSocket消息总数
    std::atomic<uint64_t> g_message_count(0);
    
    // 是否记录消息日志 - 开启后会记录所有WebSocket消息
    bool g_log_messages = true;
    // 最大连接数 - 服务器允许的最大并发连接数
    int g_max_connections = 100;
    // 连接超时时间 - 空闲连接的超时时间，单位：秒
    int g_connection_timeout = 60;
    
    // WebSocket消息处理轮询间隔 - 每次消息处理之间的休眠时间，单位：毫秒
    int g_ws_poll_interval = 100;
    
    // WebSocket服务运行状态 - true表示服务正在运行，false表示已停止
    bool g_ws_server_running = false;
}

// 线程控制
namespace thread_control {
    // 全局线程运行标志 - true表示线程可以运行，false表示应当停止
    bool g_thread_running = true;
    // 定义推理变量
    bool g_infer_running = false;
}

// YOLO配置相关变量
namespace yolo_config {
    std::string g_model_path = "model/wwqy416.rknn";  // 默认相对路径，相对于可执行文件
    // 是否重载
    bool g_reload_model = false;
    // 当前YOLO模型的类别数量，默认为wwqy游戏的4个类别
    int g_yolo_class_num = 4;
    // 使用结构体映射替代JSON字符串
    std::unordered_map<std::string, GameConfig> g_game_configs = {
        {"apex", {
            0.7f,                   // 检测框阈值
            0.45f,                  // 非极大值抑制阈值
            1,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"R"},                  // 类别名称列表
            "model/apex416.rknn"          // 权重文件路径
        }},
        {"cf", {
            0.7f,                   // 检测框阈值
            0.45f,                  // 非极大值抑制阈值
            2,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"FF", "JF"},            // 类别名称列表
            "model/cf416.rknn"            // 权重文件路径
        }},
        {"cfhd", {
            0.7f,                   // 检测框阈值
            0.45f,                  // 非极大值抑制阈值
            2,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"R", "ZX"},            // 类别名称列表
            "model/csgo416.rknn"          // 权重文件路径
        }},
        {"pubg", {
            0.7f,                   // 检测框阈值
            0.45f,                  // 非极大值抑制阈值
            2,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"R", "ZX"},            // 类别名称列表
            "model/pubg416.rknn"          // 权重文件路径
        }},
        {"sjz", {
            0.7f,                   // 检测框阈值
            0.45f,                  // 非极大值抑制阈值
            1,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"R"},            // 类别名称列表
            "model/sjz416.rknn"          // 权重文件路径
        }},
        {"wwqy", {
            0.7f,                   // 检测框阈值
            0.7f,                   // 非极大值抑制阈值 - 提高到0.7减少重复框
            4,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"BS", "FF", "M", "JF"},                  // 类别名称列表
            "model/wwqy416.rknn"          // 权重文件路径，相对于可执行文件
        }},
        {"ssjj2", {
            0.4f,                   // 检测框阈值
            0.3f,                   // 非极大值抑制阈值
            1,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"R"},                  // 类别名称列表
            "model/csgo416.rknn"          // 权重文件路径
        }},
        {"csgo2", {
            0.7f,                   // 检测框阈值
            0.45f,                  // 非极大值抑制阈值
            2,                      // 目标类别数量
            128,                    // 最大目标数量
            64,                     // 目标名称最大长度
            {"R", "ZX"},            // 类别名称列表
            "model/csgo416.rknn"          // 权重文件路径
        }}
    };
    
    // 工具函数实现：根据游戏名称更新YOLO类别数量
    void UpdateYoloClassNum(const std::string& game_name) {
        auto it = g_game_configs.find(game_name);
        if (it != g_game_configs.end()) {
            g_yolo_class_num = it->second.obj_class_num;
            LOG_INFO("更新YOLO类别数量: 游戏={}, 类别数={}", game_name, g_yolo_class_num);
        } else {
            LOG_WARNING("未找到游戏配置: {}, 保持当前类别数: {}", game_name, g_yolo_class_num);
        }
    }
}


// 鼠标按键和协议相关定义实现
namespace lkm {
    // 鼠标按键定义 - 全局常量
    // 基本按键
    const uint8_t MOUSE_NONE   = 0x00;  // 无按键状态
    const uint8_t MOUSE_LEFT   = 0x01;  // 左键
    const uint8_t MOUSE_RIGHT  = 0x02;  // 右键
    const uint8_t MOUSE_MIDDLE = 0x04;  // 中键/滚轮按键
    const uint8_t MOUSE_SIDE1  = 0x08;  // 侧键1
    const uint8_t MOUSE_SIDE2  = 0x10;  // 侧键2
    
    // 组合按键
    const uint8_t MOUSE_LEFT_RIGHT   = 0x03;  // 左键+右键
    const uint8_t MOUSE_LEFT_MIDDLE  = 0x05;  // 左键+中键
    const uint8_t MOUSE_RIGHT_MIDDLE = 0x06;  // 右键+中键
    const uint8_t MOUSE_LEFT_SIDE1   = 0x09;  // 左键+侧键1
    const uint8_t MOUSE_RIGHT_SIDE1  = 0x0A;  // 右键+侧键1
    const uint8_t MOUSE_LEFT_SIDE2   = 0x11;  // 左键+侧键2
    const uint8_t MOUSE_RIGHT_SIDE2  = 0x12;  // 右键+侧键2
    
    // 键盘按键定义 - 全局常量
    // 修饰键
    const uint8_t KB_NONE       = 0x00;  // 无按键状态
    const uint8_t KB_LEFT_CTRL  = 0x01;  // 左Ctrl键
    const uint8_t KB_LEFT_SHIFT = 0x02;  // 左Shift键
    const uint8_t KB_LEFT_ALT   = 0x04;  // 左Alt键
    const uint8_t KB_LEFT_WIN   = 0x08;  // 左Win键
    const uint8_t KB_RIGHT_CTRL = 0x10;  // 右Ctrl键
    const uint8_t KB_RIGHT_SHIFT = 0x20;  // 右Shift键
    const uint8_t KB_RIGHT_ALT  = 0x40;  // 右Alt键
    const uint8_t KB_RIGHT_WIN  = 0x80;  // 右Win键
    
    // 常用键盘HID码定义
    const uint8_t HID_KEY_A      = 0x04;  // A键
    const uint8_t HID_KEY_B      = 0x05;  // B键
    const uint8_t HID_KEY_C      = 0x06;  // C键
    const uint8_t HID_KEY_D      = 0x07;  // D键
    const uint8_t HID_KEY_E      = 0x08;  // E键
    const uint8_t HID_KEY_ENTER  = 0x28;  // 回车键
    const uint8_t HID_KEY_ESC    = 0x29;  // ESC键
    const uint8_t HID_KEY_SPACE  = 0x2C;  // 空格键
    
    // 旧的协议头定义已被统一的LIGHTNING_PROTOCOL_HEADER替代
    
    // 协议常量定义 - 统一使用闪电键鼠标准协议
    const uint8_t LIGHTNING_PROTOCOL_HEADER[4] = {0xFF, 0xAA, 0x55, 0xFE};  // 闪电键鼠协议头部
    
    // 通用协议常量
    const size_t PACKET_SIZE = 15;          // 数据包大小 15字节 (帧头4 + 按键状态5 + 坐标4 + 滚轮1 + 校验1)
    const uint8_t CRC_INIT_VALUE = 0xAA;    // CRC初始值
    const int STANDARD_BAUD_RATE = 230400;  // 标准波特率 230400
    
    // 重连相关参数
    const int MAX_RECONNECT_ATTEMPTS = 5;   // 最大重连次数
    const int RECONNECT_DELAY_MS = 1000;    // 重连延迟(毫秒)
}

// 核心变量定义
namespace core_vars {
    // target_height
    double target_height = 0.0;
    // target_width
    double target_width = 0.0;
    // 鼠标移动相关
    double mouse_x = 0.0;             // 鼠标X轴移动量
    double mouse_y = 0.0;             // 鼠标Y轴移动量

    // -------------------测量FOV用-------------------
    // 定义目标中心点
    double target_center_x = 0.0;
    double target_center_y = 0.0;
    // 定义目标距离
    double target_distance = 0.0;
    // 屏幕中心点
    double screen_center_x = 0.0;
    double screen_center_y = 0.0;
    // -------------------测量FOV用-------------------

    // 键鼠状态-鼠标按键是否被按下
    bool mouse_pressed = false;  // 鼠标按键是否被按下

    
    // 移动值
    int move_x = 0;
    int move_y = 0;

    // 自动开火随机系数相关变量定义
    double g_fire_sleep_coeff = 1.0;    // 开火睡眠时间随机系数，默认1.0（无随机）
    double g_fire_interval_coeff = 1.0; // 开火间隔时间随机系数，默认1.0（无随机）
    
    // PID控制器随机系数相关变量定义
    double g_near_pid_coeff = 1.0;      // 近端PID系数随机变量，默认1.0（无随机）
    double g_far_pid_coeff = 1.0;       // 远端PID系数随机变量，默认1.0（无随机）
    
    // 背闪检测变量（仅用于wwqy游戏）
    bool g_flash_detected = false;      // 背闪检测状态：true表示检测到背闪，false表示未检测到
}

// 卡密系统相关配置
namespace jfkm_config {
    std::string g_access_key = "xkl2kdl1xos8ksbt";      // 访问密钥
    std::string g_api_host_standard = "https://antkma.antszy.com"; // 普通版API主机地址
    std::string g_api_host_pro = "https://blkm.antszy.com"; // Pro版API主机地址
    // adcd0efd-8224-41dc-b6d2-95c73409c79e  3ae2b224-5403-4377-ae4e-c4d26ac01471
    // std::string g_card_key = "adcd0efd-8224-41dc-b6d2-95c73409c79e";                   // 卡密，默认为空
    std::string g_card_key = webui::home::g_card_key;                   // 卡密，默认为空
    std::string g_app_flag = "fps";                 // 应用标识，默认为cf
    std::atomic<int> g_auth_status{0};                         // 认证状态码：0-未验证
}

// WSQ流水线架构相关实现
namespace wsq_models {
    // WSQ队列全局实例定义
    std::unique_ptr<CommandWSQ> g_command_queue = nullptr;
    
    // WSQ队列同步机制定义 - 与npu_work_queue完全相同的高性能机制
    std::mutex g_wsq_mutex;
    std::condition_variable g_wsq_cv;
    
    // WSQ多线程架构定义 - 只用于瞄准
    const u32 g_wsq_thread_count = 1;           // WSQ执行器线程数量（只用于瞄准）
    std::atomic<u32> g_next_wsq_thread_index{0}; // 原子轮询分配索引
}
