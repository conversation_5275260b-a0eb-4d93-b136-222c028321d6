#include "utils/logger.h"
#include "utils/gmodels.h"  // 在cpp文件中引入全局变量定义

namespace base::log {

void FmtLogMessageImpl(Level log_level,
                       const char* filename,
                       unsigned int line_num,
                       const char* function,
                       const char* format,
                       const fmt::format_args& args) {

    // 现在在宏层面已经检查了g_log_enabled，这里不需要重复检查
    const auto message_body = fmt::vformat(format, args);
    const auto message = fmt::format("{}:{}:{}: {}", filename, line_num, function, message_body);
    fmt::print(log_level >= Level::Warning ? stderr : stdout, "{}\n", message);
}

void Panic(const std::string& reason) {
    fmt::print("Panic: {}\n", reason);
    abort();
}

}  // namespace base::log 