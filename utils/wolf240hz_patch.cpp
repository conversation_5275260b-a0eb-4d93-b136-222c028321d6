#include <iostream>
#include <fstream>
#include <cstring>
#include <unistd.h>
#include <fcntl.h>
#include <linux/videodev2.h>
#include <sys/ioctl.h>
#include <errno.h>
#include <random>
#include <chrono>
#include <vector>
#include <string>


// 函数声明 - 移除默认参数
void apply_wolf240hz_patch(const char* device);

// 基础EDID数据模板 (240Hz优化)
unsigned char edid_template[] = {
    0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x5d, 0xec, 0x00, 0xf1, 0x01, 0x00, 0x00, 0x00,
    0x2d, 0x1f, 0x01, 0x03, 0x80, 0x78, 0x44, 0x78, 0x0a, 0xcf, 0x74, 0xa3, 0x57, 0x4c, 0xb0, 0x23,
    0x09, 0x48, 0x4c, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08, 0xe8, 0x00, 0x30, 0xf2, 0x70, 0x5a, 0x80, 0xb0, 0x58,
    0x8a, 0x00, 0xc0, 0x1c, 0x32, 0x00, 0x00, 0x1e, 0x5c, 0xc1, 0x80, 0x18, 0x71, 0x38, 0x2d, 0x40,
    0x58, 0x2c, 0x45, 0x00, 0xe0, 0x0e, 0x11, 0x00, 0x00, 0x1e, 0x06, 0xc4, 0x00, 0x80, 0x50, 0xc0,
    0x1e, 0x30, 0x30, 0x20, 0x35, 0x00, 0x40, 0xf0, 0x10, 0x00, 0x00, 0x1e, 0x00, 0xc6, 0x00, 0x80,
    0x50, 0xd0, 0x1e, 0x20, 0x30, 0x20, 0x35, 0x00, 0x40, 0xb4, 0x10, 0x00, 0x00, 0x1e, 0x01, 0x12,
    0x02, 0x03, 0x22, 0x70, 0x41, 0x61, 0x23, 0x09, 0x07, 0x07, 0x83, 0x01, 0x00, 0x00, 0x67, 0x03,
    0x0c, 0x00, 0x30, 0x00, 0x00, 0x78, 0x67, 0xd8, 0x5d, 0xc4, 0x01, 0x78, 0xc0, 0x00, 0xe3, 0x05,
    0x03, 0x01, 0x08, 0xe8, 0x00, 0x30, 0xf2, 0x70, 0x5a, 0x80, 0xb0, 0x58, 0x8a, 0x00, 0xc0, 0x1c,
    0x32, 0x00, 0x00, 0x1e, 0x08, 0xe8, 0x80, 0x18, 0x71, 0x38, 0x2d, 0x40, 0x58, 0x2c, 0x45, 0x00,
    0xe0, 0x0e, 0x11, 0x00, 0x00, 0x1e, 0xc2, 0xe2, 0xa0, 0x18, 0x51, 0x38, 0x2d, 0x40, 0x58, 0x2c,
    0x45, 0x00, 0x68, 0x0e, 0x11, 0x00, 0x00, 0x1e, 0x98, 0xe2, 0x00, 0xa0, 0xa0, 0xa0, 0x29, 0x50,
    0x30, 0x20, 0x35, 0x00, 0x80, 0x68, 0x21, 0x00, 0x00, 0x1e, 0xde, 0xd1, 0x00, 0xa0, 0xa0, 0x40,
    0x2e, 0x60, 0x30, 0x20, 0x36, 0x00, 0x80, 0x90, 0x21, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0xf7
};

// 真实主流显示器型号列表 (24寸-34寸，100个型号)
const std::vector<std::string> monitor_names = {
    // ASUS 华硕系列
    "PG248Q", "PG258Q", "PG259QN", "PG279Q", "PG279QM", "PG32UQ", "PG329Q", "PG348Q", "PG35VQ",
    "VG248QE", "VG258QR", "VG259Q", "VG27AQ", "VG27WQ", "VG32VQ", "VG34VQL1B", "XG248Q", "XG258Q",
    
    // Acer 宏碁系列
    "XF240H", "XF250Q", "XF252Q", "XV240Y", "XV272U", "XV273K", "XZ271U", "XZ321Q", "XZ342CK",
    "Predator X25", "Predator X27", "Predator X32", "Predator X34", "Nitro VG240Y", "Nitro VG270",
    
    // AOC 冠捷系列
    "24G2", "24G2U", "25G3ZM", "27G2", "27G2G5", "32G2", "CQ27G2", "CU34G2X", "AG273QCX",
    "AGON AG251FZ", "AGON AG271QX", "AGON AG322QC4", "AGON AG353UCG", "C24G1", "C27G1", "C32G1",
    
    // BenQ 明基系列
    "XL2411P", "XL2430T", "XL2540", "XL2546K", "XL2740", "EX2510", "EX2780Q", "EX3203R", "EX3501R",
    "ZOWIE XL2411K", "ZOWIE XL2546", "ZOWIE XL2740", "SW240", "SW270C", "SW320", "PD2500Q", "PD3200U",
    
    // LG 乐金系列
    "24GL600F", "25UM58", "27GL650F", "27GL83A", "27GN750", "32GK650F", "34GK950F", "34WK95U",
    "UltraGear 24GN650", "UltraGear 27GN850", "UltraGear 32GP850", "UltraGear 34GP83A", "27UP850",
    
    // Samsung 三星系列
    "C24FG73", "C27FG73", "C32HG70", "C34H890", "Odyssey G3", "Odyssey G5", "Odyssey G7", "Odyssey G9",
    "CRG5", "CRG9", "CF390", "CF591", "UR590C", "LU28E590DS", "C24RG50", "C27RG50", "C32G55T",
    
    // MSI 微星系列
    "Optix G24C4", "Optix G27C4", "Optix MAG274QRF", "Optix MPG27CQ", "Optix MPG341CQR", "MAG272CQR",
    "MAG274QRF-QD", "MAG321CQR", "MAG342CQR", "Optix G241", "Optix G271", "Optix G321CU", "MEG381CQR",
    
    // Dell 戴尔系列
    "S2419HGF", "S2721DGF", "S2721DS", "S3220DGF", "AW2518H", "AW2720HF", "AW3418DW", "AW3420DW",
    "U2419H", "U2720Q", "U3219Q", "U3421WE", "P2414H", "P2719H", "P3221D", "S2422HZ"
};

// 生成随机显示器名称并更新EDID
std::string generate_random_monitor_name() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, monitor_names.size() - 1);
    
    return monitor_names[dis(gen)];
}

// 计算EDID校验和
unsigned char calculate_edid_checksum(const unsigned char* edid, int block_size) {
    unsigned char sum = 0;
    for (int i = 0; i < block_size - 1; i++) {
        sum += edid[i];
    }
    return (256 - sum) & 0xFF;
}

// 更新EDID中的显示器名称
void update_monitor_name_in_edid(unsigned char* edid, const std::string& name) {
    bool name_updated = false;
    
    // 方法1: 在基础EDID块中查找显示器名称描述符 (0xFC)
    // 查找第一个128字节块中的描述符
    for (int i = 54; i < 126; i += 18) {
        // EDID描述符格式: 00 00 00 FC 00 [名称...]
        if (edid[i] == 0x00 && edid[i+1] == 0x00 && 
            edid[i+2] == 0x00 && edid[i+3] == 0xFC && 
            edid[i+4] == 0x00) {
            
            std::cout << "在基础块中找到显示器名称描述符，位置: " << i << std::endl;
            
            // 清除旧名称
            memset(&edid[i+5], 0x20, 13);
            
            // 写入新名称 (最多13字节)
            std::string display_name = name;
            if (display_name.length() > 13) {
                display_name = display_name.substr(0, 13);
            }
            
            memcpy(&edid[i+5], display_name.c_str(), display_name.length());
            
            // 确保以换行符结束
            if (display_name.length() < 13) {
                edid[i+5+display_name.length()] = 0x0A;
            }
            
            name_updated = true;
            std::cout << "✅ 已在基础块中更新显示器名称为: " << display_name << std::endl;
            break;
        }
    }
    
    // 方法2: 如果在基础块中没找到，在扩展块中查找
    if (!name_updated && sizeof(edid_template) > 128) {
        const int extension_block_start = 128;
        
        for (int i = extension_block_start + 4; i < 254; i++) {
            if (edid[i] == 0x00 && edid[i+1] == 0x00 && 
                edid[i+2] == 0x00 && edid[i+3] == 0xFC && 
                edid[i+4] == 0x00) {
                
                std::cout << "在扩展块中找到显示器名称描述符，位置: " << i << std::endl;
                
                // 清除旧名称
                memset(&edid[i+5], 0x20, 13);
                
                // 写入新名称
                std::string display_name = name;
                if (display_name.length() > 13) {
                    display_name = display_name.substr(0, 13);
                }
                
                memcpy(&edid[i+5], display_name.c_str(), display_name.length());
                
                if (display_name.length() < 13) {
                    edid[i+5+display_name.length()] = 0x0A;
                }
                
                name_updated = true;
                std::cout << "✅ 已在扩展块中更新显示器名称为: " << display_name << std::endl;
                break;
            }
        }
    }
    
    // 方法3: 如果都没找到，直接在预设位置插入名称描述符
    if (!name_updated) {
        std::cout << "⚠️  未找到现有的显示器名称描述符，在位置72创建新的描述符" << std::endl;
        
        // 在第一个可用的描述符位置 (通常是72) 创建新的名称描述符
        int pos = 72;
        edid[pos] = 0x00;
        edid[pos+1] = 0x00;
        edid[pos+2] = 0x00;
        edid[pos+3] = 0xFC;  // 显示器名称标识符
        edid[pos+4] = 0x00;
        
        // 清除名称区域
        memset(&edid[pos+5], 0x20, 13);
        
        // 写入新名称
        std::string display_name = name;
        if (display_name.length() > 13) {
            display_name = display_name.substr(0, 13);
        }
        
        memcpy(&edid[pos+5], display_name.c_str(), display_name.length());
        
        if (display_name.length() < 13) {
            edid[pos+5+display_name.length()] = 0x0A;
        }
        
        name_updated = true;
        std::cout << "✅ 已创建新的显示器名称描述符: " << display_name << std::endl;
    }
    
    if (!name_updated) {
        std::cout << "❌ 无法更新显示器名称" << std::endl;
    }
    
    // 重新计算校验和
    edid[127] = calculate_edid_checksum(edid, 128);  // 第一个块
    if (sizeof(edid_template) > 128) {
        edid[255] = calculate_edid_checksum(&edid[128], 128);  // 第二个块
    }
    
    std::cout << "🔧 已重新计算EDID校验和" << std::endl;
}

void apply_edid_patch(const char* device) {
    // 检查root权限
    if (geteuid() != 0) {
        std::cerr << "错误: 需要root权限来应用补丁" << std::endl;
        exit(1);
    }
    
    // 打开设备
    int fd = open(device, O_RDWR);
    if (fd < 0) {
        std::cerr << "错误: 无法打开设备 " << device << std::endl;
        perror("open");
        exit(1);
    }
    
    // 创建EDID副本，确保内存对齐
    unsigned char* edid_copy = new unsigned char[sizeof(edid_template)];
    memcpy(edid_copy, edid_template, sizeof(edid_template));
    
    // 生成随机显示器名称并更新EDID
    std::string random_name = generate_random_monitor_name();
    std::cout << "🎲 随机生成显示器名称: " << random_name << std::endl;
    update_monitor_name_in_edid(edid_copy, random_name);
    
    // 设置EDID数据
    struct v4l2_edid edid;
    memset(&edid, 0, sizeof(edid));
    edid.pad = 0;
    edid.start_block = 0;
    edid.blocks = sizeof(edid_template) / 128;
    edid.edid = edid_copy;
    
    std::cout << "尝试设置HDMI驱动数据, 共 " << edid.blocks << " 个块..." << std::endl;
    
    int ret = ioctl(fd, VIDIOC_S_EDID, &edid);
    if (ret < 0) {
        std::cerr << "错误: 设置HDMI驱动失败 (错误码: " << errno << ")" << std::endl;
        perror("ioctl");
        
        // 尝试诊断问题
        std::cout << "尝试获取设备信息以诊断问题..." << std::endl;
        struct v4l2_capability cap;
        if (ioctl(fd, VIDIOC_QUERYCAP, &cap) >= 0) {
            std::cout << "设备信息:" << std::endl;
            std::cout << "  驱动: " << cap.driver << std::endl;
            std::cout << "  卡名: " << cap.card << std::endl;
            std::cout << "  总线信息: " << cap.bus_info << std::endl;
            
            if (!(cap.capabilities & V4L2_CAP_VIDEO_OUTPUT)) {
                std::cout << "警告: 该设备可能不支持视频输出功能" << std::endl;
            }
        }
        
        delete[] edid_copy;
        close(fd);
        exit(1);
    }
    
    delete[] edid_copy;
    close(fd);
    std::cout << "成功应用高刷新率补丁到 " << device << std::endl;
}

// 新增：可被外部调用的补丁应用函数
void apply_wolf240hz_patch(const char* device) {
    std::cout << "WOLF 240Hz 补丁工具" << std::endl;
    std::cout << "=========================" << std::endl;
    
    std::cout << "正在应用补丁到设备: " << device << std::endl;
    apply_edid_patch(device);
    
    std::cout << "补丁应用成功! 你现在应该可以使用更高的刷新率了。" << std::endl;
    std::cout << "如果没有立即生效，请尝试重新插拔显示器或重启系统。" << std::endl;
}


