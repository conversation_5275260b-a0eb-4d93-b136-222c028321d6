#pragma once
#include <string>
#include <vector>
#include <unordered_map>
#include <array>
#include <cstdint>
#include <atomic>
#include <mutex>
#include <chrono>  // 添加chrono头文件
#include <memory>  // 添加智能指针支持
#include <mutex>   // 添加mutex支持
#include <condition_variable>  // 添加condition_variable支持

#include "../infer/base/types.h"  // 引入u32等类型定义
#include "../infer/base/wsq.h"  // 引入WorkStealingQueue

// 白名单文件列表相关
namespace whitelist {
    // 允许打印日志的文件白名单列表
    extern std::vector<std::string> g_allowed_files;  // 存放允许打印日志的文件列表
}

// 日志系统全局变量
namespace logger {
    extern bool g_log_enabled;              // 是否启用日志打印：true表示打印日志，false表示不打印
    extern bool g_whitelist_enabled;        // 是否启用白名单
    extern int g_current_level;             // 当前日志级别
}

// UI全局变量统一管理
namespace webui {
    // 登录相关
    namespace login {
        // 用户认证相关常量
        extern const std::string DEFAULT_USERNAME;     // 默认用户名
        extern const std::string DEFAULT_PASSWORD;     // 默认密码
        extern const std::string DEFAULT_TOKEN;        // 默认token值
    }
    // 注册相关
    namespace myregister {
        extern std::vector<std::string> g_game_list;    // 游戏列表
        extern std::string defaultGame;                 // 默认游戏
        extern const std::string username;     // 默认用户名
        extern const std::string password;     // 默认密码
        extern const std::string token;        // 默认token值
    }
    // 页头
    namespace header {
        extern std::string g_game_name;
        extern bool databaseStatus;    // 数据库连接状态：true表示已连接，false表示未连接
        extern bool cardKeyStatus;    // 卡密验证状态：true表示卡密有效，false表示无效
        extern bool keyMouseStatus;   // 键鼠控制状态：true表示正常，false表示异常
        extern bool inferenceStatus; // AI推理状态：true表示推理引擎正常，false表示异常
        extern bool isPro;           // Pro版本状态：true表示Pro版本，false表示标准版本
        extern double cpuTemperature; // CPU温度监控 - 单位：摄氏度
        extern double cpuUsage; // CPU使用率监控 - 百分比值（0-100）
        extern uint32_t frame_rate; // HDMI输入的刷新率 - 单位：Hz
        extern uint32_t width;     // HDMI输入的宽度 - 单位：像素
        extern uint32_t height;    // HDMI输入的高度 - 单位：像素
        extern std::string currentVersion;  // 当前版本号
        extern std::string latestVersion;   // 最新版本号
        extern bool versionUpdateRequested; // 版本更新请求标志
    }
    
    // Home页面相关
    namespace home {
        // 用户名 - 对应数据库中的username字段，用于标识当前选择的用户
        extern std::string g_username;
        extern std::string g_game_name;         // 当前选择的游戏ID，对应schema中的game_name
        extern std::string g_card_key;                 // 当前使用的卡密
        extern bool forceAuthNow;        // 强制验证标志
    }

    // 功能设置相关
    namespace function {
        // 配置结构体定义
        struct Config {
            std::string ai_mode;          // AI模式：PID, FOV, FOVPID
            std::string lock_position;    // 锁定位置：头部，颈部，胸部
            std::string selected_faction; // 阵营选择：警方，匪方，无
            std::string hotkey;           // 触发热键：左键，右键，中键等
            std::string trigger_switch;   // 扳机开关：true表示开启，false表示关闭
            std::string weapon_switch;    // 切枪功能开关：true表示开启，false表示关闭
            std::string flash_shield;     // 背闪防护开关：true表示开启，false表示关闭
            std::string enabled;          // 是否启用：true表示启用，false表示不启用
            std::string name;             // 配置名称
        };

        // 预设配置
        extern std::vector<Config> PRESET_CONFIGS;
        
        // 当前激活的配置索引
        extern int g_active_config_index;

        // 当前配置参数（使用普通变量）
        extern std::string g_ai_mode;
        extern std::string g_lock_position;
        extern std::string g_hotkey;
        extern bool g_trigger_switch;           // 当前扳机开关状态：从PRESET_CONFIGS[g_active_config_index].trigger_switch同步
    }

    // PID配置相关
    namespace pid {
        extern double g_near_move_factor;        // 近端移动速度
        extern double g_near_stabilizer;         // 近端跟踪速度
        extern double g_near_response_rate;      // 近端抖动力度
        extern double g_near_assist_zone;        // 近端死区大小
        extern double g_near_response_delay;     // 近端回弹速度
        extern double g_near_max_adjustment;     // 近端积分限制
        extern double g_far_factor;              // 远端系数
        extern double g_y_axis_factor;           // Y轴系数
        extern double g_pid_random_factor;       // PID随机系数
    }

    // FOV配置相关
    namespace fov {
        extern double g_fov;             // 视野范围
        extern int g_fov_time;          // 视野时间
        extern bool g_start_measure;    // FOV测量标志，true表示正在测量中
    }

    // 瞄准配置相关
    namespace aim {
        extern int g_aim_range;               // 瞄准范围(0-416)
        extern float g_track_range;             // 跟踪范围(0-100)
        extern double g_head_height;             // 头部高度(0-100)
        extern double g_neck_height;             // 颈部高度(0-100)
        extern double g_chest_height;            // 胸部高度(0-100)
        extern double g_head_range_x;            // 头部X方向范围(0-100)
        extern double g_head_range_y;            // 头部Y方向范围(0-100)
        extern double g_neck_range_x;            // 颈部X方向范围(0-100)
        extern double g_neck_range_y;            // 颈部Y方向范围(0-100)
        extern double g_chest_range_x;           // 胸部X方向范围(0-100)
        extern double g_chest_range_y;           // 胸部Y方向范围(0-100)
    }

    // 开火配置相关
    namespace fire {
        extern int g_rifle_sleep;        // 步枪开火睡眠时间(ms)
        extern int g_rifle_interval;     // 步枪开火间隔(ms)
        extern int g_pistol_sleep;       // 手枪开火睡眠时间(ms)
        extern int g_pistol_interval;    // 手枪开火间隔(ms)
        extern int g_sniper_sleep;       // 狙击开火睡眠时间(ms)
        extern int g_sniper_interval;    // 狙击开火间隔(ms)
    }

    // 网络相关 - 只保留cpp中存在的变量
    namespace network {
        extern std::string g_server_ip;       // 服务器IP
        extern int g_server_port;             // 服务器端口
    }

    // 数据采集相关 - 调整为与cpp文件一致
    namespace collect {
        extern bool g_is_enabled;                   // 是否启用数据收集
        extern std::string g_map_hotkey;            // 地图数据收集热键
        extern std::string g_target_hotkey;         // 目标数据收集热键
        extern std::string g_team_side;             // 阵营选择
        extern std::string g_collection_name;       // 数据收集名称
        extern bool g_map_hotkey_pressed;           // 地图热键状态 - 使用翻转模式控制
        extern bool g_target_hotkey_pressed;        // 目标热键状态 - 使用翻转模式控制
    }

    // 数据库相关
    namespace database {
        extern std::string g_db_host;         // 数据库主机地址
        extern int g_db_port;                 // 数据库端口
        extern std::string g_db_name;         // 数据库名称
        extern std::string g_db_user;         // 数据库用户名
        extern std::string g_db_password;     // 数据库密码
        extern std::string g_db_root_user;    // 数据库root用户名
        extern std::string g_db_root_password;// 数据库root密码
        extern int g_db_timeout;              // 数据库连接超时时间(ms)
        extern int g_db_pool_size;            // 数据库连接池大小
    }
}

// WebSocket全局变量和配置
namespace websocket {
    // WebSocket服务配置
    extern std::string g_ws_ip;                // WebSocket服务IP
    extern uint16_t g_ws_port;                // WebSocket服务端口
    // 客户端连接相关
    extern std::atomic<int> g_client_count;   // 当前连接的客户端数量
    extern std::atomic<uint64_t> g_message_count; // 已处理的消息数量
    
    // 事件回调
    extern bool g_log_messages;               // 是否记录消息
    extern int g_max_connections;             // 最大连接数
    extern int g_connection_timeout;          // 连接超时时间(秒)
    
    // 轮询间隔
    extern int g_ws_poll_interval;            // WebSocket消息处理轮询间隔(毫秒)
    
    // 服务状态
    extern bool g_ws_server_running;          // WebSocket服务运行状态
}

// 线程开关控制
namespace thread_control {
    extern bool g_thread_running;
    extern bool g_infer_running;
}


// YOLO配置相关变量
namespace yolo_config {
    // 游戏配置结构体
    struct GameConfig {
        float box_thresh;         // 检测框阈值
        float nms_thresh;         // 非极大值抑制阈值
        int obj_class_num;        // 目标类别数量
        int obj_numb_max_size;    // 最大目标数量
        int obj_name_max_size;    // 目标名称最大长度
        std::vector<std::string> class_names; // 类别名称列表
        std::string weight_path;  // 权重文件路径
    };

    extern std::string g_model_path;       // 模型文件路径
    extern bool g_reload_model;            // 是否重载模型
    extern int g_yolo_class_num;           // 当前YOLO模型的类别数量（可配置）
    
    // 修改为结构体映射，替代JSON字符串
    extern std::unordered_map<std::string, GameConfig> g_game_configs;
    
    // 工具函数：根据游戏名称更新YOLO类别数量
    void UpdateYoloClassNum(const std::string& game_name);
}


// 鼠标和键盘协议相关定义
namespace lkm {
    // 鼠标按键定义 - 全局常量
    // 基本按键
    extern const uint8_t MOUSE_NONE;   // 无按键状态
    extern const uint8_t MOUSE_LEFT;   // 左键
    extern const uint8_t MOUSE_RIGHT;  // 右键
    extern const uint8_t MOUSE_MIDDLE; // 中键/滚轮按键
    extern const uint8_t MOUSE_SIDE1;  // 侧键1
    extern const uint8_t MOUSE_SIDE2;  // 侧键2
    
    // 组合按键
    extern const uint8_t MOUSE_LEFT_RIGHT;   // 左键+右键
    extern const uint8_t MOUSE_LEFT_MIDDLE;  // 左键+中键
    extern const uint8_t MOUSE_RIGHT_MIDDLE; // 右键+中键
    extern const uint8_t MOUSE_LEFT_SIDE1;   // 左键+侧键1
    extern const uint8_t MOUSE_RIGHT_SIDE1;  // 右键+侧键1
    extern const uint8_t MOUSE_LEFT_SIDE2;   // 左键+侧键2
    extern const uint8_t MOUSE_RIGHT_SIDE2;  // 右键+侧键2
    
    // 键盘按键定义 - 全局常量
    // 修饰键
    extern const uint8_t KB_NONE;       // 无按键状态
    extern const uint8_t KB_LEFT_CTRL;  // 左Ctrl键
    extern const uint8_t KB_LEFT_SHIFT; // 左Shift键
    extern const uint8_t KB_LEFT_ALT;   // 左Alt键
    extern const uint8_t KB_LEFT_WIN;   // 左Win键
    extern const uint8_t KB_RIGHT_CTRL; // 右Ctrl键
    extern const uint8_t KB_RIGHT_SHIFT; // 右Shift键
    extern const uint8_t KB_RIGHT_ALT;  // 右Alt键
    extern const uint8_t KB_RIGHT_WIN;  // 右Win键
    
    // 常用键盘HID码定义
    extern const uint8_t HID_KEY_A;      // A键 (0x04)
    extern const uint8_t HID_KEY_B;      // B键 (0x05)
    extern const uint8_t HID_KEY_C;      // C键 (0x06)
    extern const uint8_t HID_KEY_D;      // D键 (0x07)
    extern const uint8_t HID_KEY_E;      // E键 (0x08)
    extern const uint8_t HID_KEY_ENTER;  // 回车键 (0x28)
    extern const uint8_t HID_KEY_ESC;    // ESC键 (0x29)
    extern const uint8_t HID_KEY_SPACE;  // 空格键 (0x2C)
    
    // 协议常量定义 - 统一使用闪电键鼠标准协议
    extern const uint8_t LIGHTNING_PROTOCOL_HEADER[4];  // 闪电键鼠协议头部 {0xFF, 0xAA, 0x55, 0xFE}
    
    // 通用协议常量
    extern const size_t PACKET_SIZE;          // 数据包大小 15字节 (帧头4 + 按键状态5 + 坐标4 + 滚轮1 + 校验1)
    extern const uint8_t CRC_INIT_VALUE;      // CRC初始值 0xAA
    extern const int STANDARD_BAUD_RATE;      // 标准波特率 230400
    
    // 重连相关参数
    extern const int MAX_RECONNECT_ATTEMPTS;  // 最大重连次数
    extern const int RECONNECT_DELAY_MS;      // 重连延迟(毫秒)
    
    // 鼠标状态结构体
    struct MouseState {
        uint8_t buttons{0};   // 按键状态
        int16_t x{0};         // X坐标变化量
        int16_t y{0};         // Y坐标变化量
        int8_t wheel{0};      // 滚轮变化
    } __attribute__((packed));
    
    // 键盘状态结构体
    struct KeyboardState {
        uint8_t modifiers{0};     // 修饰键状态（Ctrl, Shift, Alt, Win）
        uint8_t keys[6]{0};       // 最多6个按键的同时按下
    } __attribute__((packed));
}

// 核心变量命名空间，存放全局共享的核心变量
namespace core_vars {
    // 目标高度
    extern double target_height;
    // 目标宽度
    extern double target_width;
    // 鼠标移动相关
    extern double mouse_x;             // 鼠标X轴移动量
    extern double mouse_y;             // 鼠标Y轴移动量

    // -------------------测量FOV用-------------------
    // 定义目标中心点
    extern double target_center_x;
    extern double target_center_y;
    extern double target_distance;
    extern double target_width;
    extern double screen_center_x;
    extern double screen_center_y;
    // -------------------测量FOV用-------------------
    
    // 键鼠状态-鼠标按键是否被按下
    extern bool mouse_pressed;      // 鼠标按键是否被按下

    // 移动值
    extern int move_x;
    extern int move_y;
    
    // 自动开火随机系数相关变量
    extern double g_fire_sleep_coeff;    // 开火睡眠时间随机系数（0.8-1.2）
    extern double g_fire_interval_coeff; // 开火间隔时间随机系数（0.8-1.2）
    
    // PID控制器随机系数相关变量
    extern double g_near_pid_coeff;      // 近端PID系数随机变量（0.8-1.2）
    extern double g_far_pid_coeff;       // 远端PID系数随机变量（0.8-1.2）
    
    // 背闪检测变量（仅用于wwqy游戏）
    extern bool g_flash_detected;        // 背闪检测状态：true表示检测到背闪，false表示未检测到
}

// 卡密系统配置相关全局变量
namespace jfkm_config {
    // 卡密API访问密钥
    extern std::string g_access_key;
    
    // 卡密API主机地址 - 普通版
    extern std::string g_api_host_standard;
    
    // 卡密API主机地址 - Pro版
    extern std::string g_api_host_pro;
    
    // 应用标识
    extern std::string g_app_flag;
    
    // 当前卡密
    extern std::string g_card_key;
    
    // 验证状态: 0=未验证, 1=已验证, 2=验证失败
    extern std::atomic<int> g_auth_status;
}

// WSQ流水线架构相关定义
namespace wsq_models {

// WSQ命令数据结构
struct WSQCommand {
    // 基础信息
    bool has_target = false;              // 是否检测到目标
    int64_t frame_id = 0;                 // 帧ID，用于去重
    std::chrono::high_resolution_clock::time_point timestamp; // 时间戳
    
    // 注意：移除了复杂的need_标记，直接使用原始数据字段进行线程分工
    
    // 目标信息
    double target_x = 0, target_y = 0;    // 目标中心坐标
    double aim_y = 0;                     // 锁定部位调整后Y坐标
    double target_distance = 0;           // 目标距离(用于近远端判断)
    
    // 移动控制
    int move_x = 0, move_y = 0;           // 预计算移动量
    bool should_move = false;             // 是否需要移动
    
    // 开火控制
    bool g_is_fire = false;               // 开火状态控制
    
    // 特殊功能
    bool flash_detected = false;          // 背闪检测
    bool weapon_switch = false;           // 切枪功能
    bool fov_measure = false;             // FOV测量
};

// 使用现有的WorkStealingQueue模板
using CommandWSQ = WorkStealingQueue<WSQCommand>;

// WSQ队列全局实例声明
extern std::unique_ptr<CommandWSQ> g_command_queue;

// WSQ队列同步机制 - 与npu_work_queue完全相同的高性能机制
extern std::mutex g_wsq_mutex;
extern std::condition_variable g_wsq_cv;

// WSQ多线程架构 - 完全模仿NPU架构
extern const u32 g_wsq_thread_count;           // WSQ执行器线程数量（默认3个，与NPU相同）
extern std::atomic<u32> g_next_wsq_thread_index; // 原子轮询分配索引

} // namespace wsq_models