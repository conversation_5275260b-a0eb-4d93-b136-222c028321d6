cmake_minimum_required(VERSION 3.10)

# 创建utils静态库，明确列出所有源文件
add_library(utils STATIC
    ${CMAKE_CURRENT_SOURCE_DIR}/utils/gmodels.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/utils/gmodels.h
    ${CMAKE_CURRENT_SOURCE_DIR}/utils/logger.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/utils/logger.h
    ${CMAKE_CURRENT_SOURCE_DIR}/utils/wolf240hz_patch.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/utils/wolf240hz_patch.h
)

# 添加包含路径
target_include_directories(utils PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 链接所需的库
target_link_libraries(utils PUBLIC 
    fmt::fmt
    spdlog::spdlog
    ${ATOMIC_LIBRARY}
) 