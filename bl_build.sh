#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目名称
EDGE_AI_EXEC="BL"
BUILD_DIR="build"
BIN_DIR="${BUILD_DIR}/bin"

# 清空控制台
clear_console() {
    clear
}

# 检查脚本是否具有执行权限
check_permissions() {
    if [ ! -x "$0" ]; then
        echo -e "${YELLOW}添加执行权限到脚本...${NC}"
        chmod +x "$0"
        echo -e "${GREEN}执行权限已添加，请重新运行脚本${NC}"
        exit 0
    fi
}

# 检查是否安装了必要的工具
check_dependencies() {
    if ! command -v cmake &> /dev/null; then
        echo -e "${RED}错误: 未安装CMake${NC}"
        exit 1
    fi
    if ! command -v make &> /dev/null; then
        echo -e "${RED}错误: 未安装Make${NC}"
        exit 1
    fi
}

# 编译项目
compile() {
    echo -e "${YELLOW}开始编译BL...${NC}"
    
    # 保存当前目录
    CURRENT_DIR=$(pwd)
    
    # 创建构建目录
    if [ ! -d "$BUILD_DIR" ]; then
        mkdir -p "$BUILD_DIR"
    fi
    
    # 设置build目录的最高权限，确保编译过程中不会遇到权限问题
    echo -e "${YELLOW}设置build目录权限...${NC}"
    sudo chmod -R 777 "$BUILD_DIR"
    
    # 运行CMake，明确指定源目录和构建目录
    echo -e "${YELLOW}运行CMake配置...${NC}"
    cmake -S . -B "${BUILD_DIR}"
    if [ $? -ne 0 ]; then
        echo -e "${RED}CMake配置失败${NC}"
        return 1
    fi
    
    # 切换到构建目录
    cd "${BUILD_DIR}" || exit 1
    
    # 只编译BL目标
    echo -e "${YELLOW}编译BL目标...${NC}"
    make -j$(nproc) BL
    if [ $? -ne 0 ]; then
        echo -e "${RED}编译失败${NC}"
        cd "$CURRENT_DIR"
        return 1
    fi
    
    # 验证可执行文件是否存在
    if [ -f "bin/${EDGE_AI_EXEC}" ]; then
        echo -e "${GREEN}编译成功！${NC}"
        echo -e "${GREEN}可执行文件位置: bin/${EDGE_AI_EXEC}${NC}"
    else
        echo -e "${RED}编译完成，但找不到可执行文件。查找可能的位置...${NC}"
        # 尝试在其他路径查找
        EXEC_PATH=$(find . -name "${EDGE_AI_EXEC}")
        if [ -n "$EXEC_PATH" ]; then
            echo -e "${GREEN}找到可执行文件: $EXEC_PATH${NC}"
        else
            echo -e "${RED}无法找到可执行文件${NC}"
            cd "$CURRENT_DIR"
            return 1
        fi
    fi
    
    # 复制模型文件
    echo -e "${YELLOW}复制模型文件到bin目录...${NC}"
    
    # 直接复制整个model文件夹
    MODEL_SRC_DIR="../infer/model"
    if [ -d "${MODEL_SRC_DIR}" ]; then
        cp -r "${MODEL_SRC_DIR}" "bin/"
        echo -e "${GREEN}已复制: model文件夹${NC}"
    else
        echo -e "${RED}无法找到模型文件夹: ${MODEL_SRC_DIR}${NC}"
    fi
    
    # 复制RKNN库文件
    echo -e "${YELLOW}复制RKNN运行环境到bin/lib目录...${NC}"
    
    # 创建lib目录
    if [ ! -d "bin/lib" ]; then
        mkdir -p "bin/lib"
    fi
    
    # 复制librknnrt.so文件
    RKNN_LIB_SRC="../3rdparty/rknn/linux/librknnrt.so"
    if [ -f "${RKNN_LIB_SRC}" ]; then
        cp "${RKNN_LIB_SRC}" "bin/lib/"
        echo -e "${GREEN}已复制: librknnrt.so${NC}"
    else
        echo -e "${RED}无法找到RKNN库文件: ${RKNN_LIB_SRC}${NC}"
    fi
    
    # 手动复制RGA库文件
    echo -e "${YELLOW}复制RGA库到bin/lib目录...${NC}"
    RGA_LIB_SRC="../3rdparty/librga/linux/librga.a"
    if [ -f "${RGA_LIB_SRC}" ]; then
        cp "${RGA_LIB_SRC}" "bin/lib/"
        echo -e "${GREEN}已复制: librga.a${NC}"
    else
        echo -e "${RED}无法找到RGA库文件: ${RGA_LIB_SRC}${NC}"
    fi
    
    # 返回原始目录
    cd "$CURRENT_DIR"
    return 0
}

# 运行BL主程序
run_BL() {
    echo -e "${YELLOW}运行BL主程序...${NC}"
    
    # 保存当前目录
    CURRENT_DIR=$(pwd)
    
    # 检查可执行文件是否存在
    EXEC_PATH="${BIN_DIR}/${EDGE_AI_EXEC}"
    if [ ! -f "$EXEC_PATH" ]; then
        echo -e "${YELLOW}在默认路径未找到可执行文件，尝试查找...${NC}"
        EXEC_PATH=$(find "${BUILD_DIR}" -name "${EDGE_AI_EXEC}")
        if [ -n "$EXEC_PATH" ]; then
            echo -e "${GREEN}找到可执行文件: $EXEC_PATH${NC}"
        else
            echo -e "${RED}错误: ${EDGE_AI_EXEC}可执行文件不存在，请先编译项目${NC}"
            read -p "按Enter键继续..."
            return 1
        fi
    fi
    
    # 设置串口设备权限
    for port in /dev/ttyUSB* /dev/ttyACM*; do
        if [ -e "$port" ]; then
            echo -e "${YELLOW}设置串口设备权限: $port${NC}"
            sudo chmod 666 "$port" || echo -e "${RED}无法设置权限，可能需要管理员权限${NC}"
        fi
    done
    
    # 运行程序
    echo -e "${GREEN}启动BL主程序...${NC}"
    echo -e "${YELLOW}按Ctrl+C退出${NC}"
    
    # 尝试不同的显示选项（取消注释一个选项使用）
    
    # 选项1: 使用本地显示（如果有本地X服务器）
    # export DISPLAY=:0
    # echo -e "${GREEN}使用本地显示: DISPLAY=${DISPLAY}${NC}"
    
    # 选项2: 使用远程X服务器（需要在远程服务器允许连接）
    export DISPLAY=192.168.5.50:0
    echo -e "${GREEN}使用远程显示: DISPLAY=${DISPLAY}${NC}"
    
    # 选项3: 使用Qt VNC平台（不需要X服务器）
    # export QT_QPA_PLATFORM=vnc
    # echo -e "${GREEN}使用VNC平台: QT_QPA_PLATFORM=${QT_QPA_PLATFORM}${NC}"
    
    # 选项4: 使用离线渲染（无界面模式）
    # export QT_QPA_PLATFORM=offscreen
    # echo -e "${GREEN}使用离线渲染: QT_QPA_PLATFORM=${QT_QPA_PLATFORM}${NC}"
    
    # 设置RKNN库路径
    LIB_PATH="$(dirname "$EXEC_PATH")/lib"
    export LD_LIBRARY_PATH="$LIB_PATH:$LD_LIBRARY_PATH"
    echo -e "${GREEN}已设置库路径: LD_LIBRARY_PATH=${LIB_PATH}${NC}"
    
    # 切换到可执行文件所在目录，确保相对路径正确
    EXEC_DIR="$(dirname "$EXEC_PATH")"
    cd "$EXEC_DIR"
    echo -e "${GREEN}已切换到可执行文件目录: $(pwd)${NC}"
    
    # 列出当前目录和model目录下的文件，帮助调试
    echo -e "${YELLOW}当前目录下的文件:${NC}"
    ls -la
    
    echo -e "${YELLOW}model目录下的文件:${NC}"
    ls -la model/ || echo -e "${RED}model目录不存在${NC}"
    
    # 执行程序并捕获退出状态
    "./${EDGE_AI_EXEC}"
    RUN_STATUS=$?
    
    if [ $RUN_STATUS -ne 0 ]; then
        echo -e "${RED}程序异常退出，退出码: $RUN_STATUS${NC}"
    fi
    
    # 返回原始目录
    cd "$CURRENT_DIR"
    
    # 暂停以便查看输出
    read -p "按Enter键继续..."
    
    return $RUN_STATUS
}

# 停止所有BL进程
stop_BL() {
    echo -e "${YELLOW}正在停止所有BL进程...${NC}"
    
    # 更精确地查找所有BL进程，避免误抓取grep本身
    EDGE_AI_PIDS=$(pgrep -f "${EDGE_AI_EXEC}" || echo "")
    
    if [ -n "$EDGE_AI_PIDS" ]; then
        echo -e "${GREEN}找到以下BL进程:${NC}"
        
        # 首先尝试直接使用SIGKILL终止所有进程
        # 这是最强硬的方式，但对于出现Segmentation fault的进程最有效
        for pid in $EDGE_AI_PIDS; do
            echo "  - PID: $pid"
            echo -e "${YELLOW}直接发送SIGKILL强制终止进程...${NC}"
            kill -9 $pid 2>/dev/null
        done
        
        # 等待短暂时间让SIGKILL生效
        sleep 2
        
        # 检查是否还有未终止的进程
        REMAINING=$(pgrep -f "${EDGE_AI_EXEC}" || echo "")
        if [ -n "$REMAINING" ]; then
            echo -e "${RED}警告：使用SIGKILL后仍有进程存在，尝试更极端的方法...${NC}"
            
            # 使用pkill尝试终止所有相关进程
            echo -e "${YELLOW}使用pkill -9尝试终止所有BL相关进程...${NC}"
            pkill -9 -f "${EDGE_AI_EXEC}" 2>/dev/null
            
            # 再次等待
            sleep 2
            
            # 最后一次检查
            FINAL_CHECK=$(pgrep -f "${EDGE_AI_EXEC}" || echo "")
            if [ -n "$FINAL_CHECK" ]; then
                echo -e "${RED}严重警告：以下BL进程无法终止: $FINAL_CHECK${NC}"
                echo -e "${RED}请手动使用'sudo kill -9 $FINAL_CHECK'或重启系统${NC}"
            else
                echo -e "${GREEN}所有BL进程已最终终止${NC}"
            fi
        else
            echo -e "${GREEN}所有BL进程已成功终止${NC}"
        fi
    else
        echo -e "${YELLOW}未找到运行中的BL进程${NC}"
    fi
    
    # 额外清理：查找和终止可能的zombie或孤儿进程
    ZOMBIE_PIDS=$(ps -ef | grep defunct | grep -v grep | awk '{print $2}')
    if [ -n "$ZOMBIE_PIDS" ]; then
        echo -e "${YELLOW}发现zombie进程，尝试清理...${NC}"
        for zpid in $ZOMBIE_PIDS; do
            kill -9 $zpid 2>/dev/null
        done
    fi
}

# 清理并重新编译
clean_and_compile() {
    echo -e "${YELLOW}清理并重新编译BL...${NC}"
    
    # 保存当前目录
    CURRENT_DIR=$(pwd)
    
    # 停止所有运行中的进程
    stop_BL
    
    # 删除旧的构建文件
    if [ -d "$BUILD_DIR" ]; then
        echo -e "${YELLOW}删除旧的构建文件...${NC}"
        sudo rm -rf "${BUILD_DIR}"
    fi
    
    # 创建新的build目录并设置权限
    mkdir -p "$BUILD_DIR"
    echo -e "${YELLOW}设置build目录权限...${NC}"
    sudo chmod -R 777 "$BUILD_DIR"
    
    # 重新编译
    compile
    
    # 返回原始目录
    cd "$CURRENT_DIR"
}

# 列出可用串口
list_serial_ports() {
    echo -e "${YELLOW}正在扫描可用串口设备...${NC}"
    
    # 检查常见的串口设备
    echo -e "${GREEN}USB串口设备:${NC}"
    ls -l /dev/ttyUSB* 2>/dev/null || echo "  无USB串口设备"
    
    echo -e "${GREEN}ACM串口设备:${NC}"
    ls -l /dev/ttyACM* 2>/dev/null || echo "  无ACM串口设备"
    
    echo -e "${GREEN}标准串口设备:${NC}"
    ls -l /dev/ttyS* 2>/dev/null || echo "  无标准串口设备"
    
    # 使用dmesg检查最近连接的串口设备
    echo -e "\n${GREEN}最近连接的串口设备信息:${NC}"
    dmesg | grep -E "tty(USB|ACM|S)" | tail -10
    
    read -p "按Enter键继续..."
}

# 显示菜单
show_menu() {
    clear_console
    echo -e "\n${GREEN}===== BL构建与运行工具 =====${NC}"
    echo "1. 编译项目"
    echo "2. 清理并重新编译"
    echo "3. 运行BL主程序"
    echo "4. 停止BL进程"
    echo "5. 列出可用串口设备"
    echo "6. 退出"
    echo -e "${YELLOW}请选择操作 (1-6): ${NC}"
}

# 主循环
main() {
    # 启动时清空控制台
    clear_console
    check_permissions
    check_dependencies
    
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                compile
                read -p "按Enter键继续..."
                ;;
            2)
                clean_and_compile
                read -p "按Enter键继续..."
                ;;
            3)
                run_BL
                ;;
            4)
                stop_BL
                read -p "按Enter键继续..."
                ;;
            5)
                list_serial_ports
                ;;
            6)
                echo -e "${GREEN}退出程序${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                read -p "按Enter键继续..."
                ;;
        esac
    done
}

# 运行主函数
main 