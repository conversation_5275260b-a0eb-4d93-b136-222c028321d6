# EdgeAI 启动与停止说明

## 启动方式

EdgeAI支持两种运行模式：
1. **APP版本**：默认的EdgeAI应用程序
2. **Web版本**：通过Web界面访问的版本

## APP版本（默认）

### 安装screen（如未安装）
```bash
sudo pacman -S screen
```

### 一键启动APP版本
```bash
screen -dmS edgeai-app bash -c "cd $(pwd) && ./start.sh; exec bash"
```
### 一键停止APP版本
```bash
# 停止所有edgeai-app会话（推荐）
pkill -f "SCREEN.*edgeai-app"
```

### 查看APP版本运行状态
```bash
### 查看APP版本日志
```bash
screen -r edgeai-app
```

## Web版本

### 一键启动Web版本
```bash
screen -dmS edgeai-web bash -c "cd $(pwd) && (./start.sh &) && sleep 2 && ./pyserve.sh; exec bash"
```

### 一键停止Web版本
```bash
pkill -f "SCREEN.*edgeai-web" && sudo pkill -9 EdgeAI && sudo pkill -9 -f "python.*spa_server.py"
```

### 查看Web版本日志
```bash
# 查看Web服务器和后台服务日志
screen -r edgeai-web
```

### 访问Web界面
启动后通过浏览器访问：
- 默认地址：http://localhost:8000
- 备用地址：http://localhost:8001（当默认端口被占用时）

## 通用操作

### 连接到运行中的会话后的操作
- 查看日志：程序输出会直接显示在屏幕上
- 分离会话（保持程序在后台运行）：按下 `Ctrl+A` 然后按 `D`
- 停止程序：按 `Ctrl+C`

### 强制终止进程
```bash
# 终止所有EdgeAI进程
sudo pkill -9 EdgeAI

# 终止所有Web服务器进程
sudo pkill -9 -f "python.*spa_server.py"
```

## 注意事项
- 确保脚本具有执行权限：`chmod +x start.sh pyserve.sh`
- 系统重启后需要重新启动程序
- 如需自启动，可将启动命令添加到系统自启动配置中
- APP版本和Web版本可以同时运行
