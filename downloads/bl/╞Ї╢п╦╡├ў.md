# EdgeAI 启动与停止说明

## 启动方式

EdgeAI支持两种运行模式：
1. **APP版本**：默认的EdgeAI应用程序
2. **Web版本**：通过Web界面访问的版本

## APP版本（默认）

### 安装screen（如未安装）
```bash
sudo pacman -S screen
```


### 导入命令：
```json
{"commands":[{"id":"19be9830-f4b3-4014-bd1d-802307fe0756","name":"删除版本文件夹","command":"rm -rf ~/bl","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T10:32:09.773941","updatedAt":"2025-05-14T20:39:51.044971"},{"id":"41829726-11f6-40ef-8e0a-e26ed235e965","name":"启动","command":"cd ~ && rm -rf ~/bl && unzip -o bl.zip && chmod -R 777 ~/bl && screen -dmS edgeai-web bash -c \"cd ~/bl && (./start.sh &) && sleep 2 && ./pyserve.sh; exec bash\"","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T11:39:37.188537","updatedAt":"2025-05-14T11:39:37.188537"},{"id":"05868ec0-b6ee-4abb-b9e6-3c756536a5fe","name":"停止","command":"pkill -f \"SCREEN.*edgeai-web\" && sudo pkill -9 EdgeAI && sudo pkill -9 -f \"python.*spa_server.py\"","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T11:39:54.514570","updatedAt":"2025-05-14T11:39:54.514570"},{"id":"f01bcf20-93c8-49d5-8a7f-5dacd2a5aa9e","name":"高刷","command":"cd ~ && sudo chmod -R 777 wolf240hz_patch && sudo ./wolf240hz_patch","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T11:40:12.049024","updatedAt":"2025-05-14T11:40:12.049024"},{"id":"9950cd5d-d1fb-4c21-b070-a89973062e4b","name":"ping卡密情况","command":"ping antkma.antszy.com","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T22:53:26.739549","updatedAt":"2025-05-14T22:53:26.739549"}],"sessions":[{"id":"42391bca-2439-46c3-8631-d8fc9c4f3eca","name":"bred@************:22","host":"************","port":22,"username":"bred","password":"bred","isFavorite":false,"createdAt":"2025-05-14T11:39:11.077678","lastConnectedAt":"2025-05-14T22:52:46.014633"},{"id":"fad9deac-64d3-48f6-9c1b-25e9f0c0630c","name":"bred@************:22","host":"************","port":22,"username":"bred","password":"bred","isFavorite":false,"createdAt":"2025-05-14T08:55:17.459865","lastConnectedAt":"2025-05-14T22:52:46.014043"}],"exportTime":"2025-05-14T22:53:42.883868","version":"1.0"}
```


### 一键更新命令
### 删除bl与bl.zip
```bash
rm -rf ~/bl
```

### 高刷命令：
```bash
cd ~ && sudo chmod -R 777 wolf240hz_patch && sudo ./wolf240hz_patch
```


## Web版本

### 一键启动Web版本
```bash
cd ~ && rm -rf ~/bl && unzip -o bl.zip && chmod -R 777 ~/bl && screen -dmS edgeai-web bash -c "cd ~/bl && (./start.sh &) && sleep 2 && ./pyserve.sh; exec bash"
# 启动前先删除bl文件夹，确保解压为全新内容
```

### 一键停止Web版本
```bash
pkill -f "SCREEN.*edgeai-web" && sudo pkill -9 EdgeAI && sudo pkill -9 -f "python.*spa_server.py"
```

### 查看Web版本日志
```bash
# 查看Web服务器和后台服务日志
screen -r edgeai-web
```

### 访问Web界面
启动后通过浏览器访问：
- 默认地址：http://localhost:8000
- 备用地址：http://localhost:8001（当默认端口被占用时）


### 一键启动APP版本
```bash
cd ~ && rm -rf ~/bl && unzip -o bl.zip && chmod -R 777 ~/bl && screen -dmS edgeai-app bash -c "cd ~/bl && ./start.sh; exec bash"
# 启动前先删除bl文件夹，确保解压为全新内容
```
### 一键停止APP版本
```bash
# 停止所有edgeai-app会话（推荐）
pkill -f "SCREEN.*edgeai-app"
```

### 查看APP版本运行状态
```bash
### 查看APP版本日志
```bash
screen -r edgeai-app
```

## 通用操作

### 连接到运行中的会话后的操作
- 查看日志：程序输出会直接显示在屏幕上
- 分离会话（保持程序在后台运行）：按下 `Ctrl+A` 然后按 `D`
- 停止程序：按 `Ctrl+C`

### 强制终止进程
```bash
# 终止所有EdgeAI进程
sudo pkill -9 EdgeAI

# 终止所有Web服务器进程
sudo pkill -9 -f "python.*spa_server.py"
```



## 注意事项
- 确保脚本具有执行权限：`chmod +x ~/bl/start.sh ~/bl/pyserve.sh`
- 系统重启后需要重新启动程序
- 如需自启动，可将启动命令添加到系统自启动配置中
- APP版本和Web版本可以同时运行
