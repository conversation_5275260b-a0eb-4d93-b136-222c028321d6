import http.server
import socketserver
import os
import sys
import time
import socket

PORT = 8000
BACKUP_PORT = 8001

class SPAHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # 检查请求的路径是否对应实际文件
        path = self.translate_path(self.path)
        if not os.path.exists(path) or os.path.isdir(path) and not self.path.endswith('/'):
            # 如果路径不存在或是目录但不以/结尾，重定向到index.html
            self.path = '/index.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

Handler = SPAHandler

def is_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

max_retries = 3
retry_count = 0

while retry_count < max_retries:
    try:
        # 如果端口仍然被占用，切换到备用端口
        if is_port_in_use(PORT):
            if PORT == 8000:
                print(f"端口 {PORT} 被占用，自动切换到备用端口 {BACKUP_PORT}")
                PORT = BACKUP_PORT
            else:
                # 如果备用端口也被占用，则报错退出
                print(f"错误: 端口 {PORT} 被占用，无法启动服务器")
                sys.exit(1)
                
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"为SPA应用提供服务在端口 {PORT}")
            print(f"访问地址: http://localhost:{PORT}")
            httpd.serve_forever()
        break
    except OSError as e:
        if e.errno == 98:  # Address already in use
            retry_count += 1
            if retry_count >= max_retries:
                if PORT == 8000:
                    # 如果当前使用的是主端口，切换到备用端口
                    print(f"无法使用端口 {PORT}，切换到备用端口 {BACKUP_PORT}")
                    PORT = BACKUP_PORT
                    retry_count = 0  # 重置重试计数器
                else:
                    # 如果已经在使用备用端口，则报错退出
                    print(f"错误: 端口 {PORT} 已被占用，无法启动服务器")
                    sys.exit(1)
            else:
                print(f"端口 {PORT} 被占用，尝试等待释放 (尝试 {retry_count}/{max_retries})...")
                time.sleep(2)
        else:
            print(f"启动服务器时出错: {e}")
            sys.exit(1)
