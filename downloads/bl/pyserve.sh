#!/bin/bash

# 输出颜色设置
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

PORT=8000
BACKUP_PORT=8001

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 定义一个杀死占用端口进程的函数 - 采用多种方法
kill_port_process() {
    local port=$1
    echo -e "${RED}尝试强制结束占用端口 ${port} 的进程...${NC}"
    
    # 尝试使用fuser
    if command -v fuser &> /dev/null; then
        fuser -k ${port}/tcp &> /dev/null
    fi
    
    # 尝试使用netstat和kill组合
    if command -v netstat &> /dev/null; then
        local pid=$(netstat -tlnp 2>/dev/null | grep ":${port} " | awk '{print $7}' | cut -d'/' -f1)
        if [ -n "$pid" ] && [ "$pid" != "-" ]; then
            echo "发现进程ID: $pid，尝试终止..."
            kill -9 $pid &> /dev/null
        fi
    fi
    
    # 尝试使用ss和kill组合
    if command -v ss &> /dev/null; then
        local pid=$(ss -tlnp | grep ":${port} " | sed -n 's/.*pid=\([0-9]*\).*/\1/p')
        if [ -n "$pid" ]; then
            echo "发现进程ID: $pid，尝试终止..."
            kill -9 $pid &> /dev/null
        fi
    fi
    
    sleep 1
}

# 检查端口是否被占用
check_port_in_use() {
    local port=$1
    local port_in_use=0
    
    if command -v ss &> /dev/null; then
        port_in_use=$(ss -tuln | grep ":${port} " | wc -l)
    elif command -v netstat &> /dev/null; then
        port_in_use=$(netstat -tuln | grep ":${port} " | wc -l)
    fi
    
    echo $port_in_use
}

echo -e "${YELLOW}检查端口 ${PORT} 是否被占用...${NC}"

# 尝试杀死占用端口的进程
PORT_IN_USE=$(check_port_in_use $PORT)

if [ "$PORT_IN_USE" -gt 0 ]; then
    echo -e "${RED}端口 ${PORT} 被占用，尝试结束进程...${NC}"
    kill_port_process $PORT
    
    # 再次检查端口是否已释放
    sleep 1
    PORT_IN_USE=$(check_port_in_use $PORT)
    
    # 如果端口仍然被占用，直接切换到备用端口
    if [ "$PORT_IN_USE" -gt 0 ]; then
        echo -e "${YELLOW}端口 ${PORT} 仍然被占用，尝试使用备用端口 ${BACKUP_PORT}...${NC}"
        
        # 检查备用端口是否可用
        BACKUP_PORT_IN_USE=$(check_port_in_use $BACKUP_PORT)
        if [ "$BACKUP_PORT_IN_USE" -gt 0 ]; then
            echo -e "${RED}备用端口 ${BACKUP_PORT} 也被占用，尝试释放...${NC}"
            kill_port_process $BACKUP_PORT
            
            # 再次检查备用端口
            sleep 1
            BACKUP_PORT_IN_USE=$(check_port_in_use $BACKUP_PORT)
            if [ "$BACKUP_PORT_IN_USE" -gt 0 ]; then
                echo -e "${RED}无法释放备用端口 ${BACKUP_PORT}，将继续尝试使用当前端口${NC}"
            else
                echo -e "${GREEN}成功释放备用端口 ${BACKUP_PORT}${NC}"
                PORT=$BACKUP_PORT
            fi
        else
            echo -e "${GREEN}备用端口 ${BACKUP_PORT} 可用${NC}"
            PORT=$BACKUP_PORT
        fi
    else
        echo -e "${GREEN}端口 ${PORT} 已成功释放${NC}"
    fi
fi

echo -e "${YELLOW}启动支持SPA的静态文件服务器...${NC}"
echo -e "${GREEN}访问地址: http://localhost:${PORT}${NC}"

# 进入web目录
WEB_DIR="${SCRIPT_DIR}/web"
if [ -d "$WEB_DIR" ]; then
    echo -e "${YELLOW}进入web目录: ${WEB_DIR}${NC}"
    cd "$WEB_DIR"
else
    echo -e "${RED}警告: web目录不存在 (${WEB_DIR})，使用当前目录${NC}"
fi

# 创建简单的SPA服务器脚本
cat > spa_server.py << EOF
import http.server
import socketserver
import os
import sys
import time
import socket

PORT = ${PORT}
BACKUP_PORT = ${BACKUP_PORT}

class SPAHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # 检查请求的路径是否对应实际文件
        path = self.translate_path(self.path)
        if not os.path.exists(path) or os.path.isdir(path) and not self.path.endswith('/'):
            # 如果路径不存在或是目录但不以/结尾，重定向到index.html
            self.path = '/index.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

Handler = SPAHandler

def is_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

max_retries = 3
retry_count = 0

while retry_count < max_retries:
    try:
        # 如果端口仍然被占用，切换到备用端口
        if is_port_in_use(PORT):
            if PORT == 8000:
                print(f"端口 {PORT} 被占用，自动切换到备用端口 {BACKUP_PORT}")
                PORT = BACKUP_PORT
            else:
                # 如果备用端口也被占用，则报错退出
                print(f"错误: 端口 {PORT} 被占用，无法启动服务器")
                sys.exit(1)
                
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"为SPA应用提供服务在端口 {PORT}")
            print(f"访问地址: http://localhost:{PORT}")
            httpd.serve_forever()
        break
    except OSError as e:
        if e.errno == 98:  # Address already in use
            retry_count += 1
            if retry_count >= max_retries:
                if PORT == 8000:
                    # 如果当前使用的是主端口，切换到备用端口
                    print(f"无法使用端口 {PORT}，切换到备用端口 {BACKUP_PORT}")
                    PORT = BACKUP_PORT
                    retry_count = 0  # 重置重试计数器
                else:
                    # 如果已经在使用备用端口，则报错退出
                    print(f"错误: 端口 {PORT} 已被占用，无法启动服务器")
                    sys.exit(1)
            else:
                print(f"端口 {PORT} 被占用，尝试等待释放 (尝试 {retry_count}/{max_retries})...")
                time.sleep(2)
        else:
            print(f"启动服务器时出错: {e}")
            sys.exit(1)
EOF

# 运行SPA服务器
python3 spa_server.py 