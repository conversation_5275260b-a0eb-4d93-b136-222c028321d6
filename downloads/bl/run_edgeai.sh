#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 设置库路径环境变量
export LD_LIBRARY_PATH="${SCRIPT_DIR}/bin/lib:$LD_LIBRARY_PATH"

# 定义screen会话名称
BACKEND_SCREEN="backend"
FRONTEND_SCREEN="frontend"
BACKEND_LOG_FILE="${SCRIPT_DIR}/edgeai_log.txt"
FRONTEND_LOG_FILE="${SCRIPT_DIR}/frontend_log.txt"

# 检查并设置权限
check_permissions() {
    echo "检查并设置必要的文件权限..."
    
    # 给bin目录下所有文件添加执行权限
    if [ -d "${SCRIPT_DIR}/bin" ]; then
        sudo chmod -R +x "${SCRIPT_DIR}/bin"
        echo "已设置bin目录文件权限"
    fi
    
    # 给web目录下所有文件添加执行权限
    if [ -d "${SCRIPT_DIR}/web" ]; then
        sudo chmod -R +x "${SCRIPT_DIR}/web"
        echo "已设置web目录文件权限"
    fi
    
    # 特别确保EdgeAI和pyserve.sh有执行权限
    if [ -f "${SCRIPT_DIR}/bin/EdgeAI" ]; then
        sudo chmod +x "${SCRIPT_DIR}/bin/EdgeAI"
    fi
    
    if [ -f "${SCRIPT_DIR}/web/pyserve.sh" ]; then
        sudo chmod +x "${SCRIPT_DIR}/web/pyserve.sh"
    fi
    
    echo "权限设置完成"
}

# 启动后端函数
start_backend() {
    # 检查EdgeAI文件是否存在且可执行
    if [ ! -f "${SCRIPT_DIR}/bin/EdgeAI" ]; then
        echo "错误：EdgeAI文件不存在"
        return 1
    fi
    
    # 确保EdgeAI有执行权限
    sudo chmod +x "${SCRIPT_DIR}/bin/EdgeAI"
    
    # 检查是否已经在运行
    if screen -list | grep -q "$BACKEND_SCREEN"; then
        echo "后端已经在运行中..."
        return 0
    fi
    
    echo "正在启动后端..."
    # 使用screen在后台运行EdgeAI
    cd "${SCRIPT_DIR}/bin"
    screen -dmS $BACKEND_SCREEN bash -c "sudo LD_LIBRARY_PATH=\"${LD_LIBRARY_PATH}\" ./EdgeAI > \"$BACKEND_LOG_FILE\" 2>&1"
    echo "后端已在后台启动，使用screen会话：$BACKEND_SCREEN"
}

# 启动前端函数
start_frontend() {
    # 检查前端目录是否存在
    if [ ! -d "${SCRIPT_DIR}/web" ]; then
        echo "错误：web目录不存在"
        return 1
    fi
    
    # 检查pyserve.sh是否存在
    if [ ! -f "${SCRIPT_DIR}/web/pyserve.sh" ]; then
        echo "错误：pyserve.sh文件不存在"
        return 1
    fi
    
    # 确保pyserve.sh有执行权限
    sudo chmod +x "${SCRIPT_DIR}/web/pyserve.sh"
    
    # 检查是否已经在运行
    if screen -list | grep -q "$FRONTEND_SCREEN"; then
        echo "前端已经在运行中..."
        return 0
    fi
    
    echo "正在启动前端..."
    # 使用screen在后台运行前端
    cd "${SCRIPT_DIR}/web"
    screen -dmS $FRONTEND_SCREEN bash -c "sudo ./pyserve.sh > \"$FRONTEND_LOG_FILE\" 2>&1"
    echo "前端已在后台启动，使用screen会话：$FRONTEND_SCREEN"
}

# 停止后端函数
stop_backend() {
    if ! screen -list | grep -q "$BACKEND_SCREEN"; then
        echo "后端未运行..."
        return 0
    fi
    
    echo "正在停止后端..."
    screen -S $BACKEND_SCREEN -X quit
    echo "后端已停止"
}

# 停止前端函数
stop_frontend() {
    if ! screen -list | grep -q "$FRONTEND_SCREEN"; then
        echo "前端未运行..."
        return 0
    fi
    
    echo "正在停止前端..."
    screen -S $FRONTEND_SCREEN -X quit
    echo "前端已停止"
}

# 启动全部函数
start_all() {
    start_backend
    start_frontend
}

# 停止全部函数
stop_all() {
    stop_backend
    stop_frontend
}

# 查看后端日志函数
view_backend_log() {
    if [ -f "$BACKEND_LOG_FILE" ]; then
        echo "显示后端日志（按Ctrl+C退出）："
        tail -f "$BACKEND_LOG_FILE"
    else
        echo "日志文件不存在"
    fi
}

# 查看前端日志函数
view_frontend_log() {
    if [ -f "$FRONTEND_LOG_FILE" ]; then
        echo "显示前端日志（按Ctrl+C退出）："
        tail -f "$FRONTEND_LOG_FILE"
    else
        echo "日志文件不存在"
    fi
}

# 查看screen会话
view_screen() {
    echo "当前screen会话列表："
    screen -ls
}

# 显示菜单函数
show_menu() {
    echo "====== 系统管理菜单 ======"
    echo "1. 启动后端(EdgeAI)"
    echo "2. 启动前端(web)"
    echo "3. 启动全部(后端+前端)"
    echo "4. 停止后端"
    echo "5. 停止前端"
    echo "6. 停止全部"
    echo "7. 查看后端日志"
    echo "8. 查看前端日志"
    echo "9. 查看screen会话"
    echo "0. 退出"
    echo "=========================="
    echo -n "请输入选项 [0-9]: "
}

# 主程序循环
while true; do
    show_menu
    read -r choice
    
    case $choice in
        1)
            check_permissions
            start_backend
            ;;
        2)
            check_permissions
            start_frontend
            ;;
        3)
            check_permissions
            start_all
            ;;
        4)
            stop_backend
            ;;
        5)
            stop_frontend
            ;;
        6)
            stop_all
            ;;
        7)
            view_backend_log
            ;;
        8)
            view_frontend_log
            ;;
        9)
            view_screen
            ;;
        0)
            echo "退出程序"
            exit 0
            ;;
        *)
            echo "无效选项，请重新输入"
            ;;
    esac
    
    echo ""
done 