<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeAI 启动与停止说明</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        h2 {
            color: #2c3e50;
            margin-top: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        h3 {
            color: #3498db;
            margin-top: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            position: relative;
            overflow: hidden;
        }
        code {
            display: block;
            white-space: pre-wrap;
            word-wrap: break-word;
            padding-right: 30px;
        }
        .copy-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 3px 8px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background-color: #2980b9;
        }
        .copied {
            background-color: #27ae60;
        }
    </style>
</head>
<body>
    <h1>EdgeAI 启动与停止说明</h1>

    <h2>启动方式</h2>
    <p>EdgeAI支持两种运行模式：</p>
    <ol>
        <li><strong>APP版本</strong>：默认的EdgeAI应用程序</li>
        <li><strong>Web版本</strong>：通过Web界面访问的版本</li>
    </ol>

    <h2>APP版本（默认）</h2>

    <h3>安装screen（如未安装）</h3>
    <pre><code>sudo pacman -S screen</code><button class="copy-btn">复制</button></pre>

    <h3>导入命令：</h3>
    <pre><code>{"commands":[{"id":"19be9830-f4b3-4014-bd1d-802307fe0756","name":"删除版本文件夹","command":"sudo rm -rf ~/bl","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T10:32:09.773941","updatedAt":"2025-05-22T14:31:15.455994"},{"id":"41829726-11f6-40ef-8e0a-e26ed235e965","name":"启动","command":"cd ~ && sudo rm -rf ~/bl && unzip -o bl.zip && chmod -R 777 ~/bl && screen -dmS edgeai-web bash -c \"cd ~/bl && (./start.sh &) && sleep 2 && ./pyserve.sh; exec bash\"","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T11:39:37.188537","updatedAt":"2025-05-22T14:39:09.392919"},{"id":"05868ec0-b6ee-4abb-b9e6-3c756536a5fe","name":"停止","command":"pkill -f \"SCREEN.*edgeai-web\" && sudo pkill -9 EdgeAI && sudo pkill -9 -f \"python.*spa_server.py\"","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T11:39:54.514570","updatedAt":"2025-05-14T11:39:54.514570"},{"id":"f01bcf20-93c8-49d5-8a7f-5dacd2a5aa9e","name":"高刷","command":"cd ~ && sudo chmod -R 777 wolf240hz_patch && sudo ./wolf240hz_patch","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T11:40:12.049024","updatedAt":"2025-05-14T11:40:12.049024"},{"id":"9950cd5d-d1fb-4c21-b070-a89973062e4b","name":"ping卡密情况","command":"ping antkma.antszy.com","description":"","type":"SSHCommandType.custom","isFavorite":false,"tags":[],"createdAt":"2025-05-14T22:53:26.739549","updatedAt":"2025-05-14T22:53:26.739549"}],"exportTime":"2025-05-22T14:39:34.079329","version":"1.0"}</code><button class="copy-btn">复制</button></pre>

    <h3>删除bl与bl.zip</h3>
    <pre><code>sudo rm -rf ~/bl</code><button class="copy-btn">复制</button></pre>

    <h3>高刷命令：</h3>
    <pre><code>cd ~ && sudo chmod -R 777 wolf240hz_patch && sudo ./wolf240hz_patch</code><button class="copy-btn">复制</button></pre>

    <h2>Web版本</h2>

    <h3>一键启动Web版本</h3>
    <pre><code>cd ~ && sudo rm -rf ~/bl && unzip -o bl.zip && chmod -R 777 ~/bl && screen -dmS edgeai-web bash -c "cd ~/bl && (./start.sh &) && sleep 2 && ./pyserve.sh; exec bash"</code><button class="copy-btn">复制</button></pre>
    <p>启动前先删除bl文件夹，确保解压为全新内容</p>

    <h3>一键停止Web版本</h3>
    <pre><code>pkill -f "SCREEN.*edgeai-web" && sudo pkill -9 EdgeAI && sudo pkill -9 -f "python.*spa_server.py"</code><button class="copy-btn">复制</button></pre>

    <h3>查看Web版本日志</h3>
    <pre><code>screen -r edgeai-web</code><button class="copy-btn">复制</button></pre>
    <p>查看Web服务器和后台服务日志</p>

    <h3>访问Web界面</h3>
    <p>启动后通过浏览器访问：</p>
    <ul>
        <li>默认地址：<a href="http://localhost:8000">http://localhost:8000</a></li>
        <li>备用地址：<a href="http://localhost:8001">http://localhost:8001</a>（当默认端口被占用时）</li>
    </ul>

    <h3>一键启动APP版本</h3>
    <pre><code>cd ~ && sudo rm -rf ~/bl && unzip -o bl.zip && chmod -R 777 ~/bl && screen -dmS edgeai-app bash -c "cd ~/bl && ./start.sh; exec bash"</code><button class="copy-btn">复制</button></pre>
    <p>启动前先删除bl文件夹，确保解压为全新内容</p>

    <h3>一键停止APP版本</h3>
    <pre><code>pkill -f "SCREEN.*edgeai-app"</code><button class="copy-btn">复制</button></pre>
    <p>停止所有edgeai-app会话（推荐）</p>

    <h3>查看APP版本运行状态</h3>
    <h3>查看APP版本日志</h3>
    <pre><code>screen -r edgeai-app</code><button class="copy-btn">复制</button></pre>

    <h2>通用操作</h2>

    <h3>连接到运行中的会话后的操作</h3>
    <ul>
        <li>查看日志：程序输出会直接显示在屏幕上</li>
        <li>分离会话（保持程序在后台运行）：按下 <code>Ctrl+A</code> 然后按 <code>D</code></li>
        <li>停止程序：按 <code>Ctrl+C</code></li>
    </ul>

    <h3>强制终止进程</h3>
    <pre><code>sudo pkill -9 EdgeAI</code><button class="copy-btn">复制</button></pre>
    <p>终止所有EdgeAI进程</p>

    <pre><code>sudo pkill -9 -f "python.*spa_server.py"</code><button class="copy-btn">复制</button></pre>
    <p>终止所有Web服务器进程</p>

    <h2>注意事项</h2>
    <ul>
        <li>确保脚本具有执行权限：<code>chmod +x ~/bl/start.sh ~/bl/pyserve.sh</code></li>
        <li>系统重启后需要重新启动程序</li>
        <li>如需自启动，可将启动命令添加到系统自启动配置中</li>
        <li>APP版本和Web版本可以同时运行</li>
    </ul>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButtons = document.querySelectorAll('.copy-btn');
            
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const codeBlock = this.previousElementSibling;
                    const textToCopy = codeBlock.textContent;
                    
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        this.textContent = '已复制!';
                        this.classList.add('copied');
                        
                        setTimeout(() => {
                            this.textContent = '复制';
                            this.classList.remove('copied');
                        }, 2000);
                    }).catch(err => {
                        console.error('复制失败:', err);
                    });
                });
            });
        });
    </script>
</body>
</html> 