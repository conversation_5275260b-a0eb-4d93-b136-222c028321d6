# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DHAVE_MYSQL_SUPPORT -DHAVE_NETINET_IN_H -DHAVE_SO_REUSEPORT -DPOCO_CMAKE -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_NO_AUTOMATIC_LIBS -DPOCO_OS_FAMILY_UNIX -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500

CXX_INCLUDES = -I/home/<USER>/mywork/poco_serverdemo -I/home/<USER>/mywork/poco_serverdemo/utils -I/home/<USER>/mywork/poco_serverdemo/ThreadManager -I/home/<USER>/mywork/poco_serverdemo/infer -I/home/<USER>/mywork/poco_serverdemo/lkm -I/home/<USER>/mywork/poco_serverdemo/algorithm -I/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/nlohmann_json/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/librga/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src -I/home/<USER>/mywork/poco_serverdemo/3rdparty/CImg -I/home/<USER>/mywork/poco_serverdemo/backend/.. -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/Foundation/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/Net/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/Util/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/JSON/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/XML/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/Data/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/Data/MySQL/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/Zip/include -I/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/rknn/include -I/usr/include/mysql

CXX_FLAGS =  -O3 -march=native -O3 -DNDEBUG -O3 -DNDEBUG -std=c++17

