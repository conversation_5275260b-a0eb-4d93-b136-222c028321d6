#include "utils/logger.h"
#include "utils/gmodels.h"
#include "backend/blsql/mysql_db.h"
#include "ThreadManager/web_server.h"
#include "backend/blserver/message_handlers.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <Poco/Environment.h>

// 信号处理
volatile sig_atomic_t g_running = true;

void signalHandler(int sig) {
    // 避免未使用参数警告
    (void)sig;
    g_running = false;
}

int main() {
    LOG_INFO("服务器启动中...");
    
    // 初始化配置处理函数
    MessageHandlers::initialize();
    LOG_INFO("配置处理函数初始化完成");
    
    // 初始化MySQL数据库连接
    LOG_INFO("正在初始化数据库连接...");
    try {
        LOG_INFO("Poco库版本: {}", Poco::format("%d.%d.%d", 
                static_cast<int>(Poco::Environment::libraryVersion() >> 24), 
                static_cast<int>((Poco::Environment::libraryVersion() >> 16) & 0xFF),
                static_cast<int>((Poco::Environment::libraryVersion() >> 8) & 0xFF)));
        
        LOG_INFO("成功连接到MySQL数据库: {}@{}", webui::database::g_db_user, webui::database::g_db_host);
    } catch (const Poco::Exception& e) {
        LOG_ERROR("无法连接到MySQL数据库: {}@{}, 错误: {}", webui::database::g_db_user, webui::database::g_db_host, e.displayText());
    }
    
    if (!blweb::MySQLDB::getInstance().init()) {
        LOG_ERROR("数据库连接初始化失败: {}", blweb::MySQLDB::getInstance().getLastError());
        LOG_WARNING("系统将在没有数据库支持的情况下继续运行，某些功能可能受限");
    } else {
        LOG_INFO("数据库连接初始化成功");
    }
    
    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    // 初始化WebSocket服务器
    WebServer& wsServer = WebServer::getInstance();
    if (!wsServer.initialize("0.0.0.0", 8080, "/ws")) {
        LOG_ERROR("WebSocket服务器初始化失败");
        return 1;
    }
    
    // 启动WebSocket服务器
    if (!wsServer.start()) {
        LOG_ERROR("WebSocket服务器启动失败");
        return 1;
    }
    
    LOG_INFO("WebSocket服务器已启动，等待连接...");
    LOG_INFO("服务器地址: ws://0.0.0.0:8080/ws");
    
    // 主循环 - 等待信号中断
    while (g_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    // 停止WebSocket服务器
    LOG_INFO("正在停止WebSocket服务器...");
    wsServer.stop();
    
    // 关闭数据库连接
    LOG_INFO("正在关闭数据库连接...");
    blweb::MySQLDB::getInstance().close();
    
    LOG_INFO("服务器已正常退出");
    
    return 0;
} 