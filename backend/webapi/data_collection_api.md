# 数据收集系统API

本文档描述数据收集系统的WebSocket API请求和响应格式。

## 请求和响应格式

系统使用四种主要操作：
- `data_collection_read`: 读取数据收集设置
- `data_collection_modify`: 修改数据收集设置（保存按钮API）
- `data_collection_fetch`: 获取数据（获取当前imgs文件夹中所有子文件夹名称以及每个文件夹中图像和标签文件数量）
- `data_collection_upload`: 上传数据（将收集的数据上传到远程服务器）

### 读取数据收集设置 (data_collection_read)

**客户端请求**：

```json
{
  "action": "data_collection_read",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称"
  }
}
```

**服务器响应**：

```json
{
  "action": "data_collection_read_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "isEnabled": true,
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "collectionName": "csgo2_20230601_1430_jf_admin",
    "teamSide": "警方",
    "createdAt": "2023-06-01T14:30:00Z",
    "updatedAt": "2023-06-01T15:00:00Z"
  }
}
```

### 修改数据收集设置 (data_collection_modify)

此API对应保存按钮，用于保存数据收集名称、阵营、地图热键、目标热键以及是否启用数据收集的开关状态。

**客户端请求**：

```json
{
  "action": "data_collection_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "isEnabled": true,
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "collectionName": "csgo2_aaa_jf_admin",
    "teamSide": "警方",
    "createdAt": "2023-06-01T14:30:00Z",
    "updatedAt": "2023-06-02T10:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "data_collection_modify_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "isEnabled": true,
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "collectionName": "csgo2_aaa_jf_admin",
    "teamSide": "警方",
    "createdAt": "2023-06-01T14:30:00Z",
    "updatedAt": "2023-06-02T10:00:00Z"
  }
}
```

### 获取数据信息 (data_collection_fetch)

此API用于获取当前imgs文件夹中所有子文件夹名称以及每个文件夹中图像和标签文件的数量。客户端发送请求后，后端会扫描文件系统并返回详细数据。

**客户端请求**：

```json
{
  "action": "data_collection_fetch",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称"
  }
}
```

**服务器响应**：

```json
{
  "action": "data_collection_fetch_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "collections": [
      {
        "name": "csgo2_aaa_jf_admin",
        "path": "./imgs/csgo2_aaa_jf_admin",
        "folders": [
          {
            "name": "maps",
            "imageCount": 60,
            "labelCount": 60,
            "fileCount": 120,
            "lastUpdated": "2023-06-02T09:15:00Z"
          },
          {
            "name": "targets",
            "imageCount": 45,
            "labelCount": 40,
            "fileCount": 85,
            "lastUpdated": "2023-06-02T09:20:00Z"
          }
        ],
        "totalFiles": 205,
        "detailedStats": {
          "maps": {
            "images": 60,
            "labels": 60,
            "total": 120
          },
          "targets": {
            "images": 45,
            "labels": 40,
            "total": 85
          }
        },
        "summary": {
          "images": 105,
          "labels": 100,
          "total": 205
        },
        "lastUpdated": "2023-06-02T10:05:00Z"
      },
      {
        "name": "csgo2_bbb_ff_admin",
        "path": "./imgs/csgo2_bbb_ff_admin",
        "folders": [
          {
            "name": "maps",
            "imageCount": 30,
            "labelCount": 30,
            "fileCount": 60,
            "lastUpdated": "2023-06-02T16:15:00Z"
          },
          {
            "name": "targets",
            "imageCount": 25,
            "labelCount": 20,
            "fileCount": 45,
            "lastUpdated": "2023-06-02T16:20:00Z"
          }
        ],
        "totalFiles": 105,
        "detailedStats": {
          "maps": {
            "images": 30,
            "labels": 30,
            "total": 60
          },
          "targets": {
            "images": 25,
            "labels": 20,
            "total": 45
          }
        },
        "summary": {
          "images": 55,
          "labels": 50,
          "total": 105
        },
        "lastUpdated": "2023-06-02T16:25:00Z"
      }
    ],
    "totalCollections": 2,
    "timestamp": "2023-06-02T17:05:00Z"
  }
}
```

### 上传数据 (data_collection_upload)

此API用于将收集的数据上传到远程服务器。前端发送上传请求后，后端会在内部执行向其他服务器发送上传文件的操作，上传完成后会返回是否上传成功的状态。

**客户端请求**：

```json
{
  "action": "data_collection_upload",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "teamSide": "警方",
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "timestamp": "2023-06-02T10:10:00Z",
    "deleteAll": false
  }
}
```

**服务器响应（成功）**：

```json
{
  "action": "data_collection_upload_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "uploadStatus": "success",
    "uploadComplete": true,
    "filesDeleted": true,
    "message": "数据上传成功并已删除源文件"
  }
}
```

**服务器响应（失败）**：

```json
{
  "action": "data_collection_upload_response",
  "status": "error",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "uploadStatus": "failed",
    "uploadComplete": false,
    "filesDeleted": false,
    "error": {
      "code": "SERVER_ERROR",
      "message": "无法连接到上传服务器"
    }
  }
}
```

**服务器响应（部分成功）**：

```json
{
  "action": "data_collection_upload_response",
  "status": "warning",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "uploadStatus": "partial",
    "uploadComplete": false,
    "filesDeleted": false,
    "message": "部分数据上传成功，但出现错误: 网络连接中断"
  }
}
```

## 参数说明

| 参数名称 | 类型 | 描述 | 可选值 |
|---------|------|------|-------|
| username | string | 用户名 | 任意字符串 |
| gameName | string | 游戏名称 | apex, cf, cfhd, csgo2, pubg, sjz, ssjj2, wwqy |
| isEnabled | boolean | 是否启用数据收集 | true, false |
| mapHotkey | string | 地图数据收集热键 | 左键, 右键, 中键, 前侧, 后侧, 无 |
| targetHotkey | string | 目标数据收集热键 | 左键, 右键, 中键, 前侧, 后侧, 无 |
| collectionName | string | 数据收集名称 | 在UI中用户可自由输入，但发送到服务器时会格式化为"游戏名称_数据收集名称_阵营"，如"csgo2_qqq_jf"。用于data_collection_modify请求，但在data_collection_fetch和data_collection_upload请求中不需要 |
| teamSide | string | 阵营选择 | 警方, 匪方, 无 |
| createdAt | string | 创建时间戳 | ISO8601格式的时间字符串 |
| updatedAt | string | 更新时间戳 | ISO8601格式的时间字符串 |
| timestamp | string | 操作时间戳 | ISO8601格式的时间字符串 |
| deleteAll | boolean | 是否删除所有数据文件夹 | true (删除imgs下所有子文件夹), false (仅删除当前上传的文件夹，默认值) |
| totalCollections | number | data_collection_fetch响应中的采集文件夹总数 | 整数 |

## 数据结构说明

### collections 数组结构

data_collection_fetch 响应中的 collections 数组包含所有数据采集文件夹的信息，每个采集文件夹包含以下字段：

| 字段 | 类型 | 描述 |
|------|------|------|
| name | string | 采集文件夹名称 |
| path | string | 采集文件夹路径 |
| folders | array | 包含该采集文件夹中的分类子文件夹（如maps、targets）信息的数组 |
| totalFiles | number | 该采集文件夹中的文件总数 |
| detailedStats | object | 详细统计信息，包含各分类的图像、标签及文件总数 |
| summary | object | 汇总信息，包含总的图像、标签及文件总数 |
| lastUpdated | string | 最后更新时间 |

### stats 对象结构

| 字段 | 类型 | 描述 |
|------|------|------|
| totalImages | number | 所有分类中图像文件的总数 |
| totalLabels | number | 所有分类中标签文件的总数 |
| totalFiles | number | 所有文件的总数（图像+标签） |
| categories | object | 包含各分类详细统计的对象 |

### categories 对象结构

每个分类（如maps、targets）都包含以下字段：

| 字段 | 类型 | 描述 |
|------|------|------|
| images | number | 该分类中图像文件的数量 |
| labels | number | 该分类中标签文件的数量 |
| total | number | 该分类中文件总数（图像+标签） |

## 错误码说明

| 错误码 | 描述 |
|-------|------|
| SERVER_ERROR | 服务器内部错误 |
| SERVER_UNREACHABLE | 无法连接到上传服务器 |
| AUTH_FAILED | 认证失败 |
| INVALID_COLLECTION | 无效的收集名称 |
| NO_DATA | 没有可上传的数据 |
| UPLOAD_INCOMPLETE | 上传未完成 |
| DISK_FULL | 目标服务器存储空间不足 