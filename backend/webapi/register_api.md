# 注册系统API

本文档描述注册系统的WebSocket API请求和响应格式。

## 请求和响应格式

系统使用以下主要操作：
- `register_modify`: 用户注册

### 用户注册 (register_modify)

**客户端请求**：

```json
{
  "action": "register_modify",
  "content": {
    "gameList": ["apex", "cf", "cfhd", "csgo2", "sjz", "ssjj2", "pubg", "wwqy"],
    "defaultGame": "csgo2",
    "username": "用户名",
    "password": "密码",
    "isPro": false,
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

**服务器响应 - 成功**：

```json
{
  "action": "register_modify_response",
  "status": "success",
  "message": "注册成功",
  "data": {
    "userInfo": {
      "username": "用户名",
      "token": "身份验证令牌",
      "isPro": false
    },
    "homeConfig": {
      "gameName": "游戏名称",
      "cardKey": "卡密"
    }
  }
}
```

**服务器响应 - 失败**：

```json
{
  "action": "register_modify_response",
  "status": "error",
  "message": "用户名已存在",
  "data": null
}
```

### Pro版注册

客户端可以指定`isPro`参数为`true`进行Pro版注册，此信息会被保存到数据库中。

**客户端请求**：

```json
{
  "action": "register_modify",
  "content": {
    "gameList": ["apex", "cf", "cfhd", "csgo2", "sjz", "ssjj2", "pubg", "wwqy"],
    "defaultGame": "csgo2",
    "username": "用户名",
    "password": "密码",
    "isPro": true,
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

## 参数说明

| 参数名称 | 类型 | 描述 | 必填 |
|---------|------|------|-------|
| gameList | array | 支持的游戏列表 | 是 |
| defaultGame | string | 默认游戏 | 是 |
| username | string | 注册用户名 | 是 |
| password | string | 注册密码 | 是 |
| isPro | boolean | 是否为Pro版注册 | 否(默认为false) |
| createdAt | string | 请求创建时间(ISO 8601格式) | 是 |
| updatedAt | string | 请求更新时间(ISO 8601格式) | 是 |

## 响应参数说明

| 参数名称 | 类型 | 描述 |
|---------|------|------|
| status | string | 请求状态："success" 或 "error" |
| message | string | 状态描述消息 |
| data.userInfo.username | string | 用户名 |
| data.userInfo.token | string | 身份验证令牌，用于后续请求验证 |
| data.userInfo.isPro | boolean | 是否为Pro用户 |
| data.homeConfig.gameName | string | 当前游戏名称 |
| data.homeConfig.cardKey | string | 用户卡密 |

## 错误代码

| 状态码 | 描述 |
|-------|------|
| error | 一般错误，见message字段了解具体错误原因 |

## 常见错误消息

| 错误消息 | 描述 |
|---------|------|
| 用户名已存在 | 注册的用户名已被其他用户使用 |
| 密码不符合要求 | 密码长度或复杂度不符合系统要求 |
| 注册失败，请重试 | 服务器内部错误导致注册失败 | 

## 自动创建的默认配置

注册成功后，系统会自动为用户创建完整的默认配置，包括：

### 功能配置
为每个游戏创建4个预设配置，每个配置都包含阵营选择和切枪功能：
- **配置1**: PID模式，头部瞄准，警方阵营，左键触发，扳机开启，切枪关闭
- **配置2**: FOV模式，胸部瞄准，匪方阵营，右键触发，扳机关闭，切枪开启
- **配置3**: FOVPID模式，颈部瞄准，无阵营，中键触发，扳机开启，切枪关闭
- **配置4**: PID模式，颈部瞄准，警方阵营，右键触发，扳机关闭，切枪开启

### 其他配置
- **PID配置**: 默认的PID控制参数
- **视野配置**: 默认的FOV设置
- **瞄准配置**: 默认的瞄准参数
- **射击配置**: 默认的射击间隔设置
- **数据收集配置**: 默认关闭的数据收集设置

所有配置都可以在注册后通过相应的API进行修改。 