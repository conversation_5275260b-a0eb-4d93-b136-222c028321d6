# 首页配置系统API

本文档描述首页配置系统的WebSocket API请求和响应格式。

## 请求和响应格式

系统使用两种主要操作：
- `home_read`: 读取首页配置
- `home_modify`: 修改首页配置

### 读取首页配置 (home_read)

**客户端请求**：

```json
{
  "action": "home_read",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "isPro": true,
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "home_read",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "updatedAt": "2023-06-01T13:30:00Z"
  }
}
```

### 修改首页配置 (home_modify)

**客户端请求**：

```json
{
  "action": "home_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "isPro": true,
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "home_modify_response",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

## 参数说明

| 参数名称 | 类型 | 描述 | 可选值 | 备注 |
|---------|------|------|-------|------|
| username | string | 用户名 | 任意字符串 | |
| gameName | string | 游戏名称 | apex, cf, cfhd, csgo2, pubg, sjz, ssjj2, wwqy | |
| cardKey | string | 用户卡密 | 任意字符串 | |
| isPro | boolean | 是否为Pro用户 | true, false | **仅用于状态标识，不保存到数据库** |
| updatedAt | string | 更新时间戳 | ISO8601格式的时间字符串 | |

## 特殊说明

### isPro字段说明
- **用途**: 标识当前用户是否为Pro用户，用于服务器端的权限判断和功能控制
- **数据流向**: 客户端发送到服务器，服务器根据此字段选择相应的API地址和功能
- **存储策略**: **用户的Pro状态存储在数据库的SuperAdmin表中的isPro字段**
- **获取方式**: 客户端从登录响应中获取用户的Pro状态，并在后续请求中发送
- **重要性**: 此字段直接影响卡密验证使用的API地址（Pro版使用不同的API端点） 