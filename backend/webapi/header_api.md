# 页头控制系统API

本文档描述页头控制系统的WebSocket API请求和响应格式。

## 请求和响应格式

系统使用以下主要操作：
- `status_bar_query`: 查询状态栏信息（重连按钮）
- `version_update`: 版本更新请求

> **注意**：`home_read`操作已移至home_api文档中，不再属于页头控制系统API范围。心跳功能已由状态栏查询替代，ServerService会定期发送状态栏查询请求来维持连接。

### 查询状态栏信息 (status_bar_query)

**说明**：当用户点击重连按钮时，发送此请求查询服务器状态，不会更新数据库，只是获取当前服务状态。状态只有两种：正常（true，绿色）和异常（false，红色）。此请求同时起到心跳的作用，维持与服务器的连接状态。

**客户端请求**：

```json
{
  "action": "status_bar_query",
  "token": "认证令牌"
}
```

**服务器响应**：

```json
{
  "action": "status_bar_response",
  "status": "success",
  "data": {
    "dbStatus": true,          // true（绿色）或 false（红色）
    "inferenceStatus": true,   // true（绿色）或 false（红色）
    "cardKeyStatus": true,     // true（绿色）或 false（红色）
    "keyMouseStatus": true,    // true（绿色）或 false（红色）
    "currentVersion": "1.2.3", // 当前版本号
    "latestVersion": "1.2.5",  // 最新版本号
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

### 版本更新请求 (version_update)

**说明**：当用户点击版本更新按钮时，发送此请求通知服务器执行版本更新操作。服务器收到此请求后会根据内部逻辑决定是否执行更新。

**客户端请求**：

```json
{
  "action": "version_update",
  "token": "认证令牌"
}
```

**服务器响应**：

```json
{
  "action": "version_update_response",
  "status": "success",
  "message": "版本更新请求已接收"
}
```

## 参数说明

### 状态栏查询参数

**重要说明**：状态栏查询不会更新数据库数据，只反映当前服务器状态。所有状态仅有两种：正常（true）和异常（false）。此查询同时具备心跳功能，ServerService会每30秒自动发送一次。

| 参数名称 | 类型 | 描述 | 可能的值 |
|---------|------|------|---------|
| dbStatus | boolean | 数据库连接状态 | true（绿色）, false（红色） |
| inferenceStatus | boolean | 推理服务状态 | true（绿色）, false（红色） |
| cardKeyStatus | boolean | 卡密状态 | true（绿色）, false（红色） |
| keyMouseStatus | boolean | 键鼠状态 | true（绿色）, false（红色） |
| currentVersion | string | 当前版本号 | 如："1.2.3" |
| latestVersion | string | 最新版本号 | 如："1.2.5" |

## 连接维持机制

系统通过以下方式维持与服务器的连接：

1. **自动状态栏查询**: ServerService每30秒自动发送`status_bar_query`请求
2. **连接状态监控**: 客户端监控连接状态，断开时自动重连
3. **心跳检测**: 通过状态栏查询响应更新最后心跳时间
4. **健康检查**: 页头组件每3秒检查心跳健康状态（20秒超时）

这种设计简化了心跳机制，将连接维持和状态查询合二为一，减少了冗余的网络请求。 