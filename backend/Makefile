# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\" \"fmt-core\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/mywork/poco_serverdemo && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/CMakeFiles /home/<USER>/mywork/poco_serverdemo/backend//CMakeFiles/progress.marks
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/mywork/poco_serverdemo && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
backend/CMakeFiles/blweb_backend.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/CMakeFiles/blweb_backend.dir/rule
.PHONY : backend/CMakeFiles/blweb_backend.dir/rule

# Convenience name for target.
blweb_backend: backend/CMakeFiles/blweb_backend.dir/rule
.PHONY : blweb_backend

# fast build rule for target.
blweb_backend/fast:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/build
.PHONY : blweb_backend/fast

# Convenience name for target.
backend/CMakeFiles/bl_server.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 backend/CMakeFiles/bl_server.dir/rule
.PHONY : backend/CMakeFiles/bl_server.dir/rule

# Convenience name for target.
bl_server: backend/CMakeFiles/bl_server.dir/rule
.PHONY : bl_server

# fast build rule for target.
bl_server/fast:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/build
.PHONY : bl_server/fast

__/ThreadManager/web_server.o: __/ThreadManager/web_server.cpp.o
.PHONY : __/ThreadManager/web_server.o

# target to build an object file
__/ThreadManager/web_server.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o
.PHONY : __/ThreadManager/web_server.cpp.o

__/ThreadManager/web_server.i: __/ThreadManager/web_server.cpp.i
.PHONY : __/ThreadManager/web_server.i

# target to preprocess a source file
__/ThreadManager/web_server.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.i
.PHONY : __/ThreadManager/web_server.cpp.i

__/ThreadManager/web_server.s: __/ThreadManager/web_server.cpp.s
.PHONY : __/ThreadManager/web_server.s

# target to generate assembly for a file
__/ThreadManager/web_server.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.s
.PHONY : __/ThreadManager/web_server.cpp.s

blserver/aim_service.o: blserver/aim_service.cpp.o
.PHONY : blserver/aim_service.o

# target to build an object file
blserver/aim_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o
.PHONY : blserver/aim_service.cpp.o

blserver/aim_service.i: blserver/aim_service.cpp.i
.PHONY : blserver/aim_service.i

# target to preprocess a source file
blserver/aim_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.i
.PHONY : blserver/aim_service.cpp.i

blserver/aim_service.s: blserver/aim_service.cpp.s
.PHONY : blserver/aim_service.s

# target to generate assembly for a file
blserver/aim_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.s
.PHONY : blserver/aim_service.cpp.s

blserver/data_collection_service.o: blserver/data_collection_service.cpp.o
.PHONY : blserver/data_collection_service.o

# target to build an object file
blserver/data_collection_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o
.PHONY : blserver/data_collection_service.cpp.o

blserver/data_collection_service.i: blserver/data_collection_service.cpp.i
.PHONY : blserver/data_collection_service.i

# target to preprocess a source file
blserver/data_collection_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.i
.PHONY : blserver/data_collection_service.cpp.i

blserver/data_collection_service.s: blserver/data_collection_service.cpp.s
.PHONY : blserver/data_collection_service.s

# target to generate assembly for a file
blserver/data_collection_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.s
.PHONY : blserver/data_collection_service.cpp.s

blserver/fire_service.o: blserver/fire_service.cpp.o
.PHONY : blserver/fire_service.o

# target to build an object file
blserver/fire_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o
.PHONY : blserver/fire_service.cpp.o

blserver/fire_service.i: blserver/fire_service.cpp.i
.PHONY : blserver/fire_service.i

# target to preprocess a source file
blserver/fire_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.i
.PHONY : blserver/fire_service.cpp.i

blserver/fire_service.s: blserver/fire_service.cpp.s
.PHONY : blserver/fire_service.s

# target to generate assembly for a file
blserver/fire_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.s
.PHONY : blserver/fire_service.cpp.s

blserver/fov_service.o: blserver/fov_service.cpp.o
.PHONY : blserver/fov_service.o

# target to build an object file
blserver/fov_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o
.PHONY : blserver/fov_service.cpp.o

blserver/fov_service.i: blserver/fov_service.cpp.i
.PHONY : blserver/fov_service.i

# target to preprocess a source file
blserver/fov_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.i
.PHONY : blserver/fov_service.cpp.i

blserver/fov_service.s: blserver/fov_service.cpp.s
.PHONY : blserver/fov_service.s

# target to generate assembly for a file
blserver/fov_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.s
.PHONY : blserver/fov_service.cpp.s

blserver/function_service.o: blserver/function_service.cpp.o
.PHONY : blserver/function_service.o

# target to build an object file
blserver/function_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o
.PHONY : blserver/function_service.cpp.o

blserver/function_service.i: blserver/function_service.cpp.i
.PHONY : blserver/function_service.i

# target to preprocess a source file
blserver/function_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.i
.PHONY : blserver/function_service.cpp.i

blserver/function_service.s: blserver/function_service.cpp.s
.PHONY : blserver/function_service.s

# target to generate assembly for a file
blserver/function_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.s
.PHONY : blserver/function_service.cpp.s

blserver/header_service.o: blserver/header_service.cpp.o
.PHONY : blserver/header_service.o

# target to build an object file
blserver/header_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o
.PHONY : blserver/header_service.cpp.o

blserver/header_service.i: blserver/header_service.cpp.i
.PHONY : blserver/header_service.i

# target to preprocess a source file
blserver/header_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.i
.PHONY : blserver/header_service.cpp.i

blserver/header_service.s: blserver/header_service.cpp.s
.PHONY : blserver/header_service.s

# target to generate assembly for a file
blserver/header_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.s
.PHONY : blserver/header_service.cpp.s

blserver/home_service.o: blserver/home_service.cpp.o
.PHONY : blserver/home_service.o

# target to build an object file
blserver/home_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o
.PHONY : blserver/home_service.cpp.o

blserver/home_service.i: blserver/home_service.cpp.i
.PHONY : blserver/home_service.i

# target to preprocess a source file
blserver/home_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.i
.PHONY : blserver/home_service.cpp.i

blserver/home_service.s: blserver/home_service.cpp.s
.PHONY : blserver/home_service.s

# target to generate assembly for a file
blserver/home_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.s
.PHONY : blserver/home_service.cpp.s

blserver/login_service.o: blserver/login_service.cpp.o
.PHONY : blserver/login_service.o

# target to build an object file
blserver/login_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o
.PHONY : blserver/login_service.cpp.o

blserver/login_service.i: blserver/login_service.cpp.i
.PHONY : blserver/login_service.i

# target to preprocess a source file
blserver/login_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.i
.PHONY : blserver/login_service.cpp.i

blserver/login_service.s: blserver/login_service.cpp.s
.PHONY : blserver/login_service.s

# target to generate assembly for a file
blserver/login_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.s
.PHONY : blserver/login_service.cpp.s

blserver/message_handlers.o: blserver/message_handlers.cpp.o
.PHONY : blserver/message_handlers.o

# target to build an object file
blserver/message_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o
.PHONY : blserver/message_handlers.cpp.o

blserver/message_handlers.i: blserver/message_handlers.cpp.i
.PHONY : blserver/message_handlers.i

# target to preprocess a source file
blserver/message_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.i
.PHONY : blserver/message_handlers.cpp.i

blserver/message_handlers.s: blserver/message_handlers.cpp.s
.PHONY : blserver/message_handlers.s

# target to generate assembly for a file
blserver/message_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.s
.PHONY : blserver/message_handlers.cpp.s

blserver/pid_service.o: blserver/pid_service.cpp.o
.PHONY : blserver/pid_service.o

# target to build an object file
blserver/pid_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o
.PHONY : blserver/pid_service.cpp.o

blserver/pid_service.i: blserver/pid_service.cpp.i
.PHONY : blserver/pid_service.i

# target to preprocess a source file
blserver/pid_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.i
.PHONY : blserver/pid_service.cpp.i

blserver/pid_service.s: blserver/pid_service.cpp.s
.PHONY : blserver/pid_service.s

# target to generate assembly for a file
blserver/pid_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.s
.PHONY : blserver/pid_service.cpp.s

blserver/poco_websocket_server.o: blserver/poco_websocket_server.cpp.o
.PHONY : blserver/poco_websocket_server.o

# target to build an object file
blserver/poco_websocket_server.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o
.PHONY : blserver/poco_websocket_server.cpp.o

blserver/poco_websocket_server.i: blserver/poco_websocket_server.cpp.i
.PHONY : blserver/poco_websocket_server.i

# target to preprocess a source file
blserver/poco_websocket_server.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.i
.PHONY : blserver/poco_websocket_server.cpp.i

blserver/poco_websocket_server.s: blserver/poco_websocket_server.cpp.s
.PHONY : blserver/poco_websocket_server.s

# target to generate assembly for a file
blserver/poco_websocket_server.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.s
.PHONY : blserver/poco_websocket_server.cpp.s

blserver/register_service.o: blserver/register_service.cpp.o
.PHONY : blserver/register_service.o

# target to build an object file
blserver/register_service.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o
.PHONY : blserver/register_service.cpp.o

blserver/register_service.i: blserver/register_service.cpp.i
.PHONY : blserver/register_service.i

# target to preprocess a source file
blserver/register_service.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.i
.PHONY : blserver/register_service.cpp.i

blserver/register_service.s: blserver/register_service.cpp.s
.PHONY : blserver/register_service.s

# target to generate assembly for a file
blserver/register_service.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.s
.PHONY : blserver/register_service.cpp.s

blserver/update_models.o: blserver/update_models.cpp.o
.PHONY : blserver/update_models.o

# target to build an object file
blserver/update_models.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o
.PHONY : blserver/update_models.cpp.o

blserver/update_models.i: blserver/update_models.cpp.i
.PHONY : blserver/update_models.i

# target to preprocess a source file
blserver/update_models.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.i
.PHONY : blserver/update_models.cpp.i

blserver/update_models.s: blserver/update_models.cpp.s
.PHONY : blserver/update_models.s

# target to generate assembly for a file
blserver/update_models.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.s
.PHONY : blserver/update_models.cpp.s

blsql/aim_configs_db.o: blsql/aim_configs_db.cpp.o
.PHONY : blsql/aim_configs_db.o

# target to build an object file
blsql/aim_configs_db.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o
.PHONY : blsql/aim_configs_db.cpp.o

blsql/aim_configs_db.i: blsql/aim_configs_db.cpp.i
.PHONY : blsql/aim_configs_db.i

# target to preprocess a source file
blsql/aim_configs_db.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.i
.PHONY : blsql/aim_configs_db.cpp.i

blsql/aim_configs_db.s: blsql/aim_configs_db.cpp.s
.PHONY : blsql/aim_configs_db.s

# target to generate assembly for a file
blsql/aim_configs_db.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.s
.PHONY : blsql/aim_configs_db.cpp.s

blsql/data_collections_db.o: blsql/data_collections_db.cpp.o
.PHONY : blsql/data_collections_db.o

# target to build an object file
blsql/data_collections_db.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o
.PHONY : blsql/data_collections_db.cpp.o

blsql/data_collections_db.i: blsql/data_collections_db.cpp.i
.PHONY : blsql/data_collections_db.i

# target to preprocess a source file
blsql/data_collections_db.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.i
.PHONY : blsql/data_collections_db.cpp.i

blsql/data_collections_db.s: blsql/data_collections_db.cpp.s
.PHONY : blsql/data_collections_db.s

# target to generate assembly for a file
blsql/data_collections_db.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.s
.PHONY : blsql/data_collections_db.cpp.s

blsql/fire_configs_db.o: blsql/fire_configs_db.cpp.o
.PHONY : blsql/fire_configs_db.o

# target to build an object file
blsql/fire_configs_db.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o
.PHONY : blsql/fire_configs_db.cpp.o

blsql/fire_configs_db.i: blsql/fire_configs_db.cpp.i
.PHONY : blsql/fire_configs_db.i

# target to preprocess a source file
blsql/fire_configs_db.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.i
.PHONY : blsql/fire_configs_db.cpp.i

blsql/fire_configs_db.s: blsql/fire_configs_db.cpp.s
.PHONY : blsql/fire_configs_db.s

# target to generate assembly for a file
blsql/fire_configs_db.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.s
.PHONY : blsql/fire_configs_db.cpp.s

blsql/fovconfgidb.o: blsql/fovconfgidb.cpp.o
.PHONY : blsql/fovconfgidb.o

# target to build an object file
blsql/fovconfgidb.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o
.PHONY : blsql/fovconfgidb.cpp.o

blsql/fovconfgidb.i: blsql/fovconfgidb.cpp.i
.PHONY : blsql/fovconfgidb.i

# target to preprocess a source file
blsql/fovconfgidb.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.i
.PHONY : blsql/fovconfgidb.cpp.i

blsql/fovconfgidb.s: blsql/fovconfgidb.cpp.s
.PHONY : blsql/fovconfgidb.s

# target to generate assembly for a file
blsql/fovconfgidb.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.s
.PHONY : blsql/fovconfgidb.cpp.s

blsql/function_configs_db.o: blsql/function_configs_db.cpp.o
.PHONY : blsql/function_configs_db.o

# target to build an object file
blsql/function_configs_db.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o
.PHONY : blsql/function_configs_db.cpp.o

blsql/function_configs_db.i: blsql/function_configs_db.cpp.i
.PHONY : blsql/function_configs_db.i

# target to preprocess a source file
blsql/function_configs_db.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.i
.PHONY : blsql/function_configs_db.cpp.i

blsql/function_configs_db.s: blsql/function_configs_db.cpp.s
.PHONY : blsql/function_configs_db.s

# target to generate assembly for a file
blsql/function_configs_db.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.s
.PHONY : blsql/function_configs_db.cpp.s

blsql/login_db.o: blsql/login_db.cpp.o
.PHONY : blsql/login_db.o

# target to build an object file
blsql/login_db.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o
.PHONY : blsql/login_db.cpp.o

blsql/login_db.i: blsql/login_db.cpp.i
.PHONY : blsql/login_db.i

# target to preprocess a source file
blsql/login_db.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.i
.PHONY : blsql/login_db.cpp.i

blsql/login_db.s: blsql/login_db.cpp.s
.PHONY : blsql/login_db.s

# target to generate assembly for a file
blsql/login_db.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.s
.PHONY : blsql/login_db.cpp.s

blsql/mysql_db.o: blsql/mysql_db.cpp.o
.PHONY : blsql/mysql_db.o

# target to build an object file
blsql/mysql_db.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o
.PHONY : blsql/mysql_db.cpp.o

blsql/mysql_db.i: blsql/mysql_db.cpp.i
.PHONY : blsql/mysql_db.i

# target to preprocess a source file
blsql/mysql_db.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.i
.PHONY : blsql/mysql_db.cpp.i

blsql/mysql_db.s: blsql/mysql_db.cpp.s
.PHONY : blsql/mysql_db.s

# target to generate assembly for a file
blsql/mysql_db.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.s
.PHONY : blsql/mysql_db.cpp.s

blsql/pid_configs_db.o: blsql/pid_configs_db.cpp.o
.PHONY : blsql/pid_configs_db.o

# target to build an object file
blsql/pid_configs_db.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o
.PHONY : blsql/pid_configs_db.cpp.o

blsql/pid_configs_db.i: blsql/pid_configs_db.cpp.i
.PHONY : blsql/pid_configs_db.i

# target to preprocess a source file
blsql/pid_configs_db.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.i
.PHONY : blsql/pid_configs_db.cpp.i

blsql/pid_configs_db.s: blsql/pid_configs_db.cpp.s
.PHONY : blsql/pid_configs_db.s

# target to generate assembly for a file
blsql/pid_configs_db.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.s
.PHONY : blsql/pid_configs_db.cpp.s

blsql/registerdb.o: blsql/registerdb.cpp.o
.PHONY : blsql/registerdb.o

# target to build an object file
blsql/registerdb.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o
.PHONY : blsql/registerdb.cpp.o

blsql/registerdb.i: blsql/registerdb.cpp.i
.PHONY : blsql/registerdb.i

# target to preprocess a source file
blsql/registerdb.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.i
.PHONY : blsql/registerdb.cpp.i

blsql/registerdb.s: blsql/registerdb.cpp.s
.PHONY : blsql/registerdb.s

# target to generate assembly for a file
blsql/registerdb.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.s
.PHONY : blsql/registerdb.cpp.s

blsql/userinfodb.o: blsql/userinfodb.cpp.o
.PHONY : blsql/userinfodb.o

# target to build an object file
blsql/userinfodb.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o
.PHONY : blsql/userinfodb.cpp.o

blsql/userinfodb.i: blsql/userinfodb.cpp.i
.PHONY : blsql/userinfodb.i

# target to preprocess a source file
blsql/userinfodb.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.i
.PHONY : blsql/userinfodb.cpp.i

blsql/userinfodb.s: blsql/userinfodb.cpp.s
.PHONY : blsql/userinfodb.s

# target to generate assembly for a file
blsql/userinfodb.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.s
.PHONY : blsql/userinfodb.cpp.s

handlers/aim_handlers.o: handlers/aim_handlers.cpp.o
.PHONY : handlers/aim_handlers.o

# target to build an object file
handlers/aim_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o
.PHONY : handlers/aim_handlers.cpp.o

handlers/aim_handlers.i: handlers/aim_handlers.cpp.i
.PHONY : handlers/aim_handlers.i

# target to preprocess a source file
handlers/aim_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.i
.PHONY : handlers/aim_handlers.cpp.i

handlers/aim_handlers.s: handlers/aim_handlers.cpp.s
.PHONY : handlers/aim_handlers.s

# target to generate assembly for a file
handlers/aim_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.s
.PHONY : handlers/aim_handlers.cpp.s

handlers/data_collection_handlers.o: handlers/data_collection_handlers.cpp.o
.PHONY : handlers/data_collection_handlers.o

# target to build an object file
handlers/data_collection_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o
.PHONY : handlers/data_collection_handlers.cpp.o

handlers/data_collection_handlers.i: handlers/data_collection_handlers.cpp.i
.PHONY : handlers/data_collection_handlers.i

# target to preprocess a source file
handlers/data_collection_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.i
.PHONY : handlers/data_collection_handlers.cpp.i

handlers/data_collection_handlers.s: handlers/data_collection_handlers.cpp.s
.PHONY : handlers/data_collection_handlers.s

# target to generate assembly for a file
handlers/data_collection_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.s
.PHONY : handlers/data_collection_handlers.cpp.s

handlers/fire_handlers.o: handlers/fire_handlers.cpp.o
.PHONY : handlers/fire_handlers.o

# target to build an object file
handlers/fire_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o
.PHONY : handlers/fire_handlers.cpp.o

handlers/fire_handlers.i: handlers/fire_handlers.cpp.i
.PHONY : handlers/fire_handlers.i

# target to preprocess a source file
handlers/fire_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.i
.PHONY : handlers/fire_handlers.cpp.i

handlers/fire_handlers.s: handlers/fire_handlers.cpp.s
.PHONY : handlers/fire_handlers.s

# target to generate assembly for a file
handlers/fire_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.s
.PHONY : handlers/fire_handlers.cpp.s

handlers/fov_handlers.o: handlers/fov_handlers.cpp.o
.PHONY : handlers/fov_handlers.o

# target to build an object file
handlers/fov_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o
.PHONY : handlers/fov_handlers.cpp.o

handlers/fov_handlers.i: handlers/fov_handlers.cpp.i
.PHONY : handlers/fov_handlers.i

# target to preprocess a source file
handlers/fov_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.i
.PHONY : handlers/fov_handlers.cpp.i

handlers/fov_handlers.s: handlers/fov_handlers.cpp.s
.PHONY : handlers/fov_handlers.s

# target to generate assembly for a file
handlers/fov_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.s
.PHONY : handlers/fov_handlers.cpp.s

handlers/function_handlers.o: handlers/function_handlers.cpp.o
.PHONY : handlers/function_handlers.o

# target to build an object file
handlers/function_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o
.PHONY : handlers/function_handlers.cpp.o

handlers/function_handlers.i: handlers/function_handlers.cpp.i
.PHONY : handlers/function_handlers.i

# target to preprocess a source file
handlers/function_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.i
.PHONY : handlers/function_handlers.cpp.i

handlers/function_handlers.s: handlers/function_handlers.cpp.s
.PHONY : handlers/function_handlers.s

# target to generate assembly for a file
handlers/function_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.s
.PHONY : handlers/function_handlers.cpp.s

handlers/header_handlers.o: handlers/header_handlers.cpp.o
.PHONY : handlers/header_handlers.o

# target to build an object file
handlers/header_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o
.PHONY : handlers/header_handlers.cpp.o

handlers/header_handlers.i: handlers/header_handlers.cpp.i
.PHONY : handlers/header_handlers.i

# target to preprocess a source file
handlers/header_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.i
.PHONY : handlers/header_handlers.cpp.i

handlers/header_handlers.s: handlers/header_handlers.cpp.s
.PHONY : handlers/header_handlers.s

# target to generate assembly for a file
handlers/header_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.s
.PHONY : handlers/header_handlers.cpp.s

handlers/home_handlers.o: handlers/home_handlers.cpp.o
.PHONY : handlers/home_handlers.o

# target to build an object file
handlers/home_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o
.PHONY : handlers/home_handlers.cpp.o

handlers/home_handlers.i: handlers/home_handlers.cpp.i
.PHONY : handlers/home_handlers.i

# target to preprocess a source file
handlers/home_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.i
.PHONY : handlers/home_handlers.cpp.i

handlers/home_handlers.s: handlers/home_handlers.cpp.s
.PHONY : handlers/home_handlers.s

# target to generate assembly for a file
handlers/home_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.s
.PHONY : handlers/home_handlers.cpp.s

handlers/login_handlers.o: handlers/login_handlers.cpp.o
.PHONY : handlers/login_handlers.o

# target to build an object file
handlers/login_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o
.PHONY : handlers/login_handlers.cpp.o

handlers/login_handlers.i: handlers/login_handlers.cpp.i
.PHONY : handlers/login_handlers.i

# target to preprocess a source file
handlers/login_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.i
.PHONY : handlers/login_handlers.cpp.i

handlers/login_handlers.s: handlers/login_handlers.cpp.s
.PHONY : handlers/login_handlers.s

# target to generate assembly for a file
handlers/login_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.s
.PHONY : handlers/login_handlers.cpp.s

handlers/pid_handlers.o: handlers/pid_handlers.cpp.o
.PHONY : handlers/pid_handlers.o

# target to build an object file
handlers/pid_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o
.PHONY : handlers/pid_handlers.cpp.o

handlers/pid_handlers.i: handlers/pid_handlers.cpp.i
.PHONY : handlers/pid_handlers.i

# target to preprocess a source file
handlers/pid_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.i
.PHONY : handlers/pid_handlers.cpp.i

handlers/pid_handlers.s: handlers/pid_handlers.cpp.s
.PHONY : handlers/pid_handlers.s

# target to generate assembly for a file
handlers/pid_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.s
.PHONY : handlers/pid_handlers.cpp.s

handlers/register_handlers.o: handlers/register_handlers.cpp.o
.PHONY : handlers/register_handlers.o

# target to build an object file
handlers/register_handlers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o
.PHONY : handlers/register_handlers.cpp.o

handlers/register_handlers.i: handlers/register_handlers.cpp.i
.PHONY : handlers/register_handlers.i

# target to preprocess a source file
handlers/register_handlers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.i
.PHONY : handlers/register_handlers.cpp.i

handlers/register_handlers.s: handlers/register_handlers.cpp.s
.PHONY : handlers/register_handlers.s

# target to generate assembly for a file
handlers/register_handlers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/blweb_backend.dir/build.make backend/CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.s
.PHONY : handlers/register_handlers.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo && $(MAKE) $(MAKESILENT) -f backend/CMakeFiles/bl_server.dir/build.make backend/CMakeFiles/bl_server.dir/main.cpp.s
.PHONY : main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... bl_server"
	@echo "... blweb_backend"
	@echo "... __/ThreadManager/web_server.o"
	@echo "... __/ThreadManager/web_server.i"
	@echo "... __/ThreadManager/web_server.s"
	@echo "... blserver/aim_service.o"
	@echo "... blserver/aim_service.i"
	@echo "... blserver/aim_service.s"
	@echo "... blserver/data_collection_service.o"
	@echo "... blserver/data_collection_service.i"
	@echo "... blserver/data_collection_service.s"
	@echo "... blserver/fire_service.o"
	@echo "... blserver/fire_service.i"
	@echo "... blserver/fire_service.s"
	@echo "... blserver/fov_service.o"
	@echo "... blserver/fov_service.i"
	@echo "... blserver/fov_service.s"
	@echo "... blserver/function_service.o"
	@echo "... blserver/function_service.i"
	@echo "... blserver/function_service.s"
	@echo "... blserver/header_service.o"
	@echo "... blserver/header_service.i"
	@echo "... blserver/header_service.s"
	@echo "... blserver/home_service.o"
	@echo "... blserver/home_service.i"
	@echo "... blserver/home_service.s"
	@echo "... blserver/login_service.o"
	@echo "... blserver/login_service.i"
	@echo "... blserver/login_service.s"
	@echo "... blserver/message_handlers.o"
	@echo "... blserver/message_handlers.i"
	@echo "... blserver/message_handlers.s"
	@echo "... blserver/pid_service.o"
	@echo "... blserver/pid_service.i"
	@echo "... blserver/pid_service.s"
	@echo "... blserver/poco_websocket_server.o"
	@echo "... blserver/poco_websocket_server.i"
	@echo "... blserver/poco_websocket_server.s"
	@echo "... blserver/register_service.o"
	@echo "... blserver/register_service.i"
	@echo "... blserver/register_service.s"
	@echo "... blserver/update_models.o"
	@echo "... blserver/update_models.i"
	@echo "... blserver/update_models.s"
	@echo "... blsql/aim_configs_db.o"
	@echo "... blsql/aim_configs_db.i"
	@echo "... blsql/aim_configs_db.s"
	@echo "... blsql/data_collections_db.o"
	@echo "... blsql/data_collections_db.i"
	@echo "... blsql/data_collections_db.s"
	@echo "... blsql/fire_configs_db.o"
	@echo "... blsql/fire_configs_db.i"
	@echo "... blsql/fire_configs_db.s"
	@echo "... blsql/fovconfgidb.o"
	@echo "... blsql/fovconfgidb.i"
	@echo "... blsql/fovconfgidb.s"
	@echo "... blsql/function_configs_db.o"
	@echo "... blsql/function_configs_db.i"
	@echo "... blsql/function_configs_db.s"
	@echo "... blsql/login_db.o"
	@echo "... blsql/login_db.i"
	@echo "... blsql/login_db.s"
	@echo "... blsql/mysql_db.o"
	@echo "... blsql/mysql_db.i"
	@echo "... blsql/mysql_db.s"
	@echo "... blsql/pid_configs_db.o"
	@echo "... blsql/pid_configs_db.i"
	@echo "... blsql/pid_configs_db.s"
	@echo "... blsql/registerdb.o"
	@echo "... blsql/registerdb.i"
	@echo "... blsql/registerdb.s"
	@echo "... blsql/userinfodb.o"
	@echo "... blsql/userinfodb.i"
	@echo "... blsql/userinfodb.s"
	@echo "... handlers/aim_handlers.o"
	@echo "... handlers/aim_handlers.i"
	@echo "... handlers/aim_handlers.s"
	@echo "... handlers/data_collection_handlers.o"
	@echo "... handlers/data_collection_handlers.i"
	@echo "... handlers/data_collection_handlers.s"
	@echo "... handlers/fire_handlers.o"
	@echo "... handlers/fire_handlers.i"
	@echo "... handlers/fire_handlers.s"
	@echo "... handlers/fov_handlers.o"
	@echo "... handlers/fov_handlers.i"
	@echo "... handlers/fov_handlers.s"
	@echo "... handlers/function_handlers.o"
	@echo "... handlers/function_handlers.i"
	@echo "... handlers/function_handlers.s"
	@echo "... handlers/header_handlers.o"
	@echo "... handlers/header_handlers.i"
	@echo "... handlers/header_handlers.s"
	@echo "... handlers/home_handlers.o"
	@echo "... handlers/home_handlers.i"
	@echo "... handlers/home_handlers.s"
	@echo "... handlers/login_handlers.o"
	@echo "... handlers/login_handlers.i"
	@echo "... handlers/login_handlers.s"
	@echo "... handlers/pid_handlers.o"
	@echo "... handlers/pid_handlers.i"
	@echo "... handlers/pid_handlers.s"
	@echo "... handlers/register_handlers.o"
	@echo "... handlers/register_handlers.i"
	@echo "... handlers/register_handlers.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/mywork/poco_serverdemo && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

