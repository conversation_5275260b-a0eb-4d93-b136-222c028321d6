#include "function_handlers.h"
#include "../blserver/function_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blserver/update_models.h"

#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <sstream>

namespace MessageHandlers {
namespace Function {

// 处理功能配置修改请求
std::string handleFunctionModify(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "function_modify_response");
    
    try {
        // 1. 从请求中提取必要参数 - 增强参数验证
        std::string username = content->optValue("username", std::string(""));
        std::string gameName = content->optValue("gameName", std::string(""));
        
        LOG_INFO("功能配置修改请求 - 用户: '{}', 指定游戏: '{}'", username, gameName);
        
        // 2. 参数验证 - 确保用户名和游戏名都不为空
        if (username.empty()) {
            LOG_ERROR("功能配置修改失败: 用户名不能为空");
            response.set("status", "error");
            response.set("message", "用户名不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        if (gameName.empty()) {
            LOG_ERROR("功能配置修改失败: 游戏名称不能为空");
            response.set("status", "error");
            response.set("message", "游戏名称不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 检查是否有配置数组
        if (!content->isArray("configs")) {
            LOG_ERROR("功能配置修改失败: 请求中缺少configs数组");
            response.set("status", "error");
            response.set("message", "请求缺少配置数组");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 3. 获取配置数组
        Poco::JSON::Array::Ptr configs = content->getArray("configs");
        LOG_INFO("接收到游戏 '{}' 的{}个功能配置进行更新", gameName, configs->size());
        
        // 4. 【关键步骤】先更新C++全局变量 - 确保实时生效
        LOG_INFO("步骤1: 更新游戏 '{}' 的C++全局变量", gameName);
        
        // 直接更新用户名和游戏名
        webui::home::g_username = username;
        webui::home::g_game_name = gameName;
        
        // 通过updateGlobalModels更新功能配置的全局变量
        bool globalUpdated = blweb::utils::updateGlobalModels("function_modify", content);
        if (!globalUpdated) {
            LOG_WARNING("updateGlobalModels调用失败，但基本参数已更新");
        }
        
        LOG_INFO("C++全局变量更新完成 - 游戏: '{}', username: '{}', aiMode: '{}', lockPosition: '{}', hotkey: '{}', activeConfigIndex: {}", 
                 gameName, webui::home::g_username, webui::function::g_ai_mode,
                 webui::function::g_lock_position, webui::function::g_hotkey, webui::function::g_active_config_index);
        
        // 5. 【第二步骤】更新数据库 - 持久化存储到指定游戏
        LOG_INFO("步骤2: 将游戏 '{}' 的配置更新到数据库", gameName);
        blweb::services::FunctionService& functionService = blweb::services::FunctionService::getInstance();
        Poco::JSON::Object::Ptr result = functionService.updateFunctionConfigs(username, gameName, configs);
        
        // 6. 构建响应 - 即使数据库更新失败，全局变量已更新，系统仍可正常工作
        if (result->getValue<std::string>("status") == "success") {
            LOG_INFO("游戏 '{}' 功能配置修改完成: 全局变量和数据库都已更新", gameName);
            response.set("status", "ok");
        } else {
            std::string error = result->getValue<std::string>("message");
            LOG_WARNING("游戏 '{}' 功能配置部分更新: 全局变量已更新，但数据库更新失败: {}", gameName, error);
            response.set("status", "warning");
            response.set("message", "配置已实时生效，但数据库保存失败: " + error);
        }
        
        // 7. 创建响应数据对象 - 返回实际的全局变量值
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", webui::home::g_username);
        data->set("gameName", webui::home::g_game_name);
        
        // 构建配置数组，使用实际的全局变量值
        Poco::JSON::Array::Ptr responseConfigs = new Poco::JSON::Array();
        for (size_t i = 0; i < webui::function::PRESET_CONFIGS.size(); ++i) {
            Poco::JSON::Object::Ptr configObj = new Poco::JSON::Object();
            configObj->set("presetName", webui::function::PRESET_CONFIGS[i].name);
            configObj->set("aiMode", webui::function::PRESET_CONFIGS[i].ai_mode);
            configObj->set("lockPosition", webui::function::PRESET_CONFIGS[i].lock_position);
            configObj->set("selectedFaction", webui::function::PRESET_CONFIGS[i].selected_faction);
            configObj->set("hotkey", webui::function::PRESET_CONFIGS[i].hotkey);
            configObj->set("triggerSwitch", webui::function::PRESET_CONFIGS[i].trigger_switch == "true");
            configObj->set("weaponSwitch", webui::function::PRESET_CONFIGS[i].weapon_switch == "true");
            configObj->set("flashShield", webui::function::PRESET_CONFIGS[i].flash_shield == "true");
            configObj->set("enabled", webui::function::PRESET_CONFIGS[i].enabled == "true");
            responseConfigs->add(configObj);
        }
        
        data->set("configs", responseConfigs);
        data->set("activeConfigIndex", webui::function::g_active_config_index);
        
        // 获取当前时间并格式化
        Poco::DateTime now;
        std::string createdAt = content->optValue("createdAt", 
            Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ"));
        std::string updatedAt = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
        
        data->set("createdAt", createdAt);
        data->set("updatedAt", updatedAt);
        
        response.set("data", data);
        
    }
    catch (const std::exception& e) {
        std::string error = "处理功能配置修改异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理功能配置读取请求
std::string handleFunctionRead(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "function_read_response");
    
    try {
        // 1. 从请求中提取必要参数 - 增强参数验证
        std::string username = content->optValue("username", std::string(""));
        std::string gameName = content->optValue("gameName", std::string(""));
        
        LOG_INFO("功能设置读取请求 - 用户: '{}', 游戏: '{}'", username, gameName);
        
        // 2. 参数验证 - 确保用户名和游戏名都不为空
        if (username.empty()) {
            LOG_ERROR("功能配置读取失败: 用户名不能为空");
            response.set("status", "error");
            response.set("message", "用户名不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        if (gameName.empty()) {
            LOG_ERROR("功能配置读取失败: 游戏名称不能为空");
            response.set("status", "error");
            response.set("message", "游戏名称不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 3. 【关键步骤】从数据库获取指定游戏的最新功能配置数据
        LOG_INFO("步骤1: 从数据库获取游戏 '{}' 的功能配置", gameName);
        blweb::services::FunctionService& functionService = blweb::services::FunctionService::getInstance();
        Poco::JSON::Object::Ptr result = functionService.getFunctionConfigs(username, gameName);
        
        // 4. 【关键步骤】用数据库数据更新全局变量
        if (result->getValue<std::string>("status") == "success" && result->has("data")) {
            Poco::JSON::Object::Ptr data = result->getObject("data");
            
            LOG_INFO("步骤2: 用游戏 '{}' 的数据库数据更新C++全局变量", gameName);
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 如果数据中包含有效配置，用数据库数据更新全局变量
            if (data->isArray("configs") && data->getArray("configs")->size() > 0) {
                LOG_INFO("使用数据库中游戏 '{}' 的配置更新全局变量，配置数量: {}", 
                        gameName, data->getArray("configs")->size());
                
                // 创建包含完整数据的内容对象用于更新全局变量
                Poco::JSON::Object::Ptr updateContent = new Poco::JSON::Object();
                updateContent->set("username", username);
                updateContent->set("gameName", gameName);
                updateContent->set("configs", data->getArray("configs"));
                if (data->has("activeConfigIndex")) {
                    updateContent->set("activeConfigIndex", data->getValue<int>("activeConfigIndex"));
                }
                
                // 用数据库数据更新全局变量
                blweb::utils::updateGlobalModels("function_read", updateContent);
            } else {
                LOG_INFO("游戏 '{}' 的数据库配置为空，使用默认值更新全局变量", gameName);
                
                // 使用默认值更新全局变量
                webui::function::g_active_config_index = 0;
                if (webui::function::g_active_config_index < webui::function::PRESET_CONFIGS.size()) {
                    webui::function::g_ai_mode = webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].ai_mode;
                    webui::function::g_lock_position = webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].lock_position;
                    webui::function::g_hotkey = webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].hotkey;
                }
            }
            
            LOG_INFO("C++全局变量更新完成 - 游戏: '{}', username: '{}', aiMode: '{}', lockPosition: '{}', hotkey: '{}', activeConfigIndex: {}", 
                     gameName, webui::home::g_username, webui::function::g_ai_mode,
                     webui::function::g_lock_position, webui::function::g_hotkey, webui::function::g_active_config_index);
            
            // 5. 确保响应数据包含所有必需字段
            if (!data->has("username")) data->set("username", username);
            if (!data->has("gameName")) data->set("gameName", gameName);
            
            // 如果数据库没有配置，创建基于当前全局变量的默认配置
            if (!data->isArray("configs") || data->getArray("configs")->size() == 0) {
                LOG_INFO("为游戏 '{}' 创建默认配置", gameName);
                Poco::JSON::Array::Ptr configs = new Poco::JSON::Array();
                for (const auto& config : webui::function::PRESET_CONFIGS) {
                    Poco::JSON::Object::Ptr configObj = new Poco::JSON::Object();
                    configObj->set("presetName", config.name);
                    configObj->set("aiMode", config.ai_mode);
                    configObj->set("lockPosition", config.lock_position);
                    configObj->set("selectedFaction", config.selected_faction);
                    configObj->set("hotkey", config.hotkey);
                    configObj->set("triggerSwitch", config.trigger_switch == "true");
                    configObj->set("weaponSwitch", config.weapon_switch == "true");
                    configObj->set("flashShield", config.flash_shield == "true");
                    configObj->set("enabled", config.enabled == "true");
                    configs->add(configObj);
                }
                data->set("configs", configs);
            }
            
            // 确保activeConfigIndex存在
            if (!data->has("activeConfigIndex")) {
                data->set("activeConfigIndex", webui::function::g_active_config_index);
            }
            
            // 6. 设置成功响应
            response.set("status", "ok");
            response.set("data", data);
            
        } else {
            // 数据库无数据或查询失败，使用默认值并更新全局变量
            LOG_INFO("游戏 '{}' 数据库无数据或查询失败，使用默认值并更新全局变量", gameName);
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 使用默认值更新全局变量
            webui::function::g_active_config_index = 0;
            if (webui::function::g_active_config_index < webui::function::PRESET_CONFIGS.size()) {
                webui::function::g_ai_mode = webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].ai_mode;
                webui::function::g_lock_position = webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].lock_position;
                webui::function::g_hotkey = webui::function::PRESET_CONFIGS[webui::function::g_active_config_index].hotkey;
            }
            
            LOG_INFO("使用默认值更新全局变量完成 - 游戏: '{}'", gameName);
            
            // 创建默认数据对象 - 使用API文档定义的默认值
            Poco::JSON::Object::Ptr defaultData = new Poco::JSON::Object();
            defaultData->set("username", username);
            defaultData->set("gameName", gameName);
            
            // 创建默认配置数组
            Poco::JSON::Array::Ptr configs = new Poco::JSON::Array();
            for (const auto& config : webui::function::PRESET_CONFIGS) {
                Poco::JSON::Object::Ptr configObj = new Poco::JSON::Object();
                configObj->set("presetName", config.name);
                configObj->set("aiMode", config.ai_mode);
                configObj->set("lockPosition", config.lock_position);
                configObj->set("selectedFaction", config.selected_faction);
                configObj->set("hotkey", config.hotkey);
                configObj->set("triggerSwitch", config.trigger_switch == "true");
                configObj->set("enabled", config.enabled == "true");
                configs->add(configObj);
            }
            
            defaultData->set("configs", configs);
            defaultData->set("activeConfigIndex", webui::function::g_active_config_index);
            defaultData->set("exists", false);
            
            // 获取当前时间作为时间戳
            Poco::DateTime now;
            std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
            defaultData->set("createdAt", timeStr);
            defaultData->set("updatedAt", timeStr);
            
            response.set("status", "ok");
            response.set("data", defaultData);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理功能配置读取异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace Function
} // namespace MessageHandlers 

/*
 * 功能配置处理逻辑说明 (2024年修改)：
 * 
 * === 功能配置修改逻辑 (function_modify) ===
 * 1. 【优先级1】先更新C++全局变量 - 确保实时生效
 *    - 直接更新webui::home命名空间中的用户名和游戏名
 *    - 通过updateGlobalModels更新webui::function命名空间中的所有功能配置变量
 *    - 这确保了功能配置能立即生效
 *    - 即使数据库更新失败，系统仍能正常工作
 * 
 * 2. 【优先级2】再更新数据库 - 持久化存储
 *    - 调用FunctionService进行数据库更新
 *    - 如果失败，返回warning状态但不影响实时功能
 * 
 * 3. 【响应策略】
 *    - 成功：全局变量和数据库都更新成功
 *    - 警告：全局变量更新成功，数据库更新失败
 *    - 错误：参数验证失败或异常情况
 * 
 * === 功能配置读取逻辑 (function_read) ===
 * 1. 【步骤1】从数据库获取最新的功能配置数据
 *    - 调用FunctionService从数据库读取用户在指定游戏的功能配置
 *    - 确保获取到的是最新的持久化数据
 * 
 * 2. 【步骤2】用数据库数据更新C++全局变量
 *    - 将数据库中的配置值更新到webui::function命名空间的全局变量中
 *    - 确保功能配置使用的是数据库中的最新配置
 *    - 如果数据库无数据，使用默认配置更新全局变量
 * 
 * 3. 【步骤3】返回给前端
 *    - 将更新后的全局变量值返回给前端
 *    - 确保前端显示的值与后端实际使用的值一致
 * 
 * 字段映射 (API -> 数据库 -> 全局变量)：
 * - configs数组 -> function_configs表 -> PRESET_CONFIGS数组
 * - activeConfigIndex -> active_config_index -> g_active_config_index
 * - aiMode -> ai_mode -> g_ai_mode
 * - lockPosition -> lock_position -> g_lock_position
 * - hotkey -> hotkey -> g_hotkey
 */ 