#include "home_handlers.h"
#include "../blserver/home_service.h"
#include "../blserver/login_service.h"
#include "../blserver/update_models.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"

#include <sstream>

namespace blweb {
namespace utils {
    extern bool updateGlobalModels(const std::string& action, const Poco::JSON::Object::Ptr& content);
}
}

namespace MessageHandlers {
namespace Home {

namespace {
    // 常量定义
    const std::string ACTION_HOME_MODIFY_RESPONSE = "home_modify_response";
    const std::string ACTION_HOME_READ = "home_read";
    const std::string STATUS_OK = "ok";
    const std::string STATUS_ERROR = "error";
    
    // 辅助函数：构建响应数据
    Poco::JSON::Object::Ptr buildResponseData(const std::string& username, 
                                               const std::string& gameName, 
                                               const std::string& cardKey) {
        auto data = new Poco::JSON::Object();
        data->set("username", username);
        data->set("gameName", gameName);
        data->set("cardKey", cardKey);
        
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
        data->set("updatedAt", timeStr);
        
        return data;
    }
    
    // 辅助函数：更新全局状态
    void updateGlobalState(const std::string& username, const std::string& gameName, 
                          const std::string& cardKey, bool isPro) {
        webui::home::g_username = username;
        webui::home::g_game_name = gameName;
        webui::home::g_card_key = cardKey;
        jfkm_config::g_card_key = cardKey;
        webui::header::isPro = isPro;
    }
    
    // 辅助函数：检查配置有效性
    bool isConfigValid(const std::string& cardKey) {
        return !cardKey.empty();
    }
}

std::string handleHomeModify(const Poco::JSON::Object::Ptr& content) {
    Poco::JSON::Object response;
    response.set("action", ACTION_HOME_MODIFY_RESPONSE);
    
    try {
        // 提取请求参数
        std::string username = content->getValue<std::string>("username");
        std::string gameName = content->getValue<std::string>("gameName");
        std::string cardKey = content->getValue<std::string>("cardKey");
        // 注意：不再直接使用客户端传递的isPro值，从数据库获取真实状态
        
        LOG_INFO("首页配置修改 - 用户: {}, 游戏: {}", username, gameName);
        
        // 首先从数据库获取用户的真实Pro状态
        blweb::services::LoginService& loginService = blweb::services::LoginService::getInstance();
        auto userResult = loginService.getUserLoginInfo(username);
        bool dbIsPro = false;
        
        if (userResult->getValue<int>("code") == 200 && userResult->has("data")) {
            auto userData = userResult->getObject("data");
            if (userData->has("isPro")) {
                dbIsPro = userData->getValue<bool>("isPro");
                LOG_INFO("从数据库获取用户 '{}' 的Pro状态: {}", username, dbIsPro ? "Pro版" : "普通版");
            }
        }
        
        // 更新全局状态 - 使用数据库中的真实Pro状态
        updateGlobalState(username, gameName, cardKey, dbIsPro);
        webui::home::forceAuthNow = true;
        yolo_config::g_reload_model = true;
        
        // 构建包含真实isPro状态的内容对象
        auto modifyContent = new Poco::JSON::Object();
        modifyContent->set("username", username);
        modifyContent->set("gameName", gameName);
        modifyContent->set("cardKey", cardKey);
        modifyContent->set("isPro", dbIsPro);  // 使用数据库中的真实值
        
        // 调用updateGlobalModels来正确处理isPro字段
        blweb::utils::updateGlobalModels("home_modify", modifyContent);
        
        // 调用服务层处理
        auto& homeService = blweb::services::HomeService::getInstance();
        bool success = homeService.updateHomeConfig(username, gameName, cardKey);
        
        if (success) {
            response.set("status", STATUS_OK);
            response.set("data", buildResponseData(username, gameName, cardKey));
        } else {
            response.set("status", STATUS_ERROR);
            response.set("message", "更新失败: " + homeService.getLastError());
        }
    }
    catch (const std::exception& e) {
        LOG_ERROR("首页配置修改异常: {}", e.what());
        response.set("status", STATUS_ERROR);
        response.set("message", "处理异常: " + std::string(e.what()));
    }
    
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

std::string handleHomeRead(const Poco::JSON::Object::Ptr& content) {
    Poco::JSON::Object response;
    response.set("action", ACTION_HOME_READ);
    
    try {
        // 提取请求参数
        std::string username = content->getValue<std::string>("username");
        std::string gameName = content->optValue("gameName", std::string(""));
        std::string cardKey = content->optValue("cardKey", std::string(""));
        // 注意：不再直接使用客户端传递的isPro值，而是从数据库获取
        
        LOG_INFO("首页配置读取 - 用户: {}, 游戏: {}", username, gameName);
        
        // 首先从数据库获取用户的真实Pro状态
        blweb::services::LoginService& loginService = blweb::services::LoginService::getInstance();
        auto userResult = loginService.getUserLoginInfo(username);
        bool dbIsPro = false;
        
        if (userResult->getValue<int>("code") == 200 && userResult->has("data")) {
            auto userData = userResult->getObject("data");
            if (userData->has("isPro")) {
                dbIsPro = userData->getValue<bool>("isPro");
                LOG_INFO("从数据库获取用户 '{}' 的Pro状态: {}", username, dbIsPro ? "Pro版" : "普通版");
            }
        }
        
        // 更新全局状态 - 使用数据库中的真实Pro状态
        webui::header::isPro = dbIsPro;
        
        // 构建基本内容用于全局变量更新，使用数据库中的isPro值
        auto basicContent = new Poco::JSON::Object();
        basicContent->set("username", username);
        basicContent->set("isPro", dbIsPro);  // 使用数据库中的真实值
        if (!gameName.empty()) basicContent->set("gameName", gameName);
        if (!cardKey.empty()) basicContent->set("cardKey", cardKey);
        
        blweb::utils::updateGlobalModels("home_read", basicContent);
        
        // 从数据库获取配置
        auto& homeService = blweb::services::HomeService::getInstance();
        auto result = homeService.getHomeConfig(username, gameName);
        
        int code = result->getValue<int>("code");
        if (code == 200) {
            response.set("status", STATUS_OK);
            
            if (result->has("data")) {
                auto data = result->getObject("data");
                
                // 确保必要字段存在
                if (!data->has("username")) data->set("username", username);
                if (!data->has("gameName") && !gameName.empty()) data->set("gameName", gameName);
                
                // 更新全局变量
                if (data->has("gameName")) {
                    webui::home::g_game_name = data->getValue<std::string>("gameName");
                }
                if (data->has("cardKey")) {
                    std::string dbCardKey = data->getValue<std::string>("cardKey");
                    webui::home::g_card_key = dbCardKey;
                    jfkm_config::g_card_key = dbCardKey;
                }
                
                response.set("data", data);
            } else {
                // 返回默认数据
                auto defaultData = buildResponseData(username, gameName, webui::home::g_card_key);
                response.set("data", defaultData);
            }
        } else {
            response.set("status", STATUS_ERROR);
            response.set("message", result->getValue<std::string>("msg"));
        }
    }
    catch (const std::exception& e) {
        LOG_ERROR("首页配置读取异常: {}", e.what());
        response.set("status", STATUS_ERROR);
        response.set("message", "处理异常: " + std::string(e.what()));
    }
    
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace Home
} // namespace MessageHandlers 