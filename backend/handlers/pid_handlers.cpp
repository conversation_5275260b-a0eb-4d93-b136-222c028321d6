#include "pid_handlers.h"
#include "../blserver/pid_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blserver/update_models.h"

#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <sstream>

namespace MessageHandlers {
namespace PID {

// 处理PID配置修改请求
std::string handlePidModify(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "pid_modify_response");
    
    try {
        // 1. 从请求中提取必要参数 - 按照API文档定义的完整参数
        std::string username = content->optValue("username", std::string(""));
        std::string gameName = content->optValue("gameName", std::string(""));
        double nearMoveFactor = content->optValue("nearMoveFactor", 1.0);
        double nearStabilizer = content->optValue("nearStabilizer", 0.5);
        double nearResponseRate = content->optValue("nearResponseRate", 0.3);
        double nearAssistZone = content->optValue("nearAssistZone", 3.0);
        double nearResponseDelay = content->optValue("nearResponseDelay", 1.0);
        double nearMaxAdjustment = content->optValue("nearMaxAdjustment", 2.0);
        double farFactor = content->optValue("farFactor", 1.0);
        double yAxisFactor = content->optValue("yAxisFactor", 1.0);
        double pidRandomFactor = content->optValue("pidRandomFactor", 0.5);
        
        LOG_INFO("PID配置修改请求 - 用户名: {}, 游戏: {}", username, gameName);
        LOG_INFO("PID参数: nearMoveFactor={}, nearStabilizer={}, nearResponseRate={}, nearAssistZone={}, nearResponseDelay={}, nearMaxAdjustment={}, farFactor={}, yAxisFactor={}, pidRandomFactor={}", 
                 nearMoveFactor, nearStabilizer, nearResponseRate, nearAssistZone, nearResponseDelay, nearMaxAdjustment, farFactor, yAxisFactor, pidRandomFactor);
        
        // 2. 参数验证
        if (username.empty()) {
            LOG_ERROR("PID配置修改失败: 用户名不能为空");
            response.set("status", "error");
            response.set("message", "用户名不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        if (gameName.empty()) {
            LOG_ERROR("PID配置修改失败: 游戏名称不能为空");
            response.set("status", "error");
            response.set("message", "游戏名称不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 3. 【关键步骤】先更新C++全局变量 - 确保实时生效
        LOG_INFO("步骤1: 更新C++全局变量");
        
        // 直接更新全局变量，确保立即生效
        webui::home::g_username = username;
        webui::home::g_game_name = gameName;
        webui::pid::g_near_move_factor = nearMoveFactor;
        webui::pid::g_near_stabilizer = nearStabilizer;
        webui::pid::g_near_response_rate = nearResponseRate;
        webui::pid::g_near_assist_zone = nearAssistZone;
        webui::pid::g_near_response_delay = nearResponseDelay;
        webui::pid::g_near_max_adjustment = nearMaxAdjustment;
        webui::pid::g_far_factor = farFactor;
        webui::pid::g_y_axis_factor = yAxisFactor;
        webui::pid::g_pid_random_factor = pidRandomFactor;
        
        LOG_INFO("C++全局变量更新完成: nearMoveFactor={}, nearStabilizer={}, nearResponseRate={}, nearAssistZone={}, nearResponseDelay={}, nearMaxAdjustment={}, farFactor={}, yAxisFactor={}, pidRandomFactor={}", 
                 webui::pid::g_near_move_factor, webui::pid::g_near_stabilizer, webui::pid::g_near_response_rate,
                 webui::pid::g_near_assist_zone, webui::pid::g_near_response_delay, webui::pid::g_near_max_adjustment,
                 webui::pid::g_far_factor, webui::pid::g_y_axis_factor, webui::pid::g_pid_random_factor);
        
        // 4. 【可选步骤】通过updateGlobalModels进行额外的全局变量处理（如果有其他逻辑）
        bool globalUpdated = blweb::utils::updateGlobalModels("pid_modify", content);
        if (!globalUpdated) {
            LOG_WARNING("updateGlobalModels调用失败，但全局变量已直接更新，继续执行");
        }
        
        // 5. 【第二步骤】更新数据库 - 持久化存储
        LOG_INFO("步骤2: 更新数据库");
        blweb::services::PidService& pidService = blweb::services::PidService::getInstance();
        bool dbSuccess = pidService.updatePidConfig(
            username, gameName, nearMoveFactor, nearStabilizer, nearResponseRate, 
            nearAssistZone, nearResponseDelay, nearMaxAdjustment, farFactor, yAxisFactor, pidRandomFactor
        );
        
        // 6. 构建响应 - 即使数据库更新失败，全局变量已更新，系统仍可正常工作
        if (dbSuccess) {
            LOG_INFO("PID配置修改完成: 全局变量和数据库都已更新");
            response.set("status", "success");
        } else {
            LOG_WARNING("PID配置部分更新: 全局变量已更新，但数据库更新失败: {}", pidService.getLastError());
            response.set("status", "warning");
            response.set("message", "配置已实时生效，但数据库保存失败: " + pidService.getLastError());
        }
        
        // 7. 创建响应数据对象 - 返回实际的全局变量值
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", webui::home::g_username);
        data->set("gameName", webui::home::g_game_name);
        data->set("nearMoveFactor", webui::pid::g_near_move_factor);
        data->set("nearStabilizer", webui::pid::g_near_stabilizer);
        data->set("nearResponseRate", webui::pid::g_near_response_rate);
        data->set("nearAssistZone", webui::pid::g_near_assist_zone);
        data->set("nearResponseDelay", webui::pid::g_near_response_delay);
        data->set("nearMaxAdjustment", webui::pid::g_near_max_adjustment);
        data->set("farFactor", webui::pid::g_far_factor);
        data->set("yAxisFactor", webui::pid::g_y_axis_factor);
        data->set("pidRandomFactor", webui::pid::g_pid_random_factor);
        
        // 获取当前时间并格式化
        Poco::DateTime now;
        std::string createdAt = content->optValue("createdAt", 
            Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ"));
        std::string updatedAt = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
        
        data->set("createdAt", createdAt);
        data->set("updatedAt", updatedAt);
        
        response.set("data", data);
        
    }
    catch (const std::exception& e) {
        std::string error = "处理PID配置修改异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理PID配置读取请求
std::string handlePidRead(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "pid_read_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->getValue<std::string>("username");
        std::string gameName = content->getValue<std::string>("gameName");
        
        LOG_INFO("PID设置读取 - 用户: {}, 游戏: {}", username, gameName);
        
        // 2. 【关键步骤】先从数据库获取最新的PID配置数据
        LOG_INFO("步骤1: 从数据库获取最新PID配置");
        blweb::services::PidService& pidService = blweb::services::PidService::getInstance();
        Poco::JSON::Object::Ptr result = pidService.getPidConfig(username, gameName);
        
        // 3. 【关键步骤】用数据库数据更新全局变量
        if (result->getValue<int>("code") == 200 && result->has("data")) {
            Poco::JSON::Object::Ptr data = result->getObject("data");
            
            LOG_INFO("步骤2: 用数据库数据更新C++全局变量");
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 用数据库获取的值更新全局变量
            webui::pid::g_near_move_factor = data->optValue("nearMoveFactor", 1.0);
            webui::pid::g_near_stabilizer = data->optValue("nearStabilizer", 0.5);
            webui::pid::g_near_response_rate = data->optValue("nearResponseRate", 0.3);
            webui::pid::g_near_assist_zone = data->optValue("nearAssistZone", 3.0);
            webui::pid::g_near_response_delay = data->optValue("nearResponseDelay", 1.0);
            webui::pid::g_near_max_adjustment = data->optValue("nearMaxAdjustment", 2.0);
            webui::pid::g_far_factor = data->optValue("farFactor", 1.0);
            webui::pid::g_y_axis_factor = data->optValue("yAxisFactor", 1.0);
            webui::pid::g_pid_random_factor = data->optValue("pidRandomFactor", 0.5);
            
            LOG_INFO("C++全局变量更新完成: nearMoveFactor={}, nearStabilizer={}, nearResponseRate={}, nearAssistZone={}, nearResponseDelay={}, nearMaxAdjustment={}, farFactor={}, yAxisFactor={}, pidRandomFactor={}", 
                     webui::pid::g_near_move_factor, webui::pid::g_near_stabilizer, webui::pid::g_near_response_rate,
                     webui::pid::g_near_assist_zone, webui::pid::g_near_response_delay, webui::pid::g_near_max_adjustment,
                     webui::pid::g_far_factor, webui::pid::g_y_axis_factor, webui::pid::g_pid_random_factor);
            
            // 4. 确保响应数据包含所有必需字段
            if (!data->has("username")) data->set("username", username);
            if (!data->has("gameName")) data->set("gameName", gameName);
            
            // 确保所有必需的API字段都存在（使用更新后的全局变量值）
            data->set("nearMoveFactor", webui::pid::g_near_move_factor);
            data->set("nearStabilizer", webui::pid::g_near_stabilizer);
            data->set("nearResponseRate", webui::pid::g_near_response_rate);
            data->set("nearAssistZone", webui::pid::g_near_assist_zone);
            data->set("nearResponseDelay", webui::pid::g_near_response_delay);
            data->set("nearMaxAdjustment", webui::pid::g_near_max_adjustment);
            data->set("farFactor", webui::pid::g_far_factor);
            data->set("yAxisFactor", webui::pid::g_y_axis_factor);
            data->set("pidRandomFactor", webui::pid::g_pid_random_factor);
            
            // 5. 设置成功响应
            response.set("status", "success");
            response.set("data", data);
            
        } else {
            // 数据库无数据或查询失败，使用默认值并更新全局变量
            LOG_INFO("数据库无数据或查询失败，使用默认值并更新全局变量");
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 使用默认值更新全局变量
            webui::pid::g_near_move_factor = 1.0;
            webui::pid::g_near_stabilizer = 0.5;
            webui::pid::g_near_response_rate = 0.3;
            webui::pid::g_near_assist_zone = 3.0;
            webui::pid::g_near_response_delay = 1.0;
            webui::pid::g_near_max_adjustment = 2.0;
            webui::pid::g_far_factor = 1.0;
            webui::pid::g_y_axis_factor = 1.0;
            webui::pid::g_pid_random_factor = 0.5;
            
            LOG_INFO("使用默认值更新全局变量完成");
            
            // 创建默认数据对象 - 使用API文档定义的默认值
            Poco::JSON::Object::Ptr defaultData = new Poco::JSON::Object();
            defaultData->set("username", username);
            defaultData->set("gameName", gameName);
            defaultData->set("nearMoveFactor", webui::pid::g_near_move_factor);
            defaultData->set("nearStabilizer", webui::pid::g_near_stabilizer);
            defaultData->set("nearResponseRate", webui::pid::g_near_response_rate);
            defaultData->set("nearAssistZone", webui::pid::g_near_assist_zone);
            defaultData->set("nearResponseDelay", webui::pid::g_near_response_delay);
            defaultData->set("nearMaxAdjustment", webui::pid::g_near_max_adjustment);
            defaultData->set("farFactor", webui::pid::g_far_factor);
            defaultData->set("yAxisFactor", webui::pid::g_y_axis_factor);
            defaultData->set("pidRandomFactor", webui::pid::g_pid_random_factor);
            defaultData->set("exists", false);
            
            response.set("status", "success");
            response.set("data", defaultData);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理PID配置读取异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace PID
} // namespace MessageHandlers 

/*
 * PID配置处理逻辑说明 (2024年修改)：
 * 
 * === PID配置修改逻辑 (pid_modify) ===
 * 1. 【优先级1】先更新C++全局变量 - 确保实时生效
 *    - 直接更新webui::pid命名空间中的所有全局变量
 *    - 这确保了PID控制器能立即使用新参数
 *    - 即使数据库更新失败，系统仍能正常工作
 * 
 * 2. 【优先级2】再更新数据库 - 持久化存储
 *    - 调用PidService进行数据库更新
 *    - 如果失败，返回warning状态但不影响实时功能
 * 
 * 3. 【响应策略】
 *    - 成功：全局变量和数据库都更新成功
 *    - 警告：全局变量更新成功，数据库更新失败
 *    - 错误：参数验证失败或异常情况
 * 
 * === PID配置读取逻辑 (pid_read) ===
 * 1. 【步骤1】从数据库获取最新的PID配置数据
 *    - 调用PidService从数据库读取用户在指定游戏的PID配置
 *    - 确保获取到的是最新的持久化数据
 * 
 * 2. 【步骤2】用数据库数据更新C++全局变量
 *    - 将数据库中的配置值更新到webui::pid命名空间的全局变量中
 *    - 确保PID控制器使用的是数据库中的最新配置
 *    - 如果数据库无数据，使用API文档定义的默认值更新全局变量
 * 
 * 3. 【步骤3】返回给前端
 *    - 将更新后的全局变量值返回给前端
 *    - 确保前端显示的值与后端实际使用的值一致
 * 
 * 字段映射 (API -> 数据库 -> 全局变量)：
 * - nearMoveFactor -> near_move_factor -> g_near_move_factor
 * - nearStabilizer -> near_stabilizer -> g_near_stabilizer
 * - nearResponseRate -> near_response_rate -> g_near_response_rate
 * - nearAssistZone -> near_assist_zone -> g_near_assist_zone
 * - nearResponseDelay -> near_response_delay -> g_near_response_delay
 * - nearMaxAdjustment -> near_max_adjustment -> g_near_max_adjustment
 * - farFactor -> far_factor -> g_far_factor
 * - yAxisFactor -> y_axis_factor -> g_y_axis_factor
 * - pidRandomFactor -> pid_random_factor -> g_pid_random_factor
 */ 