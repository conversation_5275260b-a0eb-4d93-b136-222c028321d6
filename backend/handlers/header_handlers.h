#pragma once

#include <Poco/JSON/Object.h>
#include <string>

namespace MessageHandlers {
namespace Header {

/**
 * @brief 更新版本信息
 * 
 * 从云更新系统获取最新版本号并更新全局变量
 */
void updateVersionInfo();

/**
 * @brief 处理状态栏查询请求
 * @param content 请求内容
 * @return std::string 响应JSON字符串
 * 
 * 不更新数据库，仅返回当前系统状态
 */
std::string handleStatusBarQuery(const Poco::JSON::Object::Ptr& content);

/**
 * @brief 处理版本更新请求
 * @param content 请求内容
 * @return std::string 响应JSON字符串
 * 
 * 设置版本更新请求标志，通知系统执行版本更新
 */
std::string handleVersionUpdate(const Poco::JSON::Object::Ptr& content);

/**
 * @brief 处理心跳请求
 * @param content 请求内容
 * @return std::string 响应JSON字符串
 * 
 * 不更新数据库，仅返回服务器状态和时间信息
 */
std::string handleHeartbeat(const Poco::JSON::Object::Ptr& content);

} // namespace Header
} // namespace MessageHandlers 