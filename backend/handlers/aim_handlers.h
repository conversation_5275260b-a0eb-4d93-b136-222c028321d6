#pragma once

#include <Poco/JSON/Object.h>
#include <string>

namespace MessageHandlers {
namespace Aim {

/**
 * @brief 处理瞄准配置修改请求
 * 
 * 按照API文档中的格式处理瞄准配置修改请求，
 * 直接调用updateGlobalModels更新全局变量，并更新数据库。
 * 处理流程:
 * 1. 从请求中提取参数
 * 2. 调用updateGlobalModels更新全局变量
 * 3. 调用AimService处理业务逻辑，保存到数据库
 * 4. 构建并返回响应
 * 
 * @param content 请求内容
 * @return std::string JSON格式的响应
 */
std::string handleAimModify(const Poco::JSON::Object::Ptr& content);

/**
 * @brief 处理瞄准配置读取请求
 * 
 * 按照API文档中的格式处理瞄准配置读取请求，
 * 先从数据库获取配置数据，然后用数据库的值更新全局变量。
 * 处理流程:
 * 1. 从请求中提取用户名和游戏名
 * 2. 调用AimService从数据库获取配置
 * 3. 用数据库返回的数据构造新的内容对象并调用updateGlobalModels更新全局变量
 * 4. 构建并返回响应
 * 
 * 注意：前端只会发送用户名和游戏名，不会发送完整的配置，
 * 所以需要先从数据库获取数据，再更新全局变量。
 * 
 * @param content 请求内容，仅包含用户名和游戏名
 * @return std::string JSON格式的响应
 */
std::string handleAimRead(const Poco::JSON::Object::Ptr& content);

/**
 * @brief 处理瞄准配置获取请求（兼容旧接口）
 * 
 * 已弃用，保留仅为了向后兼容。新代码应使用handleAimRead。
 * 实际操作是直接调用handleAimRead。
 * 
 * @param content 请求内容
 * @return std::string JSON格式的响应
 */
std::string handleAimGet(const Poco::JSON::Object::Ptr& content);

} // namespace Aim
} // namespace MessageHandlers 