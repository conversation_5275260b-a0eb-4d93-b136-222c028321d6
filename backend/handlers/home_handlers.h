#pragma once

#include <Poco/JSON/Object.h>
#include <string>

namespace MessageHandlers {
namespace Home {

// 常量定义
namespace Constants {
    const std::string ACTION_HOME_MODIFY_RESPONSE = "home_modify_response";
    const std::string ACTION_HOME_READ = "home_read";
    const std::string STATUS_OK = "ok";
    const std::string STATUS_ERROR = "error";
    const std::string TIME_FORMAT_ISO = "%Y-%m-%dT%H:%M:%SZ";
}

// 处理首页配置修改请求
std::string handleHomeModify(const Poco::JSON::Object::Ptr& content);

// 处理首页配置读取请求
std::string handleHomeRead(const Poco::JSON::Object::Ptr& content);

} // namespace Home
} // namespace MessageHandlers 