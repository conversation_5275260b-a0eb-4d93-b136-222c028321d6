#include "login_handlers.h"
#include "../blserver/login_service.h"
#include "../blserver/home_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"

#include <sstream>
#include <Poco/Dynamic/Var.h>

namespace MessageHandlers {
namespace Login {

// 处理登录请求
std::string handleLoginRead(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象 - 严格按照API文档格式
    Poco::JSON::Object response;
    response.set("action", "login_read_response");
    
    try {
        // 1. 从请求中提取必要参数 - 按照简化后的API文档要求
        std::string username = content->optValue("username", std::string(""));
        std::string password = content->optValue("password", std::string(""));
        std::string token = content->optValue("token", std::string(""));
        std::string createdAt = content->optValue("createdAt", std::string(""));
        std::string updatedAt = content->optValue("updatedAt", std::string(""));
        
        LOG_INFO("登录请求 - 用户名: {}, 使用Token: {}", 
                username, 
                token.empty() ? "否" : "是");
        
        // 2. 严格按照API文档进行参数验证
        if (username.empty()) {
            response.set("status", "error");
            response.set("message", "用户名不能为空");
            
            response.set("data", Poco::Dynamic::Var());
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // API文档要求：createdAt和updatedAt是必填字段
        if (createdAt.empty()) {
            response.set("status", "error");
            response.set("message", "createdAt字段不能为空");
            response.set("data", Poco::Dynamic::Var());
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        if (updatedAt.empty()) {
            response.set("status", "error");
            response.set("message", "updatedAt字段不能为空");
            response.set("data", Poco::Dynamic::Var());
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // API文档要求：password是必填的，除非是Token登录
        if (token.empty() && password.empty()) {
            response.set("status", "error");
            response.set("message", "密码或令牌不能为空");
            response.set("data", Poco::Dynamic::Var());
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 3. 调用服务层处理业务逻辑
        blweb::services::LoginService& loginService = blweb::services::LoginService::getInstance();
        Poco::JSON::Object::Ptr result;
        
        // 根据提供的参数选择验证方式
        if (!token.empty()) {
            LOG_INFO("使用Token验证登录");
            result = loginService.verifyUserByToken(username, token);
        } else {
            LOG_INFO("使用密码验证登录");
            result = loginService.verifyUserLogin(username, password);
        }
        
        // 4. 处理服务层响应
        if (!result || !result->has("code")) {
            LOG_ERROR("登录服务返回的结果为空或缺少code字段");
            response.set("status", "error");
            response.set("message", "登录失败，请稍后重试");
            response.set("data", Poco::Dynamic::Var());
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 安全地获取code字段
        int code = 500;  // 默认值
        try {
            Poco::Dynamic::Var codeVar = result->get("code");
            if (!codeVar.isEmpty()) {
                code = codeVar.convert<int>();
            }
        } catch (const Poco::Exception& ex) {
            LOG_ERROR("获取code字段时Poco异常: {}, 使用默认值500", ex.displayText());
            code = 500;
        } catch (const std::exception& ex) {
            LOG_ERROR("获取code字段时标准异常: {}, 使用默认值500", ex.what());
            code = 500;
        } catch (...) {
            LOG_ERROR("获取code字段时未知异常，使用默认值500");
            code = 500;
        }
        
        if (code == 200) {
            // 登录成功 - 获取用户的Pro状态
            bool dbIsPro = false;
            if (result->has("data")) {
                try {
                    Poco::JSON::Object::Ptr resultData = result->getObject("data");
                    if (resultData && resultData->has("isPro")) {
                        try {
                            Poco::Dynamic::Var isProVar = resultData->get("isPro");
                            if (!isProVar.isEmpty()) {
                                dbIsPro = isProVar.convert<bool>();
                            }
                        } catch (const Poco::Exception& ex) {
                            LOG_ERROR("获取isPro字段时Poco异常: {}, 使用默认值false", ex.displayText());
                            dbIsPro = false;
                        } catch (const std::exception& ex) {
                            LOG_ERROR("获取isPro字段时标准异常: {}, 使用默认值false", ex.what());
                            dbIsPro = false;
                        } catch (...) {
                            LOG_ERROR("获取isPro字段时未知异常，使用默认值false");
                            dbIsPro = false;
                        }
                    }
                } catch (const Poco::Exception& ex) {
                    LOG_ERROR("获取data对象时异常: {}", ex.displayText());
                }
            }
            
            LOG_INFO("用户 '{}' 登录成功，Pro状态: {}", username, dbIsPro ? "Pro版" : "标准版");
            
            response.set("status", "success");
            response.set("message", "登录成功");
            
            // 5. 严格按照API文档构建响应数据结构
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            
            // 构建userInfo对象 - 按API文档要求的字段顺序和格式
            Poco::JSON::Object::Ptr userInfo = new Poco::JSON::Object();
            userInfo->set("username", username);
            
            // 设置token
            if (result->has("data")) {
                try {
                    Poco::JSON::Object::Ptr resultData = result->getObject("data");
                    if (resultData && resultData->has("token")) {
                        try {
                            Poco::Dynamic::Var tokenVar = resultData->get("token");
                            if (!tokenVar.isEmpty()) {
                                std::string tokenValue = tokenVar.convert<std::string>();
                                userInfo->set("token", tokenValue);
                            } else {
                                userInfo->set("token", "blweb_token_" + username);
                            }
                        } catch (const Poco::Exception& ex) {
                            LOG_ERROR("获取token字段时Poco异常: {}", ex.displayText());
                            userInfo->set("token", "blweb_token_" + username);
                        } catch (const std::exception& ex) {
                            LOG_ERROR("获取token字段时标准异常: {}", ex.what());
                            userInfo->set("token", "blweb_token_" + username);
                        } catch (...) {
                            LOG_ERROR("获取token字段时未知异常");
                            userInfo->set("token", "blweb_token_" + username);
                        }
                    } else {
                        userInfo->set("token", "blweb_token_" + username);
                    }
                    
                    // 设置lastLoginTime
                    if (resultData && resultData->has("lastLogin")) {
                        try {
                            Poco::Dynamic::Var lastLoginVar = resultData->get("lastLogin");
                            if (!lastLoginVar.isEmpty()) {
                                std::string lastLoginValue = lastLoginVar.convert<std::string>();
                                userInfo->set("lastLoginTime", lastLoginValue);
                            } else {
                                userInfo->set("lastLoginTime", "");
                            }
                        } catch (const Poco::Exception& ex) {
                            LOG_ERROR("获取lastLogin字段时Poco异常: {}", ex.displayText());
                            userInfo->set("lastLoginTime", "");
                        } catch (const std::exception& ex) {
                            LOG_ERROR("获取lastLogin字段时标准异常: {}", ex.what());
                            userInfo->set("lastLoginTime", "");
                        } catch (...) {
                            LOG_ERROR("获取lastLogin字段时未知异常");
                            userInfo->set("lastLoginTime", "");
                        }
                    } else {
                        userInfo->set("lastLoginTime", "");
                    }
                } catch (const Poco::Exception& ex) {
                    LOG_ERROR("处理result data时异常: {}", ex.displayText());
                    userInfo->set("token", "blweb_token_" + username);
                    userInfo->set("lastLoginTime", "");
                }
            } else {
                userInfo->set("token", "blweb_token_" + username);
                userInfo->set("lastLoginTime", "");
            }
            
            // API文档要求：isPro字段从数据库获取
            userInfo->set("isPro", dbIsPro);
            
            data->set("userInfo", userInfo);
            
            // 6. 获取首页配置
            try {
                blweb::services::HomeService& homeService = blweb::services::HomeService::getInstance();
                Poco::JSON::Object::Ptr homeResult = homeService.getHomeConfig(username, "");
                
                Poco::JSON::Object::Ptr homeConfig = new Poco::JSON::Object();
                if (homeResult && homeResult->has("code")) {
                    int homeCode = 500;
                    try {
                        Poco::Dynamic::Var homeCodeVar = homeResult->get("code");
                        if (!homeCodeVar.isEmpty()) {
                            homeCode = homeCodeVar.convert<int>();
                        }
                    } catch (const Poco::Exception& ex) {
                        LOG_ERROR("获取homeResult code字段时Poco异常: {}", ex.displayText());
                        homeCode = 500;
                    } catch (const std::exception& ex) {
                        LOG_ERROR("获取homeResult code字段时标准异常: {}", ex.what());
                        homeCode = 500;
                    } catch (...) {
                        LOG_ERROR("获取homeResult code字段时未知异常");
                        homeCode = 500;
                    }
                    
                    if (homeCode == 200 && homeResult->has("data")) {
                        try {
                            Poco::JSON::Object::Ptr homeData = homeResult->getObject("data");
                            if (homeData) {
                                if (homeData->has("gameName")) {
                                    try {
                                        Poco::Dynamic::Var gameNameVar = homeData->get("gameName");
                                        if (!gameNameVar.isEmpty()) {
                                            std::string gameNameValue = gameNameVar.convert<std::string>();
                                            homeConfig->set("gameName", gameNameValue);
                                        } else {
                                            homeConfig->set("gameName", "");
                                        }
                                    } catch (const Poco::Exception& ex) {
                                        LOG_ERROR("获取gameName字段时Poco异常: {}", ex.displayText());
                                        homeConfig->set("gameName", "");
                                    } catch (const std::exception& ex) {
                                        LOG_ERROR("获取gameName字段时标准异常: {}", ex.what());
                                        homeConfig->set("gameName", "");
                                    } catch (...) {
                                        LOG_ERROR("获取gameName字段时未知异常");
                                        homeConfig->set("gameName", "");
                                    }
                                } else {
                                    homeConfig->set("gameName", "");
                                }
                                
                                if (homeData->has("cardKey")) {
                                    try {
                                        Poco::Dynamic::Var cardKeyVar = homeData->get("cardKey");
                                        if (!cardKeyVar.isEmpty()) {
                                            std::string cardKeyValue = cardKeyVar.convert<std::string>();
                                            homeConfig->set("cardKey", cardKeyValue);
                                        } else {
                                            homeConfig->set("cardKey", "");
                                        }
                                    } catch (const Poco::Exception& ex) {
                                        LOG_ERROR("获取cardKey字段时Poco异常: {}", ex.displayText());
                                        homeConfig->set("cardKey", "");
                                    } catch (const std::exception& ex) {
                                        LOG_ERROR("获取cardKey字段时标准异常: {}", ex.what());
                                        homeConfig->set("cardKey", "");
                                    } catch (...) {
                                        LOG_ERROR("获取cardKey字段时未知异常");
                                        homeConfig->set("cardKey", "");
                                    }
                                } else {
                                    homeConfig->set("cardKey", "");
                                }
                            } else {
                                homeConfig->set("gameName", "");
                                homeConfig->set("cardKey", "");
                            }
                        } catch (const Poco::Exception& ex) {
                            LOG_ERROR("处理homeData时异常: {}", ex.displayText());
                            homeConfig->set("gameName", "");
                            homeConfig->set("cardKey", "");
                        }
                    } else {
                        homeConfig->set("gameName", "");
                        homeConfig->set("cardKey", "");
                    }
                } else {
                    homeConfig->set("gameName", "");
                    homeConfig->set("cardKey", "");
                }
                
                data->set("homeConfig", homeConfig);
            } catch (const std::exception& ex) {
                LOG_ERROR("获取首页配置时异常: {}", ex.what());
                // 即使获取首页配置失败，仍然返回用户信息
                Poco::JSON::Object::Ptr homeConfig = new Poco::JSON::Object();
                homeConfig->set("gameName", "");
                homeConfig->set("cardKey", "");
                data->set("homeConfig", homeConfig);
            }
            
            response.set("data", data);
            
            // 7. 更新全局变量
            webui::header::isPro = dbIsPro;
            webui::home::forceAuthNow = true;  // 强制触发卡密验证
            
            LOG_INFO("更新全局变量 - 用户Pro状态: {}", dbIsPro ? "Pro版本" : "标准版本");
            
        } else if (code == 404) {
            // 用户不存在
            LOG_WARNING("登录失败: 用户名错误");
            
            response.set("status", "error");
            response.set("message", "用户名错误");
            response.set("data", Poco::Dynamic::Var());
            
        } else if (code == 401) {
            // 密码或token错误
            std::string errorMsg = "密码错误";  // 默认错误消息
            
            if (result && result->has("msg")) {
                try {
                    // 使用更安全的方式获取msg字段
                    Poco::Dynamic::Var msgVar = result->get("msg");
                    if (!msgVar.isEmpty()) {
                        std::string serviceMsg = msgVar.convert<std::string>();
                        if (serviceMsg.find("令牌") != std::string::npos) {
                            errorMsg = "令牌无效或已过期";
                        } else if (!serviceMsg.empty()) {
                            // 如果服务层返回了具体的错误消息，使用它
                            errorMsg = serviceMsg;
                        }
                    }
                } catch (const Poco::Exception& ex) {
                    LOG_ERROR("获取msg字段时Poco异常: {}", ex.displayText());
                } catch (const std::exception& ex) {
                    LOG_ERROR("获取msg字段时标准异常: {}", ex.what());
                } catch (...) {
                    LOG_ERROR("获取msg字段时未知异常");
                }
            }
            
            LOG_WARNING("登录失败: {}", errorMsg);
            
            response.set("status", "error");
            response.set("message", errorMsg);
            
            // 直接设置null值
            response.set("data", Poco::Dynamic::Var());
            
        } else {
            // 其他错误
            LOG_WARNING("登录失败: 服务器内部错误，code: {}", code);
            
            response.set("status", "error");
            response.set("message", "登录失败，请稍后重试");
            response.set("data", Poco::Dynamic::Var());
        }
    }
    catch (const Poco::Exception& e) {
        std::string error = "处理登录请求Poco异常: " + e.displayText();
        LOG_ERROR("{}", error);
        
        response.set("status", "error");
        response.set("message", "登录失败，请稍后重试");
        response.set("data", Poco::Dynamic::Var());
    }
    catch (const std::exception& e) {
        std::string error = "处理登录请求标准异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        
        response.set("status", "error");
        response.set("message", "登录失败，请稍后重试");
        response.set("data", Poco::Dynamic::Var());
    }
    catch (...) {
        LOG_ERROR("处理登录请求时发生未知异常");
        
        response.set("status", "error");
        response.set("message", "登录失败，请稍后重试");
        response.set("data", Poco::Dynamic::Var());
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace Login
} // namespace MessageHandlers 