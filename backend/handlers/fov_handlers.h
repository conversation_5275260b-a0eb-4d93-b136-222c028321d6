#pragma once

#include <Poco/JSON/Object.h>
#include <string>

namespace MessageHandlers {
namespace FOV {

// 处理FOV配置修改请求
std::string handleFovModify(const Poco::JSON::Object::Ptr& content);

// 处理FOV配置读取请求
std::string handleFovRead(const Poco::JSON::Object::Ptr& content);

// 处理FOV测量启动请求
std::string handleFovMeasurementStart(const Poco::JSON::Object::Ptr& content);

} // namespace FOV
} // namespace MessageHandlers 