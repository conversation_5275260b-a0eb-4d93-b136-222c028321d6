#include "fov_handlers.h"
#include "../blserver/fov_service.h"
#include "../blserver/update_models.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"

#include <sstream>

namespace MessageHandlers {
namespace FOV {

// 处理FOV配置修改请求
std::string handleFovModify(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "fov_modify_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->getValue<std::string>("username");
        std::string gameName = content->getValue<std::string>("gameName");
        float fov = static_cast<float>(content->getValue<double>("fov"));
        int fovTime = content->getValue<int>("fovTime");
        
        LOG_INFO("FOV配置修改 - 用户名: {}, 游戏: {}", username, gameName);
        LOG_INFO("参数: fov={}, fovTime={}", fov, fovTime);
        
        // 2. 先更新全局变量
        webui::fov::g_fov = fov;
        webui::fov::g_fov_time = fovTime;
        LOG_INFO("已更新FOV全局变量");
        
        // 3. 调用服务层处理业务逻辑
        blweb::services::FovService& fovService = blweb::services::FovService::getInstance();
        bool success = fovService.updateFovConfig(username, gameName, fov, fovTime);
        
        // 4. 构建响应
        if (success) {
            response.set("status", "success");
            
            // 构建配置数据
            Poco::JSON::Object::Ptr configData = new Poco::JSON::Object();
            configData->set("username", username);
            configData->set("gameName", gameName);
            configData->set("fov", fov);
            configData->set("fovTime", fovTime);
            
            // 获取当前时间作为更新时间
            Poco::DateTime now;
            std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
            configData->set("updatedAt", timeStr);
            configData->set("exists", true);
            
            response.set("data", configData);
        } else {
            std::string error = "更新FOV配置失败: " + fovService.getLastError();
            LOG_ERROR("{}", error);
            response.set("status", "error");
            response.set("message", error);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理FOV配置修改异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理FOV配置读取请求
std::string handleFovRead(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "fov_read_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->getValue<std::string>("username");
        std::string gameName = content->getValue<std::string>("gameName");
        
        LOG_INFO("视野设置读取 - 用户: {}, 游戏: {}", username, gameName);
        
        // 2. 先更新基本参数（用户名和游戏名）到全局变量
        Poco::JSON::Object::Ptr basicContent = new Poco::JSON::Object();
        basicContent->set("username", username);
        basicContent->set("gameName", gameName);
        blweb::utils::updateGlobalModels("home_read", basicContent);
        
        // 3. 调用服务层从数据库获取配置
        blweb::services::FovService& fovService = blweb::services::FovService::getInstance();
        Poco::JSON::Object::Ptr result = fovService.getFovConfig(username, gameName);
        
        // 4. 处理响应
        int code = result->getValue<int>("code");
        if (code == 200) {
            response.set("status", "success");
            response.set("message", "获取视野设置成功");
            
            // 如果结果中有数据，则更新全局变量并添加到响应中
            if (result->has("data")) {
                Poco::JSON::Object::Ptr data = result->getObject("data");
                
                // 直接用数据库返回的数据更新全局变量
                if (data->has("fov")) webui::fov::g_fov = data->getValue<float>("fov");
                if (data->has("fovTime")) webui::fov::g_fov_time = data->getValue<int>("fovTime");
                
                LOG_INFO("使用数据库配置更新FOV全局变量: fov={}, fovTime={}", 
                         webui::fov::g_fov, webui::fov::g_fov_time);
                
                // 添加到响应
                response.set("data", data);
            } else {
                // 无数据时返回默认值
                LOG_INFO("数据库无数据，使用默认值");
                
                // 创建默认数据对象
                Poco::JSON::Object::Ptr defaultData = new Poco::JSON::Object();
                defaultData->set("username", username);
                defaultData->set("gameName", gameName);
                defaultData->set("fov", webui::fov::g_fov);
                defaultData->set("fovTime", webui::fov::g_fov_time);
                defaultData->set("exists", false);
                
                // 获取当前时间作为时间戳
                Poco::DateTime now;
                std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
                defaultData->set("createdAt", timeStr);
                defaultData->set("updatedAt", timeStr);
                
                response.set("data", defaultData);
            }
        } else {
            // 数据库查询失败
            std::string error = result->getValue<std::string>("msg");
            LOG_ERROR("获取视野设置失败: {}", error);
            response.set("status", "error");
            response.set("message", error);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理FOV配置读取异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理FOV测量启动请求
std::string handleFovMeasurementStart(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "fov_measurement_start_response");
    
    try {
        // 1. 从请求中提取必要参数，使用optValue处理可能缺失的参数
        std::string username = content->optValue<std::string>("username", webui::home::g_username);
        std::string gameName = content->optValue<std::string>("gameName", webui::home::g_game_name);
        
        LOG_INFO("FOV测量启动 - 用户: {}, 游戏: {}", username, gameName);
        
        // 2. 先更新基本参数（用户名和游戏名）到全局变量
        Poco::JSON::Object::Ptr basicContent = new Poco::JSON::Object();
        basicContent->set("username", username);
        basicContent->set("gameName", gameName);
        blweb::utils::updateGlobalModels("home_read", basicContent);
        
        // 3. 调用服务层处理业务逻辑 - 设置g_start_measure为true并等待测量完成
        blweb::services::FovService& fovService = blweb::services::FovService::getInstance();
        Poco::JSON::Object::Ptr result = fovService.startFovMeasurement(username, gameName);
        
        // 4. 无论处理结果如何，始终返回success状态
        response.set("status", "success");
        
        int code = result->getValue<int>("code");
        if (code == 200) {
            response.set("message", "FOV测量成功启动");
            
            // 如果结果中有数据，则添加到响应中
            if (result->has("data")) {
                Poco::JSON::Object::Ptr data = result->getObject("data");
                response.set("data", data);
                
                // 如果测量已完成并有FOV值，更新全局变量
                if (data->has("fov") && !data->getValue<bool>("measuring")) {
                    webui::fov::g_fov = data->getValue<float>("fov");
                    if (data->has("fovTime")) {
                        webui::fov::g_fov_time = data->getValue<int>("fovTime");
                    }
                    LOG_INFO("FOV测量完成，更新全局变量: fov={}, fovTime={}", 
                             webui::fov::g_fov, webui::fov::g_fov_time);
                }
            }
        } else {
            // 服务层处理失败，但仍然返回success状态
            std::string error = result->getValue<std::string>("msg");
            LOG_ERROR("FOV测量失败: {}", error);
            response.set("message", error);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理FOV测量启动异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        // 即使发生异常，也返回success状态
        response.set("status", "success");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace FOV
} // namespace MessageHandlers 