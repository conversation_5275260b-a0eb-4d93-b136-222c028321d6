#include "register_handlers.h"
#include "../blserver/register_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"

#include <sstream>
#include <algorithm>

namespace MessageHandlers {
namespace Register {

namespace {
    // 验证游戏是否在支持列表中
    bool isGameSupported(const std::string& gameName) {
        const auto& supportedGames = webui::myregister::g_game_list;
        return std::find(supportedGames.begin(), supportedGames.end(), gameName) != supportedGames.end();
    }
    
    // 验证游戏列表
    bool validateGameList(const Poco::JSON::Array::Ptr& gameList, std::string& errorMsg) {
        if (!gameList || gameList->size() == 0) {
            errorMsg = "游戏列表不能为空";
            return false;
        }
        
        for (size_t i = 0; i < gameList->size(); ++i) {
            std::string gameName = gameList->getElement<std::string>(i);
            if (!isGameSupported(gameName)) {
                errorMsg = "不支持的游戏: " + gameName + "，支持的游戏列表: apex, cf, cfhd, csgo2, sjz, ssjj2, pubg, wwqy";
                return false;
            }
        }
        
        return true;
    }
}

// 处理用户注册请求
std::string handleRegisterModify(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "register_modify_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->optValue("username", std::string(""));
        std::string password = content->optValue("password", std::string(""));
        std::string defaultGame = content->optValue("defaultGame", std::string("wwqy"));
        bool isPro = content->optValue("isPro", false);
        
        // 提取游戏列表并验证
        Poco::JSON::Array::Ptr gameList = nullptr;
        if (content->isArray("gameList")) {
            gameList = content->getArray("gameList");
            LOG_INFO("收到游戏列表，包含{}个游戏", gameList->size());
            
            // 验证游戏列表
            std::string validationError;
            if (!validateGameList(gameList, validationError)) {
                LOG_ERROR("游戏列表验证失败: {}", validationError);
                response.set("status", "error");
                response.set("message", validationError);
                
                std::ostringstream oss;
                response.stringify(oss);
                return oss.str();
            }
        }
        
        // 验证默认游戏
        if (!isGameSupported(defaultGame)) {
            std::string error = "不支持的默认游戏: " + defaultGame + "，支持的游戏列表: apex, cf, cfhd, csgo2, sjz, ssjj2, pubg, wwqy";
            LOG_ERROR("{}", error);
            response.set("status", "error");
            response.set("message", error);
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        LOG_INFO("用户注册 - 用户名: {}, 默认游戏: {}, isPro: {}", username, defaultGame, isPro);
        
        // 2. 调用服务层处理业务逻辑
        blweb::services::RegisterService& registerService = blweb::services::RegisterService::getInstance();
        auto registerResult = registerService.registerUser(username, password, defaultGame, gameList, isPro);
        
        // 3. 构建响应
        if (registerResult->getValue<std::string>("status") == "success") {
            response.set("status", "success");
            response.set("message", "用户注册成功");
            
            // 复制结果数据到响应中
            if (registerResult->has("data")) {
                response.set("data", registerResult->getObject("data"));
            }
        } else {
            std::string error = registerResult->getValue<std::string>("message");
            LOG_ERROR("{}", error);
            response.set("status", "error");
            response.set("message", error);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理用户注册异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace Register
} // namespace MessageHandlers 