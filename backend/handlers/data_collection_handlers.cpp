#include "data_collection_handlers.h"
#include "../blserver/data_collection_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blserver/update_models.h"
#include <iomanip>
#include "../blsql/data_collections_db.h"

#include <sstream>

namespace MessageHandlers {
namespace DataCollection {

// 处理数据采集配置修改请求
std::string handleDataCollectionModify(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "data_collection_modify_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->optValue<std::string>("username", "");
        std::string gameName = content->optValue<std::string>("gameName", "");
        
        // 获取API文档规定的必要参数
        bool isEnabled = content->optValue<bool>("isEnabled", true);
        std::string mapHotkey = content->optValue<std::string>("mapHotkey", "无");
        std::string targetHotkey = content->optValue<std::string>("targetHotkey", "无");
        std::string teamSide = content->optValue<std::string>("teamSide", "无");
        std::string collectionName = content->optValue<std::string>("collectionName", "");
        
        // createdAt和updatedAt从请求中获取，但会在数据库层更新updatedAt
        std::string createdAt = content->optValue<std::string>("createdAt", "");
        std::string updatedAt = content->optValue<std::string>("updatedAt", "");
        
        LOG_INFO("数据采集配置修改 - 用户: {}, 游戏: {}", username, gameName);
        LOG_INFO("参数: isEnabled={}, mapHotkey={}, targetHotkey={}, teamSide={}, collectionName={}",
                 isEnabled ? "是" : "否", mapHotkey, targetHotkey, teamSide, collectionName);
        
        // 检查API文档中规定的必要参数
        if (username.empty()) {
            throw std::runtime_error("用户名不能为空");
        }
        
        if (gameName.empty()) {
            throw std::runtime_error("游戏名称不能为空");
        }
        
        // 2. 更新全局变量
        blweb::utils::updateGlobalModels("data_collection_modify", content);
        
        // 3. 调用数据库服务保存配置
        blweb::db::DataCollectionsDB& dataCollectionDB = blweb::db::DataCollectionsDB::getInstance();
        bool success = dataCollectionDB.updateDataCollection(
            username, 
            gameName, 
            isEnabled, 
            mapHotkey,
            targetHotkey,
            teamSide,
            collectionName
        );
        
        // 4. 构建符合API文档的响应
        if (success) {
            response.set("status", "ok");
            
            // 构建符合API规范的响应内容
            Poco::JSON::Object::Ptr configData = new Poco::JSON::Object();
            configData->set("username", username);
            configData->set("gameName", gameName);
            configData->set("isEnabled", isEnabled);
            configData->set("mapHotkey", mapHotkey);
            configData->set("targetHotkey", targetHotkey);
            configData->set("teamSide", teamSide);
            configData->set("collectionName", collectionName);
            
            // 获取最新的时间戳
            auto dbConfig = dataCollectionDB.getDataCollection(username, gameName);
            if (!dbConfig.empty()) {
                configData->set("createdAt", dbConfig.count("created_at") ? dbConfig["created_at"] : createdAt);
                configData->set("updatedAt", dbConfig.count("updated_at") ? dbConfig["updated_at"] : updatedAt);
            } else {
                // 如果无法获取数据库时间戳，则使用请求中的时间戳
                configData->set("createdAt", createdAt);
                configData->set("updatedAt", updatedAt);
            }
            
            response.set("content", configData);
        } else {
            std::string error = "更新数据采集配置失败: " + dataCollectionDB.getLastError();
            LOG_ERROR("{}", error);
            response.set("status", "error");
            
            // 符合API规范的错误响应
            Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
            errorContent->set("username", username);
            errorContent->set("gameName", gameName);
            
            Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
            errorObj->set("code", "INVALID_COLLECTION");
            errorObj->set("message", error);
            
            errorContent->set("error", errorObj);
            response.set("content", errorContent);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理数据采集配置修改异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        
        // 符合API规范的错误响应
        Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
        errorContent->set("username", content->optValue<std::string>("username", ""));
        errorContent->set("gameName", content->optValue<std::string>("gameName", ""));
        
        Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
        errorObj->set("code", "SERVER_ERROR");
        errorObj->set("message", error);
        
        errorContent->set("error", errorObj);
        response.set("content", errorContent);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理数据采集配置读取请求
std::string handleDataCollectionRead(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "data_collection_read_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->optValue<std::string>("username", "");
        std::string gameName = content->optValue<std::string>("gameName", "");
        
        LOG_INFO("数据采集设置读取 - 用户: {}, 游戏: {}", username, gameName);
        
        // 检查API文档中规定的必要参数
        if (username.empty()) {
            throw std::runtime_error("用户名不能为空");
        }
        
        if (gameName.empty()) {
            throw std::runtime_error("游戏名称不能为空");
        }
        
        // 2. 从数据库获取数据采集配置
        blweb::db::DataCollectionsDB& dataCollectionDB = blweb::db::DataCollectionsDB::getInstance();
        auto dbConfig = dataCollectionDB.getDataCollection(username, gameName);
        
        // 3. 构建符合API文档的响应
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", username);
        data->set("gameName", gameName);
        
        if (!dbConfig.empty()) {
            // 从数据库中获取配置字段
            bool isEnabled = dbConfig.count("is_enabled") ? (dbConfig["is_enabled"] == "1") : false;
            std::string mapHotkey = dbConfig.count("map_hotkey") ? dbConfig["map_hotkey"] : "右键";
            std::string targetHotkey = dbConfig.count("target_hotkey") ? dbConfig["target_hotkey"] : "前侧";
            std::string teamSide = dbConfig.count("team_side") ? dbConfig["team_side"] : "警方";
            std::string collectionName = dbConfig.count("collection_name") ? dbConfig["collection_name"] : "";
            std::string createdAt = dbConfig.count("created_at") ? dbConfig["created_at"] : "";
            std::string updatedAt = dbConfig.count("updated_at") ? dbConfig["updated_at"] : "";
            
            // 设置到响应数据中
            data->set("isEnabled", isEnabled);
            data->set("mapHotkey", mapHotkey);
            data->set("targetHotkey", targetHotkey);
            data->set("teamSide", teamSide);
            data->set("collectionName", collectionName);
            data->set("createdAt", createdAt);
            data->set("updatedAt", updatedAt);
            
            LOG_INFO("成功获取数据采集配置 - 用户: {}, 游戏: {}", username, gameName);
        } else {
            // 配置不存在时返回默认关闭状态，不自动创建配置
            data->set("isEnabled", false);  // 默认关闭
            data->set("mapHotkey", "右键");
            data->set("targetHotkey", "前侧");
            data->set("teamSide", "警方");
            data->set("collectionName", "");
            data->set("createdAt", "");
            data->set("updatedAt", "");
            
            LOG_INFO("数据采集配置不存在，返回默认关闭状态 - 用户: {}, 游戏: {}", username, gameName);
        }
        
        response.set("status", "ok");
        response.set("content", data);
    }
    catch (const std::exception& e) {
        std::string error = "处理数据采集配置读取异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        
        // 简化的错误响应，符合API规范
        Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
        errorContent->set("username", content->optValue<std::string>("username", ""));
        errorContent->set("gameName", content->optValue<std::string>("gameName", ""));
        
        Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
        errorObj->set("code", "SERVER_ERROR");
        errorObj->set("message", error);
        
        errorContent->set("error", errorObj);
        response.set("content", errorContent);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理数据采集信息获取请求
// 获取当前imgs文件夹中所有子文件夹名称以及每个文件夹中图像和标签文件数量
std::string handleDataCollectionFetch(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "data_collection_fetch_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->optValue<std::string>("username", "");
        std::string gameName = content->optValue<std::string>("gameName", "");
        
        LOG_INFO("获取数据采集信息 - 用户: {}, 游戏: {}", username, gameName);
        
        // 检查API文档中规定的必要参数
        if (username.empty()) {
            throw std::runtime_error("用户名不能为空");
        }
        
        if (gameName.empty()) {
            throw std::runtime_error("游戏名称不能为空");
        }
        
        // 2. 调用服务层获取文件系统中的采集数据信息
        blweb::services::DataCollectionService& dataCollectionService = blweb::services::DataCollectionService::getInstance();
        Poco::JSON::Object::Ptr result = dataCollectionService.fetchDataCollection(
            username, gameName
        );
        
        // 3. 构建响应
        int code = result->getValue<int>("code");
        if (code == 200) {
            response.set("status", "ok");
            
            // 如果结果中有数据，则添加到响应中
            if (result->has("data")) {
                response.set("content", result->getObject("data"));
                
                // 验证响应内容是否符合API文档
                Poco::JSON::Object::Ptr data = result->getObject("data");
                if (!data->has("collections")) {
                    LOG_WARNING("响应数据中缺少collections字段");
                }
                if (!data->has("totalCollections")) {
                    LOG_WARNING("响应数据中缺少totalCollections字段");
                }
                if (!data->has("timestamp")) {
                    LOG_WARNING("响应数据中缺少timestamp字段");
                }
            }
        } else {
            std::string error = result->getValue<std::string>("msg");
            LOG_ERROR("获取数据采集信息失败: {}", error);
            response.set("status", "error");
            
            // 简化的错误响应，符合API规范
            Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
            errorContent->set("username", username);
            errorContent->set("gameName", gameName);
            
            // 错误类型判断
            std::string errorCode = "NO_DATA";
            if (code == 500) {
                errorCode = "SERVER_ERROR";
            } else if (code == 400) {
                errorCode = "INVALID_COLLECTION";
            }
            
            Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
            errorObj->set("code", errorCode);
            errorObj->set("message", error);
            
            errorContent->set("error", errorObj);
            response.set("content", errorContent);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理数据采集信息获取异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        
        // 简化的错误响应，符合API规范
        Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
        errorContent->set("username", content->optValue<std::string>("username", ""));
        errorContent->set("gameName", content->optValue<std::string>("gameName", ""));
        
        Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
        errorObj->set("code", "SERVER_ERROR");
        errorObj->set("message", error);
        
        errorContent->set("error", errorObj);
        response.set("content", errorContent);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理数据采集上传请求
// 将收集的数据上传到远程服务器
std::string handleDataCollectionUpload(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "data_collection_upload_response");
    
    try {
        // 1. 从请求中提取必要参数 - 按照API文档要求
        std::string username = content->optValue<std::string>("username", "");
        std::string gameName = content->optValue<std::string>("gameName", "");
        std::string teamSide = content->optValue<std::string>("teamSide", "");
        std::string mapHotkey = content->optValue<std::string>("mapHotkey", "");
        std::string targetHotkey = content->optValue<std::string>("targetHotkey", "");
        std::string timestamp = content->optValue<std::string>("timestamp", "");
        bool deleteAll = content->optValue<bool>("deleteAll", false);
        
        // collectionName是可选参数，服务层会处理为空的情况
        std::string collectionName = content->optValue<std::string>("collectionName", "");
        
        LOG_INFO("上传数据采集内容 - 用户: {}, 游戏: {}, 阵营: {}, 删除所有: {}", 
                 username, gameName, teamSide, deleteAll ? "是" : "否");
        
        // 检查API文档中定义的必要参数
        if (username.empty()) {
            throw std::runtime_error("用户名不能为空");
        }
        
        if (gameName.empty()) {
            throw std::runtime_error("游戏名称不能为空");
        }
        
        if (teamSide.empty()) {
            throw std::runtime_error("阵营选择不能为空");
        }
        
        if (mapHotkey.empty()) {
            throw std::runtime_error("地图热键不能为空");
        }
        
        if (targetHotkey.empty()) {
            throw std::runtime_error("目标热键不能为空");
        }
        
        if (timestamp.empty()) {
            throw std::runtime_error("时间戳不能为空");
        }
        
        // 先调用fetchDataCollection检查是否有有效数据可以上传
        blweb::services::DataCollectionService& dataCollectionService = blweb::services::DataCollectionService::getInstance();
        
        // 获取当前数据采集目录信息
        Poco::JSON::Object::Ptr collectionInfo = dataCollectionService.fetchDataCollection(username, gameName);
        
        // 检查是否成功获取到数据
        if (collectionInfo->getValue<int>("code") == 200 && collectionInfo->has("data")) {
            Poco::JSON::Object::Ptr data = collectionInfo->getObject("data");
            
            // 检查是否有采集目录
            if (data->has("collections") && data->getArray("collections")->size() == 0) {
                // 没有采集目录，返回错误
                LOG_WARNING("上传数据采集内容失败: 未发现任何采集目录");
                
                response.set("status", "error");
                
                Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
                errorContent->set("username", username);
                errorContent->set("gameName", gameName);
                errorContent->set("uploadStatus", "failed");
                errorContent->set("uploadComplete", false);
                errorContent->set("filesDeleted", false);
                
                Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
                errorObj->set("code", "NO_DATA");
                errorObj->set("message", "未发现任何采集目录，没有数据可以上传");
                
                errorContent->set("error", errorObj);
                response.set("content", errorContent);
                
                std::ostringstream oss;
                response.stringify(oss);
                return oss.str();
            }
            
            // 检查所有采集目录中是否有文件
            bool hasFiles = false;
            Poco::JSON::Array::Ptr collections = data->getArray("collections");
            
            for (size_t i = 0; i < collections->size(); i++) {
                if (collections->isObject(i)) {
                    Poco::JSON::Object::Ptr collection = collections->getObject(i);
                    if (collection->has("totalFiles") && collection->getValue<int>("totalFiles") > 0) {
                        hasFiles = true;
                        break;
                    }
                }
            }
            
            if (!hasFiles) {
                // 所有采集目录都没有文件，返回错误
                LOG_WARNING("上传数据采集内容失败: 所有采集目录中没有文件");
                
                response.set("status", "error");
                
                Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
                errorContent->set("username", username);
                errorContent->set("gameName", gameName);
                errorContent->set("uploadStatus", "failed");
                errorContent->set("uploadComplete", false);
                errorContent->set("filesDeleted", false);
                
                Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
                errorObj->set("code", "NO_DATA");
                errorObj->set("message", "所有采集目录中没有文件，没有数据可以上传");
                
                errorContent->set("error", errorObj);
                response.set("content", errorContent);
                
                std::ostringstream oss;
                response.stringify(oss);
                return oss.str();
            }
        }
        
        // 2. 调用服务层处理业务逻辑
        Poco::JSON::Object::Ptr result = dataCollectionService.uploadDataCollection(
            username, gameName, collectionName, teamSide, mapHotkey, targetHotkey, timestamp, deleteAll
        );
        
        // 3. 构建响应 - 严格按照API文档格式
        int code = result->getValue<int>("code");
        if (code == 200) {
            // 检查是否有特殊状态
            if (result->has("status") && result->getValue<std::string>("status") == "warning") {
                response.set("status", "warning");
            } else {
                response.set("status", "ok");
            }
            
            // 添加响应内容
            if (result->has("data")) {
                response.set("content", result->getObject("data"));
            }
        } else {
            std::string error = result->getValue<std::string>("msg");
            LOG_ERROR("上传数据采集内容失败: {}", error);
            response.set("status", "error");
            
            // 构建错误响应 - 符合API文档格式
            Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
            errorContent->set("username", username);
            errorContent->set("gameName", gameName);
            errorContent->set("uploadStatus", "failed");
            errorContent->set("uploadComplete", false);
            errorContent->set("filesDeleted", false);
            
            // 确定正确的错误码
            std::string errorCode = "SERVER_UNREACHABLE";
            if (code == 404) {
                errorCode = "INVALID_COLLECTION";
            } else if (code == 400) {
                errorCode = "NO_DATA";
            }
            
            Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
            errorObj->set("code", errorCode);
            errorObj->set("message", error);
            
            errorContent->set("error", errorObj);
            response.set("content", errorContent);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理数据采集上传异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        
        // 构建错误响应 - 符合API文档格式
        Poco::JSON::Object::Ptr errorContent = new Poco::JSON::Object();
        errorContent->set("username", content->optValue<std::string>("username", ""));
        errorContent->set("gameName", content->optValue<std::string>("gameName", ""));
        errorContent->set("uploadStatus", "failed");
        errorContent->set("uploadComplete", false);
        errorContent->set("filesDeleted", false);
        
        Poco::JSON::Object::Ptr errorObj = new Poco::JSON::Object();
        errorObj->set("code", "SERVER_ERROR");
        errorObj->set("message", error);
        
        errorContent->set("error", errorObj);
        response.set("content", errorContent);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace DataCollection
} // namespace MessageHandlers 