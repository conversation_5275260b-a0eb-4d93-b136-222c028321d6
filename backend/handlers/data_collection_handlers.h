#pragma once

#include <Poco/JSON/Object.h>
#include <string>
#include <Poco/DateTimeFormatter.h>
#include "../blsql/data_collections_db.h"

namespace MessageHandlers {
namespace DataCollection {

// 处理数据采集配置修改请求
std::string handleDataCollectionModify(const Poco::JSON::Object::Ptr& content);

// 处理数据采集配置读取请求
std::string handleDataCollectionRead(const Poco::JSON::Object::Ptr& content);

// 处理数据采集信息获取请求
std::string handleDataCollectionFetch(const Poco::JSON::Object::Ptr& content);

// 处理数据采集上传请求
std::string handleDataCollectionUpload(const Poco::JSON::Object::Ptr& content);

} // namespace DataCollection
} // namespace MessageHandlers 