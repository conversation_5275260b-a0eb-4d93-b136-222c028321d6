#pragma once

#include "base_handler.h"
#include <Poco/JSON/Object.h>
#include <string>

namespace MessageHandlers {
namespace Function {

/**
 * @class FunctionHandler
 * @brief 功能配置相关请求处理器
 * 
 * 负责处理与功能配置相关的请求，包括读取和修改
 */
class FunctionHandler : public BaseHandler {
public:
    /**
     * @brief 检查是否能处理指定的action
     * @param action 请求的action名称
     * @return bool 如果能处理返回true，否则返回false
     */
    bool canHandle(const std::string& action) const override;
    
    /**
     * @brief 处理功能配置相关的消息
     * @param action 请求的action名称
     * @param content 请求的内容对象
     * @return std::string 响应的JSON字符串
     */
    std::string handle(const std::string& action, 
                      const Poco::JSON::Object::Ptr& content) override;
    
    /**
     * @brief 处理功能配置修改请求
     * @param content 请求的内容对象
     * @return std::string 响应的JSON字符串
     */
    std::string handleFunctionModify(const Poco::JSON::Object::Ptr& content);
    
    /**
     * @brief 处理功能配置读取请求
     * @param content 请求的内容对象
     * @return std::string 响应的JSON字符串
     */
    std::string handleFunctionRead(const Poco::JSON::Object::Ptr& content);
};

} // namespace Function
} // namespace MessageHandlers 