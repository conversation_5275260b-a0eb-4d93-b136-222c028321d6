#include "fire_handlers.h"
#include "../blserver/fire_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blserver/update_models.h"

#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <sstream>

namespace MessageHandlers {
namespace Fire {

// 处理射击配置修改请求
std::string handleFireModify(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "fire_modify_response");
    
    try {
        // 1. 从请求中提取必要参数 - 按照API文档定义的完整参数
        std::string username = content->optValue("username", std::string(""));
        std::string gameName = content->optValue("gameName", std::string(""));
        int rifleSleep = content->optValue("rifleSleep", 100);
        int rifleInterval = content->optValue("rifleInterval", 200);
        int pistolSleep = content->optValue("pistolSleep", 150);
        int pistolInterval = content->optValue("pistolInterval", 250);
        int sniperSleep = content->optValue("sniperSleep", 300);
        int sniperInterval = content->optValue("sniperInterval", 500);
        
        LOG_INFO("射击配置修改请求 - 用户名: {}, 游戏: {}", username, gameName);
        LOG_INFO("射击参数: rifleSleep={}, rifleInterval={}, pistolSleep={}, pistolInterval={}, sniperSleep={}, sniperInterval={}", 
                 rifleSleep, rifleInterval, pistolSleep, pistolInterval, sniperSleep, sniperInterval);
        
        // 2. 参数验证
        if (username.empty()) {
            LOG_ERROR("射击配置修改失败: 用户名不能为空");
            response.set("status", "error");
            response.set("message", "用户名不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        if (gameName.empty()) {
            LOG_ERROR("射击配置修改失败: 游戏名称不能为空");
            response.set("status", "error");
            response.set("message", "游戏名称不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 3. 【关键步骤】先更新C++全局变量 - 确保实时生效
        LOG_INFO("步骤1: 更新C++全局变量");
        
        // 直接更新全局变量，确保立即生效
        webui::home::g_username = username;
        webui::home::g_game_name = gameName;
        webui::fire::g_rifle_sleep = rifleSleep;
        webui::fire::g_rifle_interval = rifleInterval;
        webui::fire::g_pistol_sleep = pistolSleep;
        webui::fire::g_pistol_interval = pistolInterval;
        webui::fire::g_sniper_sleep = sniperSleep;
        webui::fire::g_sniper_interval = sniperInterval;
        
        LOG_INFO("C++全局变量更新完成: rifleSleep={}, rifleInterval={}, pistolSleep={}, pistolInterval={}, sniperSleep={}, sniperInterval={}", 
                 webui::fire::g_rifle_sleep, webui::fire::g_rifle_interval, webui::fire::g_pistol_sleep,
                 webui::fire::g_pistol_interval, webui::fire::g_sniper_sleep, webui::fire::g_sniper_interval);
        
        // 4. 【可选步骤】通过updateGlobalModels进行额外的全局变量处理（如果有其他逻辑）
        bool globalUpdated = blweb::utils::updateGlobalModels("fire_modify", content);
        if (!globalUpdated) {
            LOG_WARNING("updateGlobalModels调用失败，但全局变量已直接更新，继续执行");
        }
        
        // 5. 【第二步骤】更新数据库 - 持久化存储
        LOG_INFO("步骤2: 更新数据库");
        blweb::services::FireService& fireService = blweb::services::FireService::getInstance();
        bool dbSuccess = fireService.updateFireConfig(
            username, gameName, rifleSleep, rifleInterval, pistolSleep, pistolInterval, sniperSleep, sniperInterval
        );
        
        // 6. 构建响应 - 即使数据库更新失败，全局变量已更新，系统仍可正常工作
        if (dbSuccess) {
            LOG_INFO("射击配置修改完成: 全局变量和数据库都已更新");
            response.set("status", "success");
        } else {
            LOG_WARNING("射击配置部分更新: 全局变量已更新，但数据库更新失败: {}", fireService.getLastError());
            response.set("status", "warning");
            response.set("message", "配置已实时生效，但数据库保存失败: " + fireService.getLastError());
        }
        
        // 7. 创建响应数据对象 - 返回实际的全局变量值
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", webui::home::g_username);
        data->set("gameName", webui::home::g_game_name);
        data->set("rifleSleep", webui::fire::g_rifle_sleep);
        data->set("rifleInterval", webui::fire::g_rifle_interval);
        data->set("pistolSleep", webui::fire::g_pistol_sleep);
        data->set("pistolInterval", webui::fire::g_pistol_interval);
        data->set("sniperSleep", webui::fire::g_sniper_sleep);
        data->set("sniperInterval", webui::fire::g_sniper_interval);
        
        // 获取当前时间并格式化
        Poco::DateTime now;
        std::string createdAt = content->optValue("createdAt", 
            Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ"));
        std::string updatedAt = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
        
        data->set("createdAt", createdAt);
        data->set("updatedAt", updatedAt);
        
        response.set("data", data);
        
    }
    catch (const std::exception& e) {
        std::string error = "处理射击配置修改异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理射击配置读取请求
std::string handleFireRead(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "fire_read_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->getValue<std::string>("username");
        std::string gameName = content->getValue<std::string>("gameName");
        
        LOG_INFO("射击设置读取 - 用户: {}, 游戏: {}", username, gameName);
        
        // 2. 【关键步骤】先从数据库获取最新的射击配置数据
        LOG_INFO("步骤1: 从数据库获取最新射击配置");
        blweb::services::FireService& fireService = blweb::services::FireService::getInstance();
        Poco::JSON::Object::Ptr result = fireService.getFireConfig(username, gameName);
        
        // 3. 【关键步骤】用数据库数据更新全局变量
        if (result->getValue<int>("code") == 200 && result->has("data")) {
            Poco::JSON::Object::Ptr data = result->getObject("data");
            
            LOG_INFO("步骤2: 用数据库数据更新C++全局变量");
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 用数据库获取的值更新全局变量
            webui::fire::g_rifle_sleep = data->optValue("rifleSleep", 100);
            webui::fire::g_rifle_interval = data->optValue("rifleInterval", 200);
            webui::fire::g_pistol_sleep = data->optValue("pistolSleep", 150);
            webui::fire::g_pistol_interval = data->optValue("pistolInterval", 250);
            webui::fire::g_sniper_sleep = data->optValue("sniperSleep", 300);
            webui::fire::g_sniper_interval = data->optValue("sniperInterval", 500);
            
            LOG_INFO("C++全局变量更新完成: rifleSleep={}, rifleInterval={}, pistolSleep={}, pistolInterval={}, sniperSleep={}, sniperInterval={}", 
                     webui::fire::g_rifle_sleep, webui::fire::g_rifle_interval, webui::fire::g_pistol_sleep,
                     webui::fire::g_pistol_interval, webui::fire::g_sniper_sleep, webui::fire::g_sniper_interval);
            
            // 4. 确保响应数据包含所有必需字段
            if (!data->has("username")) data->set("username", username);
            if (!data->has("gameName")) data->set("gameName", gameName);
            
            // 确保所有必需的API字段都存在（使用更新后的全局变量值）
            data->set("rifleSleep", webui::fire::g_rifle_sleep);
            data->set("rifleInterval", webui::fire::g_rifle_interval);
            data->set("pistolSleep", webui::fire::g_pistol_sleep);
            data->set("pistolInterval", webui::fire::g_pistol_interval);
            data->set("sniperSleep", webui::fire::g_sniper_sleep);
            data->set("sniperInterval", webui::fire::g_sniper_interval);
            
            // 5. 设置成功响应
            response.set("status", "success");
            response.set("data", data);
            
        } else {
            // 数据库无数据或查询失败，使用默认值并更新全局变量
            LOG_INFO("数据库无数据或查询失败，使用默认值并更新全局变量");
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 使用默认值更新全局变量
            webui::fire::g_rifle_sleep = 100;
            webui::fire::g_rifle_interval = 200;
            webui::fire::g_pistol_sleep = 150;
            webui::fire::g_pistol_interval = 250;
            webui::fire::g_sniper_sleep = 300;
            webui::fire::g_sniper_interval = 500;
            
            LOG_INFO("使用默认值更新全局变量完成");
            
            // 创建默认数据对象 - 使用API文档定义的默认值
            Poco::JSON::Object::Ptr defaultData = new Poco::JSON::Object();
            defaultData->set("username", username);
            defaultData->set("gameName", gameName);
            defaultData->set("rifleSleep", webui::fire::g_rifle_sleep);
            defaultData->set("rifleInterval", webui::fire::g_rifle_interval);
            defaultData->set("pistolSleep", webui::fire::g_pistol_sleep);
            defaultData->set("pistolInterval", webui::fire::g_pistol_interval);
            defaultData->set("sniperSleep", webui::fire::g_sniper_sleep);
            defaultData->set("sniperInterval", webui::fire::g_sniper_interval);
            defaultData->set("exists", false);
            
            response.set("status", "success");
            response.set("data", defaultData);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理射击配置读取异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace Fire
} // namespace MessageHandlers 

/*
 * 射击配置处理逻辑说明 (2024年修改)：
 * 
 * === 射击配置修改逻辑 (fire_modify) ===
 * 1. 【优先级1】先更新C++全局变量 - 确保实时生效
 *    - 直接更新webui::home命名空间中的用户名和游戏名
 *    - 通过updateGlobalModels更新webui::fire命名空间中的所有射击配置变量
 *    - 这确保了射击配置能立即生效
 *    - 即使数据库更新失败，系统仍能正常工作
 * 
 * 2. 【优先级2】再更新数据库 - 持久化存储
 *    - 调用FireService进行数据库更新
 *    - 如果失败，返回warning状态但不影响实时功能
 * 
 * 3. 【响应策略】
 *    - 成功：全局变量和数据库都更新成功
 *    - 警告：全局变量更新成功，数据库更新失败
 *    - 错误：参数验证失败或异常情况
 * 
 * === 射击配置读取逻辑 (fire_read) ===
 * 1. 【步骤1】从数据库获取最新的射击配置数据
 *    - 调用FireService从数据库读取用户在指定游戏的射击配置
 *    - 确保获取到的是最新的持久化数据
 * 
 * 2. 【步骤2】用数据库数据更新C++全局变量
 *    - 将数据库中的配置值更新到webui::fire命名空间的全局变量中
 *    - 确保射击配置使用的是数据库中的最新配置
 *    - 如果数据库无数据，使用默认配置更新全局变量
 * 
 * 3. 【步骤3】返回给前端
 *    - 将更新后的全局变量值返回给前端
 *    - 确保前端显示的值与后端实际使用的值一致
 * 
 * 字段映射 (API -> 数据库 -> 全局变量)：
 * - rifleSleep -> rifle_sleep -> g_rifle_sleep
 * - rifleInterval -> rifle_interval -> g_rifle_interval
 * - pistolSleep -> pistol_sleep -> g_pistol_sleep
 * - pistolInterval -> pistol_interval -> g_pistol_interval
 * - sniperSleep -> sniper_sleep -> g_sniper_sleep
 * - sniperInterval -> sniper_interval -> g_sniper_interval
 * 
 * 默认值 (API文档定义)：
 * - rifleSleep: 100ms (范围: 10-500ms)
 * - rifleInterval: 200ms (范围: 50-1000ms)
 * - pistolSleep: 150ms (范围: 10-500ms)
 * - pistolInterval: 250ms (范围: 50-1000ms)
 * - sniperSleep: 300ms (范围: 10-500ms)
 * - sniperInterval: 500ms (范围: 50-1000ms)
 */