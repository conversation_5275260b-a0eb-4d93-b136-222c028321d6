#pragma once

#include <Poco/JSON/Object.h>
#include <string>

namespace MessageHandlers {

/**
 * @class BaseHandler
 * @brief 消息处理器基类
 * 
 * 所有特定功能的处理器都应继承此基类，实现消息处理接口
 */
class BaseHandler {
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~BaseHandler() = default;
    
    /**
     * @brief 处理消息
     * @param content 请求的内容对象
     * @return std::string 响应的JSON字符串
     */
    virtual std::string handleMessage(const Poco::JSON::Object::Ptr& content) = 0;
};

} // namespace MessageHandlers 