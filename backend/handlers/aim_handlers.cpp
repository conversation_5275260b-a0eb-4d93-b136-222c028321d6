#include "aim_handlers.h"
#include "../blserver/aim_service.h"
#include "../blserver/update_models.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"

#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <sstream>

namespace MessageHandlers {
namespace Aim {

// 处理瞄准配置修改请求
std::string handleAimModify(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "aim_modify_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->optValue("username", std::string(""));
        std::string gameName = content->optValue("gameName", std::string(""));
        
        LOG_INFO("瞄准配置修改请求 - 用户名: {}, 游戏: {}", username, gameName);
        
        // 2. 参数验证
        if (username.empty()) {
            LOG_ERROR("瞄准配置修改失败: 用户名不能为空");
            response.set("status", "error");
            response.set("message", "用户名不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        if (gameName.empty()) {
            LOG_ERROR("瞄准配置修改失败: 游戏名称不能为空");
            response.set("status", "error");
            response.set("message", "游戏名称不能为空");
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 3. 【关键步骤】先更新C++全局变量 - 确保实时生效
        LOG_INFO("步骤1: 更新C++全局变量");
        
        // 直接更新用户名和游戏名
        webui::home::g_username = username;
        webui::home::g_game_name = gameName;
        
        // 通过updateGlobalModels更新瞄准配置的全局变量
        bool globalUpdated = blweb::utils::updateGlobalModels("aim_modify", content);
        if (!globalUpdated) {
            LOG_WARNING("updateGlobalModels调用失败，但基本参数已更新");
        }
        
        LOG_INFO("C++全局变量更新完成: username={}, gameName={}, aimRange={}, trackRange={}, headHeight={}, neckHeight={}, chestHeight={}", 
                 webui::home::g_username, webui::home::g_game_name, webui::aim::g_aim_range,
                 webui::aim::g_track_range, webui::aim::g_head_height, webui::aim::g_neck_height, webui::aim::g_chest_height);
        
        // 4. 【第二步骤】更新数据库 - 持久化存储
        LOG_INFO("步骤2: 更新数据库");
        blweb::services::AimService& aimService = blweb::services::AimService::getInstance();
        Poco::JSON::Object::Ptr result = aimService.handleAimModify(username, gameName, content);
        
        // 5. 构建响应 - 即使数据库更新失败，全局变量已更新，系统仍可正常工作
        int code = result->getValue<int>("code");
        if (code == 200) {
            LOG_INFO("瞄准配置修改完成: 全局变量和数据库都已更新");
            response.set("status", "ok");
        } else {
            std::string error = result->getValue<std::string>("msg");
            LOG_WARNING("瞄准配置部分更新: 全局变量已更新，但数据库更新失败: {}", error);
            response.set("status", "warning");
            response.set("message", "配置已实时生效，但数据库保存失败: " + error);
        }
        
        // 6. 创建响应数据对象 - 返回实际的全局变量值
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", webui::home::g_username);
        data->set("gameName", webui::home::g_game_name);
        data->set("aimRange", webui::aim::g_aim_range);
        data->set("trackRange", webui::aim::g_track_range);
        data->set("headHeight", webui::aim::g_head_height);
        data->set("neckHeight", webui::aim::g_neck_height);
        data->set("chestHeight", webui::aim::g_chest_height);
        data->set("headRangeX", webui::aim::g_head_range_x);
        data->set("headRangeY", webui::aim::g_head_range_y);
        data->set("neckRangeX", webui::aim::g_neck_range_x);
        data->set("neckRangeY", webui::aim::g_neck_range_y);
        data->set("chestRangeX", webui::aim::g_chest_range_x);
        data->set("chestRangeY", webui::aim::g_chest_range_y);
        
        // 获取当前时间并格式化
        Poco::DateTime now;
        std::string createdAt = content->optValue("createdAt", 
            Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ"));
        std::string updatedAt = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
        
        data->set("createdAt", createdAt);
        data->set("updatedAt", updatedAt);
        
        response.set("data", data);
        
    }
    catch (const std::exception& e) {
        std::string error = "处理瞄准配置修改异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理瞄准配置读取请求
std::string handleAimRead(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "aim_read_response");
    
    try {
        // 1. 从请求中提取必要参数
        std::string username = content->getValue<std::string>("username");
        std::string gameName = content->getValue<std::string>("gameName");
        
        LOG_INFO("瞄准设置读取 - 用户: {}, 游戏: {}", username, gameName);
        
        // 2. 【关键步骤】先从数据库获取最新的瞄准配置数据
        LOG_INFO("步骤1: 从数据库获取最新瞄准配置");
        blweb::services::AimService& aimService = blweb::services::AimService::getInstance();
        Poco::JSON::Object::Ptr result = aimService.getAimConfig(username, gameName);
        
        // 3. 【关键步骤】用数据库数据更新全局变量
        int code = result->getValue<int>("code");
        if (code == 200 && result->has("data")) {
            Poco::JSON::Object::Ptr data = result->getObject("data");
            
            LOG_INFO("步骤2: 用数据库数据更新C++全局变量");
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 用数据库获取的值更新全局变量
            webui::aim::g_aim_range = data->optValue("aimRange", 200.0);
            webui::aim::g_track_range = data->optValue("trackRange", 0.008);
            webui::aim::g_head_height = data->optValue("headHeight", 80.0);
            webui::aim::g_neck_height = data->optValue("neckHeight", 60.0);
            webui::aim::g_chest_height = data->optValue("chestHeight", 40.0);
            webui::aim::g_head_range_x = data->optValue("headRangeX", 5.0);
            webui::aim::g_head_range_y = data->optValue("headRangeY", 5.0);
            webui::aim::g_neck_range_x = data->optValue("neckRangeX", 5.0);
            webui::aim::g_neck_range_y = data->optValue("neckRangeY", 5.0);
            webui::aim::g_chest_range_x = data->optValue("chestRangeX", 50.0);
            webui::aim::g_chest_range_y = data->optValue("chestRangeY", 50.0);
            
            LOG_INFO("C++全局变量更新完成: aimRange={}, trackRange={}, headHeight={}, neckHeight={}, chestHeight={}", 
                     webui::aim::g_aim_range, webui::aim::g_track_range, webui::aim::g_head_height,
                     webui::aim::g_neck_height, webui::aim::g_chest_height);
            
            // 4. 确保响应数据包含所有必需字段
            if (!data->has("username")) data->set("username", username);
            if (!data->has("gameName")) data->set("gameName", gameName);
            
            // 确保所有必需的API字段都存在（使用更新后的全局变量值）
            data->set("aimRange", webui::aim::g_aim_range);
            data->set("trackRange", webui::aim::g_track_range);
            data->set("headHeight", webui::aim::g_head_height);
            data->set("neckHeight", webui::aim::g_neck_height);
            data->set("chestHeight", webui::aim::g_chest_height);
            data->set("headRangeX", webui::aim::g_head_range_x);
            data->set("headRangeY", webui::aim::g_head_range_y);
            data->set("neckRangeX", webui::aim::g_neck_range_x);
            data->set("neckRangeY", webui::aim::g_neck_range_y);
            data->set("chestRangeX", webui::aim::g_chest_range_x);
            data->set("chestRangeY", webui::aim::g_chest_range_y);
            
            // 5. 设置成功响应
            response.set("status", "ok");
            response.set("data", data);
            
        } else {
            // 数据库无数据或查询失败，使用默认值并更新全局变量
            LOG_INFO("数据库无数据或查询失败，使用默认值并更新全局变量");
            
            // 更新用户名和游戏名
            webui::home::g_username = username;
            webui::home::g_game_name = gameName;
            
            // 使用默认值更新全局变量
            webui::aim::g_aim_range = 200.0;
            webui::aim::g_track_range = 0.008;
            webui::aim::g_head_height = 80.0;
            webui::aim::g_neck_height = 60.0;
            webui::aim::g_chest_height = 40.0;
            webui::aim::g_head_range_x = 5.0;
            webui::aim::g_head_range_y = 5.0;
            webui::aim::g_neck_range_x = 5.0;
            webui::aim::g_neck_range_y = 5.0;
            webui::aim::g_chest_range_x = 50.0;
            webui::aim::g_chest_range_y = 50.0;
            
            LOG_INFO("使用默认值更新全局变量完成");
            
            // 创建默认数据对象
            Poco::JSON::Object::Ptr defaultData = new Poco::JSON::Object();
            defaultData->set("username", username);
            defaultData->set("gameName", gameName);
            defaultData->set("aimRange", webui::aim::g_aim_range);
            defaultData->set("trackRange", webui::aim::g_track_range);
            defaultData->set("headHeight", webui::aim::g_head_height);
            defaultData->set("neckHeight", webui::aim::g_neck_height);
            defaultData->set("chestHeight", webui::aim::g_chest_height);
            defaultData->set("headRangeX", webui::aim::g_head_range_x);
            defaultData->set("headRangeY", webui::aim::g_head_range_y);
            defaultData->set("neckRangeX", webui::aim::g_neck_range_x);
            defaultData->set("neckRangeY", webui::aim::g_neck_range_y);
            defaultData->set("chestRangeX", webui::aim::g_chest_range_x);
            defaultData->set("chestRangeY", webui::aim::g_chest_range_y);
            defaultData->set("exists", false);
            
            response.set("status", "ok");
            response.set("data", defaultData);
        }
    }
    catch (const std::exception& e) {
        std::string error = "处理瞄准配置读取异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理瞄准配置获取请求（兼容旧接口，实际与handleAimRead相同）
std::string handleAimGet(const Poco::JSON::Object::Ptr& content) {
    // 直接调用读取接口处理
    return handleAimRead(content);
}

} // namespace Aim
} // namespace MessageHandlers 

/*
 * 瞄准配置处理逻辑说明 (2024年修改)：
 * 
 * === 瞄准配置修改逻辑 (aim_modify) ===
 * 1. 【优先级1】先更新C++全局变量 - 确保实时生效
 *    - 直接更新webui::home命名空间中的用户名和游戏名
 *    - 通过updateGlobalModels更新webui::aim命名空间中的所有瞄准配置变量
 *    - 这确保了瞄准配置能立即生效
 *    - 即使数据库更新失败，系统仍能正常工作
 * 
 * 2. 【优先级2】再更新数据库 - 持久化存储
 *    - 调用AimService进行数据库更新
 *    - 如果失败，返回warning状态但不影响实时功能
 * 
 * 3. 【响应策略】
 *    - 成功：全局变量和数据库都更新成功
 *    - 警告：全局变量更新成功，数据库更新失败
 *    - 错误：参数验证失败或异常情况
 * 
 * === 瞄准配置读取逻辑 (aim_read) ===
 * 1. 【步骤1】从数据库获取最新的瞄准配置数据
 *    - 调用AimService从数据库读取用户在指定游戏的瞄准配置
 *    - 确保获取到的是最新的持久化数据
 * 
 * 2. 【步骤2】用数据库数据更新C++全局变量
 *    - 将数据库中的配置值更新到webui::aim命名空间的全局变量中
 *    - 确保瞄准配置使用的是数据库中的最新配置
 *    - 如果数据库无数据，使用默认配置更新全局变量
 * 
 * 3. 【步骤3】返回给前端
 *    - 将更新后的全局变量值返回给前端
 *    - 确保前端显示的值与后端实际使用的值一致
 * 
 * 字段映射 (API -> 数据库 -> 全局变量)：
 * - aimRange -> aim_range -> g_aim_range
 * - trackRange -> track_range -> g_track_range
 * - headHeight -> head_height -> g_head_height
 * - neckHeight -> neck_height -> g_neck_height
 * - chestHeight -> chest_height -> g_chest_height
 * - headRangeX -> head_range_x -> g_head_range_x
 * - headRangeY -> head_range_y -> g_head_range_y
 * - neckRangeX -> neck_range_x -> g_neck_range_x
 * - neckRangeY -> neck_range_y -> g_neck_range_y
 * - chestRangeX -> chest_range_x -> g_chest_range_x
 * - chestRangeY -> chest_range_y -> g_chest_range_y
 */ 