#include "header_handlers.h"
#include "../blserver/header_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blsql/mysql_db.h"
#include "../../blkm/cloud_update.h"

#include <sstream>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace MessageHandlers {
namespace Header {

// 更新版本信息的辅助函数
void updateVersionInfo() {
    try {
        // 根据Pro状态选择正确的API地址
        std::string api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
        LOG_DEBUG("更新版本信息 - Pro状态: {}, 使用API地址: {}", 
                 webui::header::isPro ? "Pro版本" : "标准版本", api_host);
        
        // 创建云更新管理器来获取最新版本信息
        auto km_sdk = std::make_shared<jfkm::KmSdk>(jfkm_config::g_access_key, api_host);
        jfkm::CloudUpdateManager update_manager(km_sdk);
        
        // 获取版本信息
        auto [version_info, error] = update_manager.get_version_info();
        
        if (error.empty() && !version_info.version.empty()) {
            // 更新最新版本号
            webui::header::latestVersion = version_info.version;
            LOG_DEBUG("版本信息更新成功 - 当前版本: {}, 最新版本: {}", 
                     webui::header::currentVersion, webui::header::latestVersion);
        } else {
            LOG_WARNING("获取最新版本信息失败: {}", error);
        }
    } catch (const std::exception& e) {
        LOG_ERROR("更新版本信息异常: {}", e.what());
    }
}

// 处理状态栏查询请求
std::string handleStatusBarQuery(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "status_bar_response");
    
    try {
        LOG_INFO("处理状态栏查询请求");
        
        // 检查数据库连接状态
        webui::header::databaseStatus = blweb::MySQLDB::getInstance().isConnected();
        
        // 定期更新版本信息（每10次查询更新一次，避免频繁请求）
        static int query_count = 0;
        query_count++;
        if (query_count >= 10) {
            updateVersionInfo();
            query_count = 0;
        }
        
        // 获取当前时间作为更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
        
        // 创建包含状态信息的数据对象
        Poco::JSON::Object::Ptr dataObj = new Poco::JSON::Object();
        
        // 设置状态为布尔值，符合API文档中的定义
        dataObj->set("dbStatus", webui::header::databaseStatus);
        dataObj->set("inferenceStatus", webui::header::inferenceStatus);
        dataObj->set("cardKeyStatus", webui::header::cardKeyStatus);
        dataObj->set("keyMouseStatus", webui::header::keyMouseStatus);
        dataObj->set("frameRate", webui::header::frame_rate); // 添加HDMI刷新率
        dataObj->set("width", webui::header::width);     // 添加HDMI宽度
        dataObj->set("height", webui::header::height);   // 添加HDMI高度
        dataObj->set("currentVersion", webui::header::currentVersion); // 当前版本号
        dataObj->set("latestVersion", webui::header::latestVersion);   // 最新版本号
        dataObj->set("updatedAt", timeStr);
        
        response.set("status", "success");
        response.set("data", dataObj);
        
        LOG_INFO("状态栏查询结果 - 数据库: {}, 推理: {}, 卡密: {}, 键鼠: {}, 分辨率: {}x{}, 刷新率: {}Hz, 当前版本: {}, 最新版本: {}", 
                webui::header::databaseStatus ? "true" : "false", 
                webui::header::inferenceStatus ? "true" : "false",
                webui::header::cardKeyStatus ? "true" : "false", 
                webui::header::keyMouseStatus ? "true" : "false",
                webui::header::width, webui::header::height,
                webui::header::frame_rate,
                webui::header::currentVersion,
                webui::header::latestVersion);
    }
    catch (const std::exception& e) {
        std::string error = "处理状态栏查询异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理心跳请求 - 不更新数据库
std::string handleHeartbeat(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "heartbeat_response");
    
    try {
        LOG_INFO("处理心跳请求 - 仅响应客户端，不更新数据库");
        
        // 获取请求中的参数 - 根据API文档，只需要clientStatus和updatedAt
        std::string clientStatus = "active"; // 默认值
        std::string updatedAt = "";
        
        // 如果content可能直接包含clientStatus，则直接从content获取
        if (content->has("clientStatus")) {
            clientStatus = content->getValue<std::string>("clientStatus");
        } 
        // 否则尝试从content.content对象中获取（兼容旧格式）
        else if (content->has("content") && content->isObject("content")) {
            Poco::JSON::Object::Ptr contentObj = content->getObject("content");
            clientStatus = contentObj->optValue<std::string>("clientStatus", "active");
            updatedAt = contentObj->optValue<std::string>("updatedAt", "");
        }
        
        // 获取当前时间作为服务器时间
        Poco::DateTime now;
        std::string serverTime = Poco::DateTimeFormatter::format(now, "%Y-%m-%dT%H:%M:%SZ");
        
        // 创建数据对象 - 根据API文档，只返回serverStatus和serverTime
        Poco::JSON::Object::Ptr dataObj = new Poco::JSON::Object();
        dataObj->set("serverStatus", "online");
        dataObj->set("serverTime", serverTime);
        
        response.set("status", "success");
        response.set("data", dataObj);
        
        // 记录心跳请求处理完成
        LOG_INFO("心跳响应完成 - 服务器状态: online, 客户端状态: {}", clientStatus);
    }
    catch (const std::exception& e) {
        std::string error = "处理心跳请求异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

// 处理版本更新请求
std::string handleVersionUpdate(const Poco::JSON::Object::Ptr& content) {
    // 构建响应对象
    Poco::JSON::Object response;
    response.set("action", "version_update_response");
    
    try {
        LOG_INFO("收到版本更新请求");
        
        // 记录当前状态信息
        LOG_INFO("当前版本: {}", webui::header::currentVersion);
        LOG_INFO("最新版本: {}", webui::header::latestVersion);
        LOG_INFO("Pro状态: {}", webui::header::isPro ? "Pro版本" : "标准版本");
        LOG_INFO("当前卡密: {}", jfkm_config::g_card_key);
        
        // 检查是否有有效的卡密
        if (jfkm_config::g_card_key.empty()) {
            LOG_WARNING("卡密为空，无法执行版本更新");
            response.set("status", "error");
            response.set("message", "卡密为空，无法执行版本更新");
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 检查卡密验证状态
        if (jfkm_config::g_auth_status != 1) {
            LOG_WARNING("卡密验证状态异常: {}, 无法执行版本更新", jfkm_config::g_auth_status.load());
            response.set("status", "error");
            response.set("message", "卡密验证失败，无法执行版本更新");
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 设置版本更新请求标志
        webui::header::versionUpdateRequested = true;
        
        // 记录使用的API地址
        std::string api_host = webui::header::isPro ? jfkm_config::g_api_host_pro : jfkm_config::g_api_host_standard;
        LOG_INFO("将使用API地址: {}", api_host);
        LOG_INFO("应用标识: {}", jfkm_config::g_app_flag);
        
        response.set("status", "success");
        response.set("message", "版本更新请求已接收，正在后台处理");
        
        LOG_INFO("版本更新请求标志已设置，等待后台处理");
    }
    catch (const std::exception& e) {
        std::string error = "处理版本更新请求异常: " + std::string(e.what());
        LOG_ERROR("{}", error);
        response.set("status", "error");
        response.set("message", error);
    }
    
    // 将响应转换为字符串
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

} // namespace Header
} // namespace MessageHandlers 