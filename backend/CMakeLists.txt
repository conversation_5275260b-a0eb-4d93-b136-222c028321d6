cmake_minimum_required(VERSION 3.10)
project(blweb_server VERSION 1.0.0 LANGUAGES C CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build" FORCE)
  set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# 检测系统架构
include(CheckCXXCompilerFlag)
CHECK_CXX_COMPILER_FLAG("-march=native" COMPILER_SUPPORTS_MARCH_NATIVE)
if(COMPILER_SUPPORTS_MARCH_NATIVE)
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -march=native")
else()
  # 如果是ARM架构
  if(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -march=armv8-a")
    message(STATUS "设置为ARM架构编译")
  endif()
endif()

set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")

# 设置包含目录 - 保留这行注释但删除下面的include_directories调用
# 包含目录已通过target_include_directories在目标级别设置

# 设置POCO路径 - 使用3rdparty中的POCO
set(POCO_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty/poco)
set(POCO_INCLUDE_DIRS 
    ${POCO_ROOT_DIR}/Foundation/include
    ${POCO_ROOT_DIR}/Net/include
    ${POCO_ROOT_DIR}/Util/include
    ${POCO_ROOT_DIR}/JSON/include
    ${POCO_ROOT_DIR}/XML/include
    ${POCO_ROOT_DIR}/Data/include
    ${POCO_ROOT_DIR}/Data/MySQL/include
    ${POCO_ROOT_DIR}/Zip/include
)

# 设置POCO库路径
set(POCO_LIB_DIR ${POCO_ROOT_DIR}/lib)
link_directories(${POCO_LIB_DIR})

# 定义POCO库
set(POCO_FOUNDATION_LIB PocoFoundation)
set(POCO_NET_LIB PocoNet)
set(POCO_DATA_LIB PocoData)
set(POCO_MYSQL_LIB PocoDataMySQL)
set(POCO_UTIL_LIB PocoUtil)
set(POCO_JSON_LIB PocoJSON)
set(POCO_XML_LIB PocoXML)
set(POCO_ZIP_LIB PocoZip)

# Arch Linux上的MySQL库路径
if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
  # 添加MySQL库路径
  list(APPEND CMAKE_PREFIX_PATH 
    /usr/local/lib 
    /usr/lib 
    /usr/lib/mysql 
    /usr/include/mysql
    /usr/local/include/mysql
  )
endif()

# POCO库已经手动设置了，不再需要寻找系统安装的库
message(STATUS "Using POCO from: ${POCO_ROOT_DIR}")
message(STATUS "POCO libraries location: ${POCO_LIB_DIR}")
message(STATUS "POCO include directories: ${POCO_INCLUDE_DIRS}")

# MySQL支持检查
if(EXISTS "${POCO_ROOT_DIR}/Data/MySQL")
    message(STATUS "Found POCO MySQL support")
    add_definitions(-DHAVE_MYSQL_SUPPORT)
    
    # 查找MySQL客户端库
    find_path(MYSQL_INCLUDE_DIR mysql.h
        PATHS
        /usr/include/mysql
        /usr/local/include/mysql
        /opt/mysql/include/mysql
        /opt/mysql/mysql/include
    )
    
    find_library(MYSQL_LIBRARY NAMES mysqlclient
        PATHS
        /usr/lib
        /usr/lib64
        /usr/local/lib
        /usr/local/lib64
        /opt/mysql/lib
        /opt/mysql/lib/mysql
    )
    
    if(MYSQL_INCLUDE_DIR AND MYSQL_LIBRARY)
        message(STATUS "Found MySQL: ${MYSQL_LIBRARY}")
        # 这里不再使用include_directories
    else()
        message(WARNING "MySQL client library not found. This may cause problems with POCO MySQL connector.")
    endif()
else()
    message(WARNING "POCO MySQL component not found. Database functionality will be disabled.")
    add_definitions(-DNO_DATABASE_SUPPORT)
endif()

# 查找线程库
find_package(Threads REQUIRED)

# 检查系统支持
include(CheckIncludeFile)
include(CheckSymbolExists)
CHECK_INCLUDE_FILE("netinet/in.h" HAVE_NETINET_IN_H)
CHECK_SYMBOL_EXISTS(SO_REUSEPORT "sys/socket.h" HAVE_SO_REUSEPORT)

# 为POCO库定义编译选项
add_definitions(-DPOCO_NO_AUTOMATIC_LIBS)

if(HAVE_NETINET_IN_H)
    add_definitions(-DHAVE_NETINET_IN_H)
endif()

if(HAVE_SO_REUSEPORT)
    add_definitions(-DHAVE_SO_REUSEPORT)
endif()

# 设置源文件
set(BACKEND_SOURCES
    blsql/mysql_db.cpp
    blserver/poco_websocket_server.cpp
    blserver/message_handlers.cpp
    blserver/fov_service.cpp
    blserver/register_service.cpp
    blserver/home_service.cpp
    blserver/header_service.cpp
    blserver/function_service.cpp
    blserver/pid_service.cpp
    blserver/aim_service.cpp
    blserver/login_service.cpp
    blserver/update_models.cpp
    blserver/fire_service.cpp
    blserver/data_collection_service.cpp
    blsql/registerdb.cpp
    blsql/function_configs_db.cpp
    blsql/pid_configs_db.cpp
    blsql/aim_configs_db.cpp
    blsql/login_db.cpp
    blsql/fovconfgidb.cpp
    blsql/userinfodb.cpp
    blsql/fire_configs_db.cpp
    blsql/data_collections_db.cpp
    ../ThreadManager/web_server.cpp
    # 新增的处理器源文件
    handlers/pid_handlers.cpp
    handlers/function_handlers.cpp
    handlers/home_handlers.cpp
    handlers/aim_handlers.cpp
    handlers/fov_handlers.cpp
    handlers/login_handlers.cpp
    handlers/register_handlers.cpp
    handlers/header_handlers.cpp
    handlers/fire_handlers.cpp
    handlers/data_collection_handlers.cpp
)

# 创建一个库
add_library(blweb_backend STATIC ${BACKEND_SOURCES})

# 为库添加包含目录
target_include_directories(blweb_backend PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/..
    ${POCO_INCLUDE_DIRS}
    ${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty/rknn/include  # RKNN API包含目录
)

# 如果找到MySQL包含目录，添加它
if(MYSQL_INCLUDE_DIR)
    target_include_directories(blweb_backend PUBLIC ${MYSQL_INCLUDE_DIR})
endif()

# 链接库
target_link_libraries(blweb_backend PRIVATE
    blweb_utils
    spdlog::spdlog
    Threads::Threads
    ${POCO_FOUNDATION_LIB}
    ${POCO_NET_LIB}
    ${POCO_JSON_LIB}
    ${POCO_UTIL_LIB}
    ${POCO_XML_LIB}
    ${POCO_ZIP_LIB}
    algorithm_lib
    blkmapi  # 添加BLKM API库链接，支持header_handlers.cpp中的jfkm命名空间
    ${ATOMIC_LIBRARY}
)

# 如果找到MySQL组件，添加相应的库
if(EXISTS "${POCO_ROOT_DIR}/Data/MySQL")
    target_link_libraries(blweb_backend PRIVATE
        ${POCO_DATA_LIB}
        ${POCO_MYSQL_LIB}
    )
    
    # 链接MySQL客户端库
    if(MYSQL_LIBRARY)
        target_link_libraries(blweb_backend PRIVATE ${MYSQL_LIBRARY})
    endif()
endif()

# 主可执行文件
add_executable(bl_server main.cpp)
target_include_directories(bl_server PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/..
)
target_link_libraries(bl_server PRIVATE 
    blweb_backend 
    blweb_utils 
    algorithm_lib
    blkmapi  # 添加BLKM API库链接，解决jfkm命名空间的未定义引用
    ${ATOMIC_LIBRARY}
)


# 打印配置信息
message(STATUS "CMAKE_BUILD_TYPE: ${CMAKE_BUILD_TYPE}")
message(STATUS "CMAKE_CXX_FLAGS: ${CMAKE_CXX_FLAGS}")
message(STATUS "CMAKE_CXX_FLAGS_${CMAKE_BUILD_TYPE}: ${CMAKE_CXX_FLAGS_${CMAKE_BUILD_TYPE}}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
if(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm")
    message(STATUS "Build for ARM architecture")
else()
    message(STATUS "Build for ${CMAKE_SYSTEM_PROCESSOR} architecture")
endif()
message(STATUS "Using POCO C++ Libraries from ${POCO_ROOT_DIR}")

# 安装规则
install(TARGETS bl_server
    RUNTIME DESTINATION bin
)

# 允许安装开发头文件（可选）
option(INSTALL_HEADERS "Install header files" OFF)
if(INSTALL_HEADERS)
    install(FILES 
        ../utils/logger.h
        ../utils/gmodels.h
        ../utils/system_stats.h
        blsql/mysql_db.h
        blserver/poco_websocket_server.h
        blserver/fov_service.h
        blserver/register_service.h
        blserver/aim_service.h
        blserver/login_service.h
        blserver/update_models.h
        ../ThreadManager/web_server.h
        blsql/fovconfgidb.h
        blsql/registerdb.h
        blsql/userinfodb.h
        DESTINATION include/blweb
    )
endif() 