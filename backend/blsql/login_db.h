#pragma once

#include <string>
#include <map>
#include <vector>
#include <Poco/JSON/Object.h>

namespace blweb {
namespace db {

/**
 * @class LoginDB
 * @brief 用户登录相关数据库操作类
 * 
 * 负责处理用户登录相关的数据库操作，包括更新登录时间、修改登录信息等
 */
class LoginDB {
public:
    // 定义行数据类型
    using Row = std::map<std::string, std::string>;
    using Rows = std::vector<Row>;
    
    /**
     * @brief 获取LoginDB单例实例
     * @return LoginDB& 单例引用
     */
    static LoginDB& getInstance();
    
    /**
     * @brief 构造函数
     */
    LoginDB();
    
    /**
     * @brief 析构函数
     */
    ~LoginDB();
    
    /**
     * @brief 更新用户登录信息
     * @param username 用户名
     * @param password 密码（可选，为空则不更新）
     * @param token 令牌（可选，为空则不更新）
     * @return bool 成功返回true，失败返回false
     */
    bool updateLoginInfo(const std::string& username, 
                         const std::string& password = "", 
                         const std::string& token = "");
    
    /**
     * @brief 更新用户最后登录时间
     * @param username 用户名
     * @return bool 成功返回true，失败返回false
     */
    bool updateLastLoginTime(const std::string& username);
    
    /**
     * @brief 验证用户登录凭证
     * @param username 用户名
     * @param password 密码
     * @return bool 验证成功返回true，失败返回false
     */
    bool verifyLogin(const std::string& username, const std::string& password);
    
    /**
     * @brief 获取用户登录信息
     * @param username 用户名
     * @return Row 包含用户登录信息的行数据，如果不存在则返回空
     */
    Row getLoginInfo(const std::string& username);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
    // 执行SQL查询
    Rows executeQuery(const std::string& sql);
    
    // 执行带参数的SQL查询
    Rows executeQueryParams(const std::string& sql, const std::vector<std::string>& params);
    
    // 执行SQL更新操作
    bool executeUpdate(const std::string& sql);
    
    // 执行带参数的SQL更新操作
    bool executeUpdateParams(const std::string& sql, const std::vector<std::string>& params);
    
private:
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace db
} // namespace blweb 