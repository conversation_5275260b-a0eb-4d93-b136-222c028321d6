#include "userinfodb.h"
#include "../../utils/logger.h"
#include "mysql_db.h"
#include <Poco/Data/RecordSet.h>
#include <Poco/Data/Statement.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <Poco/JSON/Object.h>
#include <Poco/JSON/Array.h>

namespace blweb {
namespace db {

// 初始化静态成员
std::unique_ptr<UserInfoDB> UserInfoDB::s_instance = nullptr;
std::mutex UserInfoDB::s_mutex;

// 获取单例实例
UserInfoDB& UserInfoDB::getInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (!s_instance) {
        s_instance.reset(new UserInfoDB());
    }
    return *s_instance;
}

// 构造函数
UserInfoDB::UserInfoDB() : m_connected(false) {
    LOG_INFO("UserInfoDB已初始化");
}

// 析构函数
UserInfoDB::~UserInfoDB() {
    close();
    LOG_INFO("UserInfoDB已销毁");
}

// 初始化数据库连接
bool UserInfoDB::init() {
    try {
        // 使用MySQLDB的连接
        m_connected = blweb::MySQLDB::getInstance().isConnected();
        return m_connected;
    }
    catch (const std::exception& e) {
        m_lastError = e.what();
        LOG_ERROR("初始化UserInfoDB失败: {}", m_lastError);
        return false;
    }
}

// 关闭数据库连接
void UserInfoDB::close() {
    m_connected = false;
}

// 执行SQL查询
UserInfoDB::Rows UserInfoDB::executeQuery(const std::string& sql) {
    Rows results;
    auto& db = MySQLDB::getInstance();
    
    if (!db.isConnected() || !db.getSession()) {
        m_lastError = "数据库未连接";
        LOG_ERROR("执行查询失败: 数据库未连接");
        return results;
    }
    
    try {
        LOG_DEBUG("执行SQL查询: {}", sql);
        
        // 创建会话和语句
        Poco::Data::Statement statement(*db.getSession());
        statement << sql;
        statement.execute();
        
        // 获取结果集
        Poco::Data::RecordSet recordSet(statement);
        
        // 如果有结果
        if (recordSet.rowCount() > 0) {
            // 获取列名称
            std::vector<std::string> columns;
            for (std::size_t col = 0; col < recordSet.columnCount(); ++col) {
                columns.push_back(recordSet.columnName(col));
            }
            
            // 提取每一行
            bool more = recordSet.moveFirst();
            while (more) {
                Row row;
                for (std::size_t col = 0; col < columns.size(); ++col) {
                    try {
                        // 检查值是否为空
                        if (recordSet[col].isEmpty()) {
                            row[columns[col]] = "NULL";
                        } else {
                            row[columns[col]] = recordSet.value(col).convert<std::string>();
                        }
                    } catch (const Poco::Exception& ex) {
                        LOG_WARNING("列 {} 的值转换失败: {}", columns[col], ex.displayText());
                        row[columns[col]] = "";  // 转换失败时使用空字符串
                    } catch (...) {
                        LOG_WARNING("列 {} 的值转换失败: 未知错误", columns[col]);
                        row[columns[col]] = "";  // 转换失败时使用空字符串
                    }
                }
                results.push_back(row);
                more = recordSet.moveNext();
            }
        }
        
        LOG_DEBUG("查询返回 {} 行", results.size());
        return results;
    }
    catch (const Poco::Exception& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("执行查询时发生错误: {}", m_lastError);
    }
    catch (const std::exception& ex) {
        m_lastError = ex.what();
        LOG_ERROR("执行查询时发生标准异常: {}", m_lastError);
    }
    
    return results;
}

// 获取用户所有配置
json UserInfoDB::getAllUserConfig(const std::string& username) {
    LOG_INFO("获取用户 '{}' 的所有配置", username);
    
    // 创建JSON对象
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    result->set("userInfo", new Poco::JSON::Object());
    result->set("homeConfig", new Poco::JSON::Object());
    result->set("functionConfig", new Poco::JSON::Array());
    result->set("pidConfig", new Poco::JSON::Array());
    result->set("aimConfig", new Poco::JSON::Array());
    result->set("fireConfig", new Poco::JSON::Array());
    result->set("fovConfig", new Poco::JSON::Array());
    result->set("dataCollection", new Poco::JSON::Array());
    
    try {
        // 用户基本信息 - admin_user表
        std::string userSql = "SELECT * FROM admin_user WHERE username = '" + username + "' LIMIT 1";
        auto userRows = executeQuery(userSql);
        if (!userRows.empty()) {
            Poco::JSON::Object::Ptr userInfo = new Poco::JSON::Object();
            for (const auto& pair : userRows[0]) {
                if (pair.first != "password" && pair.first != "token") { // 排除敏感信息
                    userInfo->set(pair.first, pair.second);
                }
            }
            result->set("userInfo", userInfo);
        } else {
            LOG_WARNING("未找到用户 '{}' 的基本信息", username);
            return result; // 如果用户不存在，提前返回
        }
        
        // 首页配置 - home_configs表 (一对一关系)
        std::string homeSql = "SELECT * FROM home_configs WHERE username = '" + username + "' LIMIT 1";
        auto homeRows = executeQuery(homeSql);
        if (!homeRows.empty()) {
            Poco::JSON::Object::Ptr homeConfig = new Poco::JSON::Object();
            for (const auto& pair : homeRows[0]) {
                // 转换列名为驼峰命名
                std::string key = pair.first;
                if (key == "game_name") key = "gameName";
                else if (key == "card_key") key = "cardKey";
                else if (key == "created_at") key = "createdAt";
                else if (key == "updated_at") key = "updatedAt";
                
                homeConfig->set(key, pair.second);
            }
            result->set("homeConfig", homeConfig);
        } else {
            LOG_WARNING("未找到用户 '{}' 的首页配置", username);
        }
        
        // 功能配置 - function_configs表 (一对多关系)
        std::string functionSql = "SELECT * FROM function_configs WHERE username = '" + username + "'";
        auto functionRows = executeQuery(functionSql);
        Poco::JSON::Array::Ptr functionConfigs = new Poco::JSON::Array();
        for (const auto& row : functionRows) {
            Poco::JSON::Object::Ptr config = new Poco::JSON::Object();
            for (const auto& pair : row) {
                // 转换列名为驼峰命名
                std::string key = pair.first;
                if (key == "game_name") key = "gameName";
                else if (key == "preset_name") key = "presetName";
                else if (key == "ai_mode") key = "aiMode";
                else if (key == "lock_position") key = "lockPosition";
                else if (key == "trigger_switch") key = "triggerSwitch";
                else if (key == "created_at") key = "createdAt";
                else if (key == "updated_at") key = "updatedAt";
                
                // 对布尔值的处理
                if (key == "triggerSwitch" || key == "enabled") {
                    config->set(key, (pair.second == "1" || pair.second == "true"));
                } else {
                    config->set(key, pair.second);
                }
            }
            functionConfigs->add(config);
        }
        result->set("functionConfig", functionConfigs);
        
        // PID配置 - pid_configs表 (一对多关系，按游戏分组)
        std::string pidSql = "SELECT * FROM pid_configs WHERE username = '" + username + "'";
        auto pidRows = executeQuery(pidSql);
        Poco::JSON::Array::Ptr pidConfigs = new Poco::JSON::Array();
        for (const auto& row : pidRows) {
            Poco::JSON::Object::Ptr config = new Poco::JSON::Object();
            for (const auto& pair : row) {
                // 转换列名为驼峰命名 - 使用新的API字段名
                std::string key = pair.first;
                if (key == "game_name") key = "gameName";
                else if (key == "near_move_factor") key = "nearMoveFactor";
                else if (key == "near_stabilizer") key = "nearStabilizer";
                else if (key == "near_response_rate") key = "nearResponseRate";
                else if (key == "near_assist_zone") key = "nearAssistZone";
                else if (key == "near_response_delay") key = "nearResponseDelay";
                else if (key == "near_max_adjustment") key = "nearMaxAdjustment";
                else if (key == "far_factor") key = "farFactor";
                else if (key == "y_axis_factor") key = "yAxisFactor";
                else if (key == "pid_random_factor") key = "pidRandomFactor";
                else if (key == "created_at") key = "createdAt";
                else if (key == "updated_at") key = "updatedAt";
                
                // 对数值型数据的处理 - 使用新的字段名
                if (key == "nearMoveFactor" || key == "nearStabilizer" || key == "nearResponseRate" || 
                    key == "nearAssistZone" || key == "nearResponseDelay" || key == "nearMaxAdjustment" ||
                    key == "farFactor" || key == "yAxisFactor" || key == "pidRandomFactor") {
                    config->set(key, std::stod(pair.second));
                } else {
                    config->set(key, pair.second);
                }
            }
            pidConfigs->add(config);
        }
        result->set("pidConfig", pidConfigs);
        
        // 瞄准配置 - aim_configs表 (一对多关系，按游戏分组)
        std::string aimSql = "SELECT * FROM aim_configs WHERE username = '" + username + "'";
        auto aimRows = executeQuery(aimSql);
        Poco::JSON::Array::Ptr aimConfigs = new Poco::JSON::Array();
        for (const auto& row : aimRows) {
            Poco::JSON::Object::Ptr config = new Poco::JSON::Object();
            for (const auto& pair : row) {
                // 转换列名为驼峰命名
                std::string key = pair.first;
                if (key == "game_name") key = "gameName";
                else if (key == "aim_range") key = "aimRange";
                else if (key == "track_range") key = "trackRange";
                else if (key == "head_height") key = "headHeight";
                else if (key == "neck_height") key = "neckHeight";
                else if (key == "chest_height") key = "chestHeight";
                else if (key == "head_range_x") key = "headRangeX";
                else if (key == "head_range_y") key = "headRangeY";
                else if (key == "neck_range_x") key = "neckRangeX";
                else if (key == "neck_range_y") key = "neckRangeY";
                else if (key == "chest_range_x") key = "chestRangeX";
                else if (key == "chest_range_y") key = "chestRangeY";
                else if (key == "created_at") key = "createdAt";
                else if (key == "updated_at") key = "updatedAt";
                
                // 对数值型数据的处理
                if (key == "aimRange" || key == "trackRange" || key == "headHeight" || 
                    key == "neckHeight" || key == "chestHeight" || key == "headRangeX" || 
                    key == "headRangeY" || key == "neckRangeX" || key == "neckRangeY" || 
                    key == "chestRangeX" || key == "chestRangeY") {
                    config->set(key, std::stod(pair.second));
                } else {
                    config->set(key, pair.second);
                }
            }
            aimConfigs->add(config);
        }
        result->set("aimConfig", aimConfigs);
        
        // 射击配置 - fire_configs表 (一对多关系，按游戏分组)
        std::string fireSql = "SELECT * FROM fire_configs WHERE username = '" + username + "'";
        auto fireRows = executeQuery(fireSql);
        Poco::JSON::Array::Ptr fireConfigs = new Poco::JSON::Array();
        for (const auto& row : fireRows) {
            Poco::JSON::Object::Ptr config = new Poco::JSON::Object();
            for (const auto& pair : row) {
                // 转换列名为驼峰命名
                std::string key = pair.first;
                if (key == "game_name") key = "gameName";
                else if (key == "rifle_sleep") key = "rifleSleep";
                else if (key == "rifle_interval") key = "rifleInterval";
                else if (key == "pistol_sleep") key = "pistolSleep";
                else if (key == "pistol_interval") key = "pistolInterval";
                else if (key == "sniper_sleep") key = "sniperSleep";
                else if (key == "sniper_interval") key = "sniperInterval";
                else if (key == "created_at") key = "createdAt";
                else if (key == "updated_at") key = "updatedAt";
                
                // 对数值型数据的处理
                if (key == "rifleSleep" || key == "rifleInterval" || key == "pistolSleep" || 
                    key == "pistolInterval" || key == "sniperSleep" || key == "sniperInterval") {
                    config->set(key, std::stoi(pair.second));
                } else {
                    config->set(key, pair.second);
                }
            }
            fireConfigs->add(config);
        }
        result->set("fireConfig", fireConfigs);
        
        // 视野配置 - fov_configs表 (一对多关系，按游戏分组)
        std::string fovSql = "SELECT * FROM fov_configs WHERE username = '" + username + "'";
        auto fovRows = executeQuery(fovSql);
        Poco::JSON::Array::Ptr fovConfigs = new Poco::JSON::Array();
        for (const auto& row : fovRows) {
            Poco::JSON::Object::Ptr config = new Poco::JSON::Object();
            for (const auto& pair : row) {
                // 转换列名为驼峰命名
                std::string key = pair.first;
                if (key == "game_name") key = "gameName";
                else if (key == "fov_time") key = "fovTime";
                else if (key == "created_at") key = "createdAt";
                else if (key == "updated_at") key = "updatedAt";
                
                // 对数值型数据的处理
                if (key == "fov") {
                    config->set(key, std::stod(pair.second));
                } else if (key == "fovTime") {
                    config->set(key, std::stoi(pair.second));
                } else {
                    config->set(key, pair.second);
                }
            }
            fovConfigs->add(config);
        }
        result->set("fovConfig", fovConfigs);
        
        // 数据收集 - data_collections表 (一对多关系)
        std::string dataSql = "SELECT * FROM data_collections WHERE username = '" + username + "'";
        auto dataRows = executeQuery(dataSql);
        Poco::JSON::Array::Ptr dataCollections = new Poco::JSON::Array();
        for (const auto& row : dataRows) {
            Poco::JSON::Object::Ptr config = new Poco::JSON::Object();
            for (const auto& pair : row) {
                // 转换列名为驼峰命名
                std::string key = pair.first;
                if (key == "game_name") key = "gameName";
                else if (key == "created_at") key = "createdAt";
                else if (key == "updated_at") key = "updatedAt";
                
                config->set(key, pair.second);
            }
            dataCollections->add(config);
        }
        result->set("dataCollection", dataCollections);
        
        LOG_INFO("成功获取用户 '{}' 的所有配置", username);
        return result;
    }
    catch (const std::exception& ex) {
        m_lastError = ex.what();
        LOG_ERROR("获取用户配置时发生错误: {}", m_lastError);
        return result;
    }
}

} // namespace db
} // namespace blweb