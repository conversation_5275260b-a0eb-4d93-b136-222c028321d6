#include "function_configs_db.h"
#include "../../utils/logger.h"
#include "mysql_db.h"

#include <Poco/Exception.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <Poco/JSON/Parser.h>

namespace blweb {
namespace db {

// 单例实现
FunctionConfigsDB& FunctionConfigsDB::getInstance() {
    static FunctionConfigsDB instance;
    return instance;
}

// 构造函数
FunctionConfigsDB::FunctionConfigsDB() : m_lastError("") {
    LOG_INFO("FunctionConfigsDB已初始化");
}

// 析构函数
FunctionConfigsDB::~FunctionConfigsDB() {
    LOG_INFO("FunctionConfigsDB已销毁");
}

// 创建用户功能配置
bool FunctionConfigsDB::createFunctionConfig(const std::string& username, const std::string& gameName) {
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    LOG_INFO("为用户 '{}' 创建游戏 '{}' 的功能配置", username, gameName);
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    bool allSuccess = true;
    // 创建四个默认配置，模仿function_modify请求的模式，包含阵营选择、切枪功能和背闪防护
    // 配置1 - PID模式，头部，警方
    std::vector<std::string> configParams1 = {
        username, gameName, "配置1", "PID", "头部", "左键", "0", "0", "0", "1", "警方", timeStr, timeStr
    };
    std::string sql = "INSERT INTO function_configs (username, game_name, preset_name, ai_mode, lock_position, "
                      "hotkey, trigger_switch, weapon_switch, flash_shield, enabled, selected_faction, created_at, updated_at) "
                      "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    LOG_INFO("为用户 '{}' 创建游戏 '{}' 的功能配置1", username, gameName);
    bool result1 = db.executeUpdateParams(sql, configParams1);
    if (!result1) {
        LOG_ERROR("创建功能配置1失败");
        allSuccess = false;
    }
    
    // 配置2 - FOV模式，胸部，匪方
    std::vector<std::string> configParams2 = {
        username, gameName, "配置2", "FOV", "胸部", "右键", "1", "1", "1", "1", "匪方", timeStr, timeStr
    };
    
    LOG_INFO("为用户 '{}' 创建游戏 '{}' 的功能配置2", username, gameName);
    bool result2 = db.executeUpdateParams(sql, configParams2);
    if (!result2) {
        LOG_ERROR("创建功能配置2失败");
        allSuccess = false;
    }
    
    // 配置3 - FOVPID模式，颈部，无
    std::vector<std::string> configParams3 = {
        username, gameName, "配置3", "FOVPID", "颈部", "中键", "0", "0", "0", "0", "无", timeStr, timeStr
    };
    
    LOG_INFO("为用户 '{}' 创建游戏 '{}' 的功能配置3", username, gameName);
    bool result3 = db.executeUpdateParams(sql, configParams3);
    if (!result3) {
        LOG_ERROR("创建功能配置3失败");
        allSuccess = false;
    }
    
    // 配置4 - PID模式，颈部，警方
    std::vector<std::string> configParams4 = {
        username, gameName, "配置4", "PID", "颈部", "右键", "0", "1", "1", "0", "警方", timeStr, timeStr
    };
    
    LOG_INFO("为用户 '{}' 创建游戏 '{}' 的功能配置4", username, gameName);
    bool result4 = db.executeUpdateParams(sql, configParams4);
    if (!result4) {
        LOG_ERROR("创建功能配置4失败");
        allSuccess = false;
    }
    
    return allSuccess;
}

// 创建或更新功能配置
bool FunctionConfigsDB::updateFunctionConfigs(
    const std::string& username,
    const std::string& gameName,
    const Poco::JSON::Array::Ptr& configs
) {
    LOG_INFO("数据库批量更新功能配置 - 用户: '{}', 游戏: '{}', 配置数量: {}", 
             username, gameName, configs ? configs->size() : 0);
    
    if (!configs || configs->size() == 0) {
        m_lastError = "配置数组为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    // 逐个更新配置，不使用事务
    bool allSuccess = true;
    try {
        LOG_INFO("开始逐个处理游戏 '{}' 的{}个配置", gameName, configs->size());
        
        // 处理每个配置
        for (size_t i = 0; i < configs->size(); i++) {
            Poco::JSON::Object::Ptr configObj = configs->getObject(i);
            if (!configObj) {
                LOG_WARNING("游戏 '{}' 配置对象为空，跳过 (索引: {})", gameName, i);
                continue;
            }
            
            std::string presetName = configObj->optValue("presetName", std::string("未知"));
            LOG_DEBUG("处理游戏 '{}' 配置 #{} - 预设名: '{}'", gameName, i+1, presetName);
            
            if (!updateSingleConfig(username, gameName, configObj)) {
                LOG_ERROR("更新游戏 '{}' 配置 '{}' 失败", gameName, presetName);
                allSuccess = false;
                // 继续处理其他配置，而不是立即中断
            }
        }
        
        if (allSuccess) {
            LOG_INFO("游戏 '{}' 批量功能配置更新成功 - 更新了{}个配置", gameName, configs->size());
        } else {
            LOG_WARNING("游戏 '{}' 部分配置更新失败", gameName);
        }
        
        return allSuccess;
    } catch (const std::exception& ex) {
        m_lastError = std::string("批量更新游戏 '") + gameName + "' 功能配置异常: " + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 更新单个功能配置
bool FunctionConfigsDB::updateSingleConfig(
    const std::string& username,
    const std::string& gameName,
    const Poco::JSON::Object::Ptr& configObject
) {
    try {
        if (!configObject) {
            m_lastError = "配置对象为空";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        // 提取字段
        std::string presetName = configObject->optValue("presetName", std::string(""));
        std::string aiMode = configObject->optValue("aiMode", std::string(""));
        std::string lockPosition = configObject->optValue("lockPosition", std::string(""));
        std::string hotkey = configObject->optValue("hotkey", std::string(""));
        std::string selectedFaction = configObject->optValue("selectedFaction", std::string("无"));
        std::string triggerSwitch = getBooleanFieldAsString(configObject, "triggerSwitch", false);
        std::string weaponSwitch = getBooleanFieldAsString(configObject, "weaponSwitch", false);
        std::string flashShield = getBooleanFieldAsString(configObject, "flashShield", false);
        std::string enabled = getBooleanFieldAsString(configObject, "enabled", true);
        
        if (presetName.empty()) {
            m_lastError = "预设名称不能为空";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("处理游戏 '{}' 的配置 '{}' - AI模式: '{}', 锁定位置: '{}', 热键: '{}', 阵营: '{}', 切枪: '{}', 背闪: '{}'", 
                 gameName, presetName, aiMode, lockPosition, hotkey, selectedFaction, weaponSwitch, flashShield);
        
        // 当前时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 检查配置是否已存在 - 使用游戏名称进行精确查询
        std::vector<std::string> queryParams = {username, gameName, presetName};
        std::string querySql = "SELECT * FROM function_configs WHERE username = ? AND game_name = ? AND preset_name = ?";
        
        LOG_DEBUG("检查游戏 '{}' 配置 '{}' 是否存在: {}", gameName, presetName, querySql);
        
        Rows existingResults = blweb::MySQLDB::getInstance().executeQueryParams(querySql, queryParams);
        bool configExists = !existingResults.empty();
        
        if (!configExists) {
            // 创建新配置
            LOG_INFO("为游戏 '{}' 创建新功能配置: '{}'", gameName, presetName);
            
            std::vector<std::string> params = {
                username, 
                gameName, 
                presetName,
                aiMode,
                lockPosition,
                hotkey,
                triggerSwitch,
                weaponSwitch,
                flashShield,
                enabled,
                selectedFaction,
                timeStr,
                timeStr
            };
            
            std::string sql = "INSERT INTO function_configs (username, game_name, preset_name, ai_mode, lock_position, "
                             "hotkey, trigger_switch, weapon_switch, flash_shield, enabled, selected_faction, created_at, updated_at) "
                             "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            bool result = blweb::MySQLDB::getInstance().executeUpdateParams(sql, params);
            if (!result) {
                m_lastError = "创建游戏 '" + gameName + "' 功能配置 '" + presetName + "' 失败: " + blweb::MySQLDB::getInstance().getLastError();
                LOG_ERROR("{}", m_lastError);
                return false;
            }
            
            LOG_INFO("游戏 '{}' 新配置 '{}' 创建成功", gameName, presetName);
        } else {
            // 更新现有配置
            LOG_INFO("更新游戏 '{}' 现有功能配置: '{}'", gameName, presetName);
            
            std::vector<std::string> params = {
                aiMode,
                lockPosition,
                hotkey,
                triggerSwitch,
                weaponSwitch,
                flashShield,
                enabled,
                selectedFaction,
                timeStr,
                username,
                gameName,
                presetName
            };
            
            std::string sql = "UPDATE function_configs SET ai_mode = ?, lock_position = ?, hotkey = ?, "
                             "trigger_switch = ?, weapon_switch = ?, flash_shield = ?, enabled = ?, selected_faction = ?, updated_at = ? "
                             "WHERE username = ? AND game_name = ? AND preset_name = ?";
            
            LOG_DEBUG("执行游戏 '{}' 配置更新SQL: {}", gameName, sql);
            
            bool result = blweb::MySQLDB::getInstance().executeUpdateParams(sql, params);
            if (!result) {
                m_lastError = "更新游戏 '" + gameName + "' 功能配置 '" + presetName + "' 失败: " + blweb::MySQLDB::getInstance().getLastError();
                LOG_ERROR("{}", m_lastError);
                return false;
            }
            
            LOG_INFO("游戏 '{}' 配置 '{}' 更新成功", gameName, presetName);
        }
        
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新游戏 '") + gameName + "' 单个功能配置异常: " + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取布尔字段作为字符串值
std::string FunctionConfigsDB::getBooleanFieldAsString(
    const Poco::JSON::Object::Ptr& jsonObj,
    const std::string& fieldName,
    bool defaultValue
) {
    bool value = defaultValue;
    
    if (jsonObj->has(fieldName)) {
        try {
            // 获取动态变量
            Poco::Dynamic::Var var = jsonObj->get(fieldName);
            
            // 根据变量类型处理
            if (var.isBoolean()) {
                value = var.convert<bool>();
            } 
            else if (var.isInteger()) {
                value = (var.convert<int>() != 0);
            }
            else if (var.isString()) {
                std::string strValue = var.convert<std::string>();
                value = (strValue == "true" || strValue == "1" || strValue == "yes");
            }
        } catch (const std::exception& ex) {
            LOG_WARNING("解析布尔字段'{}'出错: {}, 使用默认值: {}", fieldName, ex.what(), defaultValue);
            value = defaultValue;
        }
    }
    
    return value ? "1" : "0";
}

// 获取特定用户和游戏的功能配置
FunctionConfigsDB::Rows FunctionConfigsDB::getFunctionConfigs(const std::string& username, const std::string& gameName) {
    LOG_INFO("数据库查询功能配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    Rows emptyRows;
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return emptyRows;
        }
    }
    
    try {
        std::vector<std::string> params = {username, gameName};
        std::string sql = "SELECT * FROM function_configs WHERE username = ? AND game_name = ? ORDER BY preset_name";
        
        LOG_INFO("执行SQL查询: {} - 参数: username='{}', game_name='{}'", sql, username, gameName);
        
        Rows results = db.executeQueryParams(sql, params);
        LOG_INFO("数据库查询完成 - 游戏 '{}' 找到{}个功能配置记录", gameName, results.size());
        
        // 打印查询到的功能配置详细内容
        for (size_t i = 0; i < results.size(); i++) {
            const Row& row = results[i];
            LOG_INFO("游戏 '{}' 数据库记录 #{} 详情:", gameName, i + 1);
            for (const auto& pair : row) {
                LOG_INFO("    {} = '{}'", pair.first, pair.second);
            }
        }
        
        return results;
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取功能配置异常: ") + ex.what();
        LOG_ERROR("数据库查询游戏 '{}' 配置失败: {}", gameName, m_lastError);
        return emptyRows;
    }
}

// 获取最后一次错误信息
std::string FunctionConfigsDB::getLastError() const {
    return m_lastError;
}

} // namespace db
} // namespace blweb 