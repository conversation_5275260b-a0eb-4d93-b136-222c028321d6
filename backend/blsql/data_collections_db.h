#ifndef DATA_COLLECTIONS_DB_H
#define DATA_COLLECTIONS_DB_H

#include "mysql_db.h"
#include <string>
#include <vector>
#include <map>

namespace blweb {
namespace db {

/**
 * @brief 数据采集配置数据库操作类
 */
class DataCollectionsDB : public blweb::MySQLDB {
public:
    /**
     * @brief 获取单例实例
     * @return DataCollectionsDB& 单例实例引用
     */
    static DataCollectionsDB& getInstance();

    /**
     * @brief 获取用户在特定游戏下的数据采集配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return std::map<std::string, std::string> 数据采集配置映射表，如果未找到则返回空映射
     */
    std::map<std::string, std::string> getDataCollection(const std::string& username, const std::string& gameName);

    /**
     * @brief 更新或创建用户在特定游戏下的数据采集配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param isEnabled 是否启用数据收集
     * @param mapHotkey 地图热键
     * @param targetHotkey 目标热键
     * @param teamSide 阵营选择
     * @param collectionName 数据收集名称
     * @return bool 操作是否成功
     */
    bool updateDataCollection(
        const std::string& username,
        const std::string& gameName,
        bool isEnabled,
        const std::string& mapHotkey,
        const std::string& targetHotkey,
        const std::string& teamSide,
        const std::string& collectionName
    );

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    DataCollectionsDB();
    
    /**
     * @brief 私有方法：简化版的更新数据采集配置（只更新时间戳）
     * @param username 用户名
     * @param gameName 游戏名称
     * @return bool 操作是否成功
     */
    bool updateDataCollection(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 私有方法：创建用户在特定游戏下的数据采集配置 (API内部实现，非公开方法)
     * @param username 用户名
     * @param gameName 游戏名称
     * @param isEnabled 是否启用数据收集
     * @param mapHotkey 地图热键
     * @param targetHotkey 目标热键
     * @param teamSide 阵营选择
     * @param collectionName 数据收集名称
     * @return bool 操作是否成功
     */
    bool createDataCollection(
        const std::string& username,
        const std::string& gameName,
        bool isEnabled,
        const std::string& mapHotkey,
        const std::string& targetHotkey,
        const std::string& teamSide,
        const std::string& collectionName
    );
};

} // namespace db
} // namespace blweb

#endif // DATA_COLLECTIONS_DB_H 