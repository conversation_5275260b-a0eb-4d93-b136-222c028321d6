#ifndef AIM_CONFIGS_DB_H
#define AIM_CONFIGS_DB_H

#include <string>
#include <map>
#include <vector>

namespace Poco {
namespace Data {
class Session;
}
}

namespace blweb {
namespace sqlserver {

/**
 * @class AimConfigsDB
 * @brief 瞄准配置数据库操作类
 * 
 * 这个类负责处理与瞄准配置相关的数据库操作，包括更新和获取瞄准参数
 */
class AimConfigsDB {
public:
    // 获取单例实例
    static AimConfigsDB& getInstance();

    // 创建用户瞄准配置
    bool createAimConfig(const std::string& username, const std::string& gameName);

    // 更新用户瞄准配置
    bool updateAimConfig(const std::string& username, const std::string& gameName, 
                         const std::map<std::string, std::string>& aimConfigMap);

    // 获取用户瞄准配置
    std::map<std::string, std::string> getAimConfig(const std::string& username, const std::string& gameName);

    // 获取最后一次错误信息
    std::string getLastError() const;

private:
    // 构造函数和析构函数私有化，实现单例模式
    AimConfigsDB();
    ~AimConfigsDB();

    // 禁止拷贝构造和赋值
    AimConfigsDB(const AimConfigsDB&) = delete;
    AimConfigsDB& operator=(const AimConfigsDB&) = delete;

    // 最后一次错误信息
    std::string m_lastError;
};

}  // namespace sqlserver
}  // namespace blweb

#endif  // AIM_CONFIGS_DB_H 