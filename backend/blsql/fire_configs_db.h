#ifndef FIRE_CONFIGS_DB_H
#define FIRE_CONFIGS_DB_H

#include "mysql_db.h"
#include <string>
#include <vector>
#include <map>

namespace blweb {
namespace db {

/**
 * @brief 射击配置数据库操作类
 */
class FireConfigsDB : public blweb::MySQLDB {
public:
    /**
     * @brief 获取单例实例
     * @return FireConfigsDB& 单例实例引用
     */
    static FireConfigsDB& getInstance();

    /**
     * @brief 获取用户在特定游戏下的射击配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return std::map<std::string, std::string> 射击配置映射表，如果未找到则返回空映射
     */
    std::map<std::string, std::string> getFireConfig(const std::string& username, const std::string& gameName);

    /**
     * @brief 更新用户在特定游戏下的射击配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param rifleSleep 步枪睡眠时间
     * @param rifleInterval 步枪间隔时间
     * @param pistolSleep 手枪睡眠时间
     * @param pistolInterval 手枪间隔时间
     * @param sniperSleep 狙击枪睡眠时间
     * @param sniperInterval 狙击枪间隔时间
     * @return bool 操作是否成功
     */
    bool updateFireConfig(
        const std::string& username,
        const std::string& gameName,
        int rifleSleep,
        int rifleInterval,
        int pistolSleep,
        int pistolInterval,
        int sniperSleep,
        int sniperInterval
    );

    /**
     * @brief 创建用户在特定游戏下的射击配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param rifleSleep 步枪睡眠时间
     * @param rifleInterval 步枪间隔时间
     * @param pistolSleep 手枪睡眠时间
     * @param pistolInterval 手枪间隔时间
     * @param sniperSleep 狙击枪睡眠时间
     * @param sniperInterval 狙击枪间隔时间
     * @return bool 操作是否成功
     */
    bool createFireConfig(
        const std::string& username,
        const std::string& gameName,
        int rifleSleep,
        int rifleInterval,
        int pistolSleep,
        int pistolInterval,
        int sniperSleep,
        int sniperInterval
    );

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    FireConfigsDB();
};

} // namespace db
} // namespace blweb

#endif // FIRE_CONFIGS_DB_H 