#pragma once

#include <string>
#include <vector>
#include <map>
#include "Poco/JSON/Object.h"
#include "Poco/JSON/Array.h"

namespace blweb {
namespace db {

using json = Poco::JSON::Object::Ptr;

/**
 * @class FovConfigDB
 * @brief FOV视野范围配置数据库处理类
 * 
 * 该类负责处理与FOV视野范围配置相关的数据库操作，包括获取、更新FOV配置信息。
 * 遵循单例模式设计，确保全局仅有一个实例。
 */
class FovConfigDB {
public:
    // 数据库表行和行集合的类型定义
    using Row = std::map<std::string, std::string>;
    using Rows = std::vector<Row>;
    
    /**
     * @brief 获取FovConfigDB单例实例
     * @return FovConfigDB& 单例引用
     */
    static FovConfigDB& getInstance();
    
    /**
     * @brief 构造函数
     */
    FovConfigDB();
    
    /**
     * @brief 析构函数
     */
    ~FovConfigDB();
    
    /**
     * @brief 初始化数据库连接
     * @return bool 成功返回true，否则返回false
     */
    bool init();
    
    /**
     * @brief 创建用户视野配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return bool 创建成功返回true，失败返回false
     */
    bool createFovConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 获取用户在特定游戏下的FOV配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Row 包含FOV配置的行数据，如果不存在则返回空
     */
    Row getFovConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 更新或创建用户的FOV配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param fov 视野范围
     * @param fovTime 视野时间
     * @return bool 成功返回true，否则返回false
     */
    bool updateFovConfig(
        const std::string& username,
        const std::string& gameName,
        float fov,
        int fovTime
    );
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
    // 执行SQL查询
    Rows executeQuery(const std::string& sql);
    
    // 执行带参数的SQL查询
    Rows executeQueryParams(const std::string& sql, const std::vector<std::string>& params);
    
    // 执行SQL更新操作
    bool executeUpdate(const std::string& sql);
    
    // 执行带参数的SQL更新操作
    bool executeUpdateParams(const std::string& sql, const std::vector<std::string>& params);
    
    // 转义SQL字符串，防止SQL注入
    std::string escapeString(const std::string& str);
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace db
} // namespace blweb