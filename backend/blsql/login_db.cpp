#include "login_db.h"
#include "../../utils/logger.h"
#include "mysql_db.h"

#include <Poco/Exception.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <Poco/JSON/Parser.h>
#include <Poco/JSON/Object.h>
#include <Poco/Dynamic/Var.h>

namespace blweb {
namespace db {

// 单例实现
LoginDB& LoginDB::getInstance() {
    static LoginDB instance;
    return instance;
}

// 构造函数
LoginDB::LoginDB() : m_lastError("") {
    LOG_INFO("LoginDB已初始化");
}

// 析构函数
LoginDB::~LoginDB() {
    LOG_INFO("LoginDB已销毁");
}

// 执行SQL查询
LoginDB::Rows LoginDB::executeQuery(const std::string& sql) {
    return MySQLDB::getInstance().executeQuery(sql);
}

// 执行带参数的SQL查询
LoginDB::Rows LoginDB::executeQueryParams(const std::string& sql, const std::vector<std::string>& params) {
    return MySQLDB::getInstance().executeQueryParams(sql, params);
}

// 执行SQL更新操作
bool LoginDB::executeUpdate(const std::string& sql) {
    return MySQLDB::getInstance().executeUpdate(sql);
}

// 执行带参数的SQL更新操作
bool LoginDB::executeUpdateParams(const std::string& sql, const std::vector<std::string>& params) {
    return MySQLDB::getInstance().executeUpdateParams(sql, params);
}

// 更新用户登录信息
bool LoginDB::updateLoginInfo(const std::string& username, 
                              const std::string& password, 
                              const std::string& token) {
    LOG_INFO("更新用户 '{}' 的登录信息", username);
    
    if (username.empty()) {
        m_lastError = "用户名不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 检查用户是否存在
        std::vector<std::string> checkParams = {username};
        std::string checkSql = "SELECT COUNT(*) as count FROM admin_user WHERE username = ?";
        Rows results = executeQueryParams(checkSql, checkParams);
        
        if (results.empty() || results[0]["count"] == "0") {
            m_lastError = "用户不存在";
            LOG_ERROR("更新登录信息失败: 用户 '{}' 不存在", username);
            return false;
        }
        
        // 构建UPDATE语句
        std::string updateSql = "UPDATE admin_user SET updated_at = ?";
        std::vector<std::string> params = {timeStr};
        
        // 如果提供了密码，则更新密码
        if (!password.empty()) {
            updateSql += ", password = ?";
            params.push_back(password);
        }
        
        // 如果提供了token，则更新token
        if (!token.empty()) {
            updateSql += ", token = ?";
            params.push_back(token);
        }
        
        // 添加WHERE条件
        updateSql += " WHERE username = ?";
        params.push_back(username);
        
        // 执行更新
        bool success = executeUpdateParams(updateSql, params);
        
        if (!success) {
            m_lastError = "更新登录信息失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("成功更新用户 '{}' 的登录信息", username);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新登录信息异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 更新用户最后登录时间
bool LoginDB::updateLastLoginTime(const std::string& username) {
    LOG_INFO("更新用户 '{}' 的最后登录时间", username);
    
    if (username.empty()) {
        m_lastError = "用户名不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为最后登录时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 执行更新
        std::vector<std::string> params = {timeStr, username};
        std::string sql = "UPDATE admin_user SET last_login = ? WHERE username = ?";
        
        bool success = executeUpdateParams(sql, params);
        
        if (!success) {
            m_lastError = "更新最后登录时间失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("成功更新用户 '{}' 的最后登录时间", username);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新最后登录时间异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 验证用户登录凭证
bool LoginDB::verifyLogin(const std::string& username, const std::string& password) {
    LOG_INFO("验证用户 '{}' 的登录凭证", username);
    
    if (username.empty() || password.empty()) {
        m_lastError = "用户名和密码不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 查询用户记录
        std::vector<std::string> params = {username, password};
        std::string sql = "SELECT * FROM admin_user WHERE username = ? AND password = ?";
        
        Rows results = executeQueryParams(sql, params);
        
        if (results.empty()) {
            m_lastError = "用户名或密码不正确";
            LOG_WARNING("验证登录失败: 用户名或密码不正确");
            return false;
        }
        
        LOG_INFO("用户 '{}' 验证登录成功", username);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("验证登录异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取用户登录信息
LoginDB::Row LoginDB::getLoginInfo(const std::string& username) {
    LOG_INFO("获取用户 '{}' 的登录信息", username);
    
    Row emptyRow;
    
    if (username.empty()) {
        m_lastError = "用户名不能为空";
        LOG_ERROR("{}", m_lastError);
        return emptyRow;
    }
    
    // 检查数据库连接状态
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return emptyRow;
        }
    }
    
    try {
        // 查询用户记录，包含isPro字段
        std::vector<std::string> params = {username};
        std::string sql = "SELECT username, token, created_at, updated_at, last_login, is_pro FROM admin_user WHERE username = ?";
        
        Rows results = executeQueryParams(sql, params);
        
        if (results.empty()) {
            m_lastError = "用户不存在";
            LOG_WARNING("获取登录信息失败: 用户 '{}' 不存在", username);
            return emptyRow;
        }
        
        LOG_INFO("成功获取用户 '{}' 的登录信息", username);
        return results[0];
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取登录信息异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return emptyRow;
    }
}

// 获取最后一次错误信息
std::string LoginDB::getLastError() const {
    return m_lastError;
}

} // namespace db
} // namespace blweb 