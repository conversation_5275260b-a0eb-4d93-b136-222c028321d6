#pragma once

#include <string>
#include <map>
#include <vector>
#include <Poco/JSON/Object.h>
#include <Poco/JSON/Array.h>
#include <Poco/Data/Session.h>
#include <Poco/Data/RecordSet.h>
#include "mysql_db.h"
#include "userinfodb.h"

namespace blweb {
namespace db {

/**
 * @brief 用户注册相关数据库操作类
 * 
 * 提供用户注册、验证等功能，直接使用Poco库进行数据库操作
 */
class RegisterDB {
public:
    // 定义行数据类型
    using Row = std::map<std::string, std::string>;
    using Rows = std::vector<Row>;
    
    // 单例模式
    static RegisterDB& getInstance();
    
    // 构造函数
    RegisterDB();
    
    // 析构函数
    ~RegisterDB();
    
    // 初始化
    bool init();
    
    /**
     * @brief 检查用户名是否已存在
     * @param username 需要检查的用户名
     * @return bool 存在返回true，不存在返回false
     */
    bool isUserExists(const std::string& username);
    
    /**
     * @brief 注册新用户
     * @param username 用户名
     * @param password 密码
     * @param defaultGame 默认游戏名称
     * @param isPro 是否为Pro版用户
     * @return bool 注册成功返回true，失败返回false
     */
    bool registerUser(const std::string& username, const std::string& password, const std::string& defaultGame = "csgo2", bool isPro = false);
    
    /**
     * @brief 创建用户首页配置
     * @param username 用户名
     * @param defaultGame 默认游戏名称
     * @return bool 创建成功返回true，失败返回false
     */
    bool createHomeConfig(const std::string& username, const std::string& defaultGame);
    
    /**
     * @brief 创建用户射击配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return bool 创建成功返回true，失败返回false
     */
    bool createFireConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 创建用户的多游戏初始配置
     * @param username 用户名
     * @param gameNames 游戏名称列表
     * @return bool 创建成功返回true，失败返回false
     */
    bool createInitialConfigs(const std::string& username, const std::vector<std::string>& gameNames);
    
    /**
     * @brief 创建用户数据收集配置（默认关闭）
     * @param username 用户名
     * @param gameName 游戏名称
     * @return bool 创建成功返回true，失败返回false
     */
    bool createDataCollectionConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 处理用户注册请求
     * @param message 包含注册信息的JSON消息
     * @return Poco::JSON::Object::Ptr 包含处理结果的JSON响应
     */
    Poco::JSON::Object::Ptr handleRegisterRequest(const Poco::JSON::Object::Ptr& message);
    
    /**
     * @brief 获取用户的所有配置信息
     * @param username 用户名
     * @return Poco::JSON::Object::Ptr 包含用户所有配置的JSON对象
     */
    Poco::JSON::Object::Ptr getUserAllConfigs(const std::string& username);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
    // 执行SQL查询
    Rows executeQuery(const std::string& sql);
    
    // 执行SQL查询并绑定参数
    Rows executeQueryParams(const std::string& sql, const std::vector<std::string>& params);
    
    // 执行SQL更新操作
    bool executeUpdate(const std::string& sql);
    
    // 执行SQL更新操作并绑定参数
    bool executeUpdateParams(const std::string& sql, const std::vector<std::string>& params);
    
    // 转义SQL字符串
    std::string escapeString(const std::string& str);
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace db
} // namespace blweb 