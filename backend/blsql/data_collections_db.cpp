#include "data_collections_db.h"
#include "../../utils/logger.h"
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <sstream>

namespace blweb {
namespace db {

// 单例实现
DataCollectionsDB& DataCollectionsDB::getInstance() {
    static DataCollectionsDB instance;
    return instance;
}

// 构造函数
DataCollectionsDB::DataCollectionsDB() : blweb::MySQLDB() {
    LOG_INFO("DataCollectionsDB已初始化");
}

// 获取用户在特定游戏下的数据采集配置
std::map<std::string, std::string> DataCollectionsDB::getDataCollection(const std::string& username, const std::string& gameName) {
    LOG_INFO("获取数据采集配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    std::map<std::string, std::string> config;
    
    if (username.empty() || gameName.empty()) {
        m_lastError = "用户名和游戏名不能为空";
        LOG_ERROR("{}", m_lastError);
        return config;
    }
    
    // 检查数据库连接状态
    if (!isConnected()) {
        if (!init()) {
            m_lastError = "数据库连接失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return config;
        }
    }
    
    try {
        // 查询数据采集配置
        std::vector<std::string> params = {username, gameName};
        std::string sql = "SELECT * FROM data_collections WHERE username = ? AND game_name = ? LIMIT 1";
        
        Rows results = executeQueryParams(sql, params);
        
        if (results.empty()) {
            LOG_WARNING("未找到用户 '{}' 在游戏 '{}' 的数据采集配置", username, gameName);
            return config;
        }
        
        // 返回第一个匹配的结果
        LOG_INFO("成功获取用户 '{}' 在游戏 '{}' 的数据采集配置", username, gameName);
        return results[0];
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取数据采集配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return config;
    }
}

// 私有方法：简单更新用户在特定游戏下的数据采集配置（只更新时间戳）
// 此方法不符合API规范，应该标记为私有，仅供内部使用
// 注意：此方法不应被外部直接调用，所有数据采集配置修改应通过完整的updateDataCollection方法进行
bool DataCollectionsDB::updateDataCollection(const std::string& username, const std::string& gameName) {
    LOG_INFO("更新数据采集配置时间戳 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    if (username.empty() || gameName.empty()) {
        m_lastError = "用户名和游戏名不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    if (!isConnected()) {
        if (!init()) {
            m_lastError = "数据库连接失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 首先检查配置是否存在
        std::vector<std::string> checkParams = {username, gameName};
        std::string checkSql = "SELECT COUNT(*) as count FROM data_collections WHERE username = ? AND game_name = ?";
        
        Rows checkResults = executeQueryParams(checkSql, checkParams);
        int count = std::stoi(checkResults[0]["count"]);
        
        if (count == 0) {
            // 配置不存在时不自动创建，返回false
            m_lastError = "数据收集配置不存在，请先通过UI界面创建配置";
            LOG_WARNING("用户 '{}' 在游戏 '{}' 的数据收集配置不存在，不自动创建", username, gameName);
            return false;
        }
        
        // 如果存在，更新配置（仅更新时间戳）
        std::vector<std::string> params = {
            timeStr,
            username,
            gameName
        };
        
        std::string sql = "UPDATE data_collections SET updated_at = ? WHERE username = ? AND game_name = ?";
        
        bool result = executeUpdateParams(sql, params);
        
        if (!result) {
            m_lastError = "更新数据采集配置失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("成功更新用户 '{}' 在游戏 '{}' 的数据采集配置", username, gameName);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新数据采集配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 完整更新用户在特定游戏下的数据采集配置（包含所有字段）
// 此方法是唯一通过API规范中的data_collection_modify请求执行数据库写操作的方法
bool DataCollectionsDB::updateDataCollection(
    const std::string& username,
    const std::string& gameName,
    bool isEnabled,
    const std::string& mapHotkey,
    const std::string& targetHotkey,
    const std::string& teamSide,
    const std::string& collectionName
) {
    LOG_INFO("完整更新数据采集配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    LOG_INFO("参数: 启用={}, 地图热键={}, 目标热键={}, 阵营={}, 采集名称={}", 
             isEnabled ? "是" : "否", mapHotkey, targetHotkey, teamSide, collectionName);
    
    if (username.empty() || gameName.empty()) {
        m_lastError = "用户名和游戏名不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    if (!isConnected()) {
        if (!init()) {
            m_lastError = "数据库连接失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 首先检查配置是否存在
        std::vector<std::string> checkParams = {username, gameName};
        std::string checkSql = "SELECT COUNT(*) as count FROM data_collections WHERE username = ? AND game_name = ?";
        
        Rows checkResults = executeQueryParams(checkSql, checkParams);
        int count = std::stoi(checkResults[0]["count"]);
        
        if (count == 0) {
            // 如果不存在，创建新配置 (通过私有方法createDataCollection执行INSERT操作)
            return createDataCollection(username, gameName, isEnabled, mapHotkey, targetHotkey, teamSide, collectionName);
        }
        
        // 如果存在，更新配置 (执行UPDATE操作)
        std::vector<std::string> params = {
            isEnabled ? "1" : "0",
            mapHotkey,
            targetHotkey,
            teamSide,
            collectionName,
            timeStr,
            username,
            gameName
        };
        
        std::string sql = "UPDATE data_collections SET is_enabled = ?, map_hotkey = ?, target_hotkey = ?, "
                          "team_side = ?, collection_name = ?, updated_at = ? WHERE username = ? AND game_name = ?";
        
        bool result = executeUpdateParams(sql, params);
        
        if (!result) {
            m_lastError = "更新数据采集配置失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("成功更新用户 '{}' 在游戏 '{}' 的数据采集配置", username, gameName);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新数据采集配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 私有方法：完整创建用户在特定游戏下的数据采集配置（包含所有字段）
// 仅由公开的updateDataCollection方法内部调用，不应直接从外部调用
// 此方法执行INSERT操作，符合API规范中data_collection_modify的数据库写入逻辑
bool DataCollectionsDB::createDataCollection(
    const std::string& username,
    const std::string& gameName,
    bool isEnabled,
    const std::string& mapHotkey,
    const std::string& targetHotkey,
    const std::string& teamSide,
    const std::string& collectionName
) {
    LOG_INFO("创建完整数据采集配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    LOG_INFO("参数: 启用={}, 地图热键={}, 目标热键={}, 阵营={}, 采集名称={}", 
             isEnabled ? "是" : "否", mapHotkey, targetHotkey, teamSide, collectionName);
    
    if (username.empty() || gameName.empty()) {
        m_lastError = "用户名和游戏名不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    if (!isConnected()) {
        if (!init()) {
            m_lastError = "数据库连接失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为创建时间和更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        std::vector<std::string> params = {
            username,
            gameName,
            isEnabled ? "1" : "0",
            mapHotkey,
            targetHotkey,
            teamSide,
            collectionName,
            timeStr,
            timeStr
        };
        
        std::string sql = "INSERT INTO data_collections (username, game_name, is_enabled, map_hotkey, "
                          "target_hotkey, team_side, collection_name, created_at, updated_at) "
                          "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        bool result = executeUpdateParams(sql, params);
        
        if (!result) {
            m_lastError = "创建数据采集配置失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("成功创建用户 '{}' 在游戏 '{}' 的完整数据采集配置", username, gameName);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("创建数据采集配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

} // namespace db
} // namespace blweb 