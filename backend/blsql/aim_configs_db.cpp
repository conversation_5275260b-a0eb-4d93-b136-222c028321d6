#include "aim_configs_db.h"
#include "../../utils/logger.h"
#include "mysql_db.h"

#include <Poco/Exception.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace blweb {
namespace sqlserver {

// 单例实现
AimConfigsDB& AimConfigsDB::getInstance() {
    static AimConfigsDB instance;
    return instance;
}

// 构造函数
AimConfigsDB::AimConfigsDB() : m_lastError("") {
    LOG_INFO("AimConfigsDB已初始化");
}

// 析构函数
AimConfigsDB::~AimConfigsDB() {
    LOG_INFO("AimConfigsDB已销毁");
}

// 创建用户瞄准配置
bool AimConfigsDB::createAimConfig(const std::string& username, const std::string& gameName) {
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    std::vector<std::string> params = {
        username, gameName, "100", "50", "1.8", "1.6", "1.4", "5", "5", "5", "5", "5", "5", timeStr, timeStr
    };
    
    std::string sql = "INSERT INTO aim_configs (username, game_name, aim_range, track_range, head_height, "
                      "neck_height, chest_height, head_range_x, head_range_y, neck_range_x, neck_range_y, "
                      "chest_range_x, chest_range_y, created_at, updated_at) "
                      "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    bool result = db.executeUpdateParams(sql, params);
    if (!result) {
        m_lastError = "创建瞄准配置失败: " + db.getLastError();
        LOG_ERROR("{}", m_lastError);
    } else {
        LOG_INFO("成功创建用户'{}', 游戏'{}'的瞄准配置", username, gameName);
    }
    
    return result;
}

// 更新用户瞄准配置
bool AimConfigsDB::updateAimConfig(
    const std::string& username,
    const std::string& gameName,
    const std::map<std::string, std::string>& aimConfigMap
) {
    LOG_INFO("更新用户瞄准配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    if (aimConfigMap.empty()) {
        m_lastError = "配置参数为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 检查该用户的该游戏配置是否已存在
        std::vector<std::string> queryParams = {username, gameName};
        std::string querySql = "SELECT * FROM aim_configs WHERE username = ? AND game_name = ?";
        auto existingResults = db.executeQueryParams(querySql, queryParams);
        bool configExists = !existingResults.empty();
        
        if (!configExists) {
            LOG_INFO("不存在现有配置，创建新记录");
            
            // 设置默认值
            std::string aimRange = aimConfigMap.find("aimRange") != aimConfigMap.end() ? aimConfigMap.at("aimRange") : "100";
            std::string trackRange = aimConfigMap.find("trackRange") != aimConfigMap.end() ? aimConfigMap.at("trackRange") : "50";
            std::string headHeight = aimConfigMap.find("headHeight") != aimConfigMap.end() ? aimConfigMap.at("headHeight") : "1.8";
            std::string neckHeight = aimConfigMap.find("neckHeight") != aimConfigMap.end() ? aimConfigMap.at("neckHeight") : "1.6";
            std::string chestHeight = aimConfigMap.find("chestHeight") != aimConfigMap.end() ? aimConfigMap.at("chestHeight") : "1.4";
            std::string headRangeX = aimConfigMap.find("headRangeX") != aimConfigMap.end() ? aimConfigMap.at("headRangeX") : "5";
            std::string headRangeY = aimConfigMap.find("headRangeY") != aimConfigMap.end() ? aimConfigMap.at("headRangeY") : "5";
            std::string neckRangeX = aimConfigMap.find("neckRangeX") != aimConfigMap.end() ? aimConfigMap.at("neckRangeX") : "5";
            std::string neckRangeY = aimConfigMap.find("neckRangeY") != aimConfigMap.end() ? aimConfigMap.at("neckRangeY") : "5";
            std::string chestRangeX = aimConfigMap.find("chestRangeX") != aimConfigMap.end() ? aimConfigMap.at("chestRangeX") : "5";
            std::string chestRangeY = aimConfigMap.find("chestRangeY") != aimConfigMap.end() ? aimConfigMap.at("chestRangeY") : "5";
            
            // 创建新配置
            std::vector<std::string> params = {
                username, gameName, aimRange, trackRange, headHeight, neckHeight, chestHeight,
                headRangeX, headRangeY, neckRangeX, neckRangeY, chestRangeX, chestRangeY,
                timeStr, timeStr
            };
            
            std::string sql = "INSERT INTO aim_configs (username, game_name, aim_range, track_range, head_height, "
                              "neck_height, chest_height, head_range_x, head_range_y, neck_range_x, neck_range_y, "
                              "chest_range_x, chest_range_y, created_at, updated_at) "
                              "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            bool result = db.executeUpdateParams(sql, params);
            if (!result) {
                m_lastError = "创建瞄准配置失败: " + db.getLastError();
                LOG_ERROR("{}", m_lastError);
                return false;
            }
            
            LOG_INFO("成功创建瞄准配置记录");
            return true;
        } else {
            LOG_INFO("找到现有配置，更新记录");
            
            // 构建动态SQL和参数
            std::string updateSql = "UPDATE aim_configs SET updated_at = ?";
            std::vector<std::string> params = {timeStr};
            
            // 添加要更新的字段
            if (aimConfigMap.find("aimRange") != aimConfigMap.end()) {
                updateSql += ", aim_range = ?";
                params.push_back(aimConfigMap.at("aimRange"));
            }
            if (aimConfigMap.find("trackRange") != aimConfigMap.end()) {
                updateSql += ", track_range = ?";
                params.push_back(aimConfigMap.at("trackRange"));
            }
            if (aimConfigMap.find("headHeight") != aimConfigMap.end()) {
                updateSql += ", head_height = ?";
                params.push_back(aimConfigMap.at("headHeight"));
            }
            if (aimConfigMap.find("neckHeight") != aimConfigMap.end()) {
                updateSql += ", neck_height = ?";
                params.push_back(aimConfigMap.at("neckHeight"));
            }
            if (aimConfigMap.find("chestHeight") != aimConfigMap.end()) {
                updateSql += ", chest_height = ?";
                params.push_back(aimConfigMap.at("chestHeight"));
            }
            if (aimConfigMap.find("headRangeX") != aimConfigMap.end()) {
                updateSql += ", head_range_x = ?";
                params.push_back(aimConfigMap.at("headRangeX"));
            }
            if (aimConfigMap.find("headRangeY") != aimConfigMap.end()) {
                updateSql += ", head_range_y = ?";
                params.push_back(aimConfigMap.at("headRangeY"));
            }
            if (aimConfigMap.find("neckRangeX") != aimConfigMap.end()) {
                updateSql += ", neck_range_x = ?";
                params.push_back(aimConfigMap.at("neckRangeX"));
            }
            if (aimConfigMap.find("neckRangeY") != aimConfigMap.end()) {
                updateSql += ", neck_range_y = ?";
                params.push_back(aimConfigMap.at("neckRangeY"));
            }
            if (aimConfigMap.find("chestRangeX") != aimConfigMap.end()) {
                updateSql += ", chest_range_x = ?";
                params.push_back(aimConfigMap.at("chestRangeX"));
            }
            if (aimConfigMap.find("chestRangeY") != aimConfigMap.end()) {
                updateSql += ", chest_range_y = ?";
                params.push_back(aimConfigMap.at("chestRangeY"));
            }
            
            // 添加WHERE条件
            updateSql += " WHERE username = ? AND game_name = ?";
            params.push_back(username);
            params.push_back(gameName);
            
            // 执行更新
            bool result = db.executeUpdateParams(updateSql, params);
            if (!result) {
                m_lastError = "更新瞄准配置失败: " + db.getLastError();
                LOG_ERROR("{}", m_lastError);
                return false;
            }
            
            LOG_INFO("成功更新瞄准配置记录");
            return true;
        }
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新瞄准配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取用户瞄准配置
std::map<std::string, std::string> AimConfigsDB::getAimConfig(const std::string& username, const std::string& gameName) {
    LOG_INFO("获取瞄准配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    std::map<std::string, std::string> configMap;
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return configMap;
        }
    }
    
    try {
        std::vector<std::string> params = {username, gameName};
        std::string sql = "SELECT * FROM aim_configs WHERE username = ? AND game_name = ?";
        
        auto results = db.executeQueryParams(sql, params);
        LOG_INFO("查询瞄准配置结果: {} 行", results.size());
        
        if (!results.empty()) {
            auto& row = results[0];
            
            // 从各个字段中提取值并添加到结果映射中
            if (row.find("aim_range") != row.end()) configMap["aimRange"] = row["aim_range"];
            if (row.find("track_range") != row.end()) configMap["trackRange"] = row["track_range"];
            if (row.find("head_height") != row.end()) configMap["headHeight"] = row["head_height"];
            if (row.find("neck_height") != row.end()) configMap["neckHeight"] = row["neck_height"];
            if (row.find("chest_height") != row.end()) configMap["chestHeight"] = row["chest_height"];
            if (row.find("head_range_x") != row.end()) configMap["headRangeX"] = row["head_range_x"];
            if (row.find("head_range_y") != row.end()) configMap["headRangeY"] = row["head_range_y"];
            if (row.find("neck_range_x") != row.end()) configMap["neckRangeX"] = row["neck_range_x"];
            if (row.find("neck_range_y") != row.end()) configMap["neckRangeY"] = row["neck_range_y"];
            if (row.find("chest_range_x") != row.end()) configMap["chestRangeX"] = row["chest_range_x"];
            if (row.find("chest_range_y") != row.end()) configMap["chestRangeY"] = row["chest_range_y"];
        }
        
        return configMap;
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取瞄准配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return configMap;
    }
}

// 获取最后一次错误信息
std::string AimConfigsDB::getLastError() const {
    return m_lastError;
}

}  // namespace sqlserver
}  // namespace blweb 