#include "mysql_db.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include <string>
#include <Poco/Data/MySQL/Connector.h>
#include <Poco/Data/Session.h>
#include <Poco/Data/Statement.h>
#include <Poco/Data/RecordSet.h>
#include <Poco/Exception.h>

using Poco::Data::Session;
using Poco::Data::Statement;

namespace blweb {

// 单例实现
MySQLDB& MySQLDB::getInstance() {
    static MySQLDB instance;
    return instance;
}

// 构造函数
MySQLDB::MySQLDB() 
    : m_connected(false) {
    // 从全局配置读取数据库连接信息
    m_host = webui::database::g_db_host;
    m_port = webui::database::g_db_port;
    m_database = webui::database::g_db_name;
    m_username = webui::database::g_db_user;
    m_password = webui::database::g_db_password;
}

// 析构函数
MySQLDB::~MySQLDB() {
    close();
}

// 初始化数据库连接
bool MySQLDB::init() {
    try {
        LOG_INFO("连接到MySQL数据库: {}:{}/{}", m_host, m_port, m_database);
        
        // 注册MySQL连接器
        Poco::Data::MySQL::Connector::registerConnector();
        
        // 构建连接字符串
        std::string connectionString = 
            "host=" + m_host + 
            ";port=" + std::to_string(m_port) + 
            ";db=" + m_database + 
            ";user=" + m_username + 
            ";password=" + m_password + 
            ";compress=true;auto-reconnect=true";
        
        // 创建会话
        m_session = std::make_unique<Session>("MySQL", connectionString);
        
        if (m_session && m_session->isConnected()) {
            m_connected = true;
            LOG_INFO("MySQL数据库连接成功");
            return true;
        } else {
            m_lastError = "连接失败";
            LOG_ERROR("无法连接到MySQL数据库");
            return false;
        }
    }
    catch (const Poco::Data::MySQL::MySQLException& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("MySQL连接错误: {}", m_lastError);
    }
    catch (const Poco::Exception& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("POCO异常: {}", m_lastError);
    }
    catch (const std::exception& ex) {
        m_lastError = ex.what();
        LOG_ERROR("标准异常: {}", m_lastError);
    }
    catch (...) {
        m_lastError = "未知错误";
        LOG_ERROR("未知的数据库连接错误");
    }
    
    m_connected = false;
    return false;
}

// 关闭数据库连接
void MySQLDB::close() {
    if (m_session && m_connected) {
        try {
            m_session->close();
            m_connected = false;
            LOG_INFO("MySQL数据库连接已关闭");
        }
        catch (const std::exception& ex) {
            LOG_ERROR("关闭MySQL连接时发生错误: {}", ex.what());
        }
    }
    
    // 注销MySQL连接器
    try {
        Poco::Data::MySQL::Connector::unregisterConnector();
    }
    catch (...) {
        // 忽略注销错误
    }
}

// 转义SQL字符串，防止SQL注入
std::string MySQLDB::escapeString(const std::string& str) {
    // Poco::Data会自动处理参数绑定中的转义，这里只是简单实现
    std::string result = str;
    size_t pos = 0;
    
    // 替换单引号
    while ((pos = result.find('\'', pos)) != std::string::npos) {
        result.replace(pos, 1, "''");
        pos += 2;
    }
    
    return result;
}

// 执行SQL查询
MySQLDB::Rows MySQLDB::executeQuery(const std::string& sql) {
    Rows results;
    
    if (!m_connected || !m_session) {
        m_lastError = "数据库未连接";
        LOG_ERROR("执行查询失败: 数据库未连接");
        return results;
    }
    
    try {
        LOG_DEBUG("执行SQL查询: {}", sql);
        
        // 创建语句并执行
        Poco::Data::Statement statement(*m_session);
        statement << sql;
        statement.execute();
        
        // 获取结果集
        Poco::Data::RecordSet rs(statement);
        
        // 处理查询结果
        bool more = rs.moveFirst();
        while (more) {
            Row row;
            for (std::size_t colIdx = 0; colIdx < rs.columnCount(); ++colIdx) {
                std::string colName = rs.columnName(colIdx);
                std::string colValue;
                
                // 根据列类型转换为字符串
                try {
                    // 检查是否为NULL
                    if (rs[colIdx].isEmpty()) {
                        colValue = "NULL";
                    } else {
                        colValue = rs[colIdx].convert<std::string>();
                    }
                } catch (...) {
                    colValue = ""; // 转换失败时使用空字符串
                }
                
                row[colName] = colValue;
            }
            results.push_back(row);
            more = rs.moveNext();
        }
        
        LOG_DEBUG("查询返回 {} 行结果", results.size());
    }
    catch (const Poco::Exception& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("执行查询时发生错误: {}", m_lastError);
    }
    catch (const std::exception& ex) {
        m_lastError = ex.what();
        LOG_ERROR("执行查询时发生标准异常: {}", m_lastError);
    }
    
    return results;
}

// 执行SQL查询并绑定参数
MySQLDB::Rows MySQLDB::executeQueryParams(const std::string& sql, const std::vector<std::string>& params) {
    Rows results;
    
    if (!m_connected || !m_session) {
        m_lastError = "数据库未连接";
        LOG_ERROR("执行查询失败: 数据库未连接");
        return results;
    }
    
    try {
        LOG_DEBUG("执行参数化SQL查询: {}", sql);
        
        // 如果参数为空，直接调用普通查询
        if (params.empty()) {
            return executeQuery(sql);
        }
        
        // 简化实现：使用手动转义和替换参数
        std::string execSql = sql;
        for (const auto& param : params) {
            // 找到第一个问号并替换为转义后的参数值
            size_t pos = execSql.find('?');
            if (pos != std::string::npos) {
                std::string escapedValue = "'" + escapeString(param) + "'";
                execSql.replace(pos, 1, escapedValue);
            }
        }
        
        // 使用处理后的SQL执行查询
        return executeQuery(execSql);
    }
    catch (const Poco::Exception& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("执行参数化查询时发生错误: {}", m_lastError);
    }
    catch (const std::exception& ex) {
        m_lastError = ex.what();
        LOG_ERROR("执行参数化查询时发生标准异常: {}", m_lastError);
    }
    
    return results;
}

// 执行SQL更新/插入/删除操作
bool MySQLDB::executeUpdate(const std::string& sql) {
    if (!m_connected || !m_session) {
        m_lastError = "数据库未连接";
        LOG_ERROR("执行更新失败: 数据库未连接");
        return false;
    }
    
    try {
        LOG_DEBUG("执行SQL更新: {}", sql);
        
        // 创建语句并执行
        Poco::Data::Statement statement(*m_session);
        statement << sql;
        statement.execute();
        
        return true;
    }
    catch (const Poco::Exception& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("执行更新时发生错误: {}", m_lastError);
    }
    catch (const std::exception& ex) {
        m_lastError = ex.what();
        LOG_ERROR("执行更新时发生标准异常: {}", m_lastError);
    }
    
    return false;
}

// 执行SQL更新/插入/删除操作并绑定参数
bool MySQLDB::executeUpdateParams(const std::string& sql, const std::vector<std::string>& params) {
    if (!m_connected || !m_session) {
        m_lastError = "数据库未连接";
        LOG_ERROR("执行参数化更新失败: 数据库未连接");
        return false;
    }
    
    try {
        LOG_INFO("执行参数化SQL更新: {}", sql);
        LOG_INFO("参数数量: {}", params.size());
        for (size_t i = 0; i < params.size(); ++i) {
            LOG_INFO("参数[{}]: '{}'", i, params[i]);
        }
        
        // 如果参数为空，直接调用普通更新
        if (params.empty()) {
            LOG_WARNING("参数为空，转为执行无参数更新");
            return executeUpdate(sql);
        }
        
        try {
            // 使用POCO库的方式进行参数绑定 (这是更安全的方式)
            Poco::Data::Statement statement(*m_session);
            statement << sql;
            
            // 绑定参数
            for (const auto& param : params) {
                statement.bind(param);
            }
            
            // 执行语句
            int affectedRows = statement.execute();
            LOG_INFO("更新成功，影响行数: {}", affectedRows);
            return true;
        }
        catch (const Poco::Exception& ex) {
            LOG_ERROR("使用POCO参数绑定失败，尝试手动替换参数: {}", ex.displayText());
            
            // 简化实现：使用手动转义和替换参数
            std::string execSql = sql;
            for (const auto& param : params) {
                // 找到第一个问号并替换为转义后的参数值
                size_t pos = execSql.find('?');
                if (pos != std::string::npos) {
                    std::string escapedValue = "'" + escapeString(param) + "'";
                    execSql.replace(pos, 1, escapedValue);
                    LOG_DEBUG("替换参数: '?' -> {}", escapedValue);
                }
            }
            
            LOG_INFO("执行替换参数后的SQL: {}", execSql);
            
            // 使用处理后的SQL执行更新
            Poco::Data::Statement statement(*m_session);
            statement << execSql;
            int affectedRows = statement.execute();
            LOG_INFO("手动替换参数后更新成功，影响行数: {}", affectedRows);
            
            return true;
        }
    }
    catch (const Poco::Data::MySQL::MySQLException& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("MySQL异常: {}", m_lastError);
    }
    catch (const Poco::Exception& ex) {
        m_lastError = ex.displayText();
        LOG_ERROR("POCO异常: {}", m_lastError);
    }
    catch (const std::exception& ex) {
        m_lastError = ex.what();
        LOG_ERROR("标准异常: {}", m_lastError);
    }
    catch (...) {
        m_lastError = "未知错误";
        LOG_ERROR("执行参数化更新时发生未知异常");
    }
    
    return false;
}

// 获取最后一次错误信息
std::string MySQLDB::getLastError() const {
    return m_lastError;
}

} // namespace blweb