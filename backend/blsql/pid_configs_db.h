#pragma once

#include <string>
#include <vector>
#include <map>
#include <Poco/JSON/Object.h>
#include "mysql_db.h"

namespace blweb {
namespace db {

/**
 * @class PidConfigsDB
 * @brief PID配置数据库操作类
 * 
 * 这个类负责处理与PID配置相关的数据库操作，包括更新和获取PID控制器参数
 */
class PidConfigsDB {
public:
    // 数据库表行和行集合的类型定义
    using Row = std::map<std::string, std::string>;
    using Rows = std::vector<Row>;
    
    /**
     * @brief 获取PidConfigsDB单例实例
     * @return PidConfigsDB& 单例引用
     */
    static PidConfigsDB& getInstance();
    
    /**
     * @brief 构造函数
     */
    PidConfigsDB();
    
    /**
     * @brief 析构函数
     */
    ~PidConfigsDB();
    
    /**
     * @brief 创建用户PID配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return bool 创建成功返回true，失败返回false
     */
    bool createPidConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 更新PID配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param nearMoveFactor 近端移动速度
     * @param nearStabilizer 近端跟踪速度
     * @param nearResponseRate 近端抖动力度
     * @param nearAssistZone 近端死区大小
     * @param nearResponseDelay 近端回弹速度
     * @param nearMaxAdjustment 近端积分限制
     * @param farFactor 远端系数
     * @param yAxisFactor Y轴系数
     * @param pidRandomFactor PID随机系数
     * @return bool 成功返回true，否则返回false
     */
    bool updatePidConfig(
        const std::string& username,
        const std::string& gameName,
        double nearMoveFactor,
        double nearStabilizer,
        double nearResponseRate,
        double nearAssistZone,
        double nearResponseDelay,
        double nearMaxAdjustment,
        double farFactor,
        double yAxisFactor,
        double pidRandomFactor
    );
    
    /**
     * @brief 获取PID配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Row 包含PID配置的数据行，失败时返回空Row
     */
    Row getPidConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace db
} // namespace blweb 