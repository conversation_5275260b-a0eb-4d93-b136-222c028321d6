#include "pid_configs_db.h"
#include "../../utils/logger.h"
#include "mysql_db.h"

#include <Poco/Exception.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace blweb {
namespace db {

// 单例实现
PidConfigsDB& PidConfigsDB::getInstance() {
    static PidConfigsDB instance;
    return instance;
}

// 构造函数
PidConfigsDB::PidConfigsDB() : m_lastError("") {
    LOG_INFO("PidConfigsDB已初始化");
}

// 析构函数
PidConfigsDB::~PidConfigsDB() {
    LOG_INFO("PidConfigsDB已销毁");
}

// 创建用户PID配置
bool PidConfigsDB::createPidConfig(const std::string& username, const std::string& gameName) {
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    LOG_INFO("为用户'{}', 游戏'{}'创建PID配置", username, gameName);
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    std::vector<std::string> params = {
        username, gameName, "1.0", "0.5", "0.3", "3.0", "1.0", "2.0", "1.0", "1.0", "0.5", timeStr, timeStr
    };
    
    std::string sql = "INSERT INTO pid_configs (username, game_name, near_move_factor, near_stabilizer, near_response_rate, "
                      "near_assist_zone, near_response_delay, near_max_adjustment, far_factor, y_axis_factor, pid_random_factor, created_at, updated_at) "
                      "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    bool result = db.executeUpdateParams(sql, params);
    if (!result) {
        m_lastError = "创建PID配置失败: " + db.getLastError();
        LOG_ERROR("{}", m_lastError);
    } else {
        LOG_INFO("成功创建PID配置记录");
    }
    
    return result;
}

// 更新PID配置
bool PidConfigsDB::updatePidConfig(
    const std::string& username,
    const std::string& gameName,
    double nearMoveFactor,
    double nearStabilizer,
    double nearResponseRate,
    double nearAssistZone,
    double nearResponseDelay,
    double nearMaxAdjustment,
    double farFactor,
    double yAxisFactor,
    double pidRandomFactor
) {
    LOG_INFO("更新PID配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    LOG_INFO("参数: nearMoveFactor={}, nearStabilizer={}, nearResponseRate={}, nearAssistZone={}, nearResponseDelay={}, nearMaxAdjustment={}, farFactor={}, yAxisFactor={}, pidRandomFactor={}",
             nearMoveFactor, nearStabilizer, nearResponseRate, nearAssistZone, nearResponseDelay, nearMaxAdjustment, farFactor, yAxisFactor, pidRandomFactor);
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 检查配置是否已存在
        std::vector<std::string> queryParams = {username, gameName};
        std::string querySql = "SELECT * FROM pid_configs WHERE username = ? AND game_name = ?";
        Rows existingResults = db.executeQueryParams(querySql, queryParams);
        bool configExists = !existingResults.empty();
        
        if (!configExists) {
            // 创建新配置
            LOG_INFO("创建新PID配置");
            
            std::vector<std::string> params = {
                username, 
                gameName, 
                std::to_string(nearMoveFactor),
                std::to_string(nearStabilizer),
                std::to_string(nearResponseRate),
                std::to_string(nearAssistZone),
                std::to_string(nearResponseDelay),
                std::to_string(nearMaxAdjustment),
                std::to_string(farFactor),
                std::to_string(yAxisFactor),
                std::to_string(pidRandomFactor),
                timeStr,
                timeStr
            };
            
            std::string sql = "INSERT INTO pid_configs (username, game_name, near_move_factor, near_stabilizer, near_response_rate, "
                              "near_assist_zone, near_response_delay, near_max_adjustment, far_factor, y_axis_factor, pid_random_factor, created_at, updated_at) "
                              "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            bool result = db.executeUpdateParams(sql, params);
            if (!result) {
                m_lastError = "创建PID配置失败: " + db.getLastError();
                LOG_ERROR("{}", m_lastError);
                return false;
            }
        } else {
            // 更新现有配置
            LOG_INFO("更新现有PID配置");
            
            std::vector<std::string> params = {
                std::to_string(nearMoveFactor),
                std::to_string(nearStabilizer),
                std::to_string(nearResponseRate),
                std::to_string(nearAssistZone),
                std::to_string(nearResponseDelay),
                std::to_string(nearMaxAdjustment),
                std::to_string(farFactor),
                std::to_string(yAxisFactor),
                std::to_string(pidRandomFactor),
                timeStr,
                username,
                gameName
            };
            
            std::string sql = "UPDATE pid_configs SET near_move_factor = ?, near_stabilizer = ?, near_response_rate = ?, "
                              "near_assist_zone = ?, near_response_delay = ?, near_max_adjustment = ?, far_factor = ?, y_axis_factor = ?, pid_random_factor = ?, updated_at = ? "
                              "WHERE username = ? AND game_name = ?";
            
            bool result = db.executeUpdateParams(sql, params);
            if (!result) {
                m_lastError = "更新PID配置失败: " + db.getLastError();
                LOG_ERROR("{}", m_lastError);
                return false;
            }
        }
        
        LOG_INFO("PID配置更新成功");
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新PID配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取PID配置
PidConfigsDB::Row PidConfigsDB::getPidConfig(const std::string& username, const std::string& gameName) {
    LOG_INFO("获取PID配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    Row emptyRow;
    
    // 检查数据库连接
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return emptyRow;
        }
    }
    
    try {
        std::vector<std::string> params = {username, gameName};
        std::string sql = "SELECT * FROM pid_configs WHERE username = ? AND game_name = ?";
        
        Rows results = db.executeQueryParams(sql, params);
        if (results.empty()) {
            m_lastError = "找不到PID配置";
            LOG_WARNING("未找到PID配置 - 用户: '{}', 游戏: '{}'", username, gameName);
            return emptyRow;
        }
        
        // 打印查询到的PID配置详细内容
        LOG_INFO("成功获取PID配置，详细内容如下:");
        for (const auto& pair : results[0]) {
            LOG_INFO("    {} = {}", pair.first, pair.second);
        }
        
        return results[0];
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取PID配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return emptyRow;
    }
}

// 获取最后一次错误信息
std::string PidConfigsDB::getLastError() const {
    return m_lastError;
}

} // namespace db
} // namespace blweb 