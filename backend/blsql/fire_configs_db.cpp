#include "fire_configs_db.h"
#include "../../utils/logger.h"
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <sstream>

namespace blweb {
namespace db {

// 单例实现
FireConfigsDB& FireConfigsDB::getInstance() {
    static FireConfigsDB instance;
    return instance;
}

// 构造函数
FireConfigsDB::FireConfigsDB() : blweb::MySQLDB() {
    LOG_INFO("FireConfigsDB已初始化");
}

// 获取用户在特定游戏下的射击配置
std::map<std::string, std::string> FireConfigsDB::getFireConfig(const std::string& username, const std::string& gameName) {
    LOG_INFO("获取射击配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    std::map<std::string, std::string> config;
    
    if (username.empty() || gameName.empty()) {
        m_lastError = "用户名和游戏名不能为空";
        LOG_ERROR("{}", m_lastError);
        return config;
    }
    
    // 检查数据库连接状态
    if (!isConnected()) {
        if (!init()) {
            m_lastError = "数据库连接失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return config;
        }
    }
    
    try {
        // 查询射击配置
        std::vector<std::string> params = {username, gameName};
        std::string sql = "SELECT * FROM fire_configs WHERE username = ? AND game_name = ? LIMIT 1";
        
        Rows results = executeQueryParams(sql, params);
        
        if (results.empty()) {
            LOG_WARNING("未找到用户 '{}' 在游戏 '{}' 的射击配置", username, gameName);
            return config;
        }
        
        // 返回第一个匹配的结果
        LOG_INFO("成功获取用户 '{}' 在游戏 '{}' 的射击配置", username, gameName);
        return results[0];
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取射击配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return config;
    }
}

// 更新用户在特定游戏下的射击配置
bool FireConfigsDB::updateFireConfig(
    const std::string& username,
    const std::string& gameName,
    int rifleSleep,
    int rifleInterval,
    int pistolSleep,
    int pistolInterval,
    int sniperSleep,
    int sniperInterval
) {
    LOG_INFO("更新射击配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    if (username.empty() || gameName.empty()) {
        m_lastError = "用户名和游戏名不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    if (!isConnected()) {
        if (!init()) {
            m_lastError = "数据库连接失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        // 首先检查配置是否存在
        std::vector<std::string> checkParams = {username, gameName};
        std::string checkSql = "SELECT COUNT(*) as count FROM fire_configs WHERE username = ? AND game_name = ?";
        
        Rows checkResults = executeQueryParams(checkSql, checkParams);
        int count = std::stoi(checkResults[0]["count"]);
        
        if (count == 0) {
            // 如果不存在，创建新配置
            return createFireConfig(username, gameName, rifleSleep, rifleInterval, pistolSleep, pistolInterval, sniperSleep, sniperInterval);
        }
        
        // 如果存在，更新配置
        std::vector<std::string> params = {
            std::to_string(rifleSleep),
            std::to_string(rifleInterval),
            std::to_string(pistolSleep),
            std::to_string(pistolInterval),
            std::to_string(sniperSleep),
            std::to_string(sniperInterval),
            timeStr,
            username,
            gameName
        };
        
        std::string sql = "UPDATE fire_configs SET rifle_sleep = ?, rifle_interval = ?, "
                          "pistol_sleep = ?, pistol_interval = ?, sniper_sleep = ?, sniper_interval = ?, "
                          "updated_at = ? WHERE username = ? AND game_name = ?";
        
        bool result = executeUpdateParams(sql, params);
        
        if (!result) {
            m_lastError = "更新射击配置失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("成功更新用户 '{}' 在游戏 '{}' 的射击配置", username, gameName);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新射击配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 创建用户在特定游戏下的射击配置
bool FireConfigsDB::createFireConfig(
    const std::string& username,
    const std::string& gameName,
    int rifleSleep,
    int rifleInterval,
    int pistolSleep,
    int pistolInterval,
    int sniperSleep,
    int sniperInterval
) {
    LOG_INFO("创建射击配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    if (username.empty() || gameName.empty()) {
        m_lastError = "用户名和游戏名不能为空";
        LOG_ERROR("{}", m_lastError);
        return false;
    }
    
    // 检查数据库连接状态
    if (!isConnected()) {
        if (!init()) {
            m_lastError = "数据库连接失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    try {
        // 当前时间作为创建时间和更新时间
        Poco::DateTime now;
        std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
        
        std::vector<std::string> params = {
            username,
            gameName,
            std::to_string(rifleSleep),
            std::to_string(rifleInterval),
            std::to_string(pistolSleep),
            std::to_string(pistolInterval),
            std::to_string(sniperSleep),
            std::to_string(sniperInterval),
            timeStr,
            timeStr
        };
        
        std::string sql = "INSERT INTO fire_configs (username, game_name, rifle_sleep, rifle_interval, "
                          "pistol_sleep, pistol_interval, sniper_sleep, sniper_interval, created_at, updated_at) "
                          "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        bool result = executeUpdateParams(sql, params);
        
        if (!result) {
            m_lastError = "创建射击配置失败: " + getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("成功创建用户 '{}' 在游戏 '{}' 的射击配置", username, gameName);
        return true;
    } catch (const std::exception& ex) {
        m_lastError = std::string("创建射击配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

} // namespace db
} // namespace blweb 