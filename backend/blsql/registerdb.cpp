#include "registerdb.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "mysql_db.h"
#include "userinfodb.h"  // 添加UserInfoDB的头文件引用
#include "function_configs_db.h"  // 添加FunctionConfigsDB的头文件引用
#include "pid_configs_db.h"  // 添加PidConfigsDB的头文件引用
#include "aim_configs_db.h"  // 添加AimConfigsDB的头文件引用
#include "fovconfgidb.h"  // 添加FovConfigDB的头文件引用

#include <Poco/Data/Session.h>
#include <Poco/Data/Statement.h>
#include <Poco/Data/RecordSet.h>
#include <Poco/Exception.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <Poco/JSON/Parser.h>
#include <Poco/JSON/Object.h>
#include <Poco/JSON/Array.h>
#include <Poco/Dynamic/Var.h>
#include <sstream>
#include <algorithm>

namespace blweb {
namespace db {

namespace {
    // 验证游戏是否在支持列表中
    bool isGameSupported(const std::string& gameName) {
        const auto& supportedGames = webui::myregister::g_game_list;
        return std::find(supportedGames.begin(), supportedGames.end(), gameName) != supportedGames.end();
    }
}

// 单例实现
RegisterDB& RegisterDB::getInstance() {
    static RegisterDB instance;
    return instance;
}

// 构造函数
RegisterDB::RegisterDB() : m_lastError("") {
    // 空实现
}

// 析构函数
RegisterDB::~RegisterDB() {
    // 空实现
}

// 初始化
bool RegisterDB::init() {
    // 继续使用MySQLDB进行初始化和连接管理
    return MySQLDB::getInstance().init();
}

// 执行SQL查询
RegisterDB::Rows RegisterDB::executeQuery(const std::string& sql) {
    return MySQLDB::getInstance().executeQuery(sql);
}

// 执行SQL查询并绑定参数
RegisterDB::Rows RegisterDB::executeQueryParams(const std::string& sql, const std::vector<std::string>& params) {
    return MySQLDB::getInstance().executeQueryParams(sql, params);
}

// 执行SQL更新/插入/删除操作
bool RegisterDB::executeUpdate(const std::string& sql) {
    return MySQLDB::getInstance().executeUpdate(sql);
}

// 执行SQL更新/插入/删除操作并绑定参数
bool RegisterDB::executeUpdateParams(const std::string& sql, const std::vector<std::string>& params) {
    return MySQLDB::getInstance().executeUpdateParams(sql, params);
}

// 转义SQL字符串，防止SQL注入
std::string RegisterDB::escapeString(const std::string& str) {
    return MySQLDB::getInstance().escapeString(str);
}

// 检查用户名是否已存在
bool RegisterDB::isUserExists(const std::string& username) {
    // 检查超级管理员表中是否已存在该用户名
    std::vector<std::string> params = {username};
    std::string sql = "SELECT COUNT(*) as count FROM admin_user WHERE username = ?";
    Rows results = executeQueryParams(sql, params);
    
    if (!results.empty() && !results[0].empty()) {
        std::string countStr = results[0]["count"];
        int count = 0;
        
        try {
            count = std::stoi(countStr);
        } catch (...) {
            LOG_ERROR("检查用户是否存在时转换结果失败");
            return false;
        }
        
        return count > 0;
    }
    
    return false;
}

// 创建首页配置
bool RegisterDB::createHomeConfig(const std::string& username, const std::string& defaultGame) {
    // 验证游戏是否支持
    if (!isGameSupported(defaultGame)) {
        m_lastError = "不支持的游戏: " + defaultGame;
        LOG_ERROR("为用户 '{}' 创建首页配置失败: {}", username, m_lastError);
        return false;
    }
    
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    LOG_INFO("为用户 '{}' 创建首页配置，默认游戏: '{}'", username, defaultGame);
    
    std::vector<std::string> params = {username, defaultGame, "", timeStr, timeStr};
    std::string sql = "INSERT INTO home_configs (username, game_name, card_key, created_at, updated_at) VALUES (?, ?, ?, ?, ?)";
    
    return executeUpdateParams(sql, params);
}

// 创建射击配置
bool RegisterDB::createFireConfig(const std::string& username, const std::string& gameName) {
    // 验证游戏是否支持
    if (!isGameSupported(gameName)) {
        m_lastError = "不支持的游戏: " + gameName;
        LOG_ERROR("为用户 '{}' 创建射击配置失败: {}", username, m_lastError);
        return false;
    }
    
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    std::vector<std::string> params = {
        username, gameName, "100", "200", "150", "250", "300", "500", timeStr, timeStr
    };
    
    std::string sql = "INSERT INTO fire_configs (username, game_name, rifle_sleep, rifle_interval, pistol_sleep, "
                      "pistol_interval, sniper_sleep, sniper_interval, created_at, updated_at) "
                      "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    return executeUpdateParams(sql, params);
}

// 创建数据收集配置（默认关闭）
bool RegisterDB::createDataCollectionConfig(const std::string& username, const std::string& gameName) {
    // 验证游戏是否支持
    if (!isGameSupported(gameName)) {
        m_lastError = "不支持的游戏: " + gameName;
        LOG_ERROR("为用户 '{}' 创建数据收集配置失败: {}", username, m_lastError);
        return false;
    }
    
    LOG_INFO("为用户 '{}' 创建游戏 '{}' 的数据收集配置（默认关闭）", username, gameName);
    
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    std::vector<std::string> params = {
        username, 
        gameName, 
        "0",        // is_enabled: 默认关闭
        "右键",     // map_hotkey: 默认右键
        "前侧",     // target_hotkey: 默认前侧键
        "警方",     // team_side: 默认警方
        "",         // collection_name: 默认为空
        timeStr,    // created_at
        timeStr     // updated_at
    };
    
    std::string sql = "INSERT INTO data_collections (username, game_name, is_enabled, map_hotkey, "
                      "target_hotkey, team_side, collection_name, created_at, updated_at) "
                      "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    bool result = executeUpdateParams(sql, params);
    if (result) {
        LOG_INFO("成功为用户 '{}' 创建游戏 '{}' 的数据收集配置", username, gameName);
    } else {
        LOG_ERROR("为用户 '{}' 创建游戏 '{}' 的数据收集配置失败", username, gameName);
    }
    
    return result;
}

// 注册新用户
bool RegisterDB::registerUser(const std::string& username, const std::string& password, const std::string& defaultGame, bool isPro) {
    // 检查用户是否已存在
    if (isUserExists(username)) {
        m_lastError = "用户名已存在";
        LOG_ERROR("注册失败: 用户名 '{}' 已存在", username);
        return false;
    }
    
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    // 生成token
    std::string token = "blweb_token_" + username;
    
    // 向超级管理员表插入新用户
    std::vector<std::string> params = {username, password, isPro ? "1" : "0", timeStr, timeStr, token};
    std::string sql = "INSERT INTO admin_user (username, password, is_pro, created_at, updated_at, token) VALUES (?, ?, ?, ?, ?, ?)";
    
    if (!executeUpdateParams(sql, params)) {
        m_lastError = "注册用户时数据库操作失败";
        LOG_ERROR("注册用户 '{}' 失败: {}", username, m_lastError);
        return false;
    }
    
    LOG_INFO("用户 '{}' 注册成功, isPro={}", username, isPro);
    
    // 创建首页配置
    if (!createHomeConfig(username, defaultGame)) {
        LOG_WARNING("为用户 '{}' 创建首页配置失败，但用户注册成功", username);
    }
    
    return true;
}

// 创建用户的初始配置（带游戏列表版本）
bool RegisterDB::createInitialConfigs(const std::string& username, const std::vector<std::string>& gameNames) {
    bool success = true;
    
    // 为每个游戏创建配置
    for (const auto& gameName : gameNames) {
        LOG_INFO("为用户 '{}' 创建游戏 '{}' 的配置", username, gameName);
        
        // 检查游戏是否在支持列表中
        if (!isGameSupported(gameName)) {
            LOG_ERROR("游戏 '{}' 不在支持列表中，无法创建配置", gameName);
            success = false;
            continue;
        }
    
        // 创建功能配置
        if (!FunctionConfigsDB::getInstance().createFunctionConfig(username, gameName)) {
            LOG_ERROR("为用户 '{}' 创建游戏 '{}' 的功能配置失败", username, gameName);
            success = false;
        }
    
        // 创建PID配置
        if (!PidConfigsDB::getInstance().createPidConfig(username, gameName)) {
            LOG_ERROR("为用户 '{}' 创建游戏 '{}' 的PID配置失败", username, gameName);
            success = false;
        }
    
        // 创建瞄准配置
        if (!blweb::sqlserver::AimConfigsDB::getInstance().createAimConfig(username, gameName)) {
            LOG_ERROR("为用户 '{}' 创建游戏 '{}' 的瞄准配置失败", username, gameName);
            success = false;
        }
    
        // 创建射击配置
        if (!createFireConfig(username, gameName)) {
            LOG_ERROR("为用户 '{}' 创建游戏 '{}' 的射击配置失败", username, gameName);
            success = false;
        }
    
        // 创建FOV配置
        if (!FovConfigDB::getInstance().createFovConfig(username, gameName)) {
            LOG_ERROR("为用户 '{}' 创建游戏 '{}' 的视野配置失败", username, gameName);
            success = false;
        }
        
        // 创建数据收集配置（默认关闭）
        if (!createDataCollectionConfig(username, gameName)) {
            LOG_ERROR("为用户 '{}' 创建游戏 '{}' 的数据收集配置失败", username, gameName);
            success = false;
        }
    }
    
    if (success) {
        LOG_INFO("为用户 '{}' 创建所有游戏配置成功", username);
    }
    
    return success;
}

// 添加实现获取用户所有配置信息的方法
Poco::JSON::Object::Ptr RegisterDB::getUserAllConfigs(const std::string& username) {
    LOG_INFO("获取用户 '{}' 的所有配置信息", username);
    
    // 创建一个空的JSON对象作为结果
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        // 查询用户信息
        std::vector<std::string> params = {username};
        std::string sql = "SELECT * FROM admin_user WHERE username = ?";
        Rows userRows = executeQueryParams(sql, params);
        
        if (userRows.empty()) {
            LOG_ERROR("找不到用户: {}", username);
            return result;
        }
        
        // 用户信息
        Poco::JSON::Object::Ptr userInfo = new Poco::JSON::Object();
        userInfo->set("username", username);
        if (userRows[0].find("created_at") != userRows[0].end()) {
            userInfo->set("createdAt", userRows[0]["created_at"]);
        }
        if (userRows[0].find("updated_at") != userRows[0].end()) {
            userInfo->set("updatedAt", userRows[0]["updated_at"]);
        }
        if (userRows[0].find("token") != userRows[0].end()) {
            userInfo->set("token", userRows[0]["token"]);
        }
        if (userRows[0].find("is_pro") != userRows[0].end()) {
            userInfo->set("isPro", userRows[0]["is_pro"] == "1");
        } else {
            userInfo->set("isPro", false);
        }
        result->set("userInfo", userInfo);
        
        // 查询首页配置 - 获取默认游戏名称
        sql = "SELECT * FROM home_configs WHERE username = ?";
        Rows homeRows = executeQueryParams(sql, params);
        
        // 从首页配置中获取游戏名称，找不到则使用"wwqy"作为默认值
        std::string defaultGameName = "wwqy";
        
        if (!homeRows.empty()) {
            Poco::JSON::Object::Ptr homeConfig = new Poco::JSON::Object();
            homeConfig->set("username", username);
            homeConfig->set("gameName", homeRows[0]["game_name"]);
            homeConfig->set("cardKey", homeRows[0]["card_key"]);
            homeConfig->set("createdAt", homeRows[0]["created_at"]);
            homeConfig->set("updatedAt", homeRows[0]["updated_at"]);
            result->set("homeConfig", homeConfig);
            
            // 保存默认游戏名称，确保是支持的游戏
            std::string gameFromDB = homeRows[0]["game_name"];
            if (isGameSupported(gameFromDB)) {
                defaultGameName = gameFromDB;
            } else {
                LOG_WARNING("数据库中的游戏 '{}' 不在支持列表中，使用默认游戏 '{}'", gameFromDB, defaultGameName);
            }
        }
        
        LOG_INFO("用户 '{}' 的默认游戏: '{}'", username, defaultGameName);
        
        // 查询功能配置
        sql = "SELECT * FROM function_configs WHERE username = ? AND game_name = ?";
        std::vector<std::string> funcParams = {username, defaultGameName};
        Rows funcRows = executeQueryParams(sql, funcParams);
        
        Poco::JSON::Array::Ptr funcConfigArray = new Poco::JSON::Array();
        for (const auto& row : funcRows) {
            Poco::JSON::Object::Ptr funcConfig = new Poco::JSON::Object();
            funcConfig->set("username", username);
            funcConfig->set("gameName", row.at("game_name"));
            funcConfig->set("presetName", row.at("preset_name"));
            funcConfig->set("aiMode", row.at("ai_mode"));
            funcConfig->set("lockPosition", row.at("lock_position"));
            funcConfig->set("selectedFaction", row.at("selected_faction"));
            funcConfig->set("hotkey", row.at("hotkey"));
            funcConfig->set("triggerSwitch", row.at("trigger_switch") == "1");
            // 处理weaponSwitch字段，如果不存在则默认为false
            bool weaponSwitch = false;
            auto weaponSwitchIt = row.find("weapon_switch");
            if (weaponSwitchIt != row.end()) {
                weaponSwitch = (weaponSwitchIt->second == "1");
            }
            funcConfig->set("weaponSwitch", weaponSwitch);
            
            // 处理flashShield字段，如果不存在则默认为false
            bool flashShield = false;
            auto flashShieldIt = row.find("flash_shield");
            if (flashShieldIt != row.end()) {
                flashShield = (flashShieldIt->second == "1");
            }
            funcConfig->set("flashShield", flashShield);
            funcConfig->set("enabled", row.at("enabled") == "1");
            funcConfig->set("createdAt", row.at("created_at"));
            funcConfig->set("updatedAt", row.at("updated_at"));
            funcConfigArray->add(funcConfig);
        }
        result->set("functionConfig", funcConfigArray);
        
        // PID配置
        sql = "SELECT * FROM pid_configs WHERE username = ? AND game_name = ?";
        std::vector<std::string> pidParams = {username, defaultGameName};
        Rows pidRows = executeQueryParams(sql, pidParams);
        if (!pidRows.empty()) {
            Poco::JSON::Object::Ptr pidConfig = new Poco::JSON::Object();
            pidConfig->set("username", username);
            pidConfig->set("gameName", pidRows[0]["game_name"]);
            pidConfig->set("nearMoveFactor", std::stof(pidRows[0]["near_move_factor"]));
            pidConfig->set("nearStabilizer", std::stof(pidRows[0]["near_stabilizer"]));
            pidConfig->set("nearResponseRate", std::stof(pidRows[0]["near_response_rate"]));
            pidConfig->set("nearAssistZone", std::stof(pidRows[0]["near_assist_zone"]));
            pidConfig->set("nearResponseDelay", std::stof(pidRows[0]["near_response_delay"]));
            pidConfig->set("nearMaxAdjustment", std::stof(pidRows[0]["near_max_adjustment"]));
            pidConfig->set("farFactor", std::stof(pidRows[0]["far_factor"]));
            pidConfig->set("yAxisFactor", std::stof(pidRows[0]["y_axis_factor"]));
            pidConfig->set("pidRandomFactor", std::stof(pidRows[0]["pid_random_factor"]));
            pidConfig->set("createdAt", pidRows[0]["created_at"]);
            pidConfig->set("updatedAt", pidRows[0]["updated_at"]);
            result->set("pidConfig", pidConfig);
        }
        
        // FOV配置
        sql = "SELECT * FROM fov_configs WHERE username = ? AND game_name = ?";
        std::vector<std::string> fovParams = {username, defaultGameName};
        Rows fovRows = executeQueryParams(sql, fovParams);
        if (!fovRows.empty()) {
            Poco::JSON::Object::Ptr fovConfig = new Poco::JSON::Object();
            fovConfig->set("username", username);
            fovConfig->set("gameName", fovRows[0]["game_name"]);
            fovConfig->set("fov", std::stof(fovRows[0]["fov"]));
            fovConfig->set("fovTime", std::stoi(fovRows[0]["fov_time"]));
            fovConfig->set("createdAt", fovRows[0]["created_at"]);
            fovConfig->set("updatedAt", fovRows[0]["updated_at"]);
            result->set("fovConfig", fovConfig);
        }
        
        // 瞄准配置
        sql = "SELECT * FROM aim_configs WHERE username = ? AND game_name = ?";
        std::vector<std::string> aimParams = {username, defaultGameName};
        Rows aimRows = executeQueryParams(sql, aimParams);
        if (!aimRows.empty()) {
            Poco::JSON::Object::Ptr aimConfig = new Poco::JSON::Object();
            aimConfig->set("username", username);
            aimConfig->set("gameName", aimRows[0]["game_name"]);
            aimConfig->set("aimRange", std::stof(aimRows[0]["aim_range"]));
            aimConfig->set("trackRange", std::stof(aimRows[0]["track_range"]));
            aimConfig->set("headHeight", std::stof(aimRows[0]["head_height"]));
            aimConfig->set("neckHeight", std::stof(aimRows[0]["neck_height"]));
            aimConfig->set("chestHeight", std::stof(aimRows[0]["chest_height"]));
            aimConfig->set("headRangeX", std::stof(aimRows[0]["head_range_x"]));
            aimConfig->set("headRangeY", std::stof(aimRows[0]["head_range_y"]));
            aimConfig->set("neckRangeX", std::stof(aimRows[0]["neck_range_x"]));
            aimConfig->set("neckRangeY", std::stof(aimRows[0]["neck_range_y"]));
            aimConfig->set("chestRangeX", std::stof(aimRows[0]["chest_range_x"]));
            aimConfig->set("chestRangeY", std::stof(aimRows[0]["chest_range_y"]));
            aimConfig->set("createdAt", aimRows[0]["created_at"]);
            aimConfig->set("updatedAt", aimRows[0]["updated_at"]);
            result->set("aimConfig", aimConfig);
        }
        
        // 射击配置
        sql = "SELECT * FROM fire_configs WHERE username = ? AND game_name = ?";
        std::vector<std::string> fireParams = {username, defaultGameName};
        Rows fireRows = executeQueryParams(sql, fireParams);
        if (!fireRows.empty()) {
            Poco::JSON::Object::Ptr fireConfig = new Poco::JSON::Object();
            fireConfig->set("username", username);
            fireConfig->set("gameName", fireRows[0]["game_name"]);
            fireConfig->set("rifleSleep", std::stoi(fireRows[0]["rifle_sleep"]));
            fireConfig->set("rifleInterval", std::stoi(fireRows[0]["rifle_interval"]));
            fireConfig->set("pistolSleep", std::stoi(fireRows[0]["pistol_sleep"]));
            fireConfig->set("pistolInterval", std::stoi(fireRows[0]["pistol_interval"]));
            fireConfig->set("sniperSleep", std::stoi(fireRows[0]["sniper_sleep"]));
            fireConfig->set("sniperInterval", std::stoi(fireRows[0]["sniper_interval"]));
            fireConfig->set("createdAt", fireRows[0]["created_at"]);
            fireConfig->set("updatedAt", fireRows[0]["updated_at"]);
            result->set("fireConfig", fireConfig);
        }
        
        LOG_INFO("成功获取用户 '{}' 的所有配置信息", username);
    } catch (const std::exception& ex) {
        LOG_ERROR("获取用户配置信息时发生错误: {}", ex.what());
    }
    
    return result;
}

// 处理用户注册请求
Poco::JSON::Object::Ptr RegisterDB::handleRegisterRequest(const Poco::JSON::Object::Ptr& message) {
    Poco::JSON::Object::Ptr response = new Poco::JSON::Object();
    
    try {
        LOG_INFO("处理用户注册请求");
        
        // 检查消息是否为空
        if (!message) {
            throw std::runtime_error("消息对象为空");
        }
        
        // 验证必需字段
        if (!message->has("username") || !message->has("password")) {
            throw std::runtime_error("缺少'username'或'password'字段");
        }
        
        // 获取字段值
        std::string username = message->getValue<std::string>("username");
        std::string password = message->getValue<std::string>("password");
        std::string defaultGame = "csgo2";
        bool isPro = false;
        
        if (message->has("defaultGame")) {
            defaultGame = message->getValue<std::string>("defaultGame");
        }
        
        if (message->has("isPro")) {
            isPro = message->getValue<bool>("isPro");
        }
        
        LOG_INFO("用户注册参数: username={}, 默认游戏={}, isPro={}", username, defaultGame, isPro);
        
        // 获取游戏列表
        std::vector<std::string> gameNames;
        bool hasGameList = false;
        
        if (message->isArray("gameList")) {
            Poco::JSON::Array::Ptr gameList = message->getArray("gameList");
            if (gameList && gameList->size() > 0) {
                hasGameList = true;
                for (size_t i = 0; i < gameList->size(); ++i) {
                    std::string gameName = gameList->getElement<std::string>(i);
                    if (!gameName.empty()) {
                        gameNames.push_back(gameName);
                    }
                }
                LOG_INFO("收到游戏列表，包含{}个游戏", gameNames.size());
            }
        }
        
        // 打印日志（隐藏密码）
        LOG_INFO("用户注册参数: username={}, password=*****, isPro={}", username, isPro);
        
        // 检查用户名是否存在
        if (isUserExists(username)) {
            std::string errorMsg = "注册失败: 用户名 '" + username + "' 已存在";
            LOG_ERROR("{}", errorMsg);
            response->set("status", "error");
            response->set("message", errorMsg);
            return response;
        }
        
        // 用户不存在，创建新用户
        bool success = registerUser(username, password, defaultGame, isPro);
        
        if (success) {
            LOG_INFO("用户 '{}' 注册成功", username);
            
            // 有游戏列表时，为所有游戏创建配置
            if (hasGameList && !gameNames.empty()) {
                // 创建游戏配置
                if (!createInitialConfigs(username, gameNames)) {
                    LOG_WARNING("为用户 '{}' 创建部分游戏配置失败", username);
                }
            }
            
            // 获取用户的所有配置信息
            Poco::JSON::Object::Ptr userConfigs = getUserAllConfigs(username);
            
            response->set("status", "success");
            response->set("message", "用户注册成功");
            
            // 构建data对象
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            
            // 构建userInfo对象
            Poco::JSON::Object::Ptr userInfo = new Poco::JSON::Object();
            userInfo->set("username", username);
            userInfo->set("token", "blweb_token_" + username);
            userInfo->set("isPro", isPro);
            
            data->set("userInfo", userInfo);
            
            // 添加homeConfig到data对象
            if (userConfigs->has("homeConfig")) {
                data->set("homeConfig", userConfigs->getObject("homeConfig"));
            }
            
            response->set("data", data);
        } else {
            std::string error = "创建用户失败: " + getLastError();
            LOG_ERROR("{}", error);
            response->set("status", "error");
            response->set("message", error);
        }
    }
    catch (const std::exception& ex) {
        LOG_ERROR("处理用户注册请求时发生错误: {}", ex.what());
        response->set("status", "error");
        response->set("message", std::string("错误: ") + ex.what());
    }
    
    return response;
}

// 获取最后一次错误信息
std::string RegisterDB::getLastError() const {
    return m_lastError;
}

} // namespace db
} // namespace blweb 


