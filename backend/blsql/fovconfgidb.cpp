#include "fovconfgidb.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "mysql_db.h"

#include <Poco/Data/Session.h>
#include <Poco/Data/Statement.h>
#include <Poco/Data/RecordSet.h>
#include <Poco/Exception.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <Poco/JSON/Parser.h>
#include <Poco/JSON/Object.h>
#include <Poco/Dynamic/Var.h>

namespace blweb {
namespace db {

// 单例实现
FovConfigDB& FovConfigDB::getInstance() {
    static FovConfigDB instance;
    return instance;
}

// 构造函数
FovConfigDB::FovConfigDB() : m_lastError("") {
    // 空实现
}

// 析构函数
FovConfigDB::~FovConfigDB() {
    // 析构函数实现
    LOG_INFO("FovConfigDB已销毁");
}

// 初始化
bool FovConfigDB::init() {
    // 继续使用MySQLDB进行初始化和连接管理
    return MySQLDB::getInstance().init();
}

// 创建用户视野配置
bool FovConfigDB::createFovConfig(const std::string& username, const std::string& gameName) {
    // 当前时间作为创建时间和更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    
    LOG_INFO("为用户'{}', 游戏'{}'创建视野配置", username, gameName);
    
    // 检查数据库连接状态
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        LOG_ERROR("数据库未连接，尝试重新连接");
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    std::vector<std::string> params = {
        username, gameName, "90.0", "1000", timeStr, timeStr
    };
    
    std::string sql = "INSERT INTO fov_configs (username, game_name, fov, fov_time, created_at, updated_at) "
                      "VALUES (?, ?, ?, ?, ?, ?)";
    
    bool result = executeUpdateParams(sql, params);
    if (!result) {
        m_lastError = "创建FOV配置失败: " + db.getLastError();
        LOG_ERROR("{}", m_lastError);
    } else {
        LOG_INFO("成功创建FOV配置记录");
    }
    
    return result;
}

// 执行SQL查询
FovConfigDB::Rows FovConfigDB::executeQuery(const std::string& sql) {
    return MySQLDB::getInstance().executeQuery(sql);
}

// 执行带参数的SQL查询
FovConfigDB::Rows FovConfigDB::executeQueryParams(const std::string& sql, const std::vector<std::string>& params) {
    return MySQLDB::getInstance().executeQueryParams(sql, params);
}

// 执行SQL更新操作
bool FovConfigDB::executeUpdate(const std::string& sql) {
    return MySQLDB::getInstance().executeUpdate(sql);
}

// 执行带参数的SQL更新操作
bool FovConfigDB::executeUpdateParams(const std::string& sql, const std::vector<std::string>& params) {
    return MySQLDB::getInstance().executeUpdateParams(sql, params);
}

// 转义SQL字符串，防止SQL注入
std::string FovConfigDB::escapeString(const std::string& str) {
    return MySQLDB::getInstance().escapeString(str);
}

// 获取用户在特定游戏下的FOV配置
FovConfigDB::Row FovConfigDB::getFovConfig(const std::string& username, const std::string& gameName) {
    Row emptyRow;
    
    // 防止SQL注入，使用参数化查询
    std::vector<std::string> params = {username, gameName};
    
    std::string sql = "SELECT * FROM fov_configs WHERE username = ? AND game_name = ?";
    Rows results = executeQueryParams(sql, params);
    
    if (!results.empty()) {
        return results[0];
    }
    
    return emptyRow;
}

// 更新或创建用户的FOV配置
bool FovConfigDB::updateFovConfig(
    const std::string& username,
    const std::string& gameName,
    float fov,
    int fovTime
) {
    LOG_INFO("开始更新数据库中FOV配置 - 用户: '{}', 游戏: '{}', FOV: {}, FOV时间: {}", 
             username, gameName, fov, fovTime);
    
    // 检查数据库连接状态
    blweb::MySQLDB& db = blweb::MySQLDB::getInstance();
    if (!db.isConnected()) {
        LOG_ERROR("数据库未连接，尝试重新连接");
        if (!db.init()) {
            m_lastError = "数据库连接失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    }
    
    // 当前时间作为更新时间
    Poco::DateTime now;
    std::string timeStr = Poco::DateTimeFormatter::format(now, "%Y-%m-%d %H:%M:%S");
    LOG_DEBUG("生成的时间戳: {}", timeStr);
    // 检查该用户的该游戏配置是否已存在
    LOG_DEBUG("检查配置是否已存在: 用户='{}', 游戏='{}'", username, gameName);
    Row existingConfig = getFovConfig(username, gameName);
    
    bool result = false;
    if (existingConfig.empty()) {
        LOG_INFO("不存在现有配置，创建新记录");
        // 不存在，创建新记录
        std::vector<std::string> params = {
            username, 
            gameName, 
            std::to_string(fov), 
            std::to_string(fovTime), 
            timeStr, 
            timeStr
        };
        
        std::string sql = "INSERT INTO fov_configs (username, game_name, fov, fov_time, created_at, updated_at) "
                          "VALUES (?, ?, ?, ?, ?, ?)";
        
        LOG_DEBUG("执行INSERT SQL: {}", sql);
        LOG_DEBUG("参数: username='{}', game_name='{}', fov='{}', fov_time='{}', created_at='{}', updated_at='{}'",
                 params[0], params[1], params[2], params[3], params[4], params[5]);
        
        result = executeUpdateParams(sql, params);
        if (!result) {
            m_lastError = "INSERT失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
        } else {
            LOG_INFO("成功创建FOV配置记录");
        }
    } else {
        LOG_INFO("找到现有配置，更新记录");
        
        // 打印现有配置信息
        LOG_DEBUG("现有配置: ");
        for (const auto& pair : existingConfig) {
            LOG_DEBUG("  {} = {}", pair.first, pair.second);
        }
        
        // 已存在，更新记录
        std::vector<std::string> params = {
            std::to_string(fov), 
            std::to_string(fovTime), 
            timeStr,
            username, 
            gameName
        };
        
        std::string sql = "UPDATE fov_configs SET fov = ?, fov_time = ?, updated_at = ? "
                          "WHERE username = ? AND game_name = ?";
        
        LOG_DEBUG("执行UPDATE SQL: {}", sql);
        LOG_DEBUG("参数: fov='{}', fov_time='{}', updated_at='{}', username='{}', game_name='{}'",
                 params[0], params[1], params[2], params[3], params[4]);
        
        result = executeUpdateParams(sql, params);
        if (!result) {
            m_lastError = "UPDATE失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
        } else {
            LOG_INFO("成功更新FOV配置记录");
        }
    }
    
    if (result) {
        // 验证更新是否生效
        LOG_DEBUG("验证更新是否生效");
        Row updatedConfig = getFovConfig(username, gameName);
        if (!updatedConfig.empty()) {
            LOG_INFO("验证成功，配置已更新");
            for (const auto& pair : updatedConfig) {
                LOG_DEBUG("  {} = {}", pair.first, pair.second);
            }
        } else {
            LOG_WARNING("无法验证配置更新，查询不到更新后的记录");
        }
    }
    
    return result;
}

// 获取最后一次错误信息
std::string FovConfigDB::getLastError() const {
    return m_lastError;
}

} // namespace db
} // namespace blweb