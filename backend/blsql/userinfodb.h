#ifndef USERINFODB_H
#define USERINFODB_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <mutex>
#include "Poco/JSON/Object.h"
#include "Poco/JSON/Array.h"

namespace blweb {
namespace db {

// 使用Poco::JSON
using json = Poco::JSON::Object::Ptr;

// 用户信息数据库类
class UserInfoDB {
public:
    // 行和结果集的类型定义
    using Row = std::map<std::string, std::string>;
    using Rows = std::vector<Row>;
    
    // 获取单例实例
    static UserInfoDB& getInstance();
    
    // 构造函数
    UserInfoDB();
    
    // 析构函数
    ~UserInfoDB();
    
    // 初始化数据库连接
    bool init();
    
    // 关闭数据库连接
    void close();
    
    // 执行SQL查询
    Rows executeQuery(const std::string& sql);
    
    // 获取用户所有配置
    Poco::JSON::Object::Ptr getAllUserConfig(const std::string& username);
    
    // 获取最后一次错误信息
    std::string getLastError() const { return m_lastError; }
    
    // 单例实例
    static std::unique_ptr<UserInfoDB> s_instance;
    
    // 互斥锁，保证线程安全
    static std::mutex s_mutex;
    
    // 数据库连接状态
    bool m_connected;
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace db
} // namespace blweb

#endif // USERINFODB_H
