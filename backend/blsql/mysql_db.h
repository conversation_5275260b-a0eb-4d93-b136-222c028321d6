#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>

// 数据库相关头文件
#include <Poco/Data/MySQL/Connector.h>
#include <Poco/Data/MySQL/MySQLException.h>
#include <Poco/Data/MySQL/SessionImpl.h>
#include <Poco/Data/RecordSet.h>
#include <Poco/Data/Session.h>

namespace blweb {

/**
 * @brief MySQL数据库连接和操作类
 * 
 * 这个类提供MySQL数据库的连接和基本操作功能，实现单例模式。
 * 使用Poco::Data::MySQL API实现数据库操作。
 * 简化后的版本只提供基础数据库操作，业务逻辑由调用方直接实现。
 */
class MySQLDB {
public:
    // 行数据类型定义
    using Row = std::map<std::string, std::string>;
    using Rows = std::vector<Row>;
    
    /**
     * @brief 获取单例实例
     * @return MySQLDB& 单例实例的引用
     */
    static MySQLDB& getInstance();
    
    /**
     * @brief 构造函数
     */
    MySQLDB();
    
    /**
     * @brief 析构函数
     */
    ~MySQLDB();
    
    /**
     * @brief 初始化数据库连接
     * @return bool 成功返回true，否则返回false
     */
    bool init();
    
    /**
     * @brief 关闭数据库连接
     */
    void close();
    
    /**
     * @brief 执行SQL查询
     * @param sql SQL查询语句
     * @return Rows 查询结果行
     */
    Rows executeQuery(const std::string& sql);
    
    /**
     * @brief 执行SQL查询并绑定参数
     * @param sql SQL查询语句
     * @param params 参数值数组
     * @return Rows 查询结果行
     */
    Rows executeQueryParams(const std::string& sql, const std::vector<std::string>& params);
    
    /**
     * @brief 执行SQL更新/插入/删除操作
     * @param sql SQL语句
     * @return bool 成功返回true，否则返回false
     */
    bool executeUpdate(const std::string& sql);
    
    /**
     * @brief 执行SQL更新/插入/删除操作并绑定参数
     * @param sql SQL语句
     * @param params 参数值数组
     * @return bool 成功返回true，否则返回false
     */
    bool executeUpdateParams(const std::string& sql, const std::vector<std::string>& params);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
    /**
     * @brief 检查是否已连接到数据库
     * @return bool 已连接返回true，否则返回false
     */
    bool isConnected() const { return m_connected; }
    
    /**
     * @brief 获取数据库会话指针
     * @return Poco::Data::Session* 数据库会话指针，未连接时返回nullptr
     */
    Poco::Data::Session* getSession() { return m_session.get(); }
    
    /**
     * @brief 转义SQL字符串，防止SQL注入
     * @param str 需要转义的字符串
     * @return std::string 转义后的字符串
     */
    std::string escapeString(const std::string& str);
    
    // 数据库连接参数
    std::string m_host;
    int m_port;
    std::string m_database;
    std::string m_username;
    std::string m_password;
    
    // MySQL连接
    std::unique_ptr<Poco::Data::Session> m_session;
    
    // 最后一次错误信息
    std::string m_lastError;
    
    // 数据库连接状态
    bool m_connected;
};

} // namespace blweb