#pragma once

#include <string>
#include <vector>
#include <map>
#include <Poco/JSON/Object.h>
#include <Poco/JSON/Array.h>
#include "mysql_db.h"

namespace blweb {
namespace db {

/**
 * @class FunctionConfigsDB
 * @brief 游戏功能配置数据库操作类
 * 
 * 负责处理功能配置相关的数据库操作，包括批量添加和修改功能配置
 */
class FunctionConfigsDB {
public:
    using Row = std::map<std::string, std::string>;
    using Rows = std::vector<Row>;
    
    /**
     * @brief 获取FunctionConfigsDB单例实例
     * @return FunctionConfigsDB& 单例引用
     */
    static FunctionConfigsDB& getInstance();
    
    /**
     * @brief 构造函数
     */
    FunctionConfigsDB();
    
    /**
     * @brief 析构函数
     */
    ~FunctionConfigsDB();
    
    /**
     * @brief 创建用户功能配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return bool 创建成功返回true，失败返回false
     */
    bool createFunctionConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 创建或更新功能配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param configs 功能配置数组
     * @return bool 成功返回true，否则返回false
     */
    bool updateFunctionConfigs(
        const std::string& username,
        const std::string& gameName,
        const Poco::JSON::Array::Ptr& configs
    );
    
    /**
     * @brief 获取指定用户和游戏的所有功能配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Rows 包含功能配置的行数据
     */
    Rows getFunctionConfigs(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    /**
     * @brief 更新单个功能配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param configObject 功能配置对象
     * @return bool 成功返回true，否则返回false
     */
    bool updateSingleConfig(
        const std::string& username,
        const std::string& gameName,
        const Poco::JSON::Object::Ptr& configObject
    );
    
    /**
     * @brief 获取trigger_switch和enabled的布尔值并转为数据库整数值
     * @param jsonObj JSON对象
     * @param fieldName 字段名
     * @param defaultValue 默认值
     * @return string 用于保存到数据库的字符串值
     */
    std::string getBooleanFieldAsString(
        const Poco::JSON::Object::Ptr& jsonObj,
        const std::string& fieldName,
        bool defaultValue
    );
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace db
} // namespace blweb 