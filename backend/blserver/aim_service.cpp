#include "aim_service.h"
#include "../blsql/aim_configs_db.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include <Poco/JSON/Parser.h>
#include <iomanip>
#include <Poco/Dynamic/Var.h>

namespace blweb {
namespace services {

AimService& AimService::getInstance() {
    static AimService instance;
    return instance;
}

AimService::AimService() : m_lastError("") {
    // 构造函数
}

AimService::~AimService() {
    // 析构函数
}

Poco::JSON::Object::Ptr AimService::handleAimModify(
    const std::string& username,
    const std::string& gameName,
    const Poco::JSON::Object::Ptr& jsonParams
) {
    Poco::JSON::Object::Ptr response = new Poco::JSON::Object();
    if (username.empty() || gameName.empty()) {
        response->set("code", 400);
        response->set("msg", "用户名或游戏名不能为空");
        LOG_ERROR("AimService::handleAimModify - 用户名或游戏名为空");
        return response;
    }
    
    // 验证参数
    if (!validateAimParams(jsonParams)) {
        response->set("code", 400);
        response->set("msg", "瞄准配置参数无效");
        LOG_ERROR("AimService::handleAimModify - 瞄准配置参数验证失败");
        return response;
    }
    
    // 提取配置
    std::map<std::string, std::string> configs = extractAimConfigs(jsonParams);
    
    if (configs.empty()) {
        response->set("code", 400);
        response->set("msg", "没有有效的瞄准配置参数");
        LOG_ERROR("AimService::handleAimModify - 未找到有效瞄准配置参数");
        return response;
    }
    
    // 更新配置到数据库
    auto& aimConfigsDB = sqlserver::AimConfigsDB::getInstance();
    bool result = aimConfigsDB.updateAimConfig(username, gameName, configs);
    
    if (result) {
        response->set("code", 200);
        response->set("msg", "瞄准配置更新成功");
        LOG_INFO("AimService::handleAimModify - 用户[%s]在游戏[%s]的瞄准配置更新成功", 
            username.c_str(), gameName.c_str());
    } else {
        response->set("code", 500);
        response->set("msg", "瞄准配置更新失败: " + aimConfigsDB.getLastError());
        LOG_ERROR("AimService::handleAimModify - 用户[%s]在游戏[%s]的瞄准配置更新失败: %s", 
            username.c_str(), gameName.c_str(), aimConfigsDB.getLastError().c_str());
    }
    
    return response;
}

Poco::JSON::Object::Ptr AimService::getAimConfig(
    const std::string& username,
    const std::string& gameName
) {
    Poco::JSON::Object::Ptr response = new Poco::JSON::Object();
    
    if (username.empty() || gameName.empty()) {
        response->set("code", 400);
        response->set("msg", "用户名或游戏名不能为空");
        LOG_ERROR("AimService::getAimConfig - 用户名或游戏名为空");
        return response;
    }
    
    // 从数据库获取配置
    auto& aimConfigsDB = sqlserver::AimConfigsDB::getInstance();
    std::map<std::string, std::string> configs = aimConfigsDB.getAimConfig(username, gameName);
    
    if (configs.empty() && !aimConfigsDB.getLastError().empty()) {
        response->set("code", 500);
        response->set("msg", "获取瞄准配置失败: " + aimConfigsDB.getLastError());
        LOG_ERROR("AimService::getAimConfig - 获取用户[%s]在游戏[%s]的瞄准配置失败: %s", 
            username.c_str(), gameName.c_str(), aimConfigsDB.getLastError().c_str());
        return response;
    }
    
    // 创建数据对象
    Poco::JSON::Object::Ptr dataObj = new Poco::JSON::Object();
    dataObj->set("username", username);
    dataObj->set("gameName", gameName);
    
    // 简化处理逻辑，直接从数据库configs映射中获取值
    if (!configs.empty()) {
        // 如果数据库有配置，直接转换并设置
        for (const auto& config : configs) {
            const std::string& key = config.first;
            const std::string& value = config.second;
            
            try {
                // 所有aim配置都是double类型，直接转换
                double numValue = std::stod(value);
                dataObj->set(key, numValue);
            } catch (...) {
                // 转换失败时使用全局默认值
                if (key == "aimRange") dataObj->set(key, webui::aim::g_aim_range);
                else if (key == "trackRange") dataObj->set(key, webui::aim::g_track_range);
                else if (key == "headHeight") dataObj->set(key, webui::aim::g_head_height);
                else if (key == "neckHeight") dataObj->set(key, webui::aim::g_neck_height);
                else if (key == "chestHeight") dataObj->set(key, webui::aim::g_chest_height);
                else if (key == "headRangeX") dataObj->set(key, webui::aim::g_head_range_x);
                else if (key == "headRangeY") dataObj->set(key, webui::aim::g_head_range_y);
                else if (key == "neckRangeX") dataObj->set(key, webui::aim::g_neck_range_x);
                else if (key == "neckRangeY") dataObj->set(key, webui::aim::g_neck_range_y);
                else if (key == "chestRangeX") dataObj->set(key, webui::aim::g_chest_range_x);
                else if (key == "chestRangeY") dataObj->set(key, webui::aim::g_chest_range_y);
            }
        }
        
        LOG_INFO("AimService::getAimConfig - 获取用户[%s]在游戏[%s]的瞄准配置成功", 
            username.c_str(), gameName.c_str());
    } else {
        // 如果数据库没有配置，使用全局变量作为默认值
        dataObj->set("aimRange", webui::aim::g_aim_range);
        dataObj->set("trackRange", webui::aim::g_track_range);
        dataObj->set("headHeight", webui::aim::g_head_height);
        dataObj->set("neckHeight", webui::aim::g_neck_height);
        dataObj->set("chestHeight", webui::aim::g_chest_height);
        dataObj->set("headRangeX", webui::aim::g_head_range_x);
        dataObj->set("headRangeY", webui::aim::g_head_range_y);
        dataObj->set("neckRangeX", webui::aim::g_neck_range_x);
        dataObj->set("neckRangeY", webui::aim::g_neck_range_y);
        dataObj->set("chestRangeX", webui::aim::g_chest_range_x);
        dataObj->set("chestRangeY", webui::aim::g_chest_range_y);
        
        LOG_INFO("AimService::getAimConfig - 用户[%s]在游戏[%s]没有瞄准配置，使用全局变量默认值", 
            username.c_str(), gameName.c_str());
    }
    
    response->set("code", 200);
    response->set("msg", "获取瞄准配置成功");
    response->set("data", dataObj);
    
    return response;
}

bool AimService::validateAimParams(const Poco::JSON::Object::Ptr& jsonParams) {
    if (jsonParams.isNull()) {
        return false;
    }
    
    // 检查是否至少有一个有效参数
    const std::vector<std::string> validParams = {
        "aimRange", "trackRange", "headHeight", "neckHeight", "chestHeight",
        "headRangeX", "headRangeY", "neckRangeX", "neckRangeY", "chestRangeX", "chestRangeY"
    };
    
    for (const auto& param : validParams) {
        if (jsonParams->has(param)) {
            return true;
        }
    }
    
    return false;
}

std::map<std::string, std::string> AimService::extractAimConfigs(const Poco::JSON::Object::Ptr& jsonParams) {
    std::map<std::string, std::string> configs;
    
    // 可能的配置参数名称列表
    const std::vector<std::string> validParams = {
        "aimRange", "trackRange", "headHeight", "neckHeight", "chestHeight",
        "headRangeX", "headRangeY", "neckRangeX", "neckRangeY", "chestRangeX", "chestRangeY"
    };
    
    for (const auto& param : validParams) {
        if (jsonParams->has(param)) {
            try {
                // 尝试获取值
                Poco::Dynamic::Var value = jsonParams->get(param);
                if (value.isNumeric()) {
                    double numValue = value.convert<double>();
                    std::ostringstream ss;
                    ss << std::fixed << std::setprecision(6) << numValue;
                    configs[param] = ss.str();
                } else if (value.isString()) {
                    configs[param] = value.toString();
                } else if (value.isBoolean()) {
                    configs[param] = value.convert<bool>() ? "true" : "false";
                }
            } catch (const std::exception& e) {
                LOG_ERROR("AimService::extractAimConfigs - 解析参数[%s]时出错: %s", 
                    param.c_str(), e.what());
            }
        }
    }
    
    return configs;
}

std::string AimService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 