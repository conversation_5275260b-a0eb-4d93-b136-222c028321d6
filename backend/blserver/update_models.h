#ifndef UPDATE_MODELS_H
#define UPDATE_MODELS_H

#include <string>
#include <Poco/JSON/Object.h>

namespace blweb {
namespace utils {

/**
 * @brief 通用函数，根据不同的action更新对应的全局变量
 * 
 * 此函数由各个handler直接调用，而不是由message_handlers.cpp统一调用。
 * 这样设计的目的是让每个handler能够更灵活地控制全局变量的更新时机。
 * 每个handler负责在适当的时机调用此函数更新对应的全局变量。
 * 
 * @param action 操作类型，例如"aim_modify"、"aim_read"等
 * @param content JSON参数对象，包含要更新的参数值
 * @return 是否成功更新全局变量
 */
bool updateGlobalModels(const std::string& action, const Poco::JSON::Object::Ptr& content);

} // namespace utils
} // namespace blweb

#endif // UPDATE_MODELS_H
