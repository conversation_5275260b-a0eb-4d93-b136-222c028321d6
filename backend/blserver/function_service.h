#pragma once

#include <string>
#include <map>
#include <Poco/JSON/Object.h>
#include <Poco/JSON/Array.h>
#include "../blsql/function_configs_db.h"

namespace blweb {
namespace services {

/**
 * @class FunctionService
 * @brief 功能配置服务类
 * 
 * 负责功能配置相关的业务逻辑处理，包括配置的批量更新和获取
 */
class FunctionService {
public:
    /**
     * @brief 获取FunctionService单例实例
     * @return FunctionService& 单例引用
     */
    static FunctionService& getInstance();
    
    /**
     * @brief 构造函数
     */
    FunctionService();
    
    /**
     * @brief 析构函数
     */
    virtual ~FunctionService();
    
    /**
     * @brief 批量更新功能配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param configs 功能配置JSON数组
     * @return Poco::JSON::Object::Ptr 结果对象，包含状态和消息
     */
    Poco::JSON::Object::Ptr updateFunctionConfigs(
        const std::string& username,
        const std::string& gameName,
        const Poco::JSON::Array::Ptr& configs
    );
    
    /**
     * @brief 获取功能配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含功能配置的JSON对象
     */
    Poco::JSON::Object::Ptr getFunctionConfigs(
        const std::string& username,
        const std::string& gameName
    );
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    /**
     * @brief 将数据库行转换为JSON对象
     * @param row 数据库行
     * @return Poco::JSON::Object::Ptr JSON对象
     */
    Poco::JSON::Object::Ptr rowToJson(const blweb::db::FunctionConfigsDB::Row& row);
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace services
} // namespace blweb 