#include "home_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blsql/registerdb.h"

#include <Poco/JSON/Parser.h>
#include <Poco/Dynamic/Var.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace blweb {
namespace services {

namespace {
    const int SUCCESS_CODE = 200;
    const int ERROR_CODE = 500;
    const int BAD_REQUEST_CODE = 400;
    const std::string TIME_FORMAT = "%Y-%m-%d %H:%M:%S";
    const std::string ISO_TIME_FORMAT = "%Y-%m-%dT%H:%M:%SZ";
    
    bool validateInput(const std::string& username, const std::string& gameName, const std::string& cardKey) {
        return !username.empty() && !gameName.empty() && !cardKey.empty();
    }
    
    std::string getCurrentTimeString(const std::string& format = TIME_FORMAT) {
        Poco::DateTime now;
        return Poco::DateTimeFormatter::format(now, format);
    }
    
    Poco::JSON::Object::Ptr createErrorResponse(int code, const std::string& message) {
        auto result = new Poco::JSON::Object();
        result->set("code", code);
        result->set("msg", message);
        return result;
    }
    
    Poco::JSON::Object::Ptr createSuccessResponse(const Poco::JSON::Object::Ptr& data) {
        auto result = new Poco::JSON::Object();
        result->set("code", SUCCESS_CODE);
        result->set("msg", "操作成功");
        result->set("data", data);
        return result;
    }
}

// 单例实现
HomeService& HomeService::getInstance() {
    static HomeService instance;
    return instance;
}

// 构造函数
HomeService::HomeService() : m_lastError("") {
    LOG_INFO("HomeService已初始化");
}

// 析构函数
HomeService::~HomeService() {
    LOG_INFO("HomeService已销毁");
}

// 更新用户的首页配置
bool HomeService::updateHomeConfig(const std::string& username, const std::string& gameName, const std::string& cardKey) {
    try {
        LOG_INFO("更新首页配置 - 用户: {}, 游戏: {}", username, gameName);
        
        if (!validateInput(username, gameName, cardKey)) {
            m_lastError = "参数验证失败：用户名、游戏名称和卡密不能为空";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        std::string timeStr = getCurrentTimeString();
        auto& db = blweb::db::RegisterDB::getInstance();
        
        std::string sql = "UPDATE home_configs SET game_name = ?, card_key = ?, updated_at = ? WHERE username = ?";
        std::vector<std::string> params = {gameName, cardKey, timeStr, username};
        
        bool result = db.executeUpdateParams(sql, params);
        if (!result) {
            m_lastError = "数据库更新失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        LOG_INFO("首页配置更新成功");
        return true;
    }
    catch (const std::exception& ex) {
        m_lastError = "更新异常: " + std::string(ex.what());
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取用户的首页配置
Poco::JSON::Object::Ptr HomeService::getHomeConfig(const std::string& username, const std::string& gameName) {
    try {
        LOG_INFO("获取首页配置 - 用户: {}", username);
        
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            return createErrorResponse(BAD_REQUEST_CODE, m_lastError);
        }
        
        auto& db = blweb::db::RegisterDB::getInstance();
        std::string sql = "SELECT username, game_name, card_key, created_at, updated_at FROM home_configs WHERE username = ?";
        std::vector<std::string> params = {username};
        
        auto queryResult = db.executeQueryParams(sql, params);
        auto data = new Poco::JSON::Object();
        data->set("username", username);
        
        if (!gameName.empty()) {
            data->set("gameName", gameName);
        }
        
        if (queryResult.empty()) {
            LOG_INFO("未找到用户配置，返回默认值");
            std::string timeStr = getCurrentTimeString(ISO_TIME_FORMAT);
            data->set("cardKey", "");
            data->set("createdAt", timeStr);
            data->set("updatedAt", timeStr);
            data->set("exists", false);
        } else {
            const auto& row = queryResult[0];
            if (gameName.empty() && row.find("game_name") != row.end()) {
                data->set("gameName", row.at("game_name"));
            }
            data->set("cardKey", row.at("card_key"));
            data->set("createdAt", row.at("created_at"));
            data->set("updatedAt", row.at("updated_at"));
            data->set("exists", true);
        }
        
        LOG_INFO("获取首页配置成功");
        return createSuccessResponse(data);
    } 
    catch (const std::exception& ex) {
        m_lastError = "获取配置异常: " + std::string(ex.what());
        LOG_ERROR("{}", m_lastError);
        return createErrorResponse(ERROR_CODE, m_lastError);
    }
}

// 获取最后一次错误信息
std::string HomeService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 