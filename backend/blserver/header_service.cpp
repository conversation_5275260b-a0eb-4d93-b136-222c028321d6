#include "header_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"

namespace blweb {
namespace services {

// 单例实现
HeaderService& HeaderService::getInstance() {
    static HeaderService instance;
    return instance;
}

// 构造函数
HeaderService::HeaderService() : m_lastError("") {
    LOG_INFO("HeaderService已初始化");
}

// 析构函数
HeaderService::~HeaderService() {
    LOG_INFO("HeaderService已销毁");
}

// 获取最后一次错误信息
std::string HeaderService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 