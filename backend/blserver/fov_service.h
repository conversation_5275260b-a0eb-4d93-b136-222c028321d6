#pragma once

#include <string>
#include <map>
#include <Poco/JSON/Object.h>

namespace blweb {
namespace services {

/**
 * @class FovService
 * @brief FOV视野范围服务类
 * 
 * 负责FOV相关的业务逻辑处理，包括配置获取和更新
 */
class FovService {
public:
    /**
     * @brief 获取FovService单例实例
     * @return FovService& 单例引用
     */
    static FovService& getInstance();
    
    /**
     * @brief 析构函数
     */
    ~FovService();
    
    /**
     * @brief 更新用户的FOV配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param fov 视野范围
     * @param fovTime 视野时间
     * @return bool 成功返回true，否则返回false
     */
    bool updateFovConfig(
        const std::string& username,
        const std::string& gameName,
        float fov,
        int fovTime
    );
    
    /**
     * @brief 获取用户的FOV配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含FOV配置的JSON对象
     */
    Poco::JSON::Object::Ptr getFovConfig(
        const std::string& username,
        const std::string& gameName
    );
    
    /**
     * @brief 开始FOV测量过程
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含操作结果的JSON对象
     */
    Poco::JSON::Object::Ptr startFovMeasurement(
        const std::string& username,
        const std::string& gameName
    );
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    /**
     * @brief 构造函数（私有，单例模式）
     */
    FovService();
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace services
} // namespace blweb 