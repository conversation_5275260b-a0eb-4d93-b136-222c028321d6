#pragma once

#include <string>
#include <Poco/JSON/Object.h>
#include "../blsql/pid_configs_db.h"

namespace blweb {
namespace services {

/**
 * @class PidService
 * @brief PID配置服务类
 * 
 * 负责PID配置相关的业务逻辑处理，包括配置的更新和获取
 */
class PidService {
public:
    /**
     * @brief 获取PidService单例实例
     * @return PidService& 单例引用
     */
    static PidService& getInstance();
    
    /**
     * @brief 构造函数
     */
    PidService();
    
    /**
     * @brief 析构函数
     */
    virtual ~PidService();
    
    /**
     * @brief 更新PID配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param nearMoveFactor 近端移动速度
     * @param nearStabilizer 近端跟踪速度
     * @param nearResponseRate 近端抖动力度
     * @param nearAssistZone 近端死区大小
     * @param nearResponseDelay 近端回弹速度
     * @param nearMaxAdjustment 近端积分限制
     * @param farFactor 远端系数
     * @param yAxisFactor Y轴系数
     * @param pidRandomFactor PID随机系数
     * @return bool 成功返回true，否则返回false
     */
    bool updatePidConfig(
        const std::string& username,
        const std::string& gameName,
        double nearMoveFactor,
        double nearStabilizer,
        double nearResponseRate,
        double nearAssistZone,
        double nearResponseDelay,
        double nearMaxAdjustment,
        double farFactor,
        double yAxisFactor,
        double pidRandomFactor
    );
    
    /**
     * @brief 获取PID配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含PID配置的JSON对象
     */
    Poco::JSON::Object::Ptr getPidConfig(
        const std::string& username,
        const std::string& gameName
    );
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    /**
     * @brief 将数据库行转换为JSON对象
     * @param row 数据库行
     * @return Poco::JSON::Object::Ptr JSON对象
     */
    Poco::JSON::Object::Ptr rowToJson(const blweb::db::PidConfigsDB::Row& row);
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace services
} // namespace blweb 