#include "pid_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blsql/pid_configs_db.h"

#include <Poco/JSON/Parser.h>
#include <Poco/Dynamic/Var.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace blweb {
namespace services {

// 单例实现
PidService& PidService::getInstance() {
    static PidService instance;
    return instance;
}

// 构造函数
PidService::PidService() : m_lastError("") {
    LOG_INFO("PidService已初始化");
}

// 析构函数
PidService::~PidService() {
    LOG_INFO("PidService已销毁");
}

// 更新PID配置
bool PidService::updatePidConfig(
    const std::string& username,
    const std::string& gameName,
    double nearMoveFactor,
    double nearStabilizer,
    double nearResponseRate,
    double nearAssistZone,
    double nearResponseDelay,
    double nearMaxAdjustment,
    double farFactor,
    double yAxisFactor,
    double pidRandomFactor
) {
    LOG_INFO("更新PID配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    try {
        // 参数验证
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        // 调用数据库层更新配置
        blweb::db::PidConfigsDB& db = blweb::db::PidConfigsDB::getInstance();
        bool success = db.updatePidConfig(
            username, 
            gameName, 
            nearMoveFactor, 
            nearStabilizer, 
            nearResponseRate, 
            nearAssistZone, 
            nearResponseDelay, 
            nearMaxAdjustment,
            farFactor,
            yAxisFactor,
            pidRandomFactor
        );
        
        if (success) {
            LOG_INFO("PID配置更新成功");
            return true;
        } else {
            m_lastError = "更新PID配置失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新PID配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取PID配置
Poco::JSON::Object::Ptr PidService::getPidConfig(
    const std::string& username,
    const std::string& gameName
) {
    LOG_INFO("获取PID配置 - 用户: '{}', 游戏: '{}'", username, gameName);
    
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        // 参数验证
        if (username.empty() || gameName.empty()) {
            LOG_ERROR("用户名或游戏名不能为空");
            result->set("code", 400);
            result->set("msg", "用户名或游戏名不能为空");
            return result;
        }
        
        // 获取数据库数据
        blweb::db::PidConfigsDB& db = blweb::db::PidConfigsDB::getInstance();
        blweb::db::PidConfigsDB::Row row = db.getPidConfig(username, gameName);
        
        // 创建数据对象
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", username);
        data->set("gameName", gameName);
        
        if (row.empty()) {
            LOG_WARNING("找不到PID配置，返回默认值");
            data->set("nearMoveFactor", 1.0);
            data->set("nearStabilizer", 0.5);
            data->set("nearResponseRate", 0.3);
            data->set("nearAssistZone", 3.0);
            data->set("nearResponseDelay", 1.0);
            data->set("nearMaxAdjustment", 2.0);
            data->set("farFactor", 1.0);
            data->set("yAxisFactor", 1.0);
            data->set("pidRandomFactor", 0.5);
            data->set("exists", false);
        } else {
            try {
                Poco::JSON::Object::Ptr configObj = rowToJson(row);
                
                // 复制配置到数据对象
                for (const auto& name : configObj->getNames()) {
                    data->set(name, configObj->get(name));
                }
                
                data->set("exists", true);
                
                // 打印完整的JSON数据用于调试
                std::stringstream ss;
                data->stringify(ss);
                LOG_INFO("PID配置JSON详情: {}", ss.str());
            } catch (const std::exception& ex) {
                LOG_ERROR("处理PID配置数据异常: {}", ex.what());
                result->set("code", 500);
                result->set("msg", std::string("处理PID配置数据异常: ") + ex.what());
                return result;
            }
        }
        
        // 设置成功响应
        result->set("code", 200);
        result->set("msg", "获取PID配置成功");
        result->set("data", data);
        
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取PID配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("code", 500);
        result->set("msg", m_lastError);
    }
    
    return result;
}

// 将数据库行转换为JSON对象
Poco::JSON::Object::Ptr PidService::rowToJson(const blweb::db::PidConfigsDB::Row& row) {
    Poco::JSON::Object::Ptr obj = new Poco::JSON::Object();
    
    try {
        // 字段映射表 - 数据库字段名到JSON字段名的映射
        const std::map<std::string, std::string> fieldMap = {
            {"username", "username"},
            {"game_name", "gameName"},
            {"created_at", "createdAt"},
            {"updated_at", "updatedAt"}
        };
        
        // 设置字符串字段
        for (const auto& mapping : fieldMap) {
            const std::string& dbField = mapping.first;
            const std::string& jsonField = mapping.second;
            
            auto it = row.find(dbField);
            if (it != row.end()) {
                obj->set(jsonField, it->second);
            }
        }
        
        // 设置浮点型字段 - 更新为API文档定义的完整字段映射
        const std::map<std::string, std::string> floatFields = {
            {"near_move_factor", "nearMoveFactor"},
            {"near_stabilizer", "nearStabilizer"},
            {"near_response_rate", "nearResponseRate"},
            {"near_assist_zone", "nearAssistZone"},
            {"near_response_delay", "nearResponseDelay"},
            {"near_max_adjustment", "nearMaxAdjustment"},
            {"far_factor", "farFactor"},
            {"y_axis_factor", "yAxisFactor"},
            {"pid_random_factor", "pidRandomFactor"}
        };
        
        for (const auto& mapping : floatFields) {
            const std::string& dbField = mapping.first;
            const std::string& jsonField = mapping.second;
            
            auto it = row.find(dbField);
            if (it != row.end()) {
                try {
                    obj->set(jsonField, std::stod(it->second));
                } catch (...) {
                    // 根据字段名设置默认值
                    if (jsonField == "nearMoveFactor") obj->set(jsonField, 1.0);
                    else if (jsonField == "nearStabilizer") obj->set(jsonField, 0.5);
                    else if (jsonField == "nearResponseRate") obj->set(jsonField, 0.3);
                    else if (jsonField == "nearAssistZone") obj->set(jsonField, 3.0);
                    else if (jsonField == "nearResponseDelay") obj->set(jsonField, 1.0);
                    else if (jsonField == "nearMaxAdjustment") obj->set(jsonField, 2.0);
                    else if (jsonField == "farFactor") obj->set(jsonField, 1.0);
                    else if (jsonField == "yAxisFactor") obj->set(jsonField, 1.0);
                    else if (jsonField == "pidRandomFactor") obj->set(jsonField, 0.5);
                }
            }
        }
        
        // 确保所有必需字段都存在
        if (!obj->has("farFactor")) obj->set("farFactor", 1.0);
        if (!obj->has("yAxisFactor")) obj->set("yAxisFactor", 1.0);
        if (!obj->has("pidRandomFactor")) obj->set("pidRandomFactor", 0.5);
        
        // 设置exists字段
        obj->set("exists", true);
    } catch (const std::exception& ex) {
        LOG_ERROR("PID行转JSON异常: {}", ex.what());
    }
    
    return obj;
}

// 获取最后一次错误信息
std::string PidService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 