#include "message_handlers.h"
#include "poco_websocket_server.h"
#include "fov_service.h"
#include "register_service.h"
#include "home_service.h"
#include "header_service.h"
#include "function_service.h"
#include "pid_service.h"
#include "aim_service.h"
#include "login_service.h"
#include "fire_service.h"
#include "data_collection_service.h"
#include "update_models.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"

// 包含各个模块处理器
#include "../handlers/pid_handlers.h"
#include "../handlers/function_handlers.h"
#include "../handlers/home_handlers.h"
#include "../handlers/aim_handlers.h"
#include "../handlers/fov_handlers.h"
#include "../handlers/login_handlers.h"
#include "../handlers/register_handlers.h"
#include "../handlers/header_handlers.h"
#include "../handlers/fire_handlers.h"
#include "../handlers/data_collection_handlers.h"

#include <Poco/JSON/Parser.h>
#include <sstream>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

// 引入全局变量命名空间
using namespace webui;

namespace MessageHandlers {

// 初始化标志
static bool g_initialized = false;

// 统一处理消息函数
std::string handleMessage(const std::string& action, const Poco::JSON::Object::Ptr& content) {
    LOG_INFO("处理消息: action={}", action);
    
    try {
        // 根据action类型分发到对应的处理器
        if (action == "home_modify") {
            return Home::handleHomeModify(content);
        }
        else if (action == "function_modify") {
            return Function::handleFunctionModify(content);
        }
        else if (action == "pid_modify") {
            return PID::handlePidModify(content);
        }
        else if (action == "aim_modify") {
            return Aim::handleAimModify(content);
        }
        else if (action == "aim_get") {
            return Aim::handleAimGet(content);
        }
        else if (action == "login_read") {
            return Login::handleLoginRead(content);
        }
        else if (action == "login_verify") {
            // 重定向到handleLoginRead，保持向后兼容
            LOG_INFO("Login_verify操作已重定向到login_read处理");
            return Login::handleLoginRead(content);
        }
        else if (action == "login_info") {
            // 重定向到handleLoginRead，保持向后兼容
            LOG_INFO("Login_info操作已重定向到login_read处理");
            return Login::handleLoginRead(content);
        }
        else if (action == "register_modify") {
            return Register::handleRegisterModify(content);
        }
        else if (action == "fov_modify") {
            return FOV::handleFovModify(content);
        }
        else if (action == "home_read") {
            return Home::handleHomeRead(content);
        }
        else if (action == "function_read") {
            return Function::handleFunctionRead(content);
        }
        else if (action == "pid_read") {
            return PID::handlePidRead(content);
        }
        else if (action == "fov_read") {
            return FOV::handleFovRead(content);
        }
        else if (action == "aim_read") {
            return Aim::handleAimRead(content);
        }
        else if (action == "fire_read") {
            return Fire::handleFireRead(content);
        }
        else if (action == "data_collection_read") {
            return DataCollection::handleDataCollectionRead(content);
        }
        else if (action == "data_collection_fetch") {
            return DataCollection::handleDataCollectionFetch(content);
        }
        else if (action == "data_collection_upload") {
            return DataCollection::handleDataCollectionUpload(content);
        }
        else if (action == "fire_modify") {
            return Fire::handleFireModify(content);
        }
        else if (action == "data_collection_modify") {
            return DataCollection::handleDataCollectionModify(content);
        }
        else if (action == "status_bar_query") {
            return Header::handleStatusBarQuery(content);
        }
        else if (action == "version_update") {
            return Header::handleVersionUpdate(content);
        }
        else if (action == "heartbeat") {
            return Header::handleHeartbeat(content);
        }
        else if (action == "fov_measurement_start") {
            return FOV::handleFovMeasurementStart(content);
        }
        else {
            // 未知action类型
            LOG_WARNING("未知的action类型: {}", action);
            Poco::JSON::Object response;
            response.set("status", "error");
            response.set("message", "不支持的操作类型: " + action);
        
        // 将响应转换为字符串
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
        }
    }
    catch (const Poco::Exception& e) {
        LOG_ERROR("处理消息Poco异常: {}", e.displayText());
        
        // 构造错误响应
        Poco::JSON::Object response;
        response.set("status", "error");
        response.set("action", action);
        response.set("message", std::string("处理消息异常: ") + e.displayText());
        
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
    }
    catch (const std::exception& e) {
        LOG_ERROR("处理消息标准异常: {}", e.what());
        
        // 构造错误响应
        Poco::JSON::Object response;
        response.set("status", "error");
        response.set("action", action);
        response.set("message", std::string("处理消息异常: ") + e.what());
        
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
    }
    catch (...) {
        LOG_ERROR("处理消息时发生未知异常");
        
        // 构造错误响应
        Poco::JSON::Object response;
        response.set("status", "error");
        response.set("action", action);
        response.set("message", "处理消息时发生未知异常");
        
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
    }
}

// 初始化消息处理器
void initialize() {
    if (!g_initialized) {
        LOG_INFO("初始化消息处理器");
        g_initialized = true;
    }
}

// 检查消息处理器是否已初始化
bool isInitialized() {
    return g_initialized;
}

} // namespace MessageHandlers 