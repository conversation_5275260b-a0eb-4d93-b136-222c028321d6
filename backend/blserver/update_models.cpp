#include "update_models.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "login_service.h"

#include <sstream>

namespace blweb {
namespace utils {

/**
 * @brief 通用函数，根据不同的action更新对应的全局变量
 * @param action 操作类型
 * @param content JSON参数对象
 * @return 是否成功更新全局变量
 */
bool updateGlobalModels(const std::string& action, const Poco::JSON::Object::Ptr& content) {
    try {
        // 记录更新操作
        std::ostringstream oss;
        content->stringify(oss);
        LOG_INFO("更新全局变量 - action: {}, 参数: {}", action, oss.str());
        if (action == "home_modify") {
            webui::home::g_username = content->optValue("username", webui::home::g_username);
            webui::home::g_game_name = content->optValue("gameName", webui::home::g_game_name);
            webui::home::g_card_key = content->optValue("cardKey", webui::home::g_card_key);
            jfkm_config::g_card_key = webui::home::g_card_key;
            
            if (content->has("isPro")) {
                webui::header::isPro = content->getValue<bool>("isPro");
            }
            
            yolo_config::g_reload_model = true;
            
            // 根据新的游戏名称更新YOLO类别数量
            yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            
            LOG_INFO("home_modify: 更新全局变量完成 - username={}, gameName={}, isPro={}", 
                    webui::home::g_username, webui::home::g_game_name, webui::header::isPro);
            
        } else if (action == "function_modify") {
            // 更新功能相关的全局变量
            if (content->has("aiMode")) {
                webui::function::g_ai_mode = content->getValue<std::string>("aiMode");
            }
            
            if (content->has("lockPosition")) {
                webui::function::g_lock_position = content->getValue<std::string>("lockPosition");
            }
            
            if (content->has("hotkey")) {
                webui::function::g_hotkey = content->getValue<std::string>("hotkey");
            }
            
            if (content->has("activeConfigIndex")) {
                webui::function::g_active_config_index = content->getValue<int>("activeConfigIndex");
            }
            
            // 更新预设配置列表
            if (content->isArray("configs")) {
                auto configsArray = content->getArray("configs");
                int configSize = configsArray->size();
                
                // 确保不超出预设配置数组大小
                int maxPresetSize = static_cast<int>(webui::function::PRESET_CONFIGS.size());
                int updateSize = std::min(configSize, maxPresetSize);
                
                LOG_INFO("更新功能预设配置，收到{}个配置，将更新{}个", configSize, updateSize);
                
                for (int i = 0; i < updateSize; ++i) {
                    if (configsArray->isObject(i)) {
                        auto configObj = configsArray->getObject(i);
                        
                        // 获取配置项属性
                        std::string aiMode = configObj->optValue("aiMode", webui::function::PRESET_CONFIGS[i].ai_mode);
                        std::string lockPosition = configObj->optValue("lockPosition", webui::function::PRESET_CONFIGS[i].lock_position);
                        std::string selectedFaction = configObj->optValue("selectedFaction", webui::function::PRESET_CONFIGS[i].selected_faction);
                        std::string hotkey = configObj->optValue("hotkey", webui::function::PRESET_CONFIGS[i].hotkey);
                        
                        // 正确处理triggerSwitch布尔字段
                        std::string triggerSwitch = webui::function::PRESET_CONFIGS[i].trigger_switch;
                        if (configObj->has("triggerSwitch")) {
                            bool triggerSwitchBool = configObj->getValue<bool>("triggerSwitch");
                            triggerSwitch = triggerSwitchBool ? "true" : "false";
                        }
                        
                        // 正确处理weaponSwitch布尔字段
                        std::string weaponSwitch = webui::function::PRESET_CONFIGS[i].weapon_switch;
                        if (configObj->has("weaponSwitch")) {
                            bool weaponSwitchBool = configObj->getValue<bool>("weaponSwitch");
                            weaponSwitch = weaponSwitchBool ? "true" : "false";
                        }
                        
                        // 正确处理flashShield布尔字段
                        std::string flashShield = webui::function::PRESET_CONFIGS[i].flash_shield;
                        if (configObj->has("flashShield")) {
                            bool flashShieldBool = configObj->getValue<bool>("flashShield");
                            flashShield = flashShieldBool ? "true" : "false";
                        }
                        
                        // 正确处理enabled布尔字段
                        std::string enabled = webui::function::PRESET_CONFIGS[i].enabled;
                        if (configObj->has("enabled")) {
                            bool enabledBool = configObj->getValue<bool>("enabled");
                            enabled = enabledBool ? "true" : "false";
                        }
                        
                        std::string configName = configObj->optValue("configName", webui::function::PRESET_CONFIGS[i].name);
                        if (configObj->has("presetName")) {
                            configName = configObj->getValue<std::string>("presetName");
                        }
                        
                        // 更新预设配置
                        webui::function::PRESET_CONFIGS[i].ai_mode = aiMode;
                        webui::function::PRESET_CONFIGS[i].lock_position = lockPosition;
                        webui::function::PRESET_CONFIGS[i].selected_faction = selectedFaction;
                        webui::function::PRESET_CONFIGS[i].hotkey = hotkey;
                        webui::function::PRESET_CONFIGS[i].trigger_switch = triggerSwitch;
                        webui::function::PRESET_CONFIGS[i].weapon_switch = weaponSwitch;
                        webui::function::PRESET_CONFIGS[i].flash_shield = flashShield;
                        webui::function::PRESET_CONFIGS[i].enabled = enabled;
                        webui::function::PRESET_CONFIGS[i].name = configName;
                        
                        LOG_INFO("更新预设配置[{}]: 模式={}, 位置={}, 阵营={}, 热键={}, 扳机开关={}, 切枪开关={}, 背闪防护={}, 启用={}, 名称={}", 
                                 i, aiMode, lockPosition, selectedFaction, hotkey, triggerSwitch, weaponSwitch, flashShield, enabled, configName);
                    }
                }
            }
            
            LOG_INFO("更新功能全局变量: aiMode={}, lockPosition={}, hotkey={}, activeConfigIndex={}", 
                     webui::function::g_ai_mode, webui::function::g_lock_position, 
                     webui::function::g_hotkey, webui::function::g_active_config_index);
                     
        }else if (action == "pid_modify") {
            LOG_INFO("updateGlobalModels: 处理PID配置修改");
            
            // 更新用户名和游戏名
            if (content->has("username")) {
                webui::home::g_username = content->getValue<std::string>("username");
                LOG_INFO("更新用户名: {}", webui::home::g_username);
            }
            
            if (content->has("gameName")) {
                webui::home::g_game_name = content->getValue<std::string>("gameName");
                LOG_INFO("更新游戏名: {}", webui::home::g_game_name);
            }
            
            // 更新PID参数 - 按照API文档定义的字段名，确保每个参数都被正确更新
            if (content->has("nearMoveFactor")) {
                webui::pid::g_near_move_factor = content->getValue<double>("nearMoveFactor");
                LOG_DEBUG("更新nearMoveFactor: {}", webui::pid::g_near_move_factor);
            }
            
            if (content->has("nearStabilizer")) {
                webui::pid::g_near_stabilizer = content->getValue<double>("nearStabilizer");
                LOG_DEBUG("更新nearStabilizer: {}", webui::pid::g_near_stabilizer);
            }
            
            if (content->has("nearResponseRate")) {
                webui::pid::g_near_response_rate = content->getValue<double>("nearResponseRate");
                LOG_DEBUG("更新nearResponseRate: {}", webui::pid::g_near_response_rate);
            }
            
            if (content->has("nearAssistZone")) {
                webui::pid::g_near_assist_zone = content->getValue<double>("nearAssistZone");
                LOG_DEBUG("更新nearAssistZone: {}", webui::pid::g_near_assist_zone);
            }
            
            if (content->has("nearResponseDelay")) {
                webui::pid::g_near_response_delay = content->getValue<double>("nearResponseDelay");
                LOG_DEBUG("更新nearResponseDelay: {}", webui::pid::g_near_response_delay);
            }
            
            if (content->has("nearMaxAdjustment")) {
                webui::pid::g_near_max_adjustment = content->getValue<double>("nearMaxAdjustment");
                LOG_DEBUG("更新nearMaxAdjustment: {}", webui::pid::g_near_max_adjustment);
            }
            
            if (content->has("farFactor")) {
                webui::pid::g_far_factor = content->getValue<double>("farFactor");
                LOG_DEBUG("更新farFactor: {}", webui::pid::g_far_factor);
            }
            
            if (content->has("yAxisFactor")) {
                webui::pid::g_y_axis_factor = content->getValue<double>("yAxisFactor");
                LOG_DEBUG("更新yAxisFactor: {}", webui::pid::g_y_axis_factor);
            }
            
            if (content->has("pidRandomFactor")) {
                webui::pid::g_pid_random_factor = content->getValue<double>("pidRandomFactor");
                LOG_DEBUG("更新pidRandomFactor: {}", webui::pid::g_pid_random_factor);
            }
            
            LOG_INFO("updateGlobalModels: PID参数更新完成 - nearMoveFactor={}, nearStabilizer={}, nearResponseRate={}, nearAssistZone={}, nearResponseDelay={}, nearMaxAdjustment={}, farFactor={}, yAxisFactor={}, pidRandomFactor={}", 
                     webui::pid::g_near_move_factor, webui::pid::g_near_stabilizer, webui::pid::g_near_response_rate,
                     webui::pid::g_near_assist_zone, webui::pid::g_near_response_delay, webui::pid::g_near_max_adjustment,
                     webui::pid::g_far_factor, webui::pid::g_y_axis_factor, webui::pid::g_pid_random_factor);
                     
        }else if (action == "pid_read") {
            // 处理PID设置读取操作 - 直接使用内容对象的值更新全局变量
            
            // 更新用户名和游戏名（schema.prisma中定义为必填字段）
            webui::home::g_username = content->getValue<std::string>("username");
            webui::home::g_game_name = content->getValue<std::string>("gameName");
            
            // 🎯 PID配置读取时更新YOLO类别数量
            yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            
            // 更新PID参数 - 按照API文档定义的字段名
            if (content->has("nearMoveFactor")) webui::pid::g_near_move_factor = content->getValue<double>("nearMoveFactor");
            if (content->has("nearStabilizer")) webui::pid::g_near_stabilizer = content->getValue<double>("nearStabilizer");
            if (content->has("nearResponseRate")) webui::pid::g_near_response_rate = content->getValue<double>("nearResponseRate");
            if (content->has("nearAssistZone")) webui::pid::g_near_assist_zone = content->getValue<double>("nearAssistZone");
            if (content->has("nearResponseDelay")) webui::pid::g_near_response_delay = content->getValue<double>("nearResponseDelay");
            if (content->has("nearMaxAdjustment")) webui::pid::g_near_max_adjustment = content->getValue<double>("nearMaxAdjustment");
            if (content->has("farFactor")) webui::pid::g_far_factor = content->getValue<double>("farFactor");
            if (content->has("yAxisFactor")) webui::pid::g_y_axis_factor = content->getValue<double>("yAxisFactor");
            if (content->has("pidRandomFactor")) webui::pid::g_pid_random_factor = content->getValue<double>("pidRandomFactor");
            
            LOG_INFO("pid_read: 更新PID全局变量");
        }else if (action == "fov_modify") {
            // 更新FOV相关的全局变量
            webui::fov::g_fov = content->optValue("fov", webui::fov::g_fov);
            webui::fov::g_fov_time = content->optValue("fovTime", webui::fov::g_fov_time);
            
            LOG_INFO("更新FOV全局变量: fov={}, fovTime={}", webui::fov::g_fov, webui::fov::g_fov_time);
            
        }
        // 根据不同的action类型更新不同的全局变量
        else if (action == "aim_modify") {
            // 更新瞄准相关的全局变量，检查参数是否在有效范围内
            
            // 处理aimRange参数 (0.0 - 416.0)
            if (content->has("aimRange")) {
                double aimRange = content->getValue<double>("aimRange");
                if (aimRange < 0.0) aimRange = 0.0;
                if (aimRange > 416.0) aimRange = 416.0;
                webui::aim::g_aim_range = aimRange;
            }
            
            // 处理trackRange参数 (0.001 - 15.0)
            if (content->has("trackRange")) {
                double trackRange = content->getValue<double>("trackRange");
                if (trackRange < 0.001) trackRange = 0.001;
                if (trackRange > 15.0) trackRange = 15.0;
                webui::aim::g_track_range = trackRange;
            }
            
            // 处理headHeight参数 (0.0 - 50.0)
            if (content->has("headHeight")) {
                double headHeight = content->getValue<double>("headHeight");
                if (headHeight < 0.0) headHeight = 0.0;
                if (headHeight > 50.0) headHeight = 50.0;
                webui::aim::g_head_height = headHeight;
            }
            
            // 处理neckHeight参数 (0.0 - 50.0)
            if (content->has("neckHeight")) {
                double neckHeight = content->getValue<double>("neckHeight");
                webui::aim::g_neck_height = neckHeight;
            }
            
            // 处理chestHeight参数 (0.0 - 50.0)
            if (content->has("chestHeight")) {
                double chestHeight = content->getValue<double>("chestHeight");
                webui::aim::g_chest_height = chestHeight;
            }
            
            // 处理headRangeX参数 (0.1 - 1.0)
            if (content->has("headRangeX")) {
                double headRangeX = content->getValue<double>("headRangeX");
                webui::aim::g_head_range_x = headRangeX;
            }
            
            // 处理headRangeY参数 (0.1 - 1.0)
            if (content->has("headRangeY")) {
                double headRangeY = content->getValue<double>("headRangeY");
                webui::aim::g_head_range_y = headRangeY;
            }
            
            // 处理neckRangeX参数 (0.1 - 1.0)
            if (content->has("neckRangeX")) {
                double neckRangeX = content->getValue<double>("neckRangeX");
                webui::aim::g_neck_range_x = neckRangeX;
            }
            
            // 处理neckRangeY参数 (0.1 - 1.0)
            if (content->has("neckRangeY")) {
                double neckRangeY = content->getValue<double>("neckRangeY");
                webui::aim::g_neck_range_y = neckRangeY;
            }
            
            // 处理chestRangeX参数 (0.1 - 1.0)
            if (content->has("chestRangeX")) {
                double chestRangeX = content->getValue<double>("chestRangeX");
                webui::aim::g_chest_range_x = chestRangeX;
            }
            
            // 处理chestRangeY参数 (0.1 - 1.0)
            if (content->has("chestRangeY")) {
                double chestRangeY = content->getValue<double>("chestRangeY");
                webui::aim::g_chest_range_y = chestRangeY;
            }
            
            LOG_INFO("更新瞄准全局变量: aimRange={}, trackRange={}, headHeight={}, neckHeight={}, chestHeight={}, "
                     "headRangeX={}, headRangeY={}, neckRangeX={}, neckRangeY={}, chestRangeX={}, chestRangeY={}", 
                     webui::aim::g_aim_range, webui::aim::g_track_range, webui::aim::g_head_height,
                     webui::aim::g_neck_height, webui::aim::g_chest_height, webui::aim::g_head_range_x,
                     webui::aim::g_head_range_y, webui::aim::g_neck_range_x, webui::aim::g_neck_range_y,
                     webui::aim::g_chest_range_x, webui::aim::g_chest_range_y);
        }   else if (action == "fire_modify") {
            // 更新射击相关的全局变量
            webui::fire::g_rifle_sleep = content->optValue("rifleSleep", webui::fire::g_rifle_sleep);
            webui::fire::g_rifle_interval = content->optValue("rifleInterval", webui::fire::g_rifle_interval);
            webui::fire::g_pistol_sleep = content->optValue("pistolSleep", webui::fire::g_pistol_sleep);
            webui::fire::g_pistol_interval = content->optValue("pistolInterval", webui::fire::g_pistol_interval);
            webui::fire::g_sniper_sleep = content->optValue("sniperSleep", webui::fire::g_sniper_sleep);
            webui::fire::g_sniper_interval = content->optValue("sniperInterval", webui::fire::g_sniper_interval);
            
            
            LOG_INFO("更新射击全局变量: rifleSleep={}, rifleInterval={}, pistolSleep={}, pistolInterval={}, sniperSleep={}, sniperInterval={}", 
                     webui::fire::g_rifle_sleep, webui::fire::g_rifle_interval, webui::fire::g_pistol_sleep,
                     webui::fire::g_pistol_interval, webui::fire::g_sniper_sleep, webui::fire::g_sniper_interval);
                     
        } 
        else if (action == "fire_read") {
            // 更新射击相关的全局变量
            int rifleSleep = content->optValue("rifleSleep", webui::fire::g_rifle_sleep);
            int rifleInterval = content->optValue("rifleInterval", webui::fire::g_rifle_interval);
            int pistolSleep = content->optValue("pistolSleep", webui::fire::g_pistol_sleep);
            int pistolInterval = content->optValue("pistolInterval", webui::fire::g_pistol_interval);
            int sniperSleep = content->optValue("sniperSleep", webui::fire::g_sniper_sleep);
            int sniperInterval = content->optValue("sniperInterval", webui::fire::g_sniper_interval);
            
            // 更新全局变量
            webui::fire::g_rifle_sleep = rifleSleep;
            webui::fire::g_rifle_interval = rifleInterval;
            webui::fire::g_pistol_sleep = pistolSleep;
            webui::fire::g_pistol_interval = pistolInterval;
            webui::fire::g_sniper_sleep = sniperSleep;
            webui::fire::g_sniper_interval = sniperInterval;
            
            LOG_INFO("更新射击设置全局变量: rifleSleep={}, rifleInterval={}, pistolSleep={}, pistolInterval={}, sniperSleep={}, sniperInterval={}", 
                     webui::fire::g_rifle_sleep, webui::fire::g_rifle_interval, webui::fire::g_pistol_sleep,
                     webui::fire::g_pistol_interval, webui::fire::g_sniper_sleep, webui::fire::g_sniper_interval);
        } else if (action == "fov_read") {
            // 处理FOV视野设置读取操作 - 直接使用内容对象的值更新全局变量
            
            // 更新用户名和游戏名（schema.prisma中定义为必填字段）
            webui::home::g_username = content->getValue<std::string>("username");
            webui::home::g_game_name = content->getValue<std::string>("gameName");
            
            // 🎯 FOV配置读取时更新YOLO类别数量
            yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            
            // 直接更新FOV参数，如果内容中包含
            if (content->has("fov")) webui::fov::g_fov = content->getValue<float>("fov");
            if (content->has("fovTime")) webui::fov::g_fov_time = content->getValue<int>("fovTime");
            
            LOG_INFO("fov_read: 更新FOV全局变量 fov={}, fovTime={}", 
                     webui::fov::g_fov, webui::fov::g_fov_time);
        } else if (action == "data_collection_modify") {
            // 更新数据采集相关的全局变量
            bool isEnabled = content->optValue("isEnabled", webui::collect::g_is_enabled);
            std::string mapHotkey = content->optValue("mapHotkey", webui::collect::g_map_hotkey);
            std::string targetHotkey = content->optValue("targetHotkey", webui::collect::g_target_hotkey);
            std::string teamSide = content->optValue("teamSide", webui::collect::g_team_side);
            std::string collectionName = content->optValue("collectionName", webui::collect::g_collection_name);
            
            // 更新用户名和游戏名（如果存在）
            if (content->has("username")) {
                webui::home::g_username = content->getValue<std::string>("username");
            }
            
            if (content->has("gameName")) {
                webui::home::g_game_name = content->getValue<std::string>("gameName");
            }
            
            // 更新全局变量
            webui::collect::g_is_enabled = isEnabled;
            webui::collect::g_map_hotkey = mapHotkey;
            webui::collect::g_target_hotkey = targetHotkey;
            webui::collect::g_team_side = teamSide;
            webui::collect::g_collection_name = collectionName;
            
            LOG_INFO("更新数据采集全局变量: isEnabled={}, mapHotkey={}, targetHotkey={}, teamSide={}, collectionName={}", 
                     webui::collect::g_is_enabled ? "是" : "否", webui::collect::g_map_hotkey, 
                     webui::collect::g_target_hotkey, webui::collect::g_team_side, webui::collect::g_collection_name);
                     
        } else if (action == "database_modify") {
            // 更新数据库连接相关的全局变量
            std::string dbHost = content->optValue("dbHost", webui::database::g_db_host);
            int dbPort = content->optValue("dbPort", webui::database::g_db_port);
            std::string dbName = content->optValue("dbName", webui::database::g_db_name);
            std::string dbUser = content->optValue("dbUser", webui::database::g_db_user);
            std::string dbPassword = content->optValue("dbPassword", webui::database::g_db_password);
            int dbTimeout = content->optValue("dbTimeout", webui::database::g_db_timeout);
            int dbPoolSize = content->optValue("dbPoolSize", webui::database::g_db_pool_size);
            
            // 更新全局变量
            webui::database::g_db_host = dbHost;
            webui::database::g_db_port = dbPort;
            webui::database::g_db_name = dbName;
            webui::database::g_db_user = dbUser;
            webui::database::g_db_password = dbPassword;
            webui::database::g_db_timeout = dbTimeout;
            webui::database::g_db_pool_size = dbPoolSize;
            
            LOG_INFO("更新数据库连接全局变量: dbHost={}, dbPort={}, dbName={}, dbUser={}, dbTimeout={}, dbPoolSize={}", 
                     webui::database::g_db_host, webui::database::g_db_port, webui::database::g_db_name,
                     webui::database::g_db_user, webui::database::g_db_timeout, webui::database::g_db_pool_size);
                     
        } else if (action == "websocket_modify") {
            // 更新WebSocket服务相关的全局变量
            std::string wsIp = content->optValue("wsIp", websocket::g_ws_ip);
            int wsPort = content->optValue("wsPort", static_cast<int>(websocket::g_ws_port));
            bool logMessages = content->optValue("logMessages", websocket::g_log_messages);
            int maxConnections = content->optValue("maxConnections", websocket::g_max_connections);
            int connectionTimeout = content->optValue("connectionTimeout", websocket::g_connection_timeout);
            
            // 更新全局变量
            websocket::g_ws_ip = wsIp;
            websocket::g_ws_port = static_cast<uint16_t>(wsPort);
            websocket::g_log_messages = logMessages;
            websocket::g_max_connections = maxConnections;
            websocket::g_connection_timeout = connectionTimeout;
            
            LOG_INFO("更新WebSocket服务全局变量: wsIp={}, wsPort={}, logMessages={}, maxConnections={}, connectionTimeout={}", 
                     websocket::g_ws_ip, websocket::g_ws_port, websocket::g_log_messages,
                     websocket::g_max_connections, websocket::g_connection_timeout);
                     
        } else if (action == "status_bar_query") {//页头 推理 卡密 键鼠 数据库
            //无需更新  
            LOG_INFO("状态栏 - 无需更新");
                     
        } else if (action == "sidebar_refresh") {
            // 侧边栏刷新仅需更新用户名和游戏名
            std::string username = content->optValue<std::string>("username", webui::home::g_username);
            std::string gameName = content->optValue<std::string>("gameName", webui::home::g_game_name);
            
            // 只有当提供了非空值时才更新全局变量
            if (!username.empty()) {
                webui::home::g_username = username;
            }
            
            if (!gameName.empty()) {
                webui::home::g_game_name = gameName;
                
                // 🎯 侧边栏刷新时更新YOLO类别数量
                yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            }
            
            LOG_INFO("侧边栏刷新 - 更新用户名和游戏名: username={}, gameName={}", 
                     webui::home::g_username, webui::home::g_game_name);
        } else if (action == "function_read") {
            // 处理功能设置读取操作 - 直接使用内容对象的值更新全局变量
            
            // 更新用户名和游戏名（schema.prisma中定义为必填字段）
            webui::home::g_username = content->getValue<std::string>("username");
            webui::home::g_game_name = content->getValue<std::string>("gameName");
            
            // 🎯 功能配置读取时更新YOLO类别数量
            yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            
            // 更新活动配置索引（如果存在）
            if (content->has("activeConfigIndex")) {
                webui::function::g_active_config_index = content->getValue<int>("activeConfigIndex");
            }
            
            // 更新配置数组（如果存在）
            if (content->isArray("configs")) {
                auto configsArray = content->getArray("configs");
                int updateSize = std::min((int)configsArray->size(), (int)webui::function::PRESET_CONFIGS.size());
                
                LOG_INFO("function_read: 更新{}个预设配置", updateSize);
                
                for (int i = 0; i < updateSize; ++i) {
                    if (configsArray->isObject(i)) {
                        auto configObj = configsArray->getObject(i);
                        
                        // 直接使用数据库返回的值 - schema.prisma中已定义这些字段
                        if (configObj->has("aiMode")) webui::function::PRESET_CONFIGS[i].ai_mode = configObj->getValue<std::string>("aiMode");
                        if (configObj->has("lockPosition")) webui::function::PRESET_CONFIGS[i].lock_position = configObj->getValue<std::string>("lockPosition");
                        if (configObj->has("selectedFaction")) webui::function::PRESET_CONFIGS[i].selected_faction = configObj->getValue<std::string>("selectedFaction");
                        if (configObj->has("hotkey")) webui::function::PRESET_CONFIGS[i].hotkey = configObj->getValue<std::string>("hotkey");
                        if (configObj->has("triggerSwitch")) webui::function::PRESET_CONFIGS[i].trigger_switch = configObj->getValue<bool>("triggerSwitch") ? "true" : "false";
                        if (configObj->has("weaponSwitch")) webui::function::PRESET_CONFIGS[i].weapon_switch = configObj->getValue<bool>("weaponSwitch") ? "true" : "false";
                        if (configObj->has("flashShield")) webui::function::PRESET_CONFIGS[i].flash_shield = configObj->getValue<bool>("flashShield") ? "true" : "false";
                        if (configObj->has("enabled")) webui::function::PRESET_CONFIGS[i].enabled = configObj->getValue<bool>("enabled") ? "true" : "false";
                        if (configObj->has("presetName")) webui::function::PRESET_CONFIGS[i].name = configObj->getValue<std::string>("presetName");
                        else if (configObj->has("name")) webui::function::PRESET_CONFIGS[i].name = configObj->getValue<std::string>("name");
                    }
                }
            }
            
            // 更新当前使用的配置（基于活动索引）
            int activeIndex = webui::function::g_active_config_index;
            if (activeIndex >= 0 && activeIndex < webui::function::PRESET_CONFIGS.size()) {
                webui::function::g_ai_mode = webui::function::PRESET_CONFIGS[activeIndex].ai_mode;
                webui::function::g_lock_position = webui::function::PRESET_CONFIGS[activeIndex].lock_position;
                webui::function::g_hotkey = webui::function::PRESET_CONFIGS[activeIndex].hotkey;
                
                LOG_INFO("function_read: 根据活动配置[{}]更新全局变量", activeIndex);
            }
        } else if (action == "register_modify") {
            // 处理用户注册/修改相关的全局变量
            std::string username = content->optValue("username", std::string(""));
            std::string defaultGame = content->optValue("defaultGame", std::string(""));
            
            // 更新全局变量，具体根据业务需求添加
            if (!username.empty()) {
                webui::home::g_username = username;
            }
            
            if (!defaultGame.empty()) {
                webui::home::g_game_name = defaultGame;
            }
            
            // 如果有其他需要处理的字段，也可以在这里添加
            
            LOG_INFO("更新用户注册信息: username={}, defaultGame={}", 
                     webui::home::g_username, webui::home::g_game_name);
        }  else if (action == "data_collection_read") {
            // 更新数据采集相关的全局变量
            bool isEnabled = content->optValue("isEnabled", webui::collect::g_is_enabled);
            std::string mapHotkey = content->optValue("mapHotkey", webui::collect::g_map_hotkey);
            std::string targetHotkey = content->optValue("targetHotkey", webui::collect::g_target_hotkey);
            std::string teamSide = content->optValue("teamSide", webui::collect::g_team_side);
            std::string collectionName = content->optValue("collectionName", webui::collect::g_collection_name);
            
            // 更新用户名和游戏名（如果存在）
            if (content->has("username")) {
                webui::home::g_username = content->getValue<std::string>("username");
            }
            
            if (content->has("gameName")) {
                webui::home::g_game_name = content->getValue<std::string>("gameName");
                
                // 🎯 数据收集配置读取时更新YOLO类别数量
                yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            }
            
            // 更新全局变量
            webui::collect::g_is_enabled = isEnabled;
            webui::collect::g_map_hotkey = mapHotkey;
            webui::collect::g_target_hotkey = targetHotkey;
            webui::collect::g_team_side = teamSide;
            webui::collect::g_collection_name = collectionName;
            
            LOG_INFO("data_collection_read: 更新数据采集全局变量");
            LOG_INFO("参数: isEnabled={}, mapHotkey={}, targetHotkey={}, teamSide={}, collectionName={}", 
                     webui::collect::g_is_enabled ? "是" : "否", webui::collect::g_map_hotkey, 
                     webui::collect::g_target_hotkey, webui::collect::g_team_side, webui::collect::g_collection_name);
        } else if (action == "home_read") {
            webui::home::g_username = content->optValue("username", webui::home::g_username);
            
            // 不再直接使用客户端传递的isPro值，而是从数据库获取真实状态
            if (!webui::home::g_username.empty()) {
                try {
                    blweb::services::LoginService& loginService = blweb::services::LoginService::getInstance();
                    auto userResult = loginService.getUserLoginInfo(webui::home::g_username);
                    
                    if (userResult->getValue<int>("code") == 200 && userResult->has("data")) {
                        auto userData = userResult->getObject("data");
                        if (userData->has("isPro")) {
                            bool dbIsPro = userData->getValue<bool>("isPro");
                            webui::header::isPro = dbIsPro;
                            LOG_INFO("home_read: 从数据库获取用户 '{}' 的真实Pro状态: {}", 
                                    webui::home::g_username, dbIsPro ? "Pro版" : "普通版");
                        }
                    }
                } catch (const std::exception& e) {
                    LOG_ERROR("home_read: 获取用户Pro状态异常: {}", e.what());
                }
            }
            
            if (content->has("gameName")) {
                webui::home::g_game_name = content->getValue<std::string>("gameName");
                
                // 🎯 刷新数据时更新YOLO类别数量
                yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            }
            
            if (content->has("cardKey")) {
                webui::home::g_card_key = content->getValue<std::string>("cardKey");
                jfkm_config::g_card_key = webui::home::g_card_key;
            }
            
            LOG_INFO("home_read: 更新全局变量完成 - username={}, gameName={}, isPro={}", 
                    webui::home::g_username, webui::home::g_game_name, webui::header::isPro);
        } else if (action == "aim_read") {
            // 处理瞄准设置读取操作 - 直接使用内容对象的值更新全局变量
            
            // 更新用户名和游戏名（schema.prisma中定义为必填字段）
            webui::home::g_username = content->getValue<std::string>("username");
            webui::home::g_game_name = content->getValue<std::string>("gameName");
            
            // 🎯 瞄准配置读取时更新YOLO类别数量
            yolo_config::UpdateYoloClassNum(webui::home::g_game_name);
            
            // 直接更新瞄准参数 - schema.prisma中已定义这些字段的类型
            if (content->has("aimRange")) webui::aim::g_aim_range = content->getValue<double>("aimRange");
            if (content->has("trackRange")) webui::aim::g_track_range = content->getValue<double>("trackRange");
            if (content->has("headHeight")) webui::aim::g_head_height = content->getValue<double>("headHeight");
            if (content->has("neckHeight")) webui::aim::g_neck_height = content->getValue<double>("neckHeight");
            if (content->has("chestHeight")) webui::aim::g_chest_height = content->getValue<double>("chestHeight");
            if (content->has("headRangeX")) webui::aim::g_head_range_x = content->getValue<double>("headRangeX");
            if (content->has("headRangeY")) webui::aim::g_head_range_y = content->getValue<double>("headRangeY");
            if (content->has("neckRangeX")) webui::aim::g_neck_range_x = content->getValue<double>("neckRangeX");
            if (content->has("neckRangeY")) webui::aim::g_neck_range_y = content->getValue<double>("neckRangeY");
            if (content->has("chestRangeX")) webui::aim::g_chest_range_x = content->getValue<double>("chestRangeX");
            if (content->has("chestRangeY")) webui::aim::g_chest_range_y = content->getValue<double>("chestRangeY");
            
            LOG_INFO("aim_read: 更新瞄准全局变量");
        } else {
            // 未知的action类型
            LOG_WARNING("未知的action类型，无法更新全局变量: {}", action);
            return false;
        }
        
        return true;
    } catch (std::exception& e) {
        LOG_ERROR("更新全局变量异常: {}", e.what());
        return false;
    }
}

} // namespace utils
} // namespace blweb
