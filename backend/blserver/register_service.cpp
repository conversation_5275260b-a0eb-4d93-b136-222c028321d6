#include "register_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blsql/registerdb.h"

#include <Poco/JSON/Parser.h>
#include <Poco/Dynamic/Var.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace blweb {
namespace services {

// 单例实现
RegisterService& RegisterService::getInstance() {
    static RegisterService instance;
    return instance;
}

// 构造函数
RegisterService::RegisterService() : m_lastError("") {
    LOG_INFO("RegisterService已初始化");
}

// 析构函数
RegisterService::~RegisterService() {
    LOG_INFO("RegisterService已销毁");
}

// 处理用户注册请求
Poco::JSON::Object::Ptr RegisterService::registerUser(
    const std::string& username, 
    const std::string& password,
    const std::string& defaultGame,
    Poco::JSON::Array::Ptr gameList,
    bool isPro
) {
    LOG_INFO("注册用户 - 用户名: '{}', 默认游戏: '{}', isPro: {}", username, defaultGame, isPro);
    
    if (gameList && gameList->size() > 0) {
        LOG_INFO("将为用户创建{}个游戏的配置", gameList->size());
    }
    
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        // 业务逻辑验证
        if (username.empty() || password.empty()) {
            m_lastError = "用户名和密码不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("status", "error");
            result->set("message", m_lastError);
            return result;
        }
        
        // 检查用户名是否已存在
        if (isUserExists(username)) {
            m_lastError = "用户名已存在";
            LOG_ERROR("注册失败: 用户名 '{}' 已存在", username);
            
            result->set("status", "error");
            result->set("message", m_lastError);
            return result;
        }
        
        // 创建请求参数
        Poco::JSON::Object::Ptr params = new Poco::JSON::Object();
        params->set("username", username);
        params->set("password", password);
        params->set("defaultGame", defaultGame);
        params->set("isPro", isPro);
        
        // 添加游戏列表
        if (gameList && gameList->size() > 0) {
            params->set("gameList", gameList);
        }
        
        // 调用数据库层注册用户
        blweb::db::RegisterDB& registerDB = blweb::db::RegisterDB::getInstance();
        Poco::JSON::Object::Ptr dbResult = registerDB.handleRegisterRequest(params);
        
        // 检查数据库操作结果
        if (dbResult->getValue<std::string>("status") == "error") {
            m_lastError = dbResult->getValue<std::string>("message");
            LOG_ERROR("{}", m_lastError);
            
            result->set("status", "error");
            result->set("message", m_lastError);
            return result;
        }
        
        // 返回成功结果
        result->set("status", "success");
        result->set("message", "用户注册成功");
        
        // 添加数据对象
        if (dbResult->has("data")) {
            result->set("data", dbResult->getObject("data"));
        }
        
        LOG_INFO("用户 '{}' 注册成功, isPro={}", username, isPro);
    } catch (const std::exception& e) {
        m_lastError = std::string("注册用户异常: ") + e.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("status", "error");
        result->set("message", m_lastError);
    }
    
    return result;
}

// 检查用户名是否已存在
bool RegisterService::isUserExists(const std::string& username) {
    try {
        blweb::db::RegisterDB& registerDB = blweb::db::RegisterDB::getInstance();
        return registerDB.isUserExists(username);
    } catch (const std::exception& e) {
        m_lastError = std::string("检查用户是否存在异常: ") + e.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取用户的所有配置信息
Poco::JSON::Object::Ptr RegisterService::getUserAllConfigs(const std::string& username) {
    try {
        blweb::db::RegisterDB& registerDB = blweb::db::RegisterDB::getInstance();
        return registerDB.getUserAllConfigs(username);
    } catch (const std::exception& e) {
        m_lastError = std::string("获取用户配置异常: ") + e.what();
        LOG_ERROR("{}", m_lastError);
        
        // 返回空对象
        return new Poco::JSON::Object();
    }
}

// 获取最后一次错误信息
std::string RegisterService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 