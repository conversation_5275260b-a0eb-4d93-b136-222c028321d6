#pragma once

#include <string>

namespace blweb {
namespace services {

class HeaderService {
public:
    // 单例模式
    static HeaderService& getInstance();
    
    // 获取最后一次错误信息
    std::string getLastError() const;
    
private:
    // 构造函数和析构函数是私有的（单例模式）
    HeaderService();
    ~HeaderService();
    
    // 禁止拷贝和赋值
    HeaderService(const HeaderService&) = delete;
    HeaderService& operator=(const HeaderService&) = delete;
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace services
} // namespace blweb 