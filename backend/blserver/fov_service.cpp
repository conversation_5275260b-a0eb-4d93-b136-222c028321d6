#include "fov_service.h"
#include "../utils/logger.h"
#include "../blsql/fovconfgidb.h"
#include "../utils/gmodels.h"
#include <Poco/JSON/Parser.h>
#include <stdexcept>
#include <string>
#include <thread>

namespace blweb {
namespace services {

// 单例实现
FovService& FovService::getInstance() {
    static FovService instance;
    return instance;
}

// 构造函数
FovService::FovService() : m_lastError("") {
    LOG_INFO("FovService已初始化");
}

// 析构函数
FovService::~FovService() {
    LOG_INFO("FovService已销毁");
}

// 更新用户的FOV配置
bool FovService::updateFovConfig(
    const std::string& username,
    const std::string& gameName,
    float fov,
    int fovTime
) {
    try {
        LOG_INFO("更新FOV配置 - 用户: {}, 游戏: {}, FOV: {}, FOV时间: {}", 
                 username, gameName, fov, fovTime);
        
        // 检查参数
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        if (fov <= 0) {
            m_lastError = "FOV值必须大于0";
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        // 调用数据库层更新配置
        blweb::db::FovConfigDB& fovDB = blweb::db::FovConfigDB::getInstance();
        if (!fovDB.updateFovConfig(username, gameName, fov, fovTime)) {
            m_lastError = "数据库更新失败: " + fovDB.getLastError();
            LOG_ERROR("{}", m_lastError);
            return false;
        }
        
        // 更新全局配置变量
        if (username == webui::home::g_username && gameName == webui::home::g_game_name) {
            webui::fov::g_fov = fov;
            webui::fov::g_fov_time = fovTime;
            LOG_INFO("全局FOV配置变量已更新");
        }
        
        LOG_INFO("FOV配置更新成功");
        return true;
    }
    catch (const std::exception& ex) {
        m_lastError = std::string("更新FOV配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取用户的FOV配置
Poco::JSON::Object::Ptr FovService::getFovConfig(
    const std::string& username,
    const std::string& gameName
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("获取FOV配置 - 用户: {}, 游戏: {}", username, gameName);
        
        // 检查参数
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 调用数据库层获取配置
        blweb::db::FovConfigDB& fovDB = blweb::db::FovConfigDB::getInstance();
        auto fovConfig = fovDB.getFovConfig(username, gameName);
        
        // 如果配置存在(非空)
        if (!fovConfig.empty()) {
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            data->set("fov", std::stof(fovConfig["fov"]));
            data->set("fovTime", std::stoi(fovConfig["fov_time"]));
            data->set("createdAt", fovConfig["created_at"]);
            data->set("updatedAt", fovConfig["updated_at"]);
            data->set("exists", true);
            
            result->set("code", 200);
            result->set("msg", "获取FOV配置成功");
            result->set("data", data);
            
            LOG_INFO("获取FOV配置成功 - 用户: {}, 游戏: {}", username, gameName);
        } else {
            // 如果配置不存在，返回默认值
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            data->set("fov", webui::fov::g_fov);
            data->set("fovTime", webui::fov::g_fov_time);
            data->set("exists", false);
            
            result->set("code", 200);
            result->set("msg", "配置不存在，返回默认值");
            result->set("data", data);
            
            LOG_INFO("FOV配置不存在，返回默认值 - 用户: {}, 游戏: {}", username, gameName);
        }
        
        return result;
    }
    catch (const std::exception& ex) {
        m_lastError = std::string("获取FOV配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("code", 500);
        result->set("msg", m_lastError);
        return result;
    }
}

// 获取最后一次错误信息
std::string FovService::getLastError() const {
    return m_lastError;
}

// 开始FOV测量过程
Poco::JSON::Object::Ptr FovService::startFovMeasurement(
    const std::string& username,
    const std::string& gameName
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("开始FOV测量 - 用户: {}, 游戏: {}", username, gameName);
        
        // 检查参数
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 设置全局测量标志为true，启动测量
        webui::fov::g_start_measure = true;
        LOG_INFO("设置FOV测量标志为true，开始测量过程");
        
        // 创建响应数据
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", username);
        data->set("gameName", gameName);
        data->set("measuring", true);
        
        // 等待测量完成 - 直到g_start_measure变为false
        int timeout = 0;
        const int MAX_TIMEOUT = 200; // 最大等待20秒(200 * 100ms)
        
        LOG_INFO("等待FOV测量完成...");
        while (webui::fov::g_start_measure && timeout < MAX_TIMEOUT) {
            // 暂停100毫秒
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            timeout++;
        }
        
        bool success = false;
        
        // 检查测量是否正常完成
        if (!webui::fov::g_start_measure) {
            LOG_INFO("FOV测量已完成，测得FOV值为: {}", webui::fov::g_fov);
            data->set("fov", webui::fov::g_fov);
            data->set("fovTime", webui::fov::g_fov_time);
            data->set("measuring", false);
            success = true;
            
            // 更新数据库中的FOV配置
            if (!updateFovConfig(username, gameName, webui::fov::g_fov, webui::fov::g_fov_time)) {
                LOG_WARNING("FOV测量成功，但更新数据库失败: {}", m_lastError);
            } else {
                LOG_INFO("FOV测量成功，并已更新数据库配置");
            }
        } else {
            // 如果超时，手动重置测量标志
            webui::fov::g_start_measure = false;
            LOG_WARNING("FOV测量超时未完成");
            data->set("measuring", false);
            m_lastError = "FOV测量超时，请重试";
        }
        
        // 设置响应结果
        if (success) {
            result->set("code", 200);
            result->set("msg", "FOV测量成功完成");
        } else {
            result->set("code", 500);
            result->set("msg", m_lastError);
        }
        
        result->set("data", data);
        return result;
    }
    catch (const std::exception& ex) {
        // 确保在异常情况下重置测量标志
        webui::fov::g_start_measure = false;
        
        m_lastError = std::string("FOV测量过程异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("code", 500);
        result->set("msg", m_lastError);
        return result;
    }
}

} // namespace services
} // namespace blweb 