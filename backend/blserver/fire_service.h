#ifndef FIRE_SERVICE_H
#define FIRE_SERVICE_H

#include <string>
#include <Poco/JSON/Object.h>

namespace blweb {
namespace services {

/**
 * @brief 射击配置服务类
 */
class FireService {
public:
    /**
     * @brief 获取单例实例
     * @return FireService& 单例实例引用
     */
    static FireService& getInstance();

    /**
     * @brief 析构函数
     */
    ~FireService();

    /**
     * @brief 更新用户的射击配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param rifleSleep 步枪睡眠时间
     * @param rifleInterval 步枪间隔时间
     * @param pistolSleep 手枪睡眠时间
     * @param pistolInterval 手枪间隔时间
     * @param sniperSleep 狙击枪睡眠时间
     * @param sniperInterval 狙击枪间隔时间
     * @return bool 操作是否成功
     */
    bool updateFireConfig(
        const std::string& username,
        const std::string& gameName,
        int rifleSleep,
        int rifleInterval,
        int pistolSleep,
        int pistolInterval,
        int sniperSleep,
        int sniperInterval
    );

    /**
     * @brief 获取用户的射击配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含射击配置的JSON对象
     */
    Poco::JSON::Object::Ptr getFireConfig(
        const std::string& username,
        const std::string& gameName
    );

    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    FireService();

    /**
     * @brief 最后一次错误信息
     */
    std::string m_lastError;
};

} // namespace services
} // namespace blweb

#endif // FIRE_SERVICE_H 