#include "function_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blsql/function_configs_db.h"

#include <Poco/JSON/Parser.h>
#include <Poco/Dynamic/Var.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <sstream>

namespace blweb {
namespace services {

// 单例实现
FunctionService& FunctionService::getInstance() {
    static FunctionService instance;
    return instance;
}

// 构造函数
FunctionService::FunctionService() : m_lastError("") {
    LOG_INFO("FunctionService已初始化");
}

// 析构函数
FunctionService::~FunctionService() {
    LOG_INFO("FunctionService已销毁");
}

// 批量更新功能配置
Poco::JSON::Object::Ptr FunctionService::updateFunctionConfigs(
    const std::string& username,
    const std::string& gameName,
    const Poco::JSON::Array::Ptr& configs
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("批量更新功能配置 - 用户: '{}', 指定游戏: '{}', 配置数量: {}", 
                 username, gameName, configs ? configs->size() : 0);
        
        // 验证参数
        if (username.empty() || gameName.empty()) {
            m_lastError = "用户名或游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("status", "error");
            result->set("message", m_lastError);
            return result;
        }
        
        if (!configs || configs->size() == 0) {
            m_lastError = "配置数组不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("status", "error");
            result->set("message", m_lastError);
            return result;
        }
        
        // 调用数据库层保存指定游戏的配置
        LOG_INFO("将游戏 '{}' 的{}个配置保存到数据库", gameName, configs->size());
        blweb::db::FunctionConfigsDB& db = blweb::db::FunctionConfigsDB::getInstance();
        bool success = db.updateFunctionConfigs(username, gameName, configs);
        
        if (success) {
            LOG_INFO("游戏 '{}' 的功能配置批量更新成功", gameName);
            
            // 构建结果对象
            result->set("status", "success");
            result->set("message", "功能配置批量更新成功");
            
            // 获取更新后的完整配置
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            
            // 添加更新成功的配置数量
            data->set("updatedCount", configs->size());
            
            result->set("data", data);
        } else {
            m_lastError = "更新游戏 '" + gameName + "' 功能配置失败: " + db.getLastError();
            LOG_ERROR("{}", m_lastError);
            
            result->set("status", "error");
            result->set("message", m_lastError);
        }
    } catch (const std::exception& ex) {
        m_lastError = std::string("更新游戏 '") + gameName + "' 功能配置异常: " + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("status", "error");
        result->set("message", m_lastError);
    }
    
    return result;
}

// 获取功能配置
Poco::JSON::Object::Ptr FunctionService::getFunctionConfigs(
    const std::string& username,
    const std::string& gameName
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("获取功能配置 - 用户: '{}', 指定游戏: '{}'", username, gameName);
        
        // 验证参数
        if (username.empty() || gameName.empty()) {
            m_lastError = "用户名或游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("status", "error");
            result->set("message", m_lastError);
            return result;
        }
        
        // 调用数据库层获取指定游戏的配置
        LOG_INFO("从数据库查询游戏 '{}' 的功能配置", gameName);
        blweb::db::FunctionConfigsDB& db = blweb::db::FunctionConfigsDB::getInstance();
        std::vector<blweb::db::FunctionConfigsDB::Row> rows = db.getFunctionConfigs(username, gameName);
        
        // 构建结果对象
        result->set("status", "success");
        
        if (rows.empty()) {
            result->set("message", "未找到功能配置，返回空列表");
            LOG_WARNING("未找到功能配置 - 用户: '{}', 游戏: '{}' (可能是第一次访问该游戏)", username, gameName);
            
            // 添加空的配置数组
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            
        Poco::JSON::Array::Ptr configsArray = new Poco::JSON::Array();
            data->set("configs", configsArray);
            data->set("exists", false);
            
            result->set("data", data);
        } else {
            result->set("message", "获取功能配置成功");
            LOG_INFO("成功获取到{}个功能配置 - 用户: '{}', 游戏: '{}'", rows.size(), username, gameName);
            
            // 添加配置数组
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            
            Poco::JSON::Array::Ptr configsArray = new Poco::JSON::Array();
            
            // 将每一行数据转换为JSON对象并添加到数组中
            for (size_t i = 0; i < rows.size(); i++) {
                const auto& row = rows[i];
                Poco::JSON::Object::Ptr configObj = rowToJson(row);
                configsArray->add(configObj);
                
                // 打印每个配置对象的详细内容
                std::stringstream ss;
                configObj->stringify(ss);
                LOG_INFO("游戏 '{}' 功能配置 #{}: {}", gameName, i+1, ss.str());
            }
            
            data->set("configs", configsArray);
            data->set("exists", true);
            
            result->set("data", data);
        }
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取功能配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("status", "error");
        result->set("message", m_lastError);
    }
    
    return result;
}

// 将数据库行转换为JSON对象
Poco::JSON::Object::Ptr FunctionService::rowToJson(const blweb::db::FunctionConfigsDB::Row& row) {
    Poco::JSON::Object::Ptr obj = new Poco::JSON::Object();
    
    try {
        // 字段映射表 - 数据库字段名到JSON字段名的映射
        const std::map<std::string, std::string> fieldMap = {
            {"preset_name", "presetName"},
            {"ai_mode", "aiMode"},
            {"lock_position", "lockPosition"},
            {"selected_faction", "selectedFaction"},
            {"hotkey", "hotkey"},
            {"created_at", "createdAt"},
            {"updated_at", "updatedAt"}
        };
        
        // 设置字符串字段
        for (const auto& mapping : fieldMap) {
            const std::string& dbField = mapping.first;
            const std::string& jsonField = mapping.second;
            
            auto it = row.find(dbField);
            if (it != row.end()) {
                obj->set(jsonField, it->second);
            }
        }
        
        // 设置布尔字段
        const std::vector<std::string> boolFields = {"trigger_switch", "weapon_switch", "flash_shield", "enabled"};
        for (const auto& field : boolFields) {
            auto it = row.find(field);
            if (it != row.end()) {
                const std::string& value = it->second;
                std::string jsonField;
                if (field == "trigger_switch") {
                    jsonField = "triggerSwitch";
                } else if (field == "weapon_switch") {
                    jsonField = "weaponSwitch";
                } else if (field == "flash_shield") {
                    jsonField = "flashShield";
                } else {
                    jsonField = "enabled";
                }
                obj->set(jsonField, value == "1" || value == "true");
            }
        }
    } catch (const std::exception& ex) {
        LOG_ERROR("行转JSON异常: {}", ex.what());
    }
    
    return obj;
}

// 获取最后一次错误信息
std::string FunctionService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 