#include "login_service.h"
#include "../../utils/logger.h"
#include "../blsql/login_db.h"

#include <Poco/JSON/Parser.h>
#include <Poco/Dynamic/Var.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace blweb {
namespace services {

// 单例实现
LoginService& LoginService::getInstance() {
    static LoginService instance;
    return instance;
}

// 构造函数
LoginService::LoginService() : m_lastError("") {
    LOG_INFO("LoginService已初始化");
}

// 析构函数
LoginService::~LoginService() {
    LOG_INFO("LoginService已销毁");
}

// 处理登录信息修改请求
Poco::JSON::Object::Ptr LoginService::handleLoginModify(
    const std::string& username,
    const std::string& password,
    const std::string& token
) {
    LOG_INFO("处理登录信息修改 - 用户: '{}'", username);
    Poco::JSON::Object::Ptr response = new Poco::JSON::Object();
    
    try {
        // 参数验证
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            response->set("code", 400);
            response->set("msg", m_lastError);
            return response;
        }
        
        // 调用数据库层更新登录信息
        blweb::db::LoginDB& loginDB = blweb::db::LoginDB::getInstance();
        bool success = loginDB.updateLoginInfo(username, password, token);
        
        if (!success) {
            m_lastError = "更新登录信息失败: " + loginDB.getLastError();
            LOG_ERROR("{}", m_lastError);
            
            response->set("code", 500);
            response->set("msg", m_lastError);
            return response;
        }
        
        // 获取最新的登录信息
        auto loginInfo = loginDB.getLoginInfo(username);
        Poco::JSON::Object::Ptr dataObj = new Poco::JSON::Object();
        
        dataObj->set("username", username);
        if (loginInfo.find("token") != loginInfo.end()) {
            dataObj->set("token", loginInfo["token"]);
        } else if (!token.empty()) {
            dataObj->set("token", token);
        }
        
        if (loginInfo.find("created_at") != loginInfo.end()) {
            dataObj->set("createdAt", loginInfo["created_at"]);
        }
        
        if (loginInfo.find("updated_at") != loginInfo.end()) {
            dataObj->set("updatedAt", loginInfo["updated_at"]);
        }
        
        if (loginInfo.find("last_login") != loginInfo.end()) {
            dataObj->set("lastLogin", loginInfo["last_login"]);
        }
        
        response->set("code", 200);
        response->set("msg", "登录信息更新成功");
        response->set("data", dataObj);
        
        LOG_INFO("用户 '{}' 的登录信息已成功更新", username);
    } catch (const std::exception& ex) {
        m_lastError = std::string("处理登录信息修改异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        response->set("code", 500);
        response->set("msg", m_lastError);
    }
    
    return response;
}

// 验证用户登录
Poco::JSON::Object::Ptr LoginService::verifyUserLogin(
    const std::string& username,
    const std::string& password
) {
    LOG_INFO("验证用户登录 - 用户: '{}'", username);
    Poco::JSON::Object::Ptr response = new Poco::JSON::Object();
    
    try {
        // 参数验证
        if (username.empty() || password.empty()) {
            m_lastError = "用户名和密码不能为空";
            LOG_ERROR("{}", m_lastError);
            
            response->set("code", 400);
            response->set("msg", m_lastError);
            return response;
        }
        
        // 调用数据库层验证登录
        blweb::db::LoginDB& loginDB = blweb::db::LoginDB::getInstance();
        bool isValid = loginDB.verifyLogin(username, password);
        
        if (!isValid) {
            m_lastError = "登录验证失败: " + loginDB.getLastError();
            LOG_WARNING("{}", m_lastError);
            
            response->set("code", 401);
            response->set("msg", "用户名或密码不正确");
            return response;
        }
        
        // 更新最后登录时间
        bool timeUpdateSuccess = loginDB.updateLastLoginTime(username);
        if (!timeUpdateSuccess) {
            LOG_WARNING("更新最后登录时间失败: {}", loginDB.getLastError());
        }
        
        // 获取用户信息，包括isPro状态
        std::string sql = "SELECT username, token, created_at, updated_at, last_login, is_pro FROM admin_user WHERE username = ?";
        std::vector<std::string> params = {username};
        auto rows = loginDB.executeQueryParams(sql, params);
        
        if (rows.empty()) {
            m_lastError = "获取用户信息失败";
            LOG_ERROR("{}", m_lastError);
            
            response->set("code", 404);
            response->set("msg", m_lastError);
            return response;
        }
        
        // 构建数据对象
        Poco::JSON::Object::Ptr dataObj = new Poco::JSON::Object();
        
        // 用户基本信息
        dataObj->set("username", username);
        
        auto& row = rows[0];
        // 如果数据库中没有token，生成一个标准格式的token
        if (row.find("token") != row.end() && !row["token"].empty()) {
            dataObj->set("token", row["token"]);
        } else {
            // 生成标准格式的token
            std::string newToken = "blweb_token_" + username;
            dataObj->set("token", newToken);
            
            // 更新数据库中的token
            LOG_INFO("为用户 '{}' 生成新的token: {}", username, newToken);
            loginDB.updateLoginInfo(username, "", newToken);
        }
        
        if (row.find("created_at") != row.end()) {
            dataObj->set("createdAt", row["created_at"]);
        }
        
        if (row.find("updated_at") != row.end()) {
            dataObj->set("updatedAt", row["updated_at"]);
        }
        
        if (row.find("last_login") != row.end()) {
            dataObj->set("lastLogin", row["last_login"]);
        }
        
        // 处理isPro字段
        if (row.find("is_pro") != row.end()) {
            // 数据库中is_pro值为"1"表示true，"0"表示false
            bool isPro = (row["is_pro"] == "1");
            dataObj->set("isPro", isPro);
            LOG_INFO("用户 '{}' 的Pro状态: {}", username, isPro ? "Pro版本" : "标准版本");
        } else {
            dataObj->set("isPro", false);
            LOG_INFO("用户 '{}' 的Pro状态未找到，默认设为false", username);
        }
        
        response->set("code", 200);
        response->set("msg", "登录成功");
        response->set("data", dataObj);
        
        LOG_INFO("用户 '{}' 登录成功", username);
    } catch (const std::exception& ex) {
        m_lastError = std::string("验证用户登录异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        response->set("code", 500);
        response->set("msg", m_lastError);
    }
    
    return response;
}

// 通过令牌验证用户登录
Poco::JSON::Object::Ptr LoginService::verifyUserByToken(
    const std::string& username,
    const std::string& token
) {
    LOG_INFO("通过令牌验证用户登录 - 用户: '{}', 令牌: '{}'", username, token);
    Poco::JSON::Object::Ptr response = new Poco::JSON::Object();
    
    try {
        // 参数验证
        if (username.empty() || token.empty()) {
            m_lastError = "用户名和令牌不能为空";
            LOG_ERROR("{}", m_lastError);
            
            response->set("code", 400);
            response->set("msg", m_lastError);
            return response;
        }
        
        // 调用数据库层验证令牌
        blweb::db::LoginDB& loginDB = blweb::db::LoginDB::getInstance();
        
        // 如果token是标准格式（blweb_token_用户名），但数据库中无token记录，先尝试更新
        if (token.find("blweb_token_" + username) == 0) {
            LOG_INFO("令牌格式符合标准格式，检查是否需要更新数据库记录");
            
            // 获取当前数据库中的token信息
            std::string checkSql = "SELECT token FROM admin_user WHERE username = ?";
            std::vector<std::string> checkParams = {username};
            auto checkRows = loginDB.executeQueryParams(checkSql, checkParams);
            
            if (!checkRows.empty() && (checkRows[0]["token"].empty() || checkRows[0]["token"] != token)) {
                LOG_INFO("数据库中的令牌需要更新，正在更新...");
                loginDB.updateLoginInfo(username, "", token);
            }
        }
        
        // 查询用户信息，验证令牌
        std::string sql = "SELECT username, token, created_at, updated_at, last_login, is_pro FROM admin_user WHERE username = ? AND token = ?";
        std::vector<std::string> params = {username, token};
        auto rows = loginDB.executeQueryParams(sql, params);
        
        if (rows.empty()) {
            // 尝试使用标准格式的token进行二次验证
            std::string standardToken = "blweb_token_" + username;
            if (token != standardToken) {
                LOG_INFO("尝试使用标准格式令牌进行二次验证: {}", standardToken);
                params = {username, standardToken};
                rows = loginDB.executeQueryParams(sql, params);
                
                if (!rows.empty()) {
                    LOG_INFO("标准格式令牌验证成功，更新客户端提供的令牌");
                    loginDB.updateLoginInfo(username, "", standardToken);
                }
            }
            
            if (rows.empty()) {
                m_lastError = "令牌验证失败";
                LOG_WARNING("{}", m_lastError);
                
                response->set("code", 401);
                response->set("msg", "令牌无效或已过期");
                return response;
            }
        }
        
        // 更新最后登录时间
        bool timeUpdateSuccess = loginDB.updateLastLoginTime(username);
        if (!timeUpdateSuccess) {
            LOG_WARNING("更新最后登录时间失败: {}", loginDB.getLastError());
        }
        
        // 构建数据对象
        Poco::JSON::Object::Ptr dataObj = new Poco::JSON::Object();
        
        // 用户基本信息
        dataObj->set("username", username);
        
        // 使用验证通过的token
        if (!rows.empty() && rows[0].find("token") != rows[0].end() && !rows[0]["token"].empty()) {
            dataObj->set("token", rows[0]["token"]);
        } else {
            dataObj->set("token", token);
        }
        
        auto& row = rows[0];
        if (row.find("created_at") != row.end()) {
            dataObj->set("createdAt", row["created_at"]);
        }
        
        if (row.find("updated_at") != row.end()) {
            dataObj->set("updatedAt", row["updated_at"]);
        }
        
        if (row.find("last_login") != row.end()) {
            dataObj->set("lastLogin", row["last_login"]);
        }
        
        // 处理isPro字段
        if (row.find("is_pro") != row.end()) {
            // 数据库中is_pro值为"1"表示true，"0"表示false
            bool isPro = (row["is_pro"] == "1");
            dataObj->set("isPro", isPro);
            LOG_INFO("用户 '{}' 的Pro状态: {}", username, isPro ? "Pro版本" : "标准版本");
        } else {
            dataObj->set("isPro", false);
            LOG_INFO("用户 '{}' 的Pro状态未找到，默认设为false", username);
        }
        
        response->set("code", 200);
        response->set("msg", "令牌验证成功");
        response->set("data", dataObj);
        
        LOG_INFO("用户 '{}' 通过令牌登录成功", username);
    } catch (const std::exception& ex) {
        m_lastError = std::string("令牌验证异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        response->set("code", 500);
        response->set("msg", m_lastError);
    }
    
    return response;
}

// 获取用户登录信息
Poco::JSON::Object::Ptr LoginService::getUserLoginInfo(const std::string& username) {
    LOG_INFO("获取用户登录信息 - 用户: '{}'", username);
    Poco::JSON::Object::Ptr response = new Poco::JSON::Object();
    
    try {
        // 参数验证
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            response->set("code", 400);
            response->set("msg", m_lastError);
            return response;
        }
        
        // 调用数据库层获取登录信息
        blweb::db::LoginDB& loginDB = blweb::db::LoginDB::getInstance();
        auto loginInfo = loginDB.getLoginInfo(username);
        
        if (loginInfo.empty()) {
            m_lastError = "获取登录信息失败: " + loginDB.getLastError();
            LOG_WARNING("{}", m_lastError);
            
            response->set("code", 404);
            response->set("msg", "用户不存在");
            return response;
        }
        
        // 构建返回数据
        Poco::JSON::Object::Ptr dataObj = new Poco::JSON::Object();
        
        dataObj->set("username", username);
        if (loginInfo.find("token") != loginInfo.end() && !loginInfo["token"].empty()) {
            dataObj->set("token", loginInfo["token"]);
        } else {
            // 生成标准格式的token并更新
            std::string newToken = "blweb_token_" + username;
            dataObj->set("token", newToken);
            loginDB.updateLoginInfo(username, "", newToken);
            LOG_INFO("为用户 '{}' 生成新的token: {}", username, newToken);
        }
        
        if (loginInfo.find("created_at") != loginInfo.end()) {
            dataObj->set("createdAt", loginInfo["created_at"]);
        }
        
        if (loginInfo.find("updated_at") != loginInfo.end()) {
            dataObj->set("updatedAt", loginInfo["updated_at"]);
        }
        
        if (loginInfo.find("last_login") != loginInfo.end()) {
            dataObj->set("lastLogin", loginInfo["last_login"]);
        }
        
        // 处理isPro字段
        if (loginInfo.find("is_pro") != loginInfo.end()) {
            // 数据库中is_pro值为"1"表示true，"0"表示false
            bool isPro = (loginInfo["is_pro"] == "1");
            dataObj->set("isPro", isPro);
            LOG_INFO("用户 '{}' 的Pro状态: {}", username, isPro ? "Pro版本" : "标准版本");
        } else {
            dataObj->set("isPro", false);
            LOG_INFO("用户 '{}' 的Pro状态未找到，默认设为false", username);
        }
        
        response->set("code", 200);
        response->set("msg", "获取登录信息成功");
        response->set("data", dataObj);
        
        LOG_INFO("成功获取用户 '{}' 的登录信息", username);
    } catch (const std::exception& ex) {
        m_lastError = std::string("获取用户登录信息异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        response->set("code", 500);
        response->set("msg", m_lastError);
    }
    
    return response;
}

// 获取最后一次错误信息
std::string LoginService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 