#ifndef HOME_SERVICE_H
#define HOME_SERVICE_H

#include "Poco/JSON/Object.h"
#include <string>

namespace blweb {
namespace services {

/**
 * @class HomeService
 * @brief 首页配置服务类
 * 
 * 该类负责处理首页配置相关操作，包括更新和获取用户的首页配置
 */
class HomeService {
public:
    /**
     * @brief 获取HomeService单例实例
     * @return HomeService& 单例引用
     */
    static HomeService& getInstance();
    
    /**
     * @brief 构造函数
     */
    HomeService();
    
    /**
     * @brief 析构函数
     */
    virtual ~HomeService();
    
    /**
     * @brief 更新用户的首页配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @param cardKey 卡密
     * @return bool 是否更新成功
     */
    bool updateHomeConfig(const std::string& username, const std::string& gameName, const std::string& cardKey);
    
    /**
     * @brief 获取用户的首页配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含首页配置的JSON对象
     */
    Poco::JSON::Object::Ptr getHomeConfig(const std::string& username, const std::string& gameName);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace services
} // namespace blweb

#endif // HOME_SERVICE_H 