#include "data_collection_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blsql/data_collections_db.h"

#include <Poco/JSON/Parser.h>
#include <Poco/Dynamic/Var.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>
#include <Poco/File.h>
#include <Poco/DirectoryIterator.h>
#include <Poco/Path.h>
#include <Poco/Net/SocketAddress.h>
#include <Poco/Net/StreamSocket.h>
#include <Poco/Net/NetException.h>
#include <Poco/Zip/Compress.h>
#include <Poco/TemporaryFile.h>
#include <filesystem>
#include <algorithm>
#include <ctime>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <Poco/Thread.h>

// 配置项：上传成功后是否删除源文件
const bool DELETE_SOURCE_AFTER_UPLOAD = true; // 可以修改为false来保留源文件

namespace blweb {
namespace services {

// 单例实现
DataCollectionService& DataCollectionService::getInstance() {
    static DataCollectionService instance;
    return instance;
}

// 构造函数
DataCollectionService::DataCollectionService() : m_lastError("") {
    LOG_INFO("DataCollectionService已初始化");
}

// 析构函数
DataCollectionService::~DataCollectionService() {
    LOG_INFO("DataCollectionService已销毁");
}

// 获取用户的数据采集配置
Poco::JSON::Object::Ptr DataCollectionService::getDataCollection(
    const std::string& username,
    const std::string& gameName
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("获取数据采集配置 - 用户: {}, 游戏: {}", username, gameName);
        
        // 检查参数
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 调用数据库层获取配置
        blweb::db::DataCollectionsDB& dataCollectionDB = blweb::db::DataCollectionsDB::getInstance();
        auto dataCollectionConfig = dataCollectionDB.getDataCollection(username, gameName);
        
        // 如果配置存在(非空)
        if (!dataCollectionConfig.empty()) {
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            data->set("createdAt", dataCollectionConfig["created_at"]);
            data->set("updatedAt", dataCollectionConfig["updated_at"]);
            
            result->set("code", 200);
            result->set("msg", "获取数据采集配置成功");
            result->set("data", data);
            
            LOG_INFO("获取数据采集配置成功 - 用户: {}, 游戏: {}", username, gameName);
        } else {
            // 如果配置不存在，返回默认值
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            
            result->set("code", 200);
            result->set("msg", "配置不存在，返回默认值");
            result->set("data", data);
            
            LOG_INFO("数据采集配置不存在，返回默认值 - 用户: {}, 游戏: {}", username, gameName);
        }
        
        return result;
    }
    catch (const std::exception& ex) {
        m_lastError = std::string("获取数据采集配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("code", 500);
        result->set("msg", m_lastError);
        return result;
    }
}

// 获取数据采集目录信息
Poco::JSON::Object::Ptr DataCollectionService::fetchDataCollection(
    const std::string& username,
    const std::string& gameName
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("获取数据采集目录信息 - 用户: {}, 游戏: {}", 
                 username, gameName);
        
        // 检查参数
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 固定的主目录和固定的分类目录 - API文档规定的文件夹结构
        const std::string imgsDir = "./imgs";
        const std::vector<std::string> categories = {"maps", "targets"};
        
        // 准备返回数据 - 符合API文档的响应格式
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", username);
        data->set("gameName", gameName);
        
        Poco::JSON::Array::Ptr collectionsArray = new Poco::JSON::Array();
        int totalCollections = 0;
        
        LOG_INFO("开始扫描imgs目录中的所有数据采集文件夹");
        
        // 确保imgs目录存在
        if (!std::filesystem::exists(imgsDir)) {
            LOG_WARNING("imgs目录不存在，创建目录");
            std::filesystem::create_directories(imgsDir);
        }
        
        // 遍历imgs目录中的所有子目录 - 每个子目录代表一个数据采集
        for (const auto& entry : std::filesystem::directory_iterator(imgsDir)) {
            if (entry.is_directory()) {
                std::string collectionName = entry.path().filename().string();
                std::string collectionPath = entry.path().string();
                
                LOG_INFO("发现数据采集文件夹: {}", collectionName);
                
                // 为每个采集目录创建一个详情对象 - 符合API文档中的结构
                Poco::JSON::Object::Ptr collectionInfo = new Poco::JSON::Object();
                collectionInfo->set("name", collectionName);
                collectionInfo->set("path", collectionPath);
                
                Poco::JSON::Array::Ptr folders = new Poco::JSON::Array();
                int totalImages = 0;
                int totalLabels = 0;
                
                // 创建详细分类统计对象
                Poco::JSON::Object::Ptr detailedStats = new Poco::JSON::Object();
                std::string lastCollectionUpdateTime = "N/A";
                bool hasCollectionUpdateTime = false;
                
                // 扫描该采集目录中的所有分类目录 (maps, targets)
                for (const auto& category : categories) {
                    std::string categoryPath = collectionPath + "/" + category;
                    if (!std::filesystem::exists(categoryPath)) {
                        // 如果分类目录不存在，创建一个空的统计对象
                        LOG_INFO("分类目录不存在: {}, 创建空统计", categoryPath);
                        
                        // 分类目录信息对象
                        Poco::JSON::Object::Ptr folderInfo = new Poco::JSON::Object();
                        folderInfo->set("name", category);
                        folderInfo->set("imageCount", 0);
                        folderInfo->set("labelCount", 0);
                        folderInfo->set("fileCount", 0);
                        folderInfo->set("lastUpdated", "N/A");
                        
                        folders->add(folderInfo);
                        
                        // 创建分类详细统计对象
                        Poco::JSON::Object::Ptr categoryDetail = new Poco::JSON::Object();
                        categoryDetail->set("images", 0);
                        categoryDetail->set("labels", 0);
                        categoryDetail->set("total", 0);
                        detailedStats->set(category, categoryDetail);
                        continue;
                    }
                    
                    try {
                        // 分类目录信息对象
                        Poco::JSON::Object::Ptr folderInfo = new Poco::JSON::Object();
                        folderInfo->set("name", category);
                            
                        // 图像和标签计数
                        int imageCount = 0;
                        int labelCount = 0;
                        auto lastUpdated = std::chrono::system_clock::now();
                        bool hasUpdatedTime = false;
                            
                        // 检查和扫描images目录
                        std::string imagesDir = categoryPath + "/images";
                        if (std::filesystem::exists(imagesDir)) {
                            try {
                                for (const auto& file : std::filesystem::directory_iterator(imagesDir)) {
                                    if (file.is_regular_file()) {
                                        imageCount++;
                                        
                                        // 更新最后修改时间
                                        try {
                                            auto fileTime = file.last_write_time();
                                            auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                                                fileTime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
                                            
                                            if (!hasUpdatedTime || sctp > lastUpdated) {
                                                lastUpdated = sctp;
                                                hasUpdatedTime = true;
                                            }
                                        } catch (const std::exception& e) {
                                            LOG_WARNING("获取文件时间时出错: {} - {}", file.path().string(), e.what());
                                        }
                                    }
                                }
                                LOG_INFO("图像目录 {} 中发现 {} 个文件", imagesDir, imageCount);
                            } catch (const std::exception& e) {
                                LOG_ERROR("遍历图像目录时异常: {} - {}", imagesDir, e.what());
                            }
                        } else {
                            std::filesystem::create_directories(imagesDir);
                            LOG_INFO("创建图像目录: {}", imagesDir);
                        }
                        
                        // 检查和扫描labels目录
                        std::string labelsDir = categoryPath + "/labels";
                        if (std::filesystem::exists(labelsDir)) {
                            try {
                                for (const auto& file : std::filesystem::directory_iterator(labelsDir)) {
                                    if (file.is_regular_file()) {
                                        labelCount++;
                                        
                                        // 更新最后修改时间
                                        try {
                                            auto fileTime = file.last_write_time();
                                            auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                                                fileTime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
                                            
                                            if (!hasUpdatedTime || sctp > lastUpdated) {
                                                lastUpdated = sctp;
                                                hasUpdatedTime = true;
                                            }
                                        } catch (const std::exception& e) {
                                            LOG_WARNING("获取文件时间时出错: {} - {}", file.path().string(), e.what());
                                        }
                                    }
                                }
                                LOG_INFO("标签目录 {} 中发现 {} 个文件", labelsDir, labelCount);
                            } catch (const std::exception& e) {
                                LOG_ERROR("遍历标签目录时异常: {} - {}", labelsDir, e.what());
                            }
                        } else {
                            std::filesystem::create_directories(labelsDir);
                            LOG_INFO("创建标签目录: {}", labelsDir);
                        }
                        
                        // 更新总计数
                        totalImages += imageCount;
                        totalLabels += labelCount;
                                
                        // 设置文件夹信息
                        folderInfo->set("imageCount", imageCount);
                        folderInfo->set("labelCount", labelCount);
                        folderInfo->set("fileCount", imageCount + labelCount);
                        
                        // 格式化最后更新时间
                        std::string lastUpdatedStr = "N/A";
                        if (hasUpdatedTime) {
                            try {
                                // 将系统时钟时间点转换为 time_t
                                std::time_t timeT = std::chrono::system_clock::to_time_t(lastUpdated);
                                // 使用Poco的DateTime来格式化时间，避免直接操作可能导致的错误
                                Poco::DateTime dateTime(Poco::Timestamp::fromEpochTime(timeT));
                                lastUpdatedStr = Poco::DateTimeFormatter::format(dateTime, "%Y-%m-%dT%H:%M:%SZ");
                                
                                // 更新整个收集的最后更新时间
                                if (!hasCollectionUpdateTime) {
                                    lastCollectionUpdateTime = lastUpdatedStr;
                                    hasCollectionUpdateTime = true;
                                } else {
                                    // 尝试比较时间
                                    try {
                                        // 正确使用DateTimeParser
                                        int tzd;
                                        Poco::DateTime parsedTime;
                                        Poco::DateTimeParser::parse("%Y-%m-%dT%H:%M:%SZ", lastCollectionUpdateTime, parsedTime, tzd);
                                        
                                        // 如果当前时间更新，则更新收集时间
                                        if (lastUpdated > std::chrono::system_clock::from_time_t(parsedTime.timestamp().epochTime())) {
                                            lastCollectionUpdateTime = lastUpdatedStr;
                                        }
                                    } catch (const std::exception& e) {
                                        LOG_WARNING("比较时间出错: {} - 使用新的更新时间", e.what());
                                        lastCollectionUpdateTime = lastUpdatedStr;
                                    }
                                }
                            } catch (const std::exception& e) {
                                LOG_WARNING("格式化时间出错: {} - 使用默认值", e.what());
                                lastUpdatedStr = "N/A";
                            }
                        }
                        folderInfo->set("lastUpdated", lastUpdatedStr);
                        
                        // 添加文件夹到结果
                        folders->add(folderInfo);
                        
                        // 创建分类详细统计对象
                        Poco::JSON::Object::Ptr categoryDetail = new Poco::JSON::Object();
                        categoryDetail->set("images", imageCount);
                        categoryDetail->set("labels", labelCount);
                        categoryDetail->set("total", imageCount + labelCount);
                        detailedStats->set(category, categoryDetail);
                        
                        LOG_INFO("添加文件夹到结果: {}, 图像: {}, 标签: {}", category, imageCount, labelCount);
                    } catch (const std::exception& e) {
                        LOG_ERROR("处理分类目录 {} 时出现异常: {}", category, e.what());
                        // 继续处理下一个分类，不中断整个过程
                    }
                }
                
                // 确保所有分类都有统计信息
                for (const auto& category : categories) {
                    if (!detailedStats->has(category)) {
                        Poco::JSON::Object::Ptr catSummary = new Poco::JSON::Object();
                        catSummary->set("images", 0);
                        catSummary->set("labels", 0);
                        catSummary->set("total", 0);
                        detailedStats->set(category, catSummary);
                    }
                }
                
                // 添加汇总数据
                Poco::JSON::Object::Ptr totalSummary = new Poco::JSON::Object();
                totalSummary->set("images", totalImages);
                totalSummary->set("labels", totalLabels);
                totalSummary->set("total", totalImages + totalLabels);
                
                // 添加统计信息到采集信息 - 严格按照API文档的结构
                collectionInfo->set("folders", folders);
                collectionInfo->set("totalFiles", totalImages + totalLabels);
                collectionInfo->set("detailedStats", detailedStats);
                collectionInfo->set("summary", totalSummary);
                collectionInfo->set("lastUpdated", hasCollectionUpdateTime ? lastCollectionUpdateTime : 
                                   Poco::DateTimeFormatter::format(Poco::DateTime(), "%Y-%m-%dT%H:%M:%SZ"));
                
                // 添加到采集数组
                collectionsArray->add(collectionInfo);
                totalCollections++;
            }
        }
        
        // 设置返回数据
        data->set("collections", collectionsArray);
        data->set("totalCollections", totalCollections);
        data->set("timestamp", Poco::DateTimeFormatter::format(Poco::DateTime(), "%Y-%m-%dT%H:%M:%SZ"));
        
        result->set("code", 200);
        result->set("msg", "获取数据采集目录信息成功");
        result->set("data", data);
        
        LOG_INFO("获取数据采集目录信息成功 - 共 {} 个采集目录", totalCollections);
        
        return result;
    }
    catch (const std::exception& ex) {
        m_lastError = std::string("获取数据采集目录信息异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("code", 500);
        result->set("msg", m_lastError);
        return result;
    }
}

// 上传数据采集内容到远程服务器
Poco::JSON::Object::Ptr DataCollectionService::uploadDataCollection(
    const std::string& username,
    const std::string& gameName,
    const std::string& collectionName,
    const std::string& teamSide,
    const std::string& mapHotkey,
    const std::string& targetHotkey,
    const std::string& timestamp,
    bool deleteAll
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("上传数据采集内容 - 用户: {}, 游戏: {}, 采集名称: {}", 
                 username, gameName, collectionName);
        LOG_INFO("参数: 阵营: {}, 地图热键: {}, 目标热键: {}, 时间戳: {}, 删除所有文件夹: {}", 
                 teamSide, mapHotkey, targetHotkey, timestamp, deleteAll ? "是" : "否");
        
        // 检查参数
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 使用非const局部变量存储最终使用的采集名称
        std::string finalCollectionName = collectionName;
        
        if (finalCollectionName.empty()) {
            LOG_INFO("未提供采集名称，尝试在imgs目录中查找");
            
            // 首先检查imgs目录是否存在
            std::string defaultCollectionName = "";
            std::filesystem::path imgsPath("./imgs");
            
            if (std::filesystem::exists(imgsPath) && std::filesystem::is_directory(imgsPath)) {
                std::vector<std::string> collections;
                
                // 列出所有采集目录
                for (const auto& entry : std::filesystem::directory_iterator(imgsPath)) {
                    if (entry.is_directory()) {
                        collections.push_back(entry.path().filename().string());
                    }
                }
                
                if (!collections.empty()) {
                    // 如果有多个目录，选择最近修改的一个
                    std::filesystem::file_time_type latest_time;
                    bool first = true;
                    
                    for (const auto& collection : collections) {
                        std::filesystem::path collection_path = imgsPath / collection;
                        auto mod_time = std::filesystem::last_write_time(collection_path);
                        
                        if (first || mod_time > latest_time) {
                            defaultCollectionName = collection;
                            latest_time = mod_time;
                            first = false;
                        }
                    }
                    
                    LOG_INFO("使用目录中最新的采集名称: {}", defaultCollectionName);
                }
            }
            
            if (defaultCollectionName.empty()) {
                // 如果找不到任何采集名称，创建一个默认的
                auto now = std::chrono::system_clock::now();
                auto now_time_t = std::chrono::system_clock::to_time_t(now);
                
                std::tm now_tm;
                localtime_r(&now_time_t, &now_tm);
                
                std::ostringstream oss;
                oss << gameName << "_"
                    << std::put_time(&now_tm, "%Y%m%d_%H%M") << "_"
                    << username;
                
                defaultCollectionName = oss.str();
                LOG_INFO("未找到可用采集名称，使用默认生成的采集名称: {}", defaultCollectionName);
                
                // 创建采集目录结构
                std::string collectionPath = "./imgs/" + defaultCollectionName;
                LOG_INFO("创建默认采集目录结构: {}", collectionPath);
                
                try {
                    // 创建采集目录及子目录
                    std::filesystem::create_directories(collectionPath + "/maps/images");
                    std::filesystem::create_directories(collectionPath + "/maps/labels");
                    std::filesystem::create_directories(collectionPath + "/targets/images");
                    std::filesystem::create_directories(collectionPath + "/targets/labels");
                    
                    LOG_INFO("成功创建默认采集目录结构");
                } catch (const std::exception& e) {
                    LOG_WARNING("创建默认采集目录结构时出错: {}", e.what());
                }
            }
            
            finalCollectionName = defaultCollectionName;
        }
        
        // 固定的目录结构
        const std::string imgsDir = "./imgs";
        const std::string collectionPath = imgsDir + "/" + finalCollectionName;
        const std::vector<std::string> categories = {"maps", "targets"};
        
        LOG_INFO("准备上传数据采集目录: {}", collectionPath);
        
        // 验证指定的collectionName是否确实存在于imgs目录中
        bool collectionExists = false;
        if (std::filesystem::exists(imgsDir)) {
            for (const auto& entry : std::filesystem::directory_iterator(imgsDir)) {
                if (entry.is_directory() && entry.path().filename().string() == finalCollectionName) {
                    collectionExists = true;
                    break;
                }
            }
        }
        
        if (!collectionExists) {
            m_lastError = "数据采集目录不存在: " + collectionPath;
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 404);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 统计文件数量
        int totalImages = 0;
        int totalLabels = 0;
        
        // 对每个分类进行扫描
        Poco::JSON::Object::Ptr detailedStats = new Poco::JSON::Object();
        
        for (const auto& category : categories) {
            try {
                std::string categoryPath = collectionPath + "/" + category;
                int categoryImageCount = 0;
                int categoryLabelCount = 0;
                
                // 检查并统计图像目录中的文件
                std::string imagesDir = categoryPath + "/images";
                if (std::filesystem::exists(imagesDir)) {
                    try {
                        std::error_code ec;
                        for (const auto& entry : std::filesystem::directory_iterator(imagesDir, ec)) {
                            if (entry.is_regular_file()) {
                                categoryImageCount++;
                            }
                        }
                        
                        if (ec) {
                            LOG_WARNING("上传准备 - 扫描图像目录时出现错误: {} - {}", imagesDir, ec.message());
                        }
                        
                        totalImages += categoryImageCount;
                        LOG_INFO("上传准备 - 图像目录 {} 中发现 {} 个文件", imagesDir, categoryImageCount);
                    } catch (const std::exception& e) {
                        LOG_ERROR("遍历图像目录时异常: {} - {}", imagesDir, e.what());
                    }
                } else {
                    LOG_WARNING("图像目录不存在: {}", imagesDir);
                }
                
                // 检查并统计标签目录中的文件
                std::string labelsDir = categoryPath + "/labels";
                if (std::filesystem::exists(labelsDir)) {
                    try {
                        std::error_code ec;
                        for (const auto& entry : std::filesystem::directory_iterator(labelsDir, ec)) {
                            if (entry.is_regular_file()) {
                                categoryLabelCount++;
                            }
                        }
                        
                        if (ec) {
                            LOG_WARNING("上传准备 - 扫描标签目录时出现错误: {} - {}", labelsDir, ec.message());
                        }
                        
                        totalLabels += categoryLabelCount;
                        LOG_INFO("上传准备 - 标签目录 {} 中发现 {} 个文件", labelsDir, categoryLabelCount);
                    } catch (const std::exception& e) {
                        LOG_ERROR("遍历标签目录时异常: {} - {}", labelsDir, e.what());
                    }
                } else {
                    LOG_WARNING("标签目录不存在: {}", labelsDir);
                }
                
                // 添加分类统计信息
                Poco::JSON::Object::Ptr catDetail = new Poco::JSON::Object();
                catDetail->set("images", categoryImageCount);
                catDetail->set("labels", categoryLabelCount);
                catDetail->set("total", categoryImageCount + categoryLabelCount);
                
                detailedStats->set(category, catDetail);
            } catch (const std::exception& e) {
                LOG_ERROR("处理分类目录 {} 时出现异常: {}", category, e.what());
                // 继续处理下一个分类，不中断整个过程
                
                // 确保该分类有一个空的统计信息
                Poco::JSON::Object::Ptr catDetail = new Poco::JSON::Object();
                catDetail->set("images", 0);
                catDetail->set("labels", 0);
                catDetail->set("total", 0);
                detailedStats->set(category, catDetail);
            }
        }
        
        // 确保所有分类都有统计信息
        for (const auto& category : categories) {
            if (!detailedStats->has(category)) {
                Poco::JSON::Object::Ptr catDetail = new Poco::JSON::Object();
                catDetail->set("images", 0);
                catDetail->set("labels", 0);
                catDetail->set("total", 0);
                detailedStats->set(category, catDetail);
            }
        }
        
        int totalFiles = totalImages + totalLabels;
        // 检查是否有文件可上传
        if (totalFiles == 0) {
            LOG_WARNING("数据采集目录为空，无文件可上传");
            
            // 构建错误响应
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);       // 必需字段
            data->set("gameName", gameName);       // 必需字段
            data->set("uploadStatus", "failed");   // API文档要求：失败状态为"failed"
            data->set("uploadComplete", false);    // API文档要求：失败时为false
            data->set("filesDeleted", false);      // API文档要求：失败时为false
            
            Poco::JSON::Object::Ptr error = new Poco::JSON::Object();
            error->set("code", "NO_DATA");         // API文档定义的错误码
            error->set("message", "数据采集目录为空，无文件可上传");
            
            data->set("error", error);
            
            result->set("code", 400);
            result->set("msg", "数据采集目录为空，无文件可上传");
            result->set("data", data);
            
            return result;
        }
        
        LOG_INFO("开始上传数据采集内容，共有 {} 张图片, {} 个标签文件, 总计 {} 个文件", 
                totalImages, totalLabels, totalFiles);
                
        // 压缩数据采集目录为ZIP文件
        std::string zipFileName = finalCollectionName + ".zip";
        std::string zipFilePath = "./temp/" + zipFileName;
        
        // 确保临时目录存在
        if (!std::filesystem::exists("./temp")) {
            std::filesystem::create_directories("./temp");
        }
        
        // 创建ZIP文件
        try {
            std::ofstream out(zipFilePath, std::ios::binary);
            Poco::Zip::Compress compressor(out, true);
            
            // 添加目录到ZIP
            compressor.addRecursive(Poco::Path(collectionPath));
            compressor.close();
            out.close();
            
            LOG_INFO("成功创建ZIP文件: {}", zipFilePath);
        } catch (const std::exception& e) {
            m_lastError = std::string("创建ZIP文件失败: ") + e.what();
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 500);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 获取ZIP文件大小
        size_t fileSize = std::filesystem::file_size(zipFilePath);
        LOG_INFO("ZIP文件大小: {} 字节", fileSize);
        
        // 准备上传ZIP文件到远程服务器
        // const std::string serverIP = "***********"; // 替换为实际的服务器IP
        const std::string serverIP = "antyundata.antszy.com"; // 替换为实际的服务器IP
        const int serverPort = 5001;
        const int BUFFER_SIZE = 8192;
        
        LOG_INFO("开始上传文件到服务器 {}:{}", serverIP, serverPort);
        
        // 实际的TCP文件传输
        bool uploadSuccess = false;
        std::string uploadErrorMsg = "";
        
        try {
            // 连接到服务器
            Poco::Net::SocketAddress address(serverIP, serverPort);
            Poco::Net::StreamSocket socket;
            socket.connect(address);
            
            LOG_INFO("已连接到服务器 {}:{}，开始准备文件传输", serverIP, serverPort);
            
            // 确认文件是否存在且可读
            if (!std::filesystem::exists(zipFilePath)) {
                throw std::runtime_error("ZIP文件不存在: " + zipFilePath);
            }
            
            if (!std::filesystem::is_regular_file(zipFilePath)) {
                throw std::runtime_error("指定的路径不是一个常规文件: " + zipFilePath);
            }
            
            // 获取文件名和大小
            std::string fileName = Poco::Path(zipFilePath).getFileName();
            size_t fileSize = std::filesystem::file_size(zipFilePath);
            
            if (fileSize == 0) {
                LOG_WARNING("警告: 文件大小为0字节: {}", zipFilePath);
            }
            
            // 发送文件名（以null字符结束）
            LOG_INFO("发送文件名: {}", fileName);
            socket.sendBytes(fileName.c_str(), fileName.length() + 1);
            
            // 然后发送文件内容
            std::ifstream file(zipFilePath, std::ios::binary);
            if (!file.is_open()) {
                throw std::runtime_error("无法打开ZIP文件进行读取: " + zipFilePath);
            }
            
            LOG_INFO("开始传输文件内容，文件大小: {} 字节", fileSize);
            
            // 读取并发送文件数据
            char buffer[BUFFER_SIZE];
            size_t totalSent = 0;
            int progressPercent = 0;
            
            while (!file.eof()) {
                file.read(buffer, BUFFER_SIZE);
                std::streamsize bytesRead = file.gcount();
                
                if (bytesRead > 0) {
                    socket.sendBytes(buffer, bytesRead);
                    totalSent += bytesRead;
                    
                    // 打印上传进度
                    int newProgressPercent = (int)((totalSent * 100) / fileSize);
                    if (newProgressPercent > progressPercent) {
                        progressPercent = newProgressPercent;
                        LOG_INFO("上传进度: {}% ({}/{} 字节)", progressPercent, totalSent, fileSize);
                    }
                }
            }
            
            file.close();
            
            // 关闭套接字连接
            socket.close();
            
            LOG_INFO("文件上传完成，总共发送 {} 字节", totalSent);
            uploadSuccess = true;
            
            // 修改这部分代码，添加删除所有文件夹的功能
            if (uploadSuccess && DELETE_SOURCE_AFTER_UPLOAD) {
                // 如果需要删除所有文件夹
                if (deleteAll) {
                    try {
                        LOG_INFO("检测到deleteAll=true，准备删除imgs目录下的所有数据采集文件夹");
                        
                        // 遍历imgs目录删除所有子文件夹
                        const std::string imgsBaseDir = "./imgs";
                        
                        // 确保imgs目录存在
                        if (std::filesystem::exists(imgsBaseDir)) {
                            int totalDirsDeleted = 0;
                            
                            // 简化：直接循环遍历第一层子目录，然后删除它们
                            for (const auto& entry : std::filesystem::directory_iterator(imgsBaseDir)) {
                                if (entry.is_directory()) {
                                    try {
                                        std::string dirPath = entry.path().string();
                                        LOG_INFO("正在删除子目录: {}", dirPath);
                                        
                                        // 执行删除
                                        std::error_code ec;
                                        std::filesystem::remove_all(entry.path(), ec);
                                        
                                        if (ec) {
                                            LOG_WARNING("删除子目录时出现错误: {} - {}", dirPath, ec.message());
                                        } else {
                                            LOG_INFO("成功删除子目录: {}", dirPath);
                                            totalDirsDeleted++;
                                        }
                                    } catch (const std::exception& e) {
                                        LOG_WARNING("删除子目录时出现异常: {} - {}", entry.path().string(), e.what());
                                    }
                                }
                            }
                            
                            LOG_INFO("删除完成，共删除 {} 个子目录", totalDirsDeleted);
                        } else {
                            LOG_WARNING("imgs目录不存在: {}", imgsBaseDir);
                        }
                    } catch (const std::exception& e) {
                        LOG_ERROR("删除所有数据采集文件夹时出现异常: {}", e.what());
                    }
                } else {
                    // 原有代码：只删除当前上传的文件夹
                    try {
                        LOG_INFO("准备删除数据采集目录: {}", collectionPath);
                        
                        // 确保路径是有效的并且包含在imgs目录下
                        if (!collectionPath.empty() && 
                            collectionPath.find("./imgs/") == 0 && 
                            std::filesystem::exists(collectionPath)) {
                            
                            // 安全检查：确保要删除的是特定子文件夹而不是整个imgs目录
                            if (collectionPath != "./imgs" && 
                                collectionPath != "./imgs/" && 
                                collectionPath.length() > 7) { // 长度大于"./imgs/"
                                
                                // 在删除前记录文件数量
                                int filesCount = 0;
                                int dirCount = 0;
                                
                                // 统计要删除的文件数量
                                try {
                                    for (const auto& entry : std::filesystem::recursive_directory_iterator(collectionPath)) {
                                        if (entry.is_regular_file()) {
                                            filesCount++;
                                        } else if (entry.is_directory()) {
                                            dirCount++;
                                        }
                                    }
                                    
                                    LOG_INFO("即将删除目录 {} 中的 {} 个文件和 {} 个子目录", 
                                             collectionPath, filesCount, dirCount);
                                } catch (const std::exception& e) {
                                    LOG_WARNING("统计文件数量时异常: {} - {}", collectionPath, e.what());
                                    LOG_INFO("即将删除目录，但无法统计文件数量: {}", collectionPath);
                                }
                                
                                // 递归删除目录
                                std::error_code ec;
                                std::filesystem::remove_all(collectionPath, ec);
                                
                                if (ec) {
                                    LOG_WARNING("删除数据采集目录时出现错误: {} - {}", collectionPath, ec.message());
                                } else {
                                    LOG_INFO("成功删除数据采集目录: {}, 共清理 {} 个文件和 {} 个子目录",
                                            collectionPath, filesCount, dirCount);
                                }
                            } else {
                                LOG_WARNING("安全检查阻止删除顶级imgs目录: {}", collectionPath);
                            }
                        } else {
                            LOG_WARNING("无效的数据采集目录路径: {}", collectionPath);
                        }
                    } catch (const std::exception& e) {
                        LOG_WARNING("删除数据采集目录时异常: {} - {}", collectionPath, e.what());
                    }
                }
            } else if (uploadSuccess && !DELETE_SOURCE_AFTER_UPLOAD) {
                LOG_INFO("上传成功，但根据配置保留源文件: {}", collectionPath);
            }
        }
        catch (const Poco::Net::NetException& e) {
            uploadErrorMsg = std::string("网络错误: ") + e.what();
            LOG_ERROR("{}", uploadErrorMsg);
        }
        catch (const std::exception& e) {
            uploadErrorMsg = std::string("上传文件异常: ") + e.what();
            LOG_ERROR("{}", uploadErrorMsg);
        }
        
        // 清理临时文件
        try {
            std::filesystem::remove(zipFilePath);
            LOG_INFO("已删除临时ZIP文件: {}", zipFilePath);
        } catch (const std::exception& e) {
            LOG_WARNING("删除临时ZIP文件失败: {} - {}", zipFilePath, e.what());
        }
        
        // 生成当前的时间戳
        std::string currentTimestamp = Poco::DateTimeFormatter::format(Poco::DateTime(), "%Y-%m-%dT%H:%M:%SZ");
        
        // 根据上传结果构建响应 - 严格按照API文档中定义的格式
        // data_collection_api.md中定义了三种响应：成功(ok)、失败(error)和部分成功(warning)
        Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
        data->set("username", username);  // 必需字段
        data->set("gameName", gameName);  // 必需字段
        
        if (uploadSuccess) {
            // 成功响应 - 对应API文档中的"服务器响应（成功）"部分
            data->set("uploadStatus", "success"); // API文档要求：成功状态为"success"
            data->set("uploadComplete", true);    // API文档要求：成功时为true
            data->set("filesDeleted", DELETE_SOURCE_AFTER_UPLOAD); // API文档要求：是否删除了源文件
            data->set("message", totalFiles > 0 ? "数据上传成功并已删除源文件" : "目录结构上传成功（无文件）"); // API文档要求：成功消息
            
            result->set("code", 200);
            result->set("msg", "数据上传成功");
        } else {
            // 失败响应 - 对应API文档中的"服务器响应（失败）"部分
            data->set("uploadStatus", "failed");  // API文档要求：失败状态为"failed"
            data->set("uploadComplete", false);   // API文档要求：失败时为false
            data->set("filesDeleted", false);     // API文档要求：失败时为false
            
            // error对象 - API文档要求包含code和message
            Poco::JSON::Object::Ptr error = new Poco::JSON::Object();
            error->set("code", "SERVER_UNREACHABLE"); // API文档定义的错误码之一
            error->set("message", uploadErrorMsg);    // 错误描述
            
            data->set("error", error);
            
            result->set("code", 500);
            result->set("msg", "数据上传失败: " + uploadErrorMsg);
        }
        
        result->set("data", data);
        
        LOG_INFO("数据上传处理完成 - 状态: {}", uploadSuccess ? "成功" : "失败");
        
        return result;
    }
    catch (const std::exception& ex) {
        m_lastError = std::string("上传数据采集内容异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        // 检查是否已准备好部分数据
        if (result->has("data")) {
            // 部分成功响应 - 对应API文档中的"服务器响应（部分成功）"部分
            Poco::JSON::Object::Ptr data = result->getObject("data");
            
            data->set("uploadStatus", "partial");  // API文档要求：部分成功状态为"partial"
            data->set("uploadComplete", false);    // API文档要求：部分成功时为false
            data->set("filesDeleted", false);      // API文档要求：部分成功时为false
            data->set("message", "部分数据上传成功，但出现错误: " + m_lastError); // API文档要求：部分成功消息
            
            result->set("code", 200);
            result->set("msg", "部分数据上传成功，但出现错误: " + m_lastError);
            result->set("status", "warning"); // API文档要求：部分成功时状态为"warning"
            return result;
        } else {
            // 完全失败的响应 - 对应API文档中的"服务器响应（失败）"部分
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);       // 必需字段
            data->set("gameName", gameName);       // 必需字段
            
            data->set("uploadStatus", "failed");   // API文档要求：失败状态为"failed"
            data->set("uploadComplete", false);    // API文档要求：失败时为false
            data->set("filesDeleted", false);      // API文档要求：失败时为false
            
            // error对象 - API文档要求包含code和message
            Poco::JSON::Object::Ptr error = new Poco::JSON::Object();
            error->set("code", "SERVER_ERROR");    // API文档定义的错误码之一
            error->set("message", m_lastError);    // 错误描述
            
            data->set("error", error);
            
            result->set("code", 500);
            result->set("msg", m_lastError);
            result->set("data", data);
            
            return result;
        }
    }
}

// 获取最后一次错误信息
std::string DataCollectionService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 