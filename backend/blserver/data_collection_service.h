#ifndef DATA_COLLECTION_SERVICE_H
#define DATA_COLLECTION_SERVICE_H

#include <string>
#include <Poco/JSON/Object.h>

namespace blweb {
namespace services {

/**
 * @brief 数据采集服务类
 */
class DataCollectionService {
public:
    /**
     * @brief 获取单例实例
     * @return DataCollectionService& 单例实例引用
     */
    static DataCollectionService& getInstance();

    /**
     * @brief 析构函数
     */
    ~DataCollectionService();

    /**
     * @brief 获取用户的数据采集配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含数据采集配置的JSON对象
     */
    Poco::JSON::Object::Ptr getDataCollection(
        const std::string& username,
        const std::string& gameName
    );

    /**
     * @brief 获取当前数据采集目录信息
     * @param username 用户名
     * @param gameName 游戏名称
     * @return Poco::JSON::Object::Ptr 包含数据采集目录信息的JSON对象
     */
    Poco::JSON::Object::Ptr fetchDataCollection(
        const std::string& username,
        const std::string& gameName
    );

    /**
     * @brief 上传数据采集内容到远程服务器
     * @param username 用户名
     * @param gameName 游戏名称
     * @param collectionName 数据采集名称
     * @param teamSide 阵营选择
     * @param mapHotkey 地图热键
     * @param targetHotkey 目标热键
     * @param timestamp 时间戳
     * @param deleteAll 是否删除所有数据文件夹(默认为false，只删除当前上传的文件夹)
     * @return Poco::JSON::Object::Ptr 包含上传结果的JSON对象
     */
    Poco::JSON::Object::Ptr uploadDataCollection(
        const std::string& username,
        const std::string& gameName,
        const std::string& collectionName,
        const std::string& teamSide,
        const std::string& mapHotkey,
        const std::string& targetHotkey,
        const std::string& timestamp,
        bool deleteAll = false
    );

    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    DataCollectionService();

    /**
     * @brief 最后一次错误信息
     */
    std::string m_lastError;
};

} // namespace services
} // namespace blweb

#endif // DATA_COLLECTION_SERVICE_H 