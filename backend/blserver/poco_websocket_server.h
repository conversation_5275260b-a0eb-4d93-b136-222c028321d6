#pragma once

#include <string>
#include <functional>
#include <unordered_map>
#include <memory>

#include "Poco/JSON/Object.h"
#include "Poco/Net/WebSocket.h"
#include "utils/logger.h"

// 消息处理器基类
class MessageHandler {
public:
    virtual ~MessageHandler() = default;
    
    // 处理消息的方法
    virtual std::string handleMessage(const Poco::JSON::Object::Ptr& jsonObj) = 0;
};

// 心跳消息处理器
class HeartbeatHandler : public MessageHandler {
public:
    HeartbeatHandler();
    virtual std::string handleMessage(const Poco::JSON::Object::Ptr& jsonObj) override;
};

// Home配置修改消息处理器
class HomeModifyHandler : public MessageHandler {
public:
    HomeModifyHandler();
    virtual std::string handleMessage(const Poco::JSON::Object::Ptr& jsonObj) override;
};

// 消息处理器管理类
class MessageHandlerManager {
public:
    // 获取单例实例
    static MessageHandlerManager& getInstance();
    
    // 禁止拷贝和赋值
    MessageHandlerManager(const MessageHandlerManager&) = delete;
    MessageHandlerManager& operator=(const MessageHandlerManager&) = delete;
    
    // 注册消息处理器
    void registerHandler(const std::string& action, std::shared_ptr<MessageHandler> handler);
    
    // 处理消息
    std::string processMessage(const std::string& message, Poco::Net::WebSocket& ws);
    
private:
    // 私有构造函数
    MessageHandlerManager();
    
    // 处理器映射
    std::unordered_map<std::string, std::shared_ptr<MessageHandler>> m_handlers;
};

// WebSocket消息处理器类 - 处理WebSocket的消息循环
class WebSocketMessageProcessor {
public:
    // 获取单例实例
    static WebSocketMessageProcessor& getInstance();
    
    // 禁止拷贝和赋值
    WebSocketMessageProcessor(const WebSocketMessageProcessor&) = delete;
    WebSocketMessageProcessor& operator=(const WebSocketMessageProcessor&) = delete;
    
    // 处理WebSocket连接
    void processConnection(Poco::Net::WebSocket& ws);
    
    // 处理单个WebSocket消息
    bool processMessage(Poco::Net::WebSocket& ws, const char* buffer, int length, int flags);
    
private:
    // 私有构造函数
    WebSocketMessageProcessor();
    
    // 处理WebSocket文本消息
    void handleTextMessage(Poco::Net::WebSocket& ws, const std::string& message);
    
    // 处理Ping消息
    void handlePingMessage(Poco::Net::WebSocket& ws, const char* buffer, int length);
    
    // 处理Pong消息
    void handlePongMessage();
    
    // 处理关闭消息
    void handleCloseMessage(Poco::Net::WebSocket& ws, const char* buffer, int length, int flags);
}; 