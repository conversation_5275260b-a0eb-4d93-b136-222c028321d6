#ifndef AIM_SERVICE_H
#define AIM_SERVICE_H

#include <string>
#include <map>
#include <memory>
#include <Poco/JSON/Object.h>
#include "../blsql/aim_configs_db.h"

namespace blweb {
namespace services {

/**
 * @brief 瞄准配置服务类，处理瞄准相关配置的更新和获取
 */
class AimService {
public:
    /**
     * @brief 获取单例实例
     * @return 瞄准配置服务单例
     */
    static AimService& getInstance();

    /**
     * @brief 处理瞄准配置修改请求
     * @param username 用户名
     * @param gameName 游戏名称
     * @param jsonParams JSON参数对象
     * @return JSON响应对象
     */
    Poco::JSON::Object::Ptr handleAimModify(
        const std::string& username,
        const std::string& gameName,
        const Poco::JSON::Object::Ptr& jsonParams
    );

    /**
     * @brief 获取用户瞄准配置
     * @param username 用户名
     * @param gameName 游戏名称
     * @return JSON响应对象
     */
    Poco::JSON::Object::Ptr getAimConfig(
        const std::string& username,
        const std::string& gameName
    );

    /**
     * @brief 获取最后一次错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    /**
     * @brief 构造函数（私有）
     */
    AimService();

    /**
     * @brief 析构函数（私有）
     */
    ~AimService();

    /**
     * @brief 禁止拷贝构造函数
     */
    AimService(const AimService&) = delete;

    /**
     * @brief 禁止赋值操作符
     */
    AimService& operator=(const AimService&) = delete;

    /**
     * @brief 验证瞄准配置参数
     * @param jsonParams JSON参数对象
     * @return 验证结果
     */
    bool validateAimParams(const Poco::JSON::Object::Ptr& jsonParams);

    /**
     * @brief 从JSON参数中提取瞄准配置
     * @param jsonParams JSON参数对象
     * @return 配置映射
     */
    std::map<std::string, std::string> extractAimConfigs(const Poco::JSON::Object::Ptr& jsonParams);

    std::string m_lastError; // 最后一次错误信息
};

} // namespace services
} // namespace blweb

#endif // AIM_SERVICE_H 