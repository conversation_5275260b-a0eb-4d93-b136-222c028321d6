#pragma once

#include <string>
#include <map>
#include <Poco/JSON/Object.h>

namespace blweb {
namespace services {

/**
 * @class LoginService
 * @brief 登录服务类
 * 
 * 负责处理登录相关的业务逻辑，包括登录凭证验证、登录信息更新等
 */
class LoginService {
public:
    /**
     * @brief 获取LoginService单例实例
     * @return LoginService& 单例引用
     */
    static LoginService& getInstance();
    
    /**
     * @brief 处理登录信息修改请求
     * @param username 用户名
     * @param password 密码
     * @param token 令牌
     * @return Poco::JSON::Object::Ptr 包含处理结果的JSON响应
     */
    Poco::JSON::Object::Ptr handleLoginModify(
        const std::string& username,
        const std::string& password,
        const std::string& token
    );
    
    /**
     * @brief 验证用户登录
     * @param username 用户名
     * @param password 密码
     * @return Poco::JSON::Object::Ptr 包含验证结果的JSON响应
     */
    Poco::JSON::Object::Ptr verifyUserLogin(
        const std::string& username,
        const std::string& password
    );
    
    /**
     * @brief 通过令牌验证用户登录
     * @param username 用户名
     * @param token 令牌
     * @return Poco::JSON::Object::Ptr 包含验证结果的JSON响应
     */
    Poco::JSON::Object::Ptr verifyUserByToken(
        const std::string& username,
        const std::string& token
    );
    
    /**
     * @brief 获取用户登录信息
     * @param username 用户名
     * @return Poco::JSON::Object::Ptr 包含用户登录信息的JSON响应
     */
    Poco::JSON::Object::Ptr getUserLoginInfo(const std::string& username);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    /**
     * @brief 构造函数（私有）
     */
    LoginService();
    
    /**
     * @brief 析构函数（私有）
     */
    ~LoginService();
    
    /**
     * @brief 禁止拷贝构造函数
     */
    LoginService(const LoginService&) = delete;
    
    /**
     * @brief 禁止赋值操作符
     */
    LoginService& operator=(const LoginService&) = delete;
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace services
} // namespace blweb 