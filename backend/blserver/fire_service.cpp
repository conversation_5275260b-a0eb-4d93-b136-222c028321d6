#include "fire_service.h"
#include "../../utils/logger.h"
#include "../../utils/gmodels.h"
#include "../blsql/fire_configs_db.h"

#include <Poco/JSON/Parser.h>
#include <Poco/Dynamic/Var.h>
#include <Poco/DateTime.h>
#include <Poco/DateTimeFormatter.h>

namespace blweb {
namespace services {

// 单例实现
FireService& FireService::getInstance() {
    static FireService instance;
    return instance;
}

// 构造函数
FireService::FireService() : m_lastError("") {
    LOG_INFO("FireService已初始化");
}

// 析构函数
FireService::~FireService() {
    LOG_INFO("FireService已销毁");
}

// 更新用户的射击配置
bool FireService::updateFireConfig(
    const std::string& username,
    const std::string& gameName,
    int rifleSleep,
    int rifleInterval,
    int pistolSleep,
    int pistolInterval,
    int sniperSleep,
    int sniperInterval
) {
    try {
        LOG_INFO("更新射击配置 - 用户: {}, 游戏: {}", username, gameName);
        LOG_INFO("参数: rifleSleep={}, rifleInterval={}, pistolSleep={}, pistolInterval={}, sniperSleep={}, sniperInterval={}", 
                 rifleSleep, rifleInterval, pistolSleep, pistolInterval, sniperSleep, sniperInterval);
        
        // 调用数据库层保存配置
        blweb::db::FireConfigsDB& fireDB = blweb::db::FireConfigsDB::getInstance();
        bool success = fireDB.updateFireConfig(username, gameName, rifleSleep, rifleInterval, pistolSleep, pistolInterval, sniperSleep, sniperInterval);
        
        if (!success) {
            m_lastError = fireDB.getLastError();
            LOG_ERROR("保存射击配置失败: {}", m_lastError);
            return false;
        }
        
        // 更新全局配置变量
        if (username == webui::home::g_username && gameName == webui::home::g_game_name) {
            webui::fire::g_rifle_sleep = rifleSleep;
            webui::fire::g_rifle_interval = rifleInterval;
            webui::fire::g_pistol_sleep = pistolSleep;
            webui::fire::g_pistol_interval = pistolInterval;
            webui::fire::g_sniper_sleep = sniperSleep;
            webui::fire::g_sniper_interval = sniperInterval;
            LOG_INFO("全局射击配置变量已更新");
        }
        
        LOG_INFO("射击配置更新成功");
        return true;
    }
    catch (const std::exception& ex) {
        m_lastError = std::string("更新射击配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        return false;
    }
}

// 获取用户的射击配置
Poco::JSON::Object::Ptr FireService::getFireConfig(
    const std::string& username,
    const std::string& gameName
) {
    Poco::JSON::Object::Ptr result = new Poco::JSON::Object();
    
    try {
        LOG_INFO("获取射击配置 - 用户: {}, 游戏: {}", username, gameName);
        
        // 检查参数
        if (username.empty()) {
            m_lastError = "用户名不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        if (gameName.empty()) {
            m_lastError = "游戏名称不能为空";
            LOG_ERROR("{}", m_lastError);
            
            result->set("code", 400);
            result->set("msg", m_lastError);
            return result;
        }
        
        // 调用数据库层获取配置
        blweb::db::FireConfigsDB& fireDB = blweb::db::FireConfigsDB::getInstance();
        auto fireConfig = fireDB.getFireConfig(username, gameName);
        
        // 如果配置存在(非空)
        if (!fireConfig.empty()) {
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            data->set("rifleSleep", std::stoi(fireConfig["rifle_sleep"]));
            data->set("rifleInterval", std::stoi(fireConfig["rifle_interval"]));
            data->set("pistolSleep", std::stoi(fireConfig["pistol_sleep"]));
            data->set("pistolInterval", std::stoi(fireConfig["pistol_interval"]));
            data->set("sniperSleep", std::stoi(fireConfig["sniper_sleep"]));
            data->set("sniperInterval", std::stoi(fireConfig["sniper_interval"]));
            data->set("createdAt", fireConfig["created_at"]);
            data->set("updatedAt", fireConfig["updated_at"]);
            data->set("exists", true);
            
            result->set("code", 200);
            result->set("msg", "获取射击配置成功");
            result->set("data", data);
            
            LOG_INFO("获取射击配置成功 - 用户: {}, 游戏: {}", username, gameName);
        } else {
            // 如果配置不存在，返回默认值
            Poco::JSON::Object::Ptr data = new Poco::JSON::Object();
            data->set("username", username);
            data->set("gameName", gameName);
            data->set("rifleSleep", webui::fire::g_rifle_sleep);
            data->set("rifleInterval", webui::fire::g_rifle_interval);
            data->set("pistolSleep", webui::fire::g_pistol_sleep);
            data->set("pistolInterval", webui::fire::g_pistol_interval);
            data->set("sniperSleep", webui::fire::g_sniper_sleep);
            data->set("sniperInterval", webui::fire::g_sniper_interval);
            data->set("exists", false);
            
            result->set("code", 200);
            result->set("msg", "配置不存在，返回默认值");
            result->set("data", data);
            
            LOG_INFO("射击配置不存在，返回默认值 - 用户: {}, 游戏: {}", username, gameName);
        }
        
        return result;
    }
    catch (const std::exception& ex) {
        m_lastError = std::string("获取射击配置异常: ") + ex.what();
        LOG_ERROR("{}", m_lastError);
        
        result->set("code", 500);
        result->set("msg", m_lastError);
        return result;
    }
}

// 获取最后一次错误信息
std::string FireService::getLastError() const {
    return m_lastError;
}

} // namespace services
} // namespace blweb 