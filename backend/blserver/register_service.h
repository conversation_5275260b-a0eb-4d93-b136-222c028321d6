#pragma once

#include <string>
#include <Poco/JSON/Object.h>
#include <Poco/JSON/Array.h>

namespace blweb {
namespace services {

/**
 * @class RegisterService
 * @brief 用户注册服务类
 * 
 * 负责用户注册相关的业务逻辑处理
 */
class RegisterService {
public:
    /**
     * @brief 获取RegisterService单例实例
     * @return RegisterService& 单例引用
     */
    static RegisterService& getInstance();
    
    /**
     * @brief 析构函数
     */
    ~RegisterService();
    
    /**
     * @brief 处理用户注册请求
     * @param username 用户名
     * @param password 密码
     * @param defaultGame 默认游戏(可选)
     * @param gameList 游戏列表(可选)
     * @param isPro 是否为Pro版本用户(可选)
     * @return Poco::JSON::Object::Ptr 包含处理结果的JSON对象
     */
    Poco::JSON::Object::Ptr registerUser(
        const std::string& username, 
        const std::string& password,
        const std::string& defaultGame = "CS:GO",
        Poco::JSON::Array::Ptr gameList = nullptr,
        bool isPro = false
    );
    
    /**
     * @brief 检查用户名是否已存在
     * @param username 用户名
     * @return bool 存在返回true，不存在返回false
     */
    bool isUserExists(const std::string& username);
    
    /**
     * @brief 获取用户的所有配置信息
     * @param username 用户名
     * @return Poco::JSON::Object::Ptr 包含用户所有配置的JSON对象
     */
    Poco::JSON::Object::Ptr getUserAllConfigs(const std::string& username);
    
    /**
     * @brief 获取最后一次错误信息
     * @return std::string 错误信息
     */
    std::string getLastError() const;
    
private:
    /**
     * @brief 构造函数（私有，单例模式）
     */
    RegisterService();
    
    // 最后一次错误信息
    std::string m_lastError;
};

} // namespace services
} // namespace blweb 