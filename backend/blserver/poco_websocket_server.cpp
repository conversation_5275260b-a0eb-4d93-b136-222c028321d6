#include "poco_websocket_server.h"
#include "utils/gmodels.h"
#include "message_handlers.h"

#include "Poco/JSON/Parser.h"
#include "Poco/JSON/Object.h"
#include "Poco/JSON/JSONException.h"
#include "Poco/Dynamic/Var.h"
#include "Poco/Exception.h"

#include <iostream>
#include <sstream>
#include <thread>

//============================== HeartbeatHandler 实现 ==============================

HeartbeatHandler::HeartbeatHandler()
{
    // 白名单管理已集中到whitelist::initializeWhitelist()中
}

std::string HeartbeatHandler::handleMessage(const Poco::JSON::Object::Ptr& jsonObj)
{
    LOG_INFO("处理心跳消息");
    
    // 从消息中提取信息
    std::string type = jsonObj->optValue("type", std::string(""));
    std::string service = jsonObj->optValue("service", std::string(""));
    
    LOG_DEBUG("心跳类型: {}, 服务: {}", type, service);
    
    // 构造心跳响应
    Poco::JSON::Object response;
    response.set("status", "ok");
    response.set("action", "heartbeat");
    response.set("type", "pong");
    response.set("service", service);
    response.set("timestamp", (long)std::time(nullptr) * 1000);
    
    std::ostringstream oss;
    response.stringify(oss);
    return oss.str();
}

//============================== HomeModifyHandler 实现 ==============================

HomeModifyHandler::HomeModifyHandler()
{
    // 白名单管理已集中到whitelist::initializeWhitelist()中
}

std::string HomeModifyHandler::handleMessage(const Poco::JSON::Object::Ptr& jsonObj)
{
    LOG_INFO("处理Home配置修改消息");
    
    try
    {
        // 检查是否有content字段
        if (!jsonObj->has("content"))
        {
            LOG_WARNING("Home配置消息缺少content字段");
            
            Poco::JSON::Object response;
            response.set("status", "error");
            response.set("message", "缺少content字段");
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
        
        // 获取content对象
        Poco::JSON::Object::Ptr content = jsonObj->getObject("content");
        
        // 提取信息
        std::string username = content->optValue("username", std::string(""));
        std::string gameName = content->optValue("gameName", std::string(""));
        std::string cardKey = content->optValue("cardKey", std::string(""));
        
        LOG_INFO("用户名: {}, 游戏: {}, 卡密: {}", username, gameName, cardKey);
        
        // 响应
        Poco::JSON::Object response;
        response.set("status", "ok");
        response.set("action", "home_modify");
        response.set("message", "配置已更新");
        
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
    }
    catch (std::exception& e)
    {
        LOG_ERROR("处理Home配置消息异常: {}", e.what());
        
        Poco::JSON::Object response;
        response.set("status", "error");
        response.set("message", std::string("处理消息异常: ") + e.what());
        
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
    }
}

//============================== MessageHandlerManager 实现 ==============================

MessageHandlerManager::MessageHandlerManager()
{
    // 白名单管理已集中到whitelist::initializeWhitelist()中
    
    // 注册默认处理器
    registerHandler("heartbeat", std::make_shared<HeartbeatHandler>());
    registerHandler("home_modify", std::make_shared<HomeModifyHandler>());
    
    LOG_INFO("消息处理器管理器初始化完成");
}

MessageHandlerManager& MessageHandlerManager::getInstance()
{
    static MessageHandlerManager instance;
    return instance;
}

void MessageHandlerManager::registerHandler(const std::string& action, std::shared_ptr<MessageHandler> handler)
{
    m_handlers[action] = handler;
    LOG_DEBUG("注册消息处理器: {}", action);
}

std::string MessageHandlerManager::processMessage(const std::string& message, Poco::Net::WebSocket& ws)
{
    // 避免未使用参数警告
    (void)ws;
    
    try
    {
        // 解析JSON消息
        Poco::JSON::Parser parser;
        Poco::Dynamic::Var result = parser.parse(message);
        Poco::JSON::Object::Ptr jsonObj = result.extract<Poco::JSON::Object::Ptr>();
        
        // 打印JSON内容
        std::ostringstream jsonStream;
        jsonObj->stringify(jsonStream, 2);  // 2表示使用2个空格进行缩进，使输出格式更美观
        LOG_DEBUG("接收到的JSON消息内容:\n{}", jsonStream.str());
        
        // 获取action字段 - 使用安全的optValue方法避免Bad cast exception
        if (jsonObj->has("action"))
        {
            // 使用optValue代替getValue，避免类型转换异常
            std::string action = jsonObj->optValue("action", std::string(""));
            
            // 验证action字段不为空
            if (action.empty()) {
                LOG_WARNING("action字段为空或类型不正确");
                
                Poco::JSON::Object response;
                response.set("status", "error");
                response.set("message", "action字段为空或类型不正确");
                
                std::ostringstream oss;
                response.stringify(oss);
                return oss.str();
            }
            
            LOG_INFO("收到动作请求: {}", action);
            
            // 初始化消息处理器（如果尚未初始化）
            if (!MessageHandlers::isInitialized()) {
                MessageHandlers::initialize();
            }
            
            // 安全地提取content对象
            Poco::JSON::Object::Ptr content;
            if (jsonObj->has("content")) {
                try {
                    content = jsonObj->getObject("content");
                } catch (const Poco::Exception& e) {
                    LOG_WARNING("content字段类型错误: {}", e.displayText());
                    content = Poco::JSON::Object::Ptr(new Poco::JSON::Object());
                }
            } else {
                content = Poco::JSON::Object::Ptr(new Poco::JSON::Object());
            }
            
            // 使用统一的消息处理函数
            LOG_DEBUG("使用消息处理函数处理: {}", action);
            return MessageHandlers::handleMessage(action, content);
        }
        else
        {
            LOG_WARNING("消息缺少action字段");
            
            Poco::JSON::Object response;
            response.set("status", "error");
            response.set("message", "缺少action字段");
            
            std::ostringstream oss;
            response.stringify(oss);
            return oss.str();
        }
    }
    catch (const Poco::Exception& e)
    {
        LOG_ERROR("Poco异常: {}", e.displayText());
        
        Poco::JSON::Object response;
        response.set("status", "error");
        response.set("message", std::string("处理消息异常: ") + e.displayText());
        
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
    }
    catch (const std::exception& e)
    {
        LOG_ERROR("标准异常: {}", e.what());
        
        Poco::JSON::Object response;
        response.set("status", "error");
        response.set("message", std::string("处理消息异常: ") + e.what());
        
        std::ostringstream oss;
        response.stringify(oss);
        return oss.str();
    }
}

//============================== WebSocketMessageProcessor 实现 ==============================

WebSocketMessageProcessor::WebSocketMessageProcessor()
{
    // 白名单管理已集中到whitelist::initializeWhitelist()中
    LOG_INFO("WebSocket消息处理器已创建");
}

WebSocketMessageProcessor& WebSocketMessageProcessor::getInstance()
{
    static WebSocketMessageProcessor instance;
    return instance;
}

void WebSocketMessageProcessor::processConnection(Poco::Net::WebSocket& ws)
{
    try
    {
        // 缓冲区用于接收消息
        char buffer[1024];
        int flags;
        int n;
        
        // 处理消息，直到连接关闭或出错
        do
        {
            // 接收消息
            n = ws.receiveFrame(buffer, sizeof(buffer), flags);
            
            if (n > 0)
            {
                // 计数器增加
                websocket::g_message_count++;
                
                // 根据消息类型处理
                if (!processMessage(ws, buffer, n, flags))
                {
                    // 如果处理返回false，表示需要关闭连接
                    break;
                }
            }
            else if (n == 0)
            {
                // 没有收到数据，可能是超时
                continue;
            }
            else
            {
                // 接收错误，可能是连接断开
                LOG_WARNING("接收消息错误或连接断开");
                break;
            }
            
            // 添加轮询间隔，避免CPU占用过高
            if (websocket::g_ws_poll_interval > 0)
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(websocket::g_ws_poll_interval));
            }
            
        } while (n > 0 && (flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) != Poco::Net::WebSocket::FRAME_OP_CLOSE);
        
        // 关闭WebSocket连接
        ws.close();
    }
    catch (std::exception& exc)
    {
        LOG_ERROR("处理WebSocket连接时发生异常: {}", exc.what());
    }
    
    // 减少活跃连接计数
    websocket::g_client_count--;
    LOG_INFO("WebSocket连接已关闭，当前活跃连接数: {}", websocket::g_client_count.load());
}

bool WebSocketMessageProcessor::processMessage(Poco::Net::WebSocket& ws, const char* buffer, int length, int flags)
{
    try
    {
        // 根据消息类型处理
        if ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) == Poco::Net::WebSocket::FRAME_OP_TEXT)
        {
            // 文本消息
            std::string message(buffer, length);
            handleTextMessage(ws, message);
            return true;
        }
        else if ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) == Poco::Net::WebSocket::FRAME_OP_PING)
        {
            // Ping消息，回复Pong
            handlePingMessage(ws, buffer, length);
            return true;
        }
        else if ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) == Poco::Net::WebSocket::FRAME_OP_PONG)
        {
            // Pong消息，忽略
            handlePongMessage();
            return true;
        }
        else if ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) == Poco::Net::WebSocket::FRAME_OP_CLOSE)
        {
            // 关闭消息
            handleCloseMessage(ws, buffer, length, flags);
            return false; // 关闭连接
        }
        else
        {
            // 其他类型的消息
            LOG_WARNING("收到未知类型的消息, flags={}", flags);
            return true;
        }
    }
    catch (const std::exception& e)
    {
        LOG_ERROR("处理消息时发生异常: {}", e.what());
        return true; // 继续处理其他消息
    }
}

void WebSocketMessageProcessor::handleTextMessage(Poco::Net::WebSocket& ws, const std::string& message)
{
    LOG_INFO("收到文本消息: {}", message.substr(0, 100) + (message.length() > 100 ? "..." : ""));
    
    // 特殊处理纯文本心跳消息
    if (message == "heart beat") {
        LOG_DEBUG("处理纯文本心跳消息");
        std::string response = "{\"status\":\"ok\",\"action\":\"heartbeat\",\"type\":\"pong\",\"timestamp\":" + 
                              std::to_string((long)std::time(nullptr) * 1000) + "}";
        ws.sendFrame(response.data(), response.size(), Poco::Net::WebSocket::FRAME_TEXT);
        return;
    }
    
    // 解析并打印JSON消息
    try {
        Poco::JSON::Parser parser;
        Poco::Dynamic::Var result = parser.parse(message);
        Poco::JSON::Object::Ptr jsonObj = result.extract<Poco::JSON::Object::Ptr>();
        
        std::ostringstream jsonStream;
        jsonObj->stringify(jsonStream, 2);  // 使用2个空格进行格式化
        LOG_DEBUG("接收到的JSON消息结构:\n{}", jsonStream.str());
    } catch (Poco::Exception& e) {
        LOG_ERROR("解析JSON失败: {}", e.displayText());
        // 对于不是心跳消息但JSON无效的情况，返回错误
        if (message != "heart beat") {
            std::string response = "{\"status\":\"error\",\"message\":\"无效的JSON格式\"}";
            ws.sendFrame(response.data(), response.size(), Poco::Net::WebSocket::FRAME_TEXT);
        }
        return;
    }
    
    // 使用消息处理器管理器处理消息
    try 
    {
        std::string response = MessageHandlerManager::getInstance().processMessage(message, ws);
        ws.sendFrame(response.data(), response.size(), Poco::Net::WebSocket::FRAME_TEXT);
    }
    catch (const Poco::Exception& e)
    {
        LOG_ERROR("处理消息时发生异常: {}", e.displayText());
        std::string response = "{\"status\":\"error\",\"message\":\"内部服务器错误\"}";
        ws.sendFrame(response.data(), response.size(), Poco::Net::WebSocket::FRAME_TEXT);
    }
}

void WebSocketMessageProcessor::handlePingMessage(Poco::Net::WebSocket& ws, const char* buffer, int length)
{
    LOG_DEBUG("收到Ping消息");
    ws.sendFrame(buffer, length, Poco::Net::WebSocket::FRAME_FLAG_FIN | Poco::Net::WebSocket::FRAME_OP_PONG);
}

void WebSocketMessageProcessor::handlePongMessage()
{
    LOG_DEBUG("收到Pong消息");
}

void WebSocketMessageProcessor::handleCloseMessage(Poco::Net::WebSocket& ws, const char* buffer, int length, int flags)
{
    LOG_INFO("收到关闭消息");
    // 发送关闭确认
    ws.sendFrame(buffer, length, flags);
} 