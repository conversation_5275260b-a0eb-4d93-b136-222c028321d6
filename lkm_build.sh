#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目名称
LKM_EXEC="lkm_test"
BUILD_DIR="build"
BIN_DIR="${BUILD_DIR}/bin"

# 清空控制台
clear_console() {
    clear
}

# 检查脚本是否具有执行权限
check_permissions() {
    if [ ! -x "$0" ]; then
        echo -e "${YELLOW}添加执行权限到脚本...${NC}"
        chmod +x "$0"
        echo -e "${GREEN}执行权限已添加，请重新运行脚本${NC}"
        exit 0
    fi
}

# 检查是否安装了必要的工具
check_dependencies() {
    if ! command -v cmake &> /dev/null; then
        echo -e "${RED}错误: 未安装CMake${NC}"
        exit 1
    fi
    if ! command -v make &> /dev/null; then
        echo -e "${RED}错误: 未安装Make${NC}"
        exit 1
    fi
}

# 编译项目
compile() {
    echo -e "${YELLOW}开始编译LKM模块...${NC}"
    
    # 创建构建目录
    if [ ! -d "$BUILD_DIR" ]; then
        mkdir -p "$BUILD_DIR"
    fi
    
    cd "$BUILD_DIR" || exit 1
    
    # 运行CMake
    cmake ..
    if [ $? -ne 0 ]; then
        echo -e "${RED}CMake配置失败${NC}"
        cd ..
        return 1
    fi
    
    # 只编译lkm_test目标
    make -j$(nproc) lkm_test
    if [ $? -ne 0 ]; then
        echo -e "${RED}编译失败${NC}"
        cd ..
        return 1
    fi
    
    # 验证可执行文件是否存在
    if [ -f "bin/${LKM_EXEC}" ]; then
        echo -e "${GREEN}编译成功！${NC}"
        echo -e "${GREEN}可执行文件位置: bin/${LKM_EXEC}${NC}"
    else
        echo -e "${RED}编译完成，但找不到可执行文件。查找可能的位置...${NC}"
        # 尝试在其他路径查找
        EXEC_PATH=$(find . -name "${LKM_EXEC}")
        if [ -n "$EXEC_PATH" ]; then
            echo -e "${GREEN}找到可执行文件: $EXEC_PATH${NC}"
        else
            echo -e "${RED}无法找到可执行文件${NC}"
            cd ..
            return 1
        fi
    fi
    
    cd ..
    return 0
}

# 运行LKM测试模块
run_lkm() {
    echo -e "${YELLOW}运行LKM测试模块...${NC}"
    
    # 检查可执行文件是否存在
    EXEC_PATH="${BIN_DIR}/${LKM_EXEC}"
    if [ ! -f "$EXEC_PATH" ]; then
        echo -e "${YELLOW}在默认路径未找到可执行文件，尝试查找...${NC}"
        EXEC_PATH=$(find "${BUILD_DIR}" -name "${LKM_EXEC}")
        if [ -n "$EXEC_PATH" ]; then
            echo -e "${GREEN}找到可执行文件: $EXEC_PATH${NC}"
        else
            echo -e "${RED}错误: ${LKM_EXEC}可执行文件不存在，请先编译项目${NC}"
            read -p "按Enter键继续..."
            return 1
        fi
    fi
    
    # 设置串口设备权限
    for port in /dev/ttyUSB* /dev/ttyACM*; do
        if [ -e "$port" ]; then
            echo -e "${YELLOW}设置串口设备权限: $port${NC}"
            sudo chmod 666 "$port" || echo -e "${RED}无法设置权限，可能需要管理员权限${NC}"
        fi
    done
    
    # 运行程序
    echo -e "${GREEN}启动LKM测试模块...${NC}"
    echo -e "${YELLOW}按Ctrl+C退出${NC}"
    
    # 执行程序并捕获退出状态
    "$EXEC_PATH"
    RUN_STATUS=$?
    
    if [ $RUN_STATUS -ne 0 ]; then
        echo -e "${RED}程序异常退出，退出码: $RUN_STATUS${NC}"
    fi
    
    # 暂停以便查看输出
    read -p "按Enter键继续..."
    
    return $RUN_STATUS
}

# 停止所有LKM测试模块进程
stop_lkm() {
    echo -e "${YELLOW}正在停止所有LKM测试模块进程...${NC}"
    
    # 查找所有lkm_test进程
    LKM_PIDS=$(ps -ef | grep "${LKM_EXEC}" | grep -v grep | awk '{print $2}')
    
    if [ -n "$LKM_PIDS" ]; then
        echo -e "${GREEN}找到以下LKM测试模块进程:${NC}"
        for pid in $LKM_PIDS; do
            echo "  - PID: $pid"
            kill -15 $pid
            sleep 1
            
            # 检查进程是否仍在运行，如果是则强制终止
            if ps -p $pid > /dev/null 2>&1; then
                echo -e "${YELLOW}进程 $pid 未响应正常终止，尝试强制终止...${NC}"
                kill -9 $pid
                sleep 1
            fi
        done
        echo -e "${GREEN}所有LKM测试模块进程已停止${NC}"
    else
        echo -e "${YELLOW}未找到运行中的LKM测试模块进程${NC}"
    fi
}

# 清理并重新编译
clean_and_compile() {
    echo -e "${YELLOW}清理并重新编译LKM模块...${NC}"
    
    # 停止所有运行中的进程
    stop_lkm
    
    # 删除旧的构建文件
    if [ -d "$BUILD_DIR" ]; then
        echo -e "${YELLOW}删除旧的LKM模块构建文件...${NC}"
        rm -rf "${BUILD_DIR}"
    fi
    
    # 重新编译
    compile
}

# 列出可用串口
list_serial_ports() {
    echo -e "${YELLOW}正在扫描可用串口设备...${NC}"
    
    # 检查常见的串口设备
    echo -e "${GREEN}USB串口设备:${NC}"
    ls -l /dev/ttyUSB* 2>/dev/null || echo "  无USB串口设备"
    
    echo -e "${GREEN}ACM串口设备:${NC}"
    ls -l /dev/ttyACM* 2>/dev/null || echo "  无ACM串口设备"
    
    echo -e "${GREEN}标准串口设备:${NC}"
    ls -l /dev/ttyS* 2>/dev/null || echo "  无标准串口设备"
    
    # 使用dmesg检查最近连接的串口设备
    echo -e "\n${GREEN}最近连接的串口设备信息:${NC}"
    dmesg | grep -E "tty(USB|ACM|S)" | tail -10
    
    read -p "按Enter键继续..."
}

# 显示菜单
show_menu() {
    clear_console
    echo -e "\n${GREEN}===== LKM模块工具 =====${NC}"
    echo "1. 编译项目"
    echo "2. 清理并重新编译"
    echo "3. 运行LKM测试模块"
    echo "4. 停止LKM测试模块"
    echo "5. 列出可用串口设备"
    echo "6. 退出"
    echo -e "${YELLOW}请选择操作 (1-6): ${NC}"
}

# 主循环
main() {
    # 启动时清空控制台
    clear_console
    check_permissions
    check_dependencies
    
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                compile
                read -p "按Enter键继续..."
                ;;
            2)
                clean_and_compile
                read -p "按Enter键继续..."
                ;;
            3)
                run_lkm
                ;;
            4)
                stop_lkm
                read -p "按Enter键继续..."
                ;;
            5)
                list_serial_ports
                ;;
            6)
                echo -e "${GREEN}退出程序${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                read -p "按Enter键继续..."
                ;;
        esac
    done
}

# 运行主函数
main 