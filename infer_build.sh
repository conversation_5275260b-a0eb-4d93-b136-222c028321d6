#!/bin/bash

# AiBox项目管理脚本

# 设置文本颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 恢复默认颜色

# 可执行文件路径
EXECUTABLE_PATH="build/bin/infer_test"
TEST_EXECUTABLE_PATH="build/bin/tests"
MODEL_DIR="build/bin/model"

# 显示主菜单
show_menu() {
    echo -e "${BLUE}=== AiBox项目管理 ===${NC}"
    echo -e "${YELLOW}1)${NC} 编译项目"
    echo -e "${YELLOW}2)${NC} 清理build目录"
    echo -e "${YELLOW}3)${NC} 运行程序"
    echo -e "${YELLOW}4)${NC} 运行测试"
    echo -e "${YELLOW}5)${NC} 查看编译结果"
    echo -e "${YELLOW}0)${NC} 退出"
    echo -e "${BLUE}======================${NC}"
    echo -n "请输入选项 [0-5]: "
}

# 编译项目
build_project() {
    echo -e "${GREEN}开始编译项目...${NC}"
    
    # 创建build目录
    mkdir -p build
    
    # 进入build目录
    cd build
    
    # 配置CMake项目
    echo -e "${YELLOW}配置项目...${NC}"
    cmake ..
    
    # 编译项目
    echo -e "${YELLOW}编译项目...${NC}"
    cmake --build .
    
    # 返回到原目录
    cd ..
    
    # 设置build目录777权限
    echo -e "${YELLOW}设置build目录权限...${NC}"
    sudo chmod -R 777 build
    
    # 创建模型目录
    mkdir -p "$MODEL_DIR"
    
    # 检查主程序和测试程序
    BUILD_SUCCESS=false
    
    if [ -f "$EXECUTABLE_PATH" ]; then
        echo -e "${GREEN}主程序编译成功！${NC}"
        echo -e "可执行文件位于: ${BLUE}$EXECUTABLE_PATH${NC}"
        
        # 移动模型文件到bin/model目录
        EXECUTABLE_DIR=$(dirname "$EXECUTABLE_PATH")
        echo -e "${YELLOW}正在将模型文件复制到模型目录...${NC}"
        
        # 复制所有模型文件到MODEL_DIR
        if [ -d "infer/model" ]; then
            echo -e "${YELLOW}复制模型文件到$MODEL_DIR...${NC}"
            cp -f infer/model/*.rknn "$MODEL_DIR/" 2>/dev/null
            echo -e "${GREEN}模型文件已复制到: ${BLUE}$MODEL_DIR/${NC}"
            
            # 同时复制到bin目录
            echo -e "${YELLOW}复制模型文件到可执行文件目录...${NC}"
            cp -f infer/model/*.rknn "$EXECUTABLE_DIR" 2>/dev/null
            echo -e "${GREEN}模型文件已复制到: ${BLUE}$EXECUTABLE_DIR${NC}"
            
            # 复制到项目根目录
            echo -e "${YELLOW}复制模型文件到项目根目录...${NC}"
            cp -f infer/model/*.rknn "./" 2>/dev/null
            echo -e "${GREEN}模型文件已复制到项目根目录${NC}"
            
            # 列出已复制的模型文件
            MODEL_FILES=$(ls -1 "$MODEL_DIR/"*.rknn 2>/dev/null)
            if [ -n "$MODEL_FILES" ]; then
                echo -e "${GREEN}已复制以下模型文件到$MODEL_DIR/:${NC}"
                echo "$MODEL_FILES"
            else
                echo -e "${YELLOW}未找到任何模型文件${NC}"
            fi
        else
            echo -e "${YELLOW}未找到模型目录: infer/model${NC}"
        fi
        
        BUILD_SUCCESS=true
    fi
    
    if [ -f "$TEST_EXECUTABLE_PATH" ]; then
        echo -e "${GREEN}测试程序编译成功！${NC}"
        echo -e "测试可执行文件位于: ${BLUE}$TEST_EXECUTABLE_PATH${NC}"
        BUILD_SUCCESS=true
    fi
    
    if [ "$BUILD_SUCCESS" = false ]; then
        echo -e "${RED}编译可能失败，未找到可执行文件！${NC}"
    fi
}

# 清理build目录
clean_build() {
    echo -e "${YELLOW}正在清理build目录...${NC}"
    
    # 检查build目录是否存在
    if [ -d "build" ]; then
        echo -e "${YELLOW}请输入密码以清理build目录（可能需要管理员权限）...${NC}"
        sudo find build -type f -exec rm -f {} \; 2>/dev/null
        sudo find build -type d -exec chmod 755 {} \; 2>/dev/null
        sudo rm -rf build
        mkdir -p build
        echo -e "${YELLOW}设置build目录权限...${NC}"
        sudo chmod -R 777 build
        echo -e "${GREEN}build目录已清理完毕${NC}"
    else
        echo -e "${YELLOW}build目录不存在，正在创建...${NC}"
        mkdir -p build
        echo -e "${YELLOW}设置build目录权限...${NC}"
        sudo chmod -R 777 build
        echo -e "${GREEN}build目录已创建${NC}"
    fi
}

# 运行程序
run_program() {
    # 检查可执行文件是否存在
    if [ -f "$EXECUTABLE_PATH" ]; then
        echo -e "${GREEN}正在运行AiBox程序...${NC}"
        ./$EXECUTABLE_PATH
    else
        echo -e "${RED}错误：可执行文件不存在，请先编译项目${NC}"
        echo -e "${YELLOW}预期路径: $EXECUTABLE_PATH${NC}"
    fi
}

# 运行测试
run_tests() {
    # 检查测试可执行文件是否存在
    if [ -f "$TEST_EXECUTABLE_PATH" ]; then
        echo -e "${GREEN}正在运行AiBox测试...${NC}"
        echo -e "${YELLOW}可用测试列表:${NC}"
        ./$TEST_EXECUTABLE_PATH --list-tests
        echo ""
        echo -e "${YELLOW}运行所有测试:${NC}"
        ./$TEST_EXECUTABLE_PATH
    else
        echo -e "${RED}错误：测试可执行文件不存在，请先编译项目${NC}"
        echo -e "${YELLOW}预期路径: $TEST_EXECUTABLE_PATH${NC}"
    fi
}

# 查看编译结果
show_build_result() {
    echo -e "${BLUE}编译结果信息:${NC}"
    
    if [ -d "build" ]; then
        # 查找可执行文件
        FOUND_EXECUTABLES=$(find build/bin -type f -executable | grep -v "CMakeFiles" | sort)
        
        if [ -n "$FOUND_EXECUTABLES" ]; then
            echo -e "${GREEN}找到以下可执行文件:${NC}"
            echo "$FOUND_EXECUTABLES" | while read -r file; do
                size=$(du -h "$file" | cut -f1)
                echo -e "${YELLOW}$file${NC} (大小: $size)"
            done
        else
            echo -e "${RED}未找到可执行文件${NC}"
        fi
        
        # 查看库文件
        FOUND_LIBS=$(find build/lib -name "*.a" -o -name "*.so" | grep -v "CMakeFiles" | sort)
        
        if [ -n "$FOUND_LIBS" ]; then
            echo -e "\n${GREEN}找到以下库文件:${NC}"
            echo "$FOUND_LIBS" | while read -r file; do
                size=$(du -h "$file" | cut -f1)
                echo -e "${YELLOW}$file${NC} (大小: $size)"
            done
        fi
        
        # 检查主可执行文件
        if [ -f "$EXECUTABLE_PATH" ]; then
            echo -e "\n${GREEN}主可执行文件信息:${NC}"
            ls -lh "$EXECUTABLE_PATH"
            file "$EXECUTABLE_PATH"
        else
            echo -e "\n${RED}主可执行文件($EXECUTABLE_PATH)不存在${NC}"
        fi
    else
        echo -e "${RED}build目录不存在，请先编译项目${NC}"
    fi
}

# 主程序
while true; do
    show_menu
    read choice
    
    case $choice in
        1)
            build_project
            ;;
        2)
            clean_build
            ;;
        3)
            run_program
            ;;
        4)
            run_tests
            ;;
        5)
            show_build_result
            ;;
        0)
            echo -e "${GREEN}感谢使用，再见！${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选项，请重新输入${NC}"
            ;;
    esac
    
    echo ""
done 