/usr/bin/c++  -O3 -march=native -O3 -DNDEBUG -O3 -DNDEBUG -Wl,--dependency-file=CMakeFiles/bl_server.dir/link.d CMakeFiles/bl_server.dir/main.cpp.o -o ../bin/bl_server   -L/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/lib  -Wl,-rpath,/home/<USER>/mywork/poco_serverdemo/backend/../3rdparty/poco/lib: ../bin/lib/libblweb_backend.a ../bin/lib/libutils.a ../bin/lib/libalgorithm_lib.a ../bin/lib/libblkmapi.a /usr/lib/libatomic.so ../bin/lib/libutils.a /usr/lib/libPocoNetSSL.so.111 /usr/lib/libPocoNet.so.111 /usr/lib/libPocoUtil.so.111 /usr/lib/libPocoJSON.so.111 /usr/lib/libPocoXML.so.111 /usr/lib/libPocoCrypto.so.111 /usr/lib/libPocoFoundation.so.111 -lpthread -ldl -lrt /usr/lib/libssl.so /usr/lib/libcrypto.so ../bin/lib/libspdlog.a ../bin/lib/libfmt.a -lPocoFoundation -lPocoNet -lPocoJSON -lPocoUtil -lPocoXML -lPocoZip /usr/lib/libatomic.so -lPocoData -lPocoDataMySQL /usr/lib/libmysqlclient.so
