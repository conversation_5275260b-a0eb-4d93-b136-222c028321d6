# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include backend/CMakeFiles/bl_server.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include backend/CMakeFiles/bl_server.dir/compiler_depend.make

# Include the progress variables for this target.
include backend/CMakeFiles/bl_server.dir/progress.make

# Include the compile flags for this target's objects.
include backend/CMakeFiles/bl_server.dir/flags.make

backend/CMakeFiles/bl_server.dir/codegen:
.PHONY : backend/CMakeFiles/bl_server.dir/codegen

backend/CMakeFiles/bl_server.dir/main.cpp.o: backend/CMakeFiles/bl_server.dir/flags.make
backend/CMakeFiles/bl_server.dir/main.cpp.o: /home/<USER>/mywork/poco_serverdemo/backend/main.cpp
backend/CMakeFiles/bl_server.dir/main.cpp.o: backend/CMakeFiles/bl_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object backend/CMakeFiles/bl_server.dir/main.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT backend/CMakeFiles/bl_server.dir/main.cpp.o -MF CMakeFiles/bl_server.dir/main.cpp.o.d -o CMakeFiles/bl_server.dir/main.cpp.o -c /home/<USER>/mywork/poco_serverdemo/backend/main.cpp

backend/CMakeFiles/bl_server.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/bl_server.dir/main.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/backend/main.cpp > CMakeFiles/bl_server.dir/main.cpp.i

backend/CMakeFiles/bl_server.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/bl_server.dir/main.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/backend && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/backend/main.cpp -o CMakeFiles/bl_server.dir/main.cpp.s

# Object files for target bl_server
bl_server_OBJECTS = \
"CMakeFiles/bl_server.dir/main.cpp.o"

# External object files for target bl_server
bl_server_EXTERNAL_OBJECTS =

bin/bl_server: backend/CMakeFiles/bl_server.dir/main.cpp.o
bin/bl_server: backend/CMakeFiles/bl_server.dir/build.make
bin/bl_server: backend/CMakeFiles/bl_server.dir/compiler_depend.ts
bin/bl_server: bin/lib/libblweb_backend.a
bin/bl_server: bin/lib/libutils.a
bin/bl_server: bin/lib/libalgorithm_lib.a
bin/bl_server: bin/lib/libblkmapi.a
bin/bl_server: /usr/lib/libatomic.so
bin/bl_server: bin/lib/libutils.a
bin/bl_server: /usr/lib/libPocoNetSSL.so.111
bin/bl_server: /usr/lib/libPocoNet.so.111
bin/bl_server: /usr/lib/libPocoUtil.so.111
bin/bl_server: /usr/lib/libPocoJSON.so.111
bin/bl_server: /usr/lib/libPocoXML.so.111
bin/bl_server: /usr/lib/libPocoCrypto.so.111
bin/bl_server: /usr/lib/libPocoFoundation.so.111
bin/bl_server: /usr/lib/libssl.so
bin/bl_server: /usr/lib/libcrypto.so
bin/bl_server: bin/lib/libspdlog.a
bin/bl_server: bin/lib/libfmt.a
bin/bl_server: /usr/lib/libatomic.so
bin/bl_server: /usr/lib/libmysqlclient.so
bin/bl_server: backend/CMakeFiles/bl_server.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ../bin/bl_server"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/backend && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bl_server.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
backend/CMakeFiles/bl_server.dir/build: bin/bl_server
.PHONY : backend/CMakeFiles/bl_server.dir/build

backend/CMakeFiles/bl_server.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_test/backend && $(CMAKE_COMMAND) -P CMakeFiles/bl_server.dir/cmake_clean.cmake
.PHONY : backend/CMakeFiles/bl_server.dir/clean

backend/CMakeFiles/bl_server.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/backend /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/backend /home/<USER>/mywork/poco_serverdemo/build_test/backend/CMakeFiles/bl_server.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : backend/CMakeFiles/bl_server.dir/depend

