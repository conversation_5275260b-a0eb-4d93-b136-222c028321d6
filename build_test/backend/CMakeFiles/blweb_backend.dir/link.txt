/usr/bin/ar qc ../bin/lib/libblweb_backend.a CMakeFiles/blweb_backend.dir/blsql/mysql_db.cpp.o CMakeFiles/blweb_backend.dir/blserver/poco_websocket_server.cpp.o CMakeFiles/blweb_backend.dir/blserver/message_handlers.cpp.o CMakeFiles/blweb_backend.dir/blserver/fov_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/register_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/home_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/header_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/function_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/pid_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/aim_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/login_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/update_models.cpp.o CMakeFiles/blweb_backend.dir/blserver/fire_service.cpp.o CMakeFiles/blweb_backend.dir/blserver/data_collection_service.cpp.o CMakeFiles/blweb_backend.dir/blsql/registerdb.cpp.o CMakeFiles/blweb_backend.dir/blsql/function_configs_db.cpp.o CMakeFiles/blweb_backend.dir/blsql/pid_configs_db.cpp.o CMakeFiles/blweb_backend.dir/blsql/aim_configs_db.cpp.o CMakeFiles/blweb_backend.dir/blsql/login_db.cpp.o CMakeFiles/blweb_backend.dir/blsql/fovconfgidb.cpp.o CMakeFiles/blweb_backend.dir/blsql/userinfodb.cpp.o CMakeFiles/blweb_backend.dir/blsql/fire_configs_db.cpp.o CMakeFiles/blweb_backend.dir/blsql/data_collections_db.cpp.o CMakeFiles/blweb_backend.dir/__/ThreadManager/web_server.cpp.o CMakeFiles/blweb_backend.dir/handlers/pid_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/function_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/home_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/aim_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/fov_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/login_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/register_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/header_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/fire_handlers.cpp.o CMakeFiles/blweb_backend.dir/handlers/data_collection_handlers.cpp.o
/usr/bin/ranlib ../bin/lib/libblweb_backend.a
