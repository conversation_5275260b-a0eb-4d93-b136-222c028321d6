# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/color_sinks.cpp \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/async.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/async_logger.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/common.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/backtracer.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/circular_q.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/console_globals.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg_buffer.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/mpmc_blocking_q.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/null_mutex.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/os.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/periodic_worker.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/registry.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/synchronous_factory.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/thread_pool.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/fmt/fmt.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/formatter.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/logger.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/pattern_formatter.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/ansicolor_sink-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/ansicolor_sink.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/sink.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_color_sinks-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_color_sinks.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/tweakme.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_function.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/functional \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/spdlog.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/spdlog.cpp \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/common-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/common.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/backtracer-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/backtracer.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/circular_q.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/console_globals.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/fmt_helper.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg_buffer-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg_buffer.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/null_mutex.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/os-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/os.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/periodic_worker.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/registry-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/registry.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/synchronous_factory.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/fmt/fmt.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/formatter.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/logger-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/logger.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/mdc.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/pattern_formatter-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/pattern_formatter.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/ansicolor_sink.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/base_sink-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/base_sink.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/sink-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/sink.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/spdlog-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/spdlog.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/tweakme.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/version.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/fcntl-linux.h \
  /usr/include/bits/fcntl.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stat.h \
  /usr/include/bits/statx-generic.h \
  /usr/include/bits/statx.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/struct_stat.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_iovec.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_statx.h \
  /usr/include/bits/types/struct_statx_timestamp.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/algorithm \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algo.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_function.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_map.h \
  /usr/include/c++/14.2.1/bits/stl_multimap.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_tree.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/stream_iterator.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/functional \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/iterator \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/map \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/fcntl.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/falloc.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stat.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/stat.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h

3rdparty/spdlog/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/stdout_sinks.cpp \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/async.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/async_logger.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/common.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/backtracer.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/circular_q.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/console_globals.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg_buffer.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/mpmc_blocking_q.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/null_mutex.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/os.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/periodic_worker.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/registry.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/synchronous_factory.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/thread_pool.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/fmt/fmt.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/formatter.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/logger.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/pattern_formatter.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/sink.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_sinks-inl.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_sinks.h \
  /home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/tweakme.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/semaphore.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h \
  /usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h \
  /usr/include/c++/14.2.1/array \
  /usr/include/c++/14.2.1/atomic \
  /usr/include/c++/14.2.1/backward/auto_ptr.h \
  /usr/include/c++/14.2.1/backward/binders.h \
  /usr/include/c++/14.2.1/bit \
  /usr/include/c++/14.2.1/bits/algorithmfwd.h \
  /usr/include/c++/14.2.1/bits/align.h \
  /usr/include/c++/14.2.1/bits/alloc_traits.h \
  /usr/include/c++/14.2.1/bits/allocated_ptr.h \
  /usr/include/c++/14.2.1/bits/allocator.h \
  /usr/include/c++/14.2.1/bits/atomic_base.h \
  /usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/14.2.1/bits/atomic_timed_wait.h \
  /usr/include/c++/14.2.1/bits/atomic_wait.h \
  /usr/include/c++/14.2.1/bits/basic_ios.h \
  /usr/include/c++/14.2.1/bits/basic_ios.tcc \
  /usr/include/c++/14.2.1/bits/basic_string.h \
  /usr/include/c++/14.2.1/bits/basic_string.tcc \
  /usr/include/c++/14.2.1/bits/char_traits.h \
  /usr/include/c++/14.2.1/bits/charconv.h \
  /usr/include/c++/14.2.1/bits/chrono.h \
  /usr/include/c++/14.2.1/bits/chrono_io.h \
  /usr/include/c++/14.2.1/bits/codecvt.h \
  /usr/include/c++/14.2.1/bits/concept_check.h \
  /usr/include/c++/14.2.1/bits/cpp_type_traits.h \
  /usr/include/c++/14.2.1/bits/cxxabi_forced.h \
  /usr/include/c++/14.2.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/14.2.1/bits/enable_special_members.h \
  /usr/include/c++/14.2.1/bits/erase_if.h \
  /usr/include/c++/14.2.1/bits/exception.h \
  /usr/include/c++/14.2.1/bits/exception_defines.h \
  /usr/include/c++/14.2.1/bits/exception_ptr.h \
  /usr/include/c++/14.2.1/bits/functexcept.h \
  /usr/include/c++/14.2.1/bits/functional_hash.h \
  /usr/include/c++/14.2.1/bits/hash_bytes.h \
  /usr/include/c++/14.2.1/bits/hashtable.h \
  /usr/include/c++/14.2.1/bits/hashtable_policy.h \
  /usr/include/c++/14.2.1/bits/invoke.h \
  /usr/include/c++/14.2.1/bits/ios_base.h \
  /usr/include/c++/14.2.1/bits/istream.tcc \
  /usr/include/c++/14.2.1/bits/iterator_concepts.h \
  /usr/include/c++/14.2.1/bits/locale_classes.h \
  /usr/include/c++/14.2.1/bits/locale_classes.tcc \
  /usr/include/c++/14.2.1/bits/locale_conv.h \
  /usr/include/c++/14.2.1/bits/locale_facets.h \
  /usr/include/c++/14.2.1/bits/locale_facets.tcc \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.h \
  /usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc \
  /usr/include/c++/14.2.1/bits/localefwd.h \
  /usr/include/c++/14.2.1/bits/max_size_type.h \
  /usr/include/c++/14.2.1/bits/memory_resource.h \
  /usr/include/c++/14.2.1/bits/memoryfwd.h \
  /usr/include/c++/14.2.1/bits/move.h \
  /usr/include/c++/14.2.1/bits/nested_exception.h \
  /usr/include/c++/14.2.1/bits/new_allocator.h \
  /usr/include/c++/14.2.1/bits/node_handle.h \
  /usr/include/c++/14.2.1/bits/ostream.tcc \
  /usr/include/c++/14.2.1/bits/ostream_insert.h \
  /usr/include/c++/14.2.1/bits/parse_numbers.h \
  /usr/include/c++/14.2.1/bits/postypes.h \
  /usr/include/c++/14.2.1/bits/predefined_ops.h \
  /usr/include/c++/14.2.1/bits/ptr_traits.h \
  /usr/include/c++/14.2.1/bits/quoted_string.h \
  /usr/include/c++/14.2.1/bits/range_access.h \
  /usr/include/c++/14.2.1/bits/ranges_algobase.h \
  /usr/include/c++/14.2.1/bits/ranges_base.h \
  /usr/include/c++/14.2.1/bits/ranges_cmp.h \
  /usr/include/c++/14.2.1/bits/ranges_uninitialized.h \
  /usr/include/c++/14.2.1/bits/ranges_util.h \
  /usr/include/c++/14.2.1/bits/refwrap.h \
  /usr/include/c++/14.2.1/bits/requires_hosted.h \
  /usr/include/c++/14.2.1/bits/semaphore_base.h \
  /usr/include/c++/14.2.1/bits/shared_ptr.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/14.2.1/bits/shared_ptr_base.h \
  /usr/include/c++/14.2.1/bits/specfun.h \
  /usr/include/c++/14.2.1/bits/sstream.tcc \
  /usr/include/c++/14.2.1/bits/std_abs.h \
  /usr/include/c++/14.2.1/bits/std_function.h \
  /usr/include/c++/14.2.1/bits/std_mutex.h \
  /usr/include/c++/14.2.1/bits/std_thread.h \
  /usr/include/c++/14.2.1/bits/stl_algo.h \
  /usr/include/c++/14.2.1/bits/stl_algobase.h \
  /usr/include/c++/14.2.1/bits/stl_bvector.h \
  /usr/include/c++/14.2.1/bits/stl_construct.h \
  /usr/include/c++/14.2.1/bits/stl_function.h \
  /usr/include/c++/14.2.1/bits/stl_heap.h \
  /usr/include/c++/14.2.1/bits/stl_iterator.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/14.2.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/14.2.1/bits/stl_pair.h \
  /usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/14.2.1/bits/stl_relops.h \
  /usr/include/c++/14.2.1/bits/stl_tempbuf.h \
  /usr/include/c++/14.2.1/bits/stl_uninitialized.h \
  /usr/include/c++/14.2.1/bits/stl_vector.h \
  /usr/include/c++/14.2.1/bits/streambuf.tcc \
  /usr/include/c++/14.2.1/bits/streambuf_iterator.h \
  /usr/include/c++/14.2.1/bits/string_view.tcc \
  /usr/include/c++/14.2.1/bits/stringfwd.h \
  /usr/include/c++/14.2.1/bits/this_thread_sleep.h \
  /usr/include/c++/14.2.1/bits/unicode-data.h \
  /usr/include/c++/14.2.1/bits/unicode.h \
  /usr/include/c++/14.2.1/bits/uniform_int_dist.h \
  /usr/include/c++/14.2.1/bits/unique_lock.h \
  /usr/include/c++/14.2.1/bits/unique_ptr.h \
  /usr/include/c++/14.2.1/bits/unordered_map.h \
  /usr/include/c++/14.2.1/bits/uses_allocator.h \
  /usr/include/c++/14.2.1/bits/uses_allocator_args.h \
  /usr/include/c++/14.2.1/bits/utility.h \
  /usr/include/c++/14.2.1/bits/vector.tcc \
  /usr/include/c++/14.2.1/bits/version.h \
  /usr/include/c++/14.2.1/cassert \
  /usr/include/c++/14.2.1/cctype \
  /usr/include/c++/14.2.1/cerrno \
  /usr/include/c++/14.2.1/charconv \
  /usr/include/c++/14.2.1/chrono \
  /usr/include/c++/14.2.1/climits \
  /usr/include/c++/14.2.1/clocale \
  /usr/include/c++/14.2.1/cmath \
  /usr/include/c++/14.2.1/compare \
  /usr/include/c++/14.2.1/concepts \
  /usr/include/c++/14.2.1/condition_variable \
  /usr/include/c++/14.2.1/cstddef \
  /usr/include/c++/14.2.1/cstdint \
  /usr/include/c++/14.2.1/cstdio \
  /usr/include/c++/14.2.1/cstdlib \
  /usr/include/c++/14.2.1/cstring \
  /usr/include/c++/14.2.1/ctime \
  /usr/include/c++/14.2.1/cwchar \
  /usr/include/c++/14.2.1/cwctype \
  /usr/include/c++/14.2.1/debug/assertions.h \
  /usr/include/c++/14.2.1/debug/debug.h \
  /usr/include/c++/14.2.1/exception \
  /usr/include/c++/14.2.1/ext/aligned_buffer.h \
  /usr/include/c++/14.2.1/ext/alloc_traits.h \
  /usr/include/c++/14.2.1/ext/atomicity.h \
  /usr/include/c++/14.2.1/ext/concurrence.h \
  /usr/include/c++/14.2.1/ext/numeric_traits.h \
  /usr/include/c++/14.2.1/ext/string_conversions.h \
  /usr/include/c++/14.2.1/ext/type_traits.h \
  /usr/include/c++/14.2.1/format \
  /usr/include/c++/14.2.1/functional \
  /usr/include/c++/14.2.1/initializer_list \
  /usr/include/c++/14.2.1/iomanip \
  /usr/include/c++/14.2.1/ios \
  /usr/include/c++/14.2.1/iosfwd \
  /usr/include/c++/14.2.1/istream \
  /usr/include/c++/14.2.1/limits \
  /usr/include/c++/14.2.1/locale \
  /usr/include/c++/14.2.1/memory \
  /usr/include/c++/14.2.1/mutex \
  /usr/include/c++/14.2.1/new \
  /usr/include/c++/14.2.1/numbers \
  /usr/include/c++/14.2.1/optional \
  /usr/include/c++/14.2.1/ostream \
  /usr/include/c++/14.2.1/pstl/execution_defs.h \
  /usr/include/c++/14.2.1/pstl/glue_memory_defs.h \
  /usr/include/c++/14.2.1/pstl/pstl_config.h \
  /usr/include/c++/14.2.1/ratio \
  /usr/include/c++/14.2.1/semaphore \
  /usr/include/c++/14.2.1/span \
  /usr/include/c++/14.2.1/sstream \
  /usr/include/c++/14.2.1/stdexcept \
  /usr/include/c++/14.2.1/stop_token \
  /usr/include/c++/14.2.1/streambuf \
  /usr/include/c++/14.2.1/string \
  /usr/include/c++/14.2.1/string_view \
  /usr/include/c++/14.2.1/system_error \
  /usr/include/c++/14.2.1/thread \
  /usr/include/c++/14.2.1/tr1/bessel_function.tcc \
  /usr/include/c++/14.2.1/tr1/beta_function.tcc \
  /usr/include/c++/14.2.1/tr1/ell_integral.tcc \
  /usr/include/c++/14.2.1/tr1/exp_integral.tcc \
  /usr/include/c++/14.2.1/tr1/gamma.tcc \
  /usr/include/c++/14.2.1/tr1/hypergeometric.tcc \
  /usr/include/c++/14.2.1/tr1/legendre_function.tcc \
  /usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/14.2.1/tr1/poly_hermite.tcc \
  /usr/include/c++/14.2.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/14.2.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/14.2.1/tr1/special_function_util.h \
  /usr/include/c++/14.2.1/tuple \
  /usr/include/c++/14.2.1/type_traits \
  /usr/include/c++/14.2.1/typeinfo \
  /usr/include/c++/14.2.1/unordered_map \
  /usr/include/c++/14.2.1/utility \
  /usr/include/c++/14.2.1/variant \
  /usr/include/c++/14.2.1/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-lp64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/semaphore.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/time.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h \
  /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h


/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_sinks.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/stdout_sinks.cpp:

/usr/include/sys/stat.h:

/usr/include/linux/stat.h:

/usr/include/fcntl.h:

/usr/include/c++/14.2.1/map:

/usr/include/c++/14.2.1/iterator:

/usr/include/c++/14.2.1/bits/stl_multimap.h:

/usr/include/bits/types/struct_statx_timestamp.h:

/usr/include/bits/types/struct_statx.h:

/usr/include/bits/struct_stat.h:

/usr/include/bits/statx.h:

/usr/include/bits/statx-generic.h:

/usr/include/bits/stat.h:

/usr/include/bits/fcntl.h:

/usr/include/bits/fcntl-linux.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/version.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/spdlog.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/sink-inl.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/base_sink-inl.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/pattern_formatter-inl.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/mdc.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/registry-inl.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg_buffer-inl.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg-inl.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/fmt_helper.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/backtracer-inl.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/syslimits.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdint.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stddef.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/limits.h:

/usr/include/time.h:

/usr/include/sys/single_threaded.h:

/usr/include/sys/select.h:

/usr/include/sys/cdefs.h:

/usr/include/strings.h:

/usr/include/string.h:

/usr/include/stdlib.h:

/usr/include/stdc-predef.h:

/usr/include/semaphore.h:

/usr/include/linux/types.h:

/usr/include/linux/stddef.h:

/usr/include/linux/limits.h:

/usr/include/linux/close_range.h:

/usr/include/limits.h:

/usr/include/gnu/stubs-lp64.h:

/usr/include/ctype.h:

/usr/include/c++/14.2.1/bits/stream_iterator.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/spdlog-inl.h:

/usr/include/c++/14.2.1/utility:

/usr/include/c++/14.2.1/unordered_map:

/usr/include/c++/14.2.1/tr1/poly_hermite.tcc:

/usr/include/c++/14.2.1/tr1/modified_bessel_func.tcc:

/usr/include/c++/14.2.1/tr1/gamma.tcc:

/usr/include/c++/14.2.1/tr1/exp_integral.tcc:

/usr/include/c++/14.2.1/tr1/beta_function.tcc:

/usr/include/c++/14.2.1/string_view:

/usr/include/pthread.h:

/usr/include/c++/14.2.1/string:

/usr/include/c++/14.2.1/streambuf:

/usr/include/c++/14.2.1/stdexcept:

/usr/include/c++/14.2.1/sstream:

/usr/include/c++/14.2.1/semaphore:

/usr/include/c++/14.2.1/ratio:

/usr/include/c++/14.2.1/pstl/pstl_config.h:

/usr/include/c++/14.2.1/pstl/glue_memory_defs.h:

/usr/include/c++/14.2.1/optional:

/usr/include/c++/14.2.1/new:

/usr/include/c++/14.2.1/memory:

/usr/include/c++/14.2.1/iosfwd:

/usr/include/math.h:

/usr/include/c++/14.2.1/ios:

/usr/include/c++/14.2.1/initializer_list:

/usr/include/c++/14.2.1/functional:

/usr/include/c++/14.2.1/ext/type_traits.h:

/usr/include/c++/14.2.1/ext/numeric_traits.h:

/usr/include/c++/14.2.1/debug/debug.h:

/usr/include/c++/14.2.1/cwctype:

/usr/include/c++/14.2.1/tr1/poly_laguerre.tcc:

/usr/include/c++/14.2.1/cwchar:

/usr/include/c++/14.2.1/ctime:

/usr/include/c++/14.2.1/cstring:

/usr/include/c++/14.2.1/cstdio:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/logger-inl.h:

/usr/include/c++/14.2.1/cstdint:

/usr/include/c++/14.2.1/cstddef:

/usr/include/c++/14.2.1/bits/ranges_algo.h:

/usr/include/c++/14.2.1/condition_variable:

/usr/include/c++/14.2.1/concepts:

/usr/include/c++/14.2.1/compare:

/usr/include/c++/14.2.1/cmath:

/usr/include/c++/14.2.1/climits:

/usr/include/c++/14.2.1/charconv:

/usr/include/bits/wctype-wchar.h:

/usr/include/bits/timesize.h:

/usr/include/bits/types/wint_t.h:

/usr/include/c++/14.2.1/bits/basic_ios.h:

/usr/include/c++/14.2.1/bits/std_thread.h:

/usr/include/bits/types/struct_timespec.h:

/usr/include/bits/types/sigset_t.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/os.h:

/usr/include/c++/14.2.1/numbers:

/usr/include/bits/semaphore.h:

/usr/include/c++/14.2.1/bits/version.h:

/usr/include/bits/types/locale_t.h:

/usr/include/bits/flt-eval-method.h:

/usr/include/c++/14.2.1/tuple:

/usr/include/bits/timex.h:

/usr/include/bits/errno.h:

/usr/include/bits/thread-shared-types.h:

/usr/include/bits/time64.h:

/usr/include/c++/14.2.1/thread:

/usr/include/bits/types/struct_timeval.h:

/usr/include/bits/stdio_lim.h:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/c++/14.2.1/bits/predefined_ops.h:

/usr/include/c++/14.2.1/format:

/usr/include/c++/14.2.1/bits/shared_ptr_atomic.h:

/usr/include/bits/stdio.h:

/usr/include/bits/stdint-least.h:

/usr/include/bits/types/error_t.h:

/usr/include/c++/14.2.1/bits/locale_conv.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/wchar.h:

/usr/include/bits/types/struct___jmp_buf_tag.h:

/usr/include/locale.h:

/usr/include/c++/14.2.1/bits/uniform_int_dist.h:

/usr/include/bits/pthreadtypes.h:

/usr/include/c++/14.2.1/bits/basic_string.h:

/usr/include/c++/14.2.1/bits/stl_iterator_base_funcs.h:

/usr/include/bits/pthreadtypes-arch.h:

/usr/include/c++/14.2.1/tr1/ell_integral.tcc:

/usr/include/bits/pthread_stack_min-dynamic.h:

/usr/include/c++/14.2.1/bits/this_thread_sleep.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/common-inl.h:

/usr/include/bits/posix_opt.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/console_globals.h:

/usr/include/bits/posix2_lim.h:

/usr/include/bits/mathcalls.h:

/usr/include/bits/types/clock_t.h:

/usr/include/bits/types/timer_t.h:

/usr/include/gnu/stubs.h:

/usr/include/c++/14.2.1/bits/localefwd.h:

/usr/include/bits/mathcalls-narrow.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/async_logger.h:

/usr/include/bits/mathcalls-macros.h:

/usr/include/asm-generic/int-ll64.h:

/usr/include/c++/14.2.1/bits/unique_ptr.h:

/usr/include/bits/struct_rwlock.h:

/usr/include/bits/types/struct_itimerspec.h:

/usr/include/bits/mathcalls-helper-functions.h:

/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include/stdarg.h:

/usr/include/bits/uio_lim.h:

/usr/include/bits/math-vector.h:

/usr/include/features-time64.h:

/usr/include/c++/14.2.1/bits/range_access.h:

/usr/include/bits/long-double.h:

/usr/include/c++/14.2.1/stop_token:

/usr/include/c++/14.2.1/mutex:

/usr/include/bits/types/__fpos_t.h:

/usr/include/asm/unistd_64.h:

/usr/include/bits/syscall.h:

/usr/include/bits/iscanonical.h:

/usr/include/c++/14.2.1/bits/codecvt.h:

/usr/include/c++/14.2.1/bits/stl_iterator.h:

/usr/include/bits/locale.h:

/usr/include/bits/local_lim.h:

/usr/include/c++/14.2.1/ext/aligned_buffer.h:

/usr/include/bits/libc-header-start.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/format.h:

/usr/include/c++/14.2.1/bits/unique_lock.h:

/usr/include/bits/stdint-intn.h:

/usr/include/bits/types/clockid_t.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/ansicolor_sink.h:

/usr/include/c++/14.2.1/bits/locale_facets_nonio.tcc:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr-default.h:

/usr/include/c++/14.2.1/bits/allocator.h:

/usr/include/c++/14.2.1/bits/uses_allocator_args.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg.h:

/usr/include/errno.h:

/usr/include/bits/getopt_posix.h:

/usr/include/linux/falloc.h:

/usr/include/bits/unistd_ext.h:

/usr/include/c++/14.2.1/bits/uses_allocator.h:

/usr/include/c++/14.2.1/span:

/usr/include/bits/libm-simd-decl-stubs.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/thread_pool.h:

/usr/include/alloca.h:

/usr/include/bits/types/mbstate_t.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/periodic_worker.h:

/usr/include/c++/14.2.1/tr1/special_function_util.h:

/usr/include/bits/select.h:

/usr/include/c++/14.2.1/bits/algorithmfwd.h:

/usr/include/bits/fp-logb.h:

/usr/include/c++/14.2.1/chrono:

/usr/include/asm-generic/errno.h:

/usr/include/bits/types/cookie_io_functions_t.h:

/usr/include/bits/types/struct_sched_param.h:

/usr/include/c++/14.2.1/bits/stl_uninitialized.h:

/usr/include/features.h:

/usr/include/bits/types/__FILE.h:

/usr/include/bits/sched.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/null_mutex.h:

/usr/include/c++/14.2.1/bits/istream.tcc:

/usr/include/bits/types/struct_tm.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/color_sinks.cpp:

/usr/include/c++/14.2.1/vector:

/usr/include/bits/floatn-common.h:

/usr/include/c++/14.2.1/bits/parse_numbers.h:

/usr/include/c++/14.2.1/bits/shared_ptr_base.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/ansicolor_sink-inl.h:

/usr/include/sys/types.h:

/usr/include/c++/14.2.1/exception:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/common.h:

/usr/include/c++/14.2.1/backward/auto_ptr.h:

/usr/include/c++/14.2.1/cerrno:

/usr/include/bits/types/__sigset_t.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/c++/14.2.1/atomic:

/usr/include/c++/14.2.1/bits/enable_special_members.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/base.h:

/usr/include/c++/14.2.1/bits/stl_function.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/async.h:

/usr/include/linux/posix_types.h:

/usr/include/bits/types/FILE.h:

/usr/include/c++/14.2.1/cctype:

/usr/include/bits/types.h:

/usr/include/c++/14.2.1/locale:

/usr/include/bits/getopt_core.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include/fmt/core.h:

/usr/include/c++/14.2.1/bits/stl_map.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/pattern_formatter.h:

/usr/include/c++/14.2.1/system_error:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/logger.h:

/usr/include/c++/14.2.1/pstl/glue_algorithm_defs.h:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/bits/waitflags.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/mpmc_blocking_q.h:

/usr/include/c++/14.2.1/tr1/riemann_zeta.tcc:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/tweakme.h:

/usr/include/c++/14.2.1/ext/concurrence.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/synchronous_factory.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/fmt/fmt.h:

/usr/include/bits/uintn-identity.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/circular_q.h:

/usr/include/c++/14.2.1/bits/char_traits.h:

/usr/include/bits/typesizes.h:

/usr/include/bits/cpu-set.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/src/spdlog.cpp:

/usr/include/wchar.h:

/usr/include/c++/14.2.1/clocale:

/usr/include/bits/posix1_lim.h:

/usr/include/wctype.h:

/usr/include/sched.h:

/usr/include/bits/fp-fast.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_color_sinks-inl.h:

/usr/include/c++/14.2.1/bits/atomic_lockfree_defines.h:

/usr/include/c++/14.2.1/bits/stl_tempbuf.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++config.h:

/usr/include/c++/14.2.1/tr1/hypergeometric.tcc:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/sink.h:

/usr/include/c++/14.2.1/ext/string_conversions.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/os_defines.h:

/usr/include/c++/14.2.1/type_traits:

/usr/include/c++/14.2.1/array:

/usr/include/c++/14.2.1/debug/assertions.h:

/usr/include/bits/time.h:

/usr/include/asm/posix_types.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/atomic_word.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_color_sinks.h:

/usr/include/c++/14.2.1/iomanip:

/usr/include/asm/types.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/asm/errno.h:

/usr/include/c++/14.2.1/bits/locale_facets.tcc:

/usr/include/bits/endian.h:

/usr/include/c++/14.2.1/istream:

/usr/include/assert.h:

/usr/include/syscall.h:

/usr/include/c++/14.2.1/bits/concept_check.h:

/usr/include/bits/byteswap.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/registry.h:

/usr/include/bits/confname.h:

/usr/include/c++/14.2.1/bits/chrono_io.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/os-inl.h:

/usr/include/libintl.h:

/usr/include/c++/14.2.1/typeinfo:

/usr/include/bits/endianness.h:

/usr/include/c++/14.2.1/cassert:

/usr/include/bits/types/struct_FILE.h:

/usr/include/bits/environments.h:

/usr/include/bits/floatn.h:

/usr/include/c++/14.2.1/bits/cxxabi_init_exception.h:

/usr/include/stdio.h:

/usr/include/bits/wordsize.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/stdout_sinks-inl.h:

/usr/include/c++/14.2.1/ostream:

/usr/include/bits/xopen_lim.h:

/usr/include/unistd.h:

/usr/include/c++/14.2.1/tr1/legendre_function.tcc:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++allocator.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_inline.h:

/usr/include/c++/14.2.1/bits/stringfwd.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/error_constants.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/gthr.h:

/usr/include/bits/types/time_t.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/messages_members.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/time_members.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/log_msg_buffer.h:

/usr/include/c++/14.2.1/bits/stl_algo.h:

/usr/include/sys/time.h:

/usr/include/c++/14.2.1/limits:

/usr/include/c++/14.2.1/backward/binders.h:

/usr/include/c++/14.2.1/bits/vector.tcc:

/usr/include/c++/14.2.1/bit:

/usr/include/c++/14.2.1/bits/memoryfwd.h:

/usr/include/c++/14.2.1/bits/requires_hosted.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/formatter.h:

/usr/include/c++/14.2.1/bits/align.h:

/usr/include/linux/errno.h:

/usr/include/c++/14.2.1/bits/allocated_ptr.h:

/usr/include/c++/14.2.1/bits/stl_tree.h:

/usr/include/c++/14.2.1/bits/atomic_base.h:

/usr/include/c++/14.2.1/pstl/execution_defs.h:

/usr/include/c++/14.2.1/bits/locale_facets.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/c++locale.h:

/usr/include/c++/14.2.1/bits/atomic_timed_wait.h:

/usr/include/c++/14.2.1/bits/atomic_wait.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/sinks/base_sink.h:

/usr/include/bits/stdlib-bsearch.h:

/usr/include/c++/14.2.1/bits/basic_ios.tcc:

/usr/include/bits/setjmp.h:

/usr/include/c++/14.2.1/bits/charconv.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/asm-generic/types.h:

/usr/include/c++/14.2.1/bits/chrono.h:

/usr/include/c++/14.2.1/bits/cpp_type_traits.h:

/usr/include/asm/bitsperlong.h:

/usr/include/c++/14.2.1/bits/cxxabi_forced.h:

/usr/include/c++/14.2.1/bits/erase_if.h:

/usr/include/c++/14.2.1/bits/exception.h:

/usr/include/bits/atomic_wide_counter.h:

/usr/include/c++/14.2.1/bits/specfun.h:

/usr/include/c++/14.2.1/bits/hashtable_policy.h:

/usr/include/c++/14.2.1/bits/exception_defines.h:

/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include/spdlog/details/backtracer.h:

/usr/include/c++/14.2.1/bits/exception_ptr.h:

/usr/include/c++/14.2.1/bits/functexcept.h:

/usr/include/c++/14.2.1/ext/alloc_traits.h:

/usr/include/bits/types/__locale_t.h:

/usr/include/c++/14.2.1/bits/functional_hash.h:

/usr/include/c++/14.2.1/bits/hash_bytes.h:

/usr/include/c++/14.2.1/bits/hashtable.h:

/usr/include/bits/types/struct_iovec.h:

/usr/include/c++/14.2.1/bits/invoke.h:

/usr/include/c++/14.2.1/bits/ios_base.h:

/usr/include/c++/14.2.1/bits/iterator_concepts.h:

/usr/include/c++/14.2.1/bits/unicode-data.h:

/usr/include/c++/14.2.1/bits/locale_classes.h:

/usr/include/bits/struct_mutex.h:

/usr/include/c++/14.2.1/bits/locale_classes.tcc:

/usr/include/c++/14.2.1/bits/locale_facets_nonio.h:

/usr/include/c++/14.2.1/bits/max_size_type.h:

/usr/include/c++/14.2.1/bits/memory_resource.h:

/usr/include/c++/14.2.1/bits/move.h:

/usr/include/c++/14.2.1/bits/nested_exception.h:

/usr/include/c++/14.2.1/bits/new_allocator.h:

/usr/include/c++/14.2.1/bits/node_handle.h:

/usr/include/stdint.h:

/usr/include/endian.h:

/usr/include/c++/14.2.1/bits/ostream.tcc:

/usr/include/c++/14.2.1/bits/ostream_insert.h:

/usr/include/c++/14.2.1/algorithm:

/usr/include/c++/14.2.1/bits/postypes.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/ctype_base.h:

/usr/include/c++/14.2.1/bits/quoted_string.h:

/usr/include/c++/14.2.1/bits/refwrap.h:

/usr/include/c++/14.2.1/bits/ranges_algobase.h:

/usr/include/bits/stdlib-float.h:

/usr/include/c++/14.2.1/bits/ranges_base.h:

/usr/include/c++/14.2.1/bits/ptr_traits.h:

/usr/include/c++/14.2.1/bits/ranges_cmp.h:

/usr/include/linux/sched/types.h:

/usr/include/c++/14.2.1/bits/ranges_uninitialized.h:

/usr/include/c++/14.2.1/bits/ranges_util.h:

/usr/include/c++/14.2.1/bits/semaphore_base.h:

/usr/include/c++/14.2.1/tr1/bessel_function.tcc:

/usr/include/asm/unistd.h:

/usr/include/c++/14.2.1/bits/shared_ptr.h:

/usr/include/c++/14.2.1/bits/alloc_traits.h:

/usr/include/c++/14.2.1/bits/sstream.tcc:

/usr/include/c++/14.2.1/cstdlib:

/usr/include/c++/14.2.1/bits/stl_algobase.h:

/usr/include/c++/14.2.1/bits/std_function.h:

/usr/include/c++/14.2.1/bits/std_mutex.h:

/usr/include/c++/14.2.1/bits/std_abs.h:

/usr/include/c++/14.2.1/bits/stl_bvector.h:

/usr/include/c++/14.2.1/bits/stl_heap.h:

/usr/include/c++/14.2.1/bits/stl_iterator_base_types.h:

/usr/include/c++/14.2.1/bits/stl_pair.h:

/usr/include/c++/14.2.1/bits/stl_raw_storage_iter.h:

/usr/include/sys/syscall.h:

/usr/include/c++/14.2.1/bits/basic_string.tcc:

/usr/include/c++/14.2.1/bits/stl_relops.h:

/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu/bits/cpu_defines.h:

/usr/include/c++/14.2.1/bits/stl_vector.h:

/usr/include/c++/14.2.1/variant:

/usr/include/c++/14.2.1/bits/streambuf.tcc:

/usr/include/c++/14.2.1/bits/streambuf_iterator.h:

/usr/include/c++/14.2.1/bits/string_view.tcc:

/usr/include/c++/14.2.1/bits/unicode.h:

/usr/include/c++/14.2.1/bits/unordered_map.h:

/usr/include/c++/14.2.1/ext/atomicity.h:

/usr/include/c++/14.2.1/bits/stl_construct.h:

/usr/include/c++/14.2.1/bits/utility.h:
