# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\" \"fmt-core\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/libusbgx//CMakeFiles/progress.marks
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusbgx/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusbgx/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusbgx/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusbgx/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/libusbgx/CMakeFiles/libusbgx.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/rule
.PHONY : 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/rule

# Convenience name for target.
libusbgx: 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/rule
.PHONY : libusbgx

# fast build rule for target.
libusbgx/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build
.PHONY : libusbgx/fast

src/function/ether.o: src/function/ether.c.o
.PHONY : src/function/ether.o

# target to build an object file
src/function/ether.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.o
.PHONY : src/function/ether.c.o

src/function/ether.i: src/function/ether.c.i
.PHONY : src/function/ether.i

# target to preprocess a source file
src/function/ether.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.i
.PHONY : src/function/ether.c.i

src/function/ether.s: src/function/ether.c.s
.PHONY : src/function/ether.s

# target to generate assembly for a file
src/function/ether.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ether.c.s
.PHONY : src/function/ether.c.s

src/function/ffs.o: src/function/ffs.c.o
.PHONY : src/function/ffs.o

# target to build an object file
src/function/ffs.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.o
.PHONY : src/function/ffs.c.o

src/function/ffs.i: src/function/ffs.c.i
.PHONY : src/function/ffs.i

# target to preprocess a source file
src/function/ffs.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.i
.PHONY : src/function/ffs.c.i

src/function/ffs.s: src/function/ffs.c.s
.PHONY : src/function/ffs.s

# target to generate assembly for a file
src/function/ffs.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ffs.c.s
.PHONY : src/function/ffs.c.s

src/function/hid.o: src/function/hid.c.o
.PHONY : src/function/hid.o

# target to build an object file
src/function/hid.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.o
.PHONY : src/function/hid.c.o

src/function/hid.i: src/function/hid.c.i
.PHONY : src/function/hid.i

# target to preprocess a source file
src/function/hid.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.i
.PHONY : src/function/hid.c.i

src/function/hid.s: src/function/hid.c.s
.PHONY : src/function/hid.s

# target to generate assembly for a file
src/function/hid.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/hid.c.s
.PHONY : src/function/hid.c.s

src/function/loopback.o: src/function/loopback.c.o
.PHONY : src/function/loopback.o

# target to build an object file
src/function/loopback.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.o
.PHONY : src/function/loopback.c.o

src/function/loopback.i: src/function/loopback.c.i
.PHONY : src/function/loopback.i

# target to preprocess a source file
src/function/loopback.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.i
.PHONY : src/function/loopback.c.i

src/function/loopback.s: src/function/loopback.c.s
.PHONY : src/function/loopback.s

# target to generate assembly for a file
src/function/loopback.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/loopback.c.s
.PHONY : src/function/loopback.c.s

src/function/midi.o: src/function/midi.c.o
.PHONY : src/function/midi.o

# target to build an object file
src/function/midi.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.o
.PHONY : src/function/midi.c.o

src/function/midi.i: src/function/midi.c.i
.PHONY : src/function/midi.i

# target to preprocess a source file
src/function/midi.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.i
.PHONY : src/function/midi.c.i

src/function/midi.s: src/function/midi.c.s
.PHONY : src/function/midi.s

# target to generate assembly for a file
src/function/midi.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/midi.c.s
.PHONY : src/function/midi.c.s

src/function/ms.o: src/function/ms.c.o
.PHONY : src/function/ms.o

# target to build an object file
src/function/ms.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.o
.PHONY : src/function/ms.c.o

src/function/ms.i: src/function/ms.c.i
.PHONY : src/function/ms.i

# target to preprocess a source file
src/function/ms.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.i
.PHONY : src/function/ms.c.i

src/function/ms.s: src/function/ms.c.s
.PHONY : src/function/ms.s

# target to generate assembly for a file
src/function/ms.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/ms.c.s
.PHONY : src/function/ms.c.s

src/function/phonet.o: src/function/phonet.c.o
.PHONY : src/function/phonet.o

# target to build an object file
src/function/phonet.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.o
.PHONY : src/function/phonet.c.o

src/function/phonet.i: src/function/phonet.c.i
.PHONY : src/function/phonet.i

# target to preprocess a source file
src/function/phonet.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.i
.PHONY : src/function/phonet.c.i

src/function/phonet.s: src/function/phonet.c.s
.PHONY : src/function/phonet.s

# target to generate assembly for a file
src/function/phonet.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/phonet.c.s
.PHONY : src/function/phonet.c.s

src/function/printer.o: src/function/printer.c.o
.PHONY : src/function/printer.o

# target to build an object file
src/function/printer.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.o
.PHONY : src/function/printer.c.o

src/function/printer.i: src/function/printer.c.i
.PHONY : src/function/printer.i

# target to preprocess a source file
src/function/printer.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.i
.PHONY : src/function/printer.c.i

src/function/printer.s: src/function/printer.c.s
.PHONY : src/function/printer.s

# target to generate assembly for a file
src/function/printer.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/printer.c.s
.PHONY : src/function/printer.c.s

src/function/serial.o: src/function/serial.c.o
.PHONY : src/function/serial.o

# target to build an object file
src/function/serial.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.o
.PHONY : src/function/serial.c.o

src/function/serial.i: src/function/serial.c.i
.PHONY : src/function/serial.i

# target to preprocess a source file
src/function/serial.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.i
.PHONY : src/function/serial.c.i

src/function/serial.s: src/function/serial.c.s
.PHONY : src/function/serial.s

# target to generate assembly for a file
src/function/serial.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/serial.c.s
.PHONY : src/function/serial.c.s

src/function/uac2.o: src/function/uac2.c.o
.PHONY : src/function/uac2.o

# target to build an object file
src/function/uac2.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.o
.PHONY : src/function/uac2.c.o

src/function/uac2.i: src/function/uac2.c.i
.PHONY : src/function/uac2.i

# target to preprocess a source file
src/function/uac2.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.i
.PHONY : src/function/uac2.c.i

src/function/uac2.s: src/function/uac2.c.s
.PHONY : src/function/uac2.s

# target to generate assembly for a file
src/function/uac2.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uac2.c.s
.PHONY : src/function/uac2.c.s

src/function/uvc.o: src/function/uvc.c.o
.PHONY : src/function/uvc.o

# target to build an object file
src/function/uvc.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.o
.PHONY : src/function/uvc.c.o

src/function/uvc.i: src/function/uvc.c.i
.PHONY : src/function/uvc.i

# target to preprocess a source file
src/function/uvc.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.i
.PHONY : src/function/uvc.c.i

src/function/uvc.s: src/function/uvc.c.s
.PHONY : src/function/uvc.s

# target to generate assembly for a file
src/function/uvc.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/function/uvc.c.s
.PHONY : src/function/uvc.c.s

src/usbg.o: src/usbg.c.o
.PHONY : src/usbg.o

# target to build an object file
src/usbg.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.o
.PHONY : src/usbg.c.o

src/usbg.i: src/usbg.c.i
.PHONY : src/usbg.i

# target to preprocess a source file
src/usbg.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.i
.PHONY : src/usbg.c.i

src/usbg.s: src/usbg.c.s
.PHONY : src/usbg.s

# target to generate assembly for a file
src/usbg.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg.c.s
.PHONY : src/usbg.c.s

src/usbg_common.o: src/usbg_common.c.o
.PHONY : src/usbg_common.o

# target to build an object file
src/usbg_common.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.o
.PHONY : src/usbg_common.c.o

src/usbg_common.i: src/usbg_common.c.i
.PHONY : src/usbg_common.i

# target to preprocess a source file
src/usbg_common.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.i
.PHONY : src/usbg_common.c.i

src/usbg_common.s: src/usbg_common.c.s
.PHONY : src/usbg_common.s

# target to generate assembly for a file
src/usbg_common.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_common.c.s
.PHONY : src/usbg_common.c.s

src/usbg_error.o: src/usbg_error.c.o
.PHONY : src/usbg_error.o

# target to build an object file
src/usbg_error.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.o
.PHONY : src/usbg_error.c.o

src/usbg_error.i: src/usbg_error.c.i
.PHONY : src/usbg_error.i

# target to preprocess a source file
src/usbg_error.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.i
.PHONY : src/usbg_error.c.i

src/usbg_error.s: src/usbg_error.c.s
.PHONY : src/usbg_error.s

# target to generate assembly for a file
src/usbg_error.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_error.c.s
.PHONY : src/usbg_error.c.s

src/usbg_schemes_none.o: src/usbg_schemes_none.c.o
.PHONY : src/usbg_schemes_none.o

# target to build an object file
src/usbg_schemes_none.c.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.o
.PHONY : src/usbg_schemes_none.c.o

src/usbg_schemes_none.i: src/usbg_schemes_none.c.i
.PHONY : src/usbg_schemes_none.i

# target to preprocess a source file
src/usbg_schemes_none.c.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.i
.PHONY : src/usbg_schemes_none.c.i

src/usbg_schemes_none.s: src/usbg_schemes_none.c.s
.PHONY : src/usbg_schemes_none.s

# target to generate assembly for a file
src/usbg_schemes_none.c.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/build.make 3rdparty/libusbgx/CMakeFiles/libusbgx.dir/src/usbg_schemes_none.c.s
.PHONY : src/usbg_schemes_none.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... libusbgx"
	@echo "... src/function/ether.o"
	@echo "... src/function/ether.i"
	@echo "... src/function/ether.s"
	@echo "... src/function/ffs.o"
	@echo "... src/function/ffs.i"
	@echo "... src/function/ffs.s"
	@echo "... src/function/hid.o"
	@echo "... src/function/hid.i"
	@echo "... src/function/hid.s"
	@echo "... src/function/loopback.o"
	@echo "... src/function/loopback.i"
	@echo "... src/function/loopback.s"
	@echo "... src/function/midi.o"
	@echo "... src/function/midi.i"
	@echo "... src/function/midi.s"
	@echo "... src/function/ms.o"
	@echo "... src/function/ms.i"
	@echo "... src/function/ms.s"
	@echo "... src/function/phonet.o"
	@echo "... src/function/phonet.i"
	@echo "... src/function/phonet.s"
	@echo "... src/function/printer.o"
	@echo "... src/function/printer.i"
	@echo "... src/function/printer.s"
	@echo "... src/function/serial.o"
	@echo "... src/function/serial.i"
	@echo "... src/function/serial.s"
	@echo "... src/function/uac2.o"
	@echo "... src/function/uac2.i"
	@echo "... src/function/uac2.s"
	@echo "... src/function/uvc.o"
	@echo "... src/function/uvc.i"
	@echo "... src/function/uvc.s"
	@echo "... src/usbg.o"
	@echo "... src/usbg.i"
	@echo "... src/usbg.s"
	@echo "... src/usbg_common.o"
	@echo "... src/usbg_common.i"
	@echo "... src/usbg_common.s"
	@echo "... src/usbg_error.o"
	@echo "... src/usbg_error.i"
	@echo "... src/usbg_error.s"
	@echo "... src/usbg_schemes_none.o"
	@echo "... src/usbg_schemes_none.i"
	@echo "... src/usbg_schemes_none.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

