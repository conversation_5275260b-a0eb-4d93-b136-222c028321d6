
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/core.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/descriptor.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/hotplug.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/io.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/events_posix.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_udev.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/linux_usbfs.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/os/threads_posix.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/strerror.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/libusb/libusb/sync.c" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o" "gcc" "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
