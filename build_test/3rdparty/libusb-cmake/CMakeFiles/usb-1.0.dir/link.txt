/usr/bin/ar qc ../../bin/lib/libusb-1.0.a "CMakeFiles/usb-1.0.dir/libusb/libusb/core.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/descriptor.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/hotplug.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/io.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/strerror.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/sync.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/os/events_posix.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/os/threads_posix.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_usbfs.c.o" "CMakeFiles/usb-1.0.dir/libusb/libusb/os/linux_udev.c.o"
/usr/bin/ranlib ../../bin/lib/libusb-1.0.a
