# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/compiler_depend.make

# Include the progress variables for this target.
include 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/flags.make

3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/codegen:
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/codegen

3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/flags.make
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_main.cpp
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o -MF CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o.d -o CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_main.cpp

3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_main.cpp > CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.i

3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/internal/catch_main.cpp -o CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.s

# Object files for target Catch2WithMain
Catch2WithMain_OBJECTS = \
"CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o"

# External object files for target Catch2WithMain
Catch2WithMain_EXTERNAL_OBJECTS =

bin/lib/libCatch2Main.a: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o
bin/lib/libCatch2Main.a: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make
bin/lib/libCatch2Main.a: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library ../../../bin/lib/libCatch2Main.a"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src && $(CMAKE_COMMAND) -P CMakeFiles/Catch2WithMain.dir/cmake_clean_target.cmake
	cd /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/Catch2WithMain.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build: bin/lib/libCatch2Main.a
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build

3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src && $(CMAKE_COMMAND) -P CMakeFiles/Catch2WithMain.dir/cmake_clean.cmake
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/clean

3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/depend

