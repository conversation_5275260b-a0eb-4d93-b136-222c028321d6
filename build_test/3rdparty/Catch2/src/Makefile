# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\" \"fmt-core\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/Catch2/src//CMakeFiles/progress.marks
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/Catch2/src/CMakeFiles/Catch2.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/rule
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/rule

# Convenience name for target.
Catch2: 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/rule
.PHONY : Catch2

# fast build rule for target.
Catch2/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build
.PHONY : Catch2/fast

# Convenience name for target.
3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/rule
.PHONY : 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/rule

# Convenience name for target.
Catch2WithMain: 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/rule
.PHONY : Catch2WithMain

# fast build rule for target.
Catch2WithMain/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build
.PHONY : Catch2WithMain/fast

catch2/benchmark/catch_chronometer.o: catch2/benchmark/catch_chronometer.cpp.o
.PHONY : catch2/benchmark/catch_chronometer.o

# target to build an object file
catch2/benchmark/catch_chronometer.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.o
.PHONY : catch2/benchmark/catch_chronometer.cpp.o

catch2/benchmark/catch_chronometer.i: catch2/benchmark/catch_chronometer.cpp.i
.PHONY : catch2/benchmark/catch_chronometer.i

# target to preprocess a source file
catch2/benchmark/catch_chronometer.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.i
.PHONY : catch2/benchmark/catch_chronometer.cpp.i

catch2/benchmark/catch_chronometer.s: catch2/benchmark/catch_chronometer.cpp.s
.PHONY : catch2/benchmark/catch_chronometer.s

# target to generate assembly for a file
catch2/benchmark/catch_chronometer.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/catch_chronometer.cpp.s
.PHONY : catch2/benchmark/catch_chronometer.cpp.s

catch2/benchmark/detail/catch_analyse.o: catch2/benchmark/detail/catch_analyse.cpp.o
.PHONY : catch2/benchmark/detail/catch_analyse.o

# target to build an object file
catch2/benchmark/detail/catch_analyse.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.o
.PHONY : catch2/benchmark/detail/catch_analyse.cpp.o

catch2/benchmark/detail/catch_analyse.i: catch2/benchmark/detail/catch_analyse.cpp.i
.PHONY : catch2/benchmark/detail/catch_analyse.i

# target to preprocess a source file
catch2/benchmark/detail/catch_analyse.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.i
.PHONY : catch2/benchmark/detail/catch_analyse.cpp.i

catch2/benchmark/detail/catch_analyse.s: catch2/benchmark/detail/catch_analyse.cpp.s
.PHONY : catch2/benchmark/detail/catch_analyse.s

# target to generate assembly for a file
catch2/benchmark/detail/catch_analyse.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_analyse.cpp.s
.PHONY : catch2/benchmark/detail/catch_analyse.cpp.s

catch2/benchmark/detail/catch_benchmark_function.o: catch2/benchmark/detail/catch_benchmark_function.cpp.o
.PHONY : catch2/benchmark/detail/catch_benchmark_function.o

# target to build an object file
catch2/benchmark/detail/catch_benchmark_function.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.o
.PHONY : catch2/benchmark/detail/catch_benchmark_function.cpp.o

catch2/benchmark/detail/catch_benchmark_function.i: catch2/benchmark/detail/catch_benchmark_function.cpp.i
.PHONY : catch2/benchmark/detail/catch_benchmark_function.i

# target to preprocess a source file
catch2/benchmark/detail/catch_benchmark_function.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.i
.PHONY : catch2/benchmark/detail/catch_benchmark_function.cpp.i

catch2/benchmark/detail/catch_benchmark_function.s: catch2/benchmark/detail/catch_benchmark_function.cpp.s
.PHONY : catch2/benchmark/detail/catch_benchmark_function.s

# target to generate assembly for a file
catch2/benchmark/detail/catch_benchmark_function.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_benchmark_function.cpp.s
.PHONY : catch2/benchmark/detail/catch_benchmark_function.cpp.s

catch2/benchmark/detail/catch_run_for_at_least.o: catch2/benchmark/detail/catch_run_for_at_least.cpp.o
.PHONY : catch2/benchmark/detail/catch_run_for_at_least.o

# target to build an object file
catch2/benchmark/detail/catch_run_for_at_least.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.o
.PHONY : catch2/benchmark/detail/catch_run_for_at_least.cpp.o

catch2/benchmark/detail/catch_run_for_at_least.i: catch2/benchmark/detail/catch_run_for_at_least.cpp.i
.PHONY : catch2/benchmark/detail/catch_run_for_at_least.i

# target to preprocess a source file
catch2/benchmark/detail/catch_run_for_at_least.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.i
.PHONY : catch2/benchmark/detail/catch_run_for_at_least.cpp.i

catch2/benchmark/detail/catch_run_for_at_least.s: catch2/benchmark/detail/catch_run_for_at_least.cpp.s
.PHONY : catch2/benchmark/detail/catch_run_for_at_least.s

# target to generate assembly for a file
catch2/benchmark/detail/catch_run_for_at_least.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_run_for_at_least.cpp.s
.PHONY : catch2/benchmark/detail/catch_run_for_at_least.cpp.s

catch2/benchmark/detail/catch_stats.o: catch2/benchmark/detail/catch_stats.cpp.o
.PHONY : catch2/benchmark/detail/catch_stats.o

# target to build an object file
catch2/benchmark/detail/catch_stats.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.o
.PHONY : catch2/benchmark/detail/catch_stats.cpp.o

catch2/benchmark/detail/catch_stats.i: catch2/benchmark/detail/catch_stats.cpp.i
.PHONY : catch2/benchmark/detail/catch_stats.i

# target to preprocess a source file
catch2/benchmark/detail/catch_stats.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.i
.PHONY : catch2/benchmark/detail/catch_stats.cpp.i

catch2/benchmark/detail/catch_stats.s: catch2/benchmark/detail/catch_stats.cpp.s
.PHONY : catch2/benchmark/detail/catch_stats.s

# target to generate assembly for a file
catch2/benchmark/detail/catch_stats.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/benchmark/detail/catch_stats.cpp.s
.PHONY : catch2/benchmark/detail/catch_stats.cpp.s

catch2/catch_approx.o: catch2/catch_approx.cpp.o
.PHONY : catch2/catch_approx.o

# target to build an object file
catch2/catch_approx.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.o
.PHONY : catch2/catch_approx.cpp.o

catch2/catch_approx.i: catch2/catch_approx.cpp.i
.PHONY : catch2/catch_approx.i

# target to preprocess a source file
catch2/catch_approx.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.i
.PHONY : catch2/catch_approx.cpp.i

catch2/catch_approx.s: catch2/catch_approx.cpp.s
.PHONY : catch2/catch_approx.s

# target to generate assembly for a file
catch2/catch_approx.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_approx.cpp.s
.PHONY : catch2/catch_approx.cpp.s

catch2/catch_assertion_result.o: catch2/catch_assertion_result.cpp.o
.PHONY : catch2/catch_assertion_result.o

# target to build an object file
catch2/catch_assertion_result.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.o
.PHONY : catch2/catch_assertion_result.cpp.o

catch2/catch_assertion_result.i: catch2/catch_assertion_result.cpp.i
.PHONY : catch2/catch_assertion_result.i

# target to preprocess a source file
catch2/catch_assertion_result.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.i
.PHONY : catch2/catch_assertion_result.cpp.i

catch2/catch_assertion_result.s: catch2/catch_assertion_result.cpp.s
.PHONY : catch2/catch_assertion_result.s

# target to generate assembly for a file
catch2/catch_assertion_result.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_assertion_result.cpp.s
.PHONY : catch2/catch_assertion_result.cpp.s

catch2/catch_config.o: catch2/catch_config.cpp.o
.PHONY : catch2/catch_config.o

# target to build an object file
catch2/catch_config.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.o
.PHONY : catch2/catch_config.cpp.o

catch2/catch_config.i: catch2/catch_config.cpp.i
.PHONY : catch2/catch_config.i

# target to preprocess a source file
catch2/catch_config.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.i
.PHONY : catch2/catch_config.cpp.i

catch2/catch_config.s: catch2/catch_config.cpp.s
.PHONY : catch2/catch_config.s

# target to generate assembly for a file
catch2/catch_config.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_config.cpp.s
.PHONY : catch2/catch_config.cpp.s

catch2/catch_get_random_seed.o: catch2/catch_get_random_seed.cpp.o
.PHONY : catch2/catch_get_random_seed.o

# target to build an object file
catch2/catch_get_random_seed.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.o
.PHONY : catch2/catch_get_random_seed.cpp.o

catch2/catch_get_random_seed.i: catch2/catch_get_random_seed.cpp.i
.PHONY : catch2/catch_get_random_seed.i

# target to preprocess a source file
catch2/catch_get_random_seed.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.i
.PHONY : catch2/catch_get_random_seed.cpp.i

catch2/catch_get_random_seed.s: catch2/catch_get_random_seed.cpp.s
.PHONY : catch2/catch_get_random_seed.s

# target to generate assembly for a file
catch2/catch_get_random_seed.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_get_random_seed.cpp.s
.PHONY : catch2/catch_get_random_seed.cpp.s

catch2/catch_message.o: catch2/catch_message.cpp.o
.PHONY : catch2/catch_message.o

# target to build an object file
catch2/catch_message.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.o
.PHONY : catch2/catch_message.cpp.o

catch2/catch_message.i: catch2/catch_message.cpp.i
.PHONY : catch2/catch_message.i

# target to preprocess a source file
catch2/catch_message.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.i
.PHONY : catch2/catch_message.cpp.i

catch2/catch_message.s: catch2/catch_message.cpp.s
.PHONY : catch2/catch_message.s

# target to generate assembly for a file
catch2/catch_message.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_message.cpp.s
.PHONY : catch2/catch_message.cpp.s

catch2/catch_registry_hub.o: catch2/catch_registry_hub.cpp.o
.PHONY : catch2/catch_registry_hub.o

# target to build an object file
catch2/catch_registry_hub.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.o
.PHONY : catch2/catch_registry_hub.cpp.o

catch2/catch_registry_hub.i: catch2/catch_registry_hub.cpp.i
.PHONY : catch2/catch_registry_hub.i

# target to preprocess a source file
catch2/catch_registry_hub.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.i
.PHONY : catch2/catch_registry_hub.cpp.i

catch2/catch_registry_hub.s: catch2/catch_registry_hub.cpp.s
.PHONY : catch2/catch_registry_hub.s

# target to generate assembly for a file
catch2/catch_registry_hub.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_registry_hub.cpp.s
.PHONY : catch2/catch_registry_hub.cpp.s

catch2/catch_session.o: catch2/catch_session.cpp.o
.PHONY : catch2/catch_session.o

# target to build an object file
catch2/catch_session.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.o
.PHONY : catch2/catch_session.cpp.o

catch2/catch_session.i: catch2/catch_session.cpp.i
.PHONY : catch2/catch_session.i

# target to preprocess a source file
catch2/catch_session.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.i
.PHONY : catch2/catch_session.cpp.i

catch2/catch_session.s: catch2/catch_session.cpp.s
.PHONY : catch2/catch_session.s

# target to generate assembly for a file
catch2/catch_session.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_session.cpp.s
.PHONY : catch2/catch_session.cpp.s

catch2/catch_tag_alias_autoregistrar.o: catch2/catch_tag_alias_autoregistrar.cpp.o
.PHONY : catch2/catch_tag_alias_autoregistrar.o

# target to build an object file
catch2/catch_tag_alias_autoregistrar.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.o
.PHONY : catch2/catch_tag_alias_autoregistrar.cpp.o

catch2/catch_tag_alias_autoregistrar.i: catch2/catch_tag_alias_autoregistrar.cpp.i
.PHONY : catch2/catch_tag_alias_autoregistrar.i

# target to preprocess a source file
catch2/catch_tag_alias_autoregistrar.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.i
.PHONY : catch2/catch_tag_alias_autoregistrar.cpp.i

catch2/catch_tag_alias_autoregistrar.s: catch2/catch_tag_alias_autoregistrar.cpp.s
.PHONY : catch2/catch_tag_alias_autoregistrar.s

# target to generate assembly for a file
catch2/catch_tag_alias_autoregistrar.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tag_alias_autoregistrar.cpp.s
.PHONY : catch2/catch_tag_alias_autoregistrar.cpp.s

catch2/catch_test_case_info.o: catch2/catch_test_case_info.cpp.o
.PHONY : catch2/catch_test_case_info.o

# target to build an object file
catch2/catch_test_case_info.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.o
.PHONY : catch2/catch_test_case_info.cpp.o

catch2/catch_test_case_info.i: catch2/catch_test_case_info.cpp.i
.PHONY : catch2/catch_test_case_info.i

# target to preprocess a source file
catch2/catch_test_case_info.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.i
.PHONY : catch2/catch_test_case_info.cpp.i

catch2/catch_test_case_info.s: catch2/catch_test_case_info.cpp.s
.PHONY : catch2/catch_test_case_info.s

# target to generate assembly for a file
catch2/catch_test_case_info.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_case_info.cpp.s
.PHONY : catch2/catch_test_case_info.cpp.s

catch2/catch_test_spec.o: catch2/catch_test_spec.cpp.o
.PHONY : catch2/catch_test_spec.o

# target to build an object file
catch2/catch_test_spec.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.o
.PHONY : catch2/catch_test_spec.cpp.o

catch2/catch_test_spec.i: catch2/catch_test_spec.cpp.i
.PHONY : catch2/catch_test_spec.i

# target to preprocess a source file
catch2/catch_test_spec.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.i
.PHONY : catch2/catch_test_spec.cpp.i

catch2/catch_test_spec.s: catch2/catch_test_spec.cpp.s
.PHONY : catch2/catch_test_spec.s

# target to generate assembly for a file
catch2/catch_test_spec.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_test_spec.cpp.s
.PHONY : catch2/catch_test_spec.cpp.s

catch2/catch_timer.o: catch2/catch_timer.cpp.o
.PHONY : catch2/catch_timer.o

# target to build an object file
catch2/catch_timer.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.o
.PHONY : catch2/catch_timer.cpp.o

catch2/catch_timer.i: catch2/catch_timer.cpp.i
.PHONY : catch2/catch_timer.i

# target to preprocess a source file
catch2/catch_timer.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.i
.PHONY : catch2/catch_timer.cpp.i

catch2/catch_timer.s: catch2/catch_timer.cpp.s
.PHONY : catch2/catch_timer.s

# target to generate assembly for a file
catch2/catch_timer.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_timer.cpp.s
.PHONY : catch2/catch_timer.cpp.s

catch2/catch_tostring.o: catch2/catch_tostring.cpp.o
.PHONY : catch2/catch_tostring.o

# target to build an object file
catch2/catch_tostring.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.o
.PHONY : catch2/catch_tostring.cpp.o

catch2/catch_tostring.i: catch2/catch_tostring.cpp.i
.PHONY : catch2/catch_tostring.i

# target to preprocess a source file
catch2/catch_tostring.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.i
.PHONY : catch2/catch_tostring.cpp.i

catch2/catch_tostring.s: catch2/catch_tostring.cpp.s
.PHONY : catch2/catch_tostring.s

# target to generate assembly for a file
catch2/catch_tostring.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_tostring.cpp.s
.PHONY : catch2/catch_tostring.cpp.s

catch2/catch_totals.o: catch2/catch_totals.cpp.o
.PHONY : catch2/catch_totals.o

# target to build an object file
catch2/catch_totals.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.o
.PHONY : catch2/catch_totals.cpp.o

catch2/catch_totals.i: catch2/catch_totals.cpp.i
.PHONY : catch2/catch_totals.i

# target to preprocess a source file
catch2/catch_totals.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.i
.PHONY : catch2/catch_totals.cpp.i

catch2/catch_totals.s: catch2/catch_totals.cpp.s
.PHONY : catch2/catch_totals.s

# target to generate assembly for a file
catch2/catch_totals.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_totals.cpp.s
.PHONY : catch2/catch_totals.cpp.s

catch2/catch_translate_exception.o: catch2/catch_translate_exception.cpp.o
.PHONY : catch2/catch_translate_exception.o

# target to build an object file
catch2/catch_translate_exception.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.o
.PHONY : catch2/catch_translate_exception.cpp.o

catch2/catch_translate_exception.i: catch2/catch_translate_exception.cpp.i
.PHONY : catch2/catch_translate_exception.i

# target to preprocess a source file
catch2/catch_translate_exception.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.i
.PHONY : catch2/catch_translate_exception.cpp.i

catch2/catch_translate_exception.s: catch2/catch_translate_exception.cpp.s
.PHONY : catch2/catch_translate_exception.s

# target to generate assembly for a file
catch2/catch_translate_exception.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_translate_exception.cpp.s
.PHONY : catch2/catch_translate_exception.cpp.s

catch2/catch_version.o: catch2/catch_version.cpp.o
.PHONY : catch2/catch_version.o

# target to build an object file
catch2/catch_version.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.o
.PHONY : catch2/catch_version.cpp.o

catch2/catch_version.i: catch2/catch_version.cpp.i
.PHONY : catch2/catch_version.i

# target to preprocess a source file
catch2/catch_version.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.i
.PHONY : catch2/catch_version.cpp.i

catch2/catch_version.s: catch2/catch_version.cpp.s
.PHONY : catch2/catch_version.s

# target to generate assembly for a file
catch2/catch_version.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/catch_version.cpp.s
.PHONY : catch2/catch_version.cpp.s

catch2/generators/catch_generator_exception.o: catch2/generators/catch_generator_exception.cpp.o
.PHONY : catch2/generators/catch_generator_exception.o

# target to build an object file
catch2/generators/catch_generator_exception.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.o
.PHONY : catch2/generators/catch_generator_exception.cpp.o

catch2/generators/catch_generator_exception.i: catch2/generators/catch_generator_exception.cpp.i
.PHONY : catch2/generators/catch_generator_exception.i

# target to preprocess a source file
catch2/generators/catch_generator_exception.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.i
.PHONY : catch2/generators/catch_generator_exception.cpp.i

catch2/generators/catch_generator_exception.s: catch2/generators/catch_generator_exception.cpp.s
.PHONY : catch2/generators/catch_generator_exception.s

# target to generate assembly for a file
catch2/generators/catch_generator_exception.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generator_exception.cpp.s
.PHONY : catch2/generators/catch_generator_exception.cpp.s

catch2/generators/catch_generators.o: catch2/generators/catch_generators.cpp.o
.PHONY : catch2/generators/catch_generators.o

# target to build an object file
catch2/generators/catch_generators.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.o
.PHONY : catch2/generators/catch_generators.cpp.o

catch2/generators/catch_generators.i: catch2/generators/catch_generators.cpp.i
.PHONY : catch2/generators/catch_generators.i

# target to preprocess a source file
catch2/generators/catch_generators.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.i
.PHONY : catch2/generators/catch_generators.cpp.i

catch2/generators/catch_generators.s: catch2/generators/catch_generators.cpp.s
.PHONY : catch2/generators/catch_generators.s

# target to generate assembly for a file
catch2/generators/catch_generators.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators.cpp.s
.PHONY : catch2/generators/catch_generators.cpp.s

catch2/generators/catch_generators_random.o: catch2/generators/catch_generators_random.cpp.o
.PHONY : catch2/generators/catch_generators_random.o

# target to build an object file
catch2/generators/catch_generators_random.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.o
.PHONY : catch2/generators/catch_generators_random.cpp.o

catch2/generators/catch_generators_random.i: catch2/generators/catch_generators_random.cpp.i
.PHONY : catch2/generators/catch_generators_random.i

# target to preprocess a source file
catch2/generators/catch_generators_random.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.i
.PHONY : catch2/generators/catch_generators_random.cpp.i

catch2/generators/catch_generators_random.s: catch2/generators/catch_generators_random.cpp.s
.PHONY : catch2/generators/catch_generators_random.s

# target to generate assembly for a file
catch2/generators/catch_generators_random.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/generators/catch_generators_random.cpp.s
.PHONY : catch2/generators/catch_generators_random.cpp.s

catch2/interfaces/catch_interfaces_capture.o: catch2/interfaces/catch_interfaces_capture.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_capture.o

# target to build an object file
catch2/interfaces/catch_interfaces_capture.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_capture.cpp.o

catch2/interfaces/catch_interfaces_capture.i: catch2/interfaces/catch_interfaces_capture.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_capture.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_capture.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_capture.cpp.i

catch2/interfaces/catch_interfaces_capture.s: catch2/interfaces/catch_interfaces_capture.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_capture.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_capture.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_capture.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_capture.cpp.s

catch2/interfaces/catch_interfaces_config.o: catch2/interfaces/catch_interfaces_config.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_config.o

# target to build an object file
catch2/interfaces/catch_interfaces_config.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_config.cpp.o

catch2/interfaces/catch_interfaces_config.i: catch2/interfaces/catch_interfaces_config.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_config.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_config.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_config.cpp.i

catch2/interfaces/catch_interfaces_config.s: catch2/interfaces/catch_interfaces_config.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_config.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_config.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_config.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_config.cpp.s

catch2/interfaces/catch_interfaces_exception.o: catch2/interfaces/catch_interfaces_exception.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_exception.o

# target to build an object file
catch2/interfaces/catch_interfaces_exception.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_exception.cpp.o

catch2/interfaces/catch_interfaces_exception.i: catch2/interfaces/catch_interfaces_exception.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_exception.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_exception.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_exception.cpp.i

catch2/interfaces/catch_interfaces_exception.s: catch2/interfaces/catch_interfaces_exception.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_exception.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_exception.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_exception.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_exception.cpp.s

catch2/interfaces/catch_interfaces_generatortracker.o: catch2/interfaces/catch_interfaces_generatortracker.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_generatortracker.o

# target to build an object file
catch2/interfaces/catch_interfaces_generatortracker.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_generatortracker.cpp.o

catch2/interfaces/catch_interfaces_generatortracker.i: catch2/interfaces/catch_interfaces_generatortracker.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_generatortracker.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_generatortracker.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_generatortracker.cpp.i

catch2/interfaces/catch_interfaces_generatortracker.s: catch2/interfaces/catch_interfaces_generatortracker.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_generatortracker.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_generatortracker.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_generatortracker.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_generatortracker.cpp.s

catch2/interfaces/catch_interfaces_registry_hub.o: catch2/interfaces/catch_interfaces_registry_hub.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_registry_hub.o

# target to build an object file
catch2/interfaces/catch_interfaces_registry_hub.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_registry_hub.cpp.o

catch2/interfaces/catch_interfaces_registry_hub.i: catch2/interfaces/catch_interfaces_registry_hub.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_registry_hub.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_registry_hub.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_registry_hub.cpp.i

catch2/interfaces/catch_interfaces_registry_hub.s: catch2/interfaces/catch_interfaces_registry_hub.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_registry_hub.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_registry_hub.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_registry_hub.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_registry_hub.cpp.s

catch2/interfaces/catch_interfaces_reporter.o: catch2/interfaces/catch_interfaces_reporter.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_reporter.o

# target to build an object file
catch2/interfaces/catch_interfaces_reporter.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_reporter.cpp.o

catch2/interfaces/catch_interfaces_reporter.i: catch2/interfaces/catch_interfaces_reporter.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_reporter.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_reporter.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_reporter.cpp.i

catch2/interfaces/catch_interfaces_reporter.s: catch2/interfaces/catch_interfaces_reporter.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_reporter.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_reporter.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_reporter.cpp.s

catch2/interfaces/catch_interfaces_reporter_factory.o: catch2/interfaces/catch_interfaces_reporter_factory.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_reporter_factory.o

# target to build an object file
catch2/interfaces/catch_interfaces_reporter_factory.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_reporter_factory.cpp.o

catch2/interfaces/catch_interfaces_reporter_factory.i: catch2/interfaces/catch_interfaces_reporter_factory.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_reporter_factory.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_reporter_factory.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_reporter_factory.cpp.i

catch2/interfaces/catch_interfaces_reporter_factory.s: catch2/interfaces/catch_interfaces_reporter_factory.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_reporter_factory.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_reporter_factory.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_reporter_factory.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_reporter_factory.cpp.s

catch2/interfaces/catch_interfaces_testcase.o: catch2/interfaces/catch_interfaces_testcase.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_testcase.o

# target to build an object file
catch2/interfaces/catch_interfaces_testcase.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.o
.PHONY : catch2/interfaces/catch_interfaces_testcase.cpp.o

catch2/interfaces/catch_interfaces_testcase.i: catch2/interfaces/catch_interfaces_testcase.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_testcase.i

# target to preprocess a source file
catch2/interfaces/catch_interfaces_testcase.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.i
.PHONY : catch2/interfaces/catch_interfaces_testcase.cpp.i

catch2/interfaces/catch_interfaces_testcase.s: catch2/interfaces/catch_interfaces_testcase.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_testcase.s

# target to generate assembly for a file
catch2/interfaces/catch_interfaces_testcase.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/interfaces/catch_interfaces_testcase.cpp.s
.PHONY : catch2/interfaces/catch_interfaces_testcase.cpp.s

catch2/internal/catch_assertion_handler.o: catch2/internal/catch_assertion_handler.cpp.o
.PHONY : catch2/internal/catch_assertion_handler.o

# target to build an object file
catch2/internal/catch_assertion_handler.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.o
.PHONY : catch2/internal/catch_assertion_handler.cpp.o

catch2/internal/catch_assertion_handler.i: catch2/internal/catch_assertion_handler.cpp.i
.PHONY : catch2/internal/catch_assertion_handler.i

# target to preprocess a source file
catch2/internal/catch_assertion_handler.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.i
.PHONY : catch2/internal/catch_assertion_handler.cpp.i

catch2/internal/catch_assertion_handler.s: catch2/internal/catch_assertion_handler.cpp.s
.PHONY : catch2/internal/catch_assertion_handler.s

# target to generate assembly for a file
catch2/internal/catch_assertion_handler.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_assertion_handler.cpp.s
.PHONY : catch2/internal/catch_assertion_handler.cpp.s

catch2/internal/catch_case_insensitive_comparisons.o: catch2/internal/catch_case_insensitive_comparisons.cpp.o
.PHONY : catch2/internal/catch_case_insensitive_comparisons.o

# target to build an object file
catch2/internal/catch_case_insensitive_comparisons.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.o
.PHONY : catch2/internal/catch_case_insensitive_comparisons.cpp.o

catch2/internal/catch_case_insensitive_comparisons.i: catch2/internal/catch_case_insensitive_comparisons.cpp.i
.PHONY : catch2/internal/catch_case_insensitive_comparisons.i

# target to preprocess a source file
catch2/internal/catch_case_insensitive_comparisons.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.i
.PHONY : catch2/internal/catch_case_insensitive_comparisons.cpp.i

catch2/internal/catch_case_insensitive_comparisons.s: catch2/internal/catch_case_insensitive_comparisons.cpp.s
.PHONY : catch2/internal/catch_case_insensitive_comparisons.s

# target to generate assembly for a file
catch2/internal/catch_case_insensitive_comparisons.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_case_insensitive_comparisons.cpp.s
.PHONY : catch2/internal/catch_case_insensitive_comparisons.cpp.s

catch2/internal/catch_clara.o: catch2/internal/catch_clara.cpp.o
.PHONY : catch2/internal/catch_clara.o

# target to build an object file
catch2/internal/catch_clara.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.o
.PHONY : catch2/internal/catch_clara.cpp.o

catch2/internal/catch_clara.i: catch2/internal/catch_clara.cpp.i
.PHONY : catch2/internal/catch_clara.i

# target to preprocess a source file
catch2/internal/catch_clara.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.i
.PHONY : catch2/internal/catch_clara.cpp.i

catch2/internal/catch_clara.s: catch2/internal/catch_clara.cpp.s
.PHONY : catch2/internal/catch_clara.s

# target to generate assembly for a file
catch2/internal/catch_clara.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_clara.cpp.s
.PHONY : catch2/internal/catch_clara.cpp.s

catch2/internal/catch_commandline.o: catch2/internal/catch_commandline.cpp.o
.PHONY : catch2/internal/catch_commandline.o

# target to build an object file
catch2/internal/catch_commandline.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.o
.PHONY : catch2/internal/catch_commandline.cpp.o

catch2/internal/catch_commandline.i: catch2/internal/catch_commandline.cpp.i
.PHONY : catch2/internal/catch_commandline.i

# target to preprocess a source file
catch2/internal/catch_commandline.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.i
.PHONY : catch2/internal/catch_commandline.cpp.i

catch2/internal/catch_commandline.s: catch2/internal/catch_commandline.cpp.s
.PHONY : catch2/internal/catch_commandline.s

# target to generate assembly for a file
catch2/internal/catch_commandline.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_commandline.cpp.s
.PHONY : catch2/internal/catch_commandline.cpp.s

catch2/internal/catch_console_colour.o: catch2/internal/catch_console_colour.cpp.o
.PHONY : catch2/internal/catch_console_colour.o

# target to build an object file
catch2/internal/catch_console_colour.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.o
.PHONY : catch2/internal/catch_console_colour.cpp.o

catch2/internal/catch_console_colour.i: catch2/internal/catch_console_colour.cpp.i
.PHONY : catch2/internal/catch_console_colour.i

# target to preprocess a source file
catch2/internal/catch_console_colour.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.i
.PHONY : catch2/internal/catch_console_colour.cpp.i

catch2/internal/catch_console_colour.s: catch2/internal/catch_console_colour.cpp.s
.PHONY : catch2/internal/catch_console_colour.s

# target to generate assembly for a file
catch2/internal/catch_console_colour.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_console_colour.cpp.s
.PHONY : catch2/internal/catch_console_colour.cpp.s

catch2/internal/catch_context.o: catch2/internal/catch_context.cpp.o
.PHONY : catch2/internal/catch_context.o

# target to build an object file
catch2/internal/catch_context.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.o
.PHONY : catch2/internal/catch_context.cpp.o

catch2/internal/catch_context.i: catch2/internal/catch_context.cpp.i
.PHONY : catch2/internal/catch_context.i

# target to preprocess a source file
catch2/internal/catch_context.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.i
.PHONY : catch2/internal/catch_context.cpp.i

catch2/internal/catch_context.s: catch2/internal/catch_context.cpp.s
.PHONY : catch2/internal/catch_context.s

# target to generate assembly for a file
catch2/internal/catch_context.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_context.cpp.s
.PHONY : catch2/internal/catch_context.cpp.s

catch2/internal/catch_debug_console.o: catch2/internal/catch_debug_console.cpp.o
.PHONY : catch2/internal/catch_debug_console.o

# target to build an object file
catch2/internal/catch_debug_console.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.o
.PHONY : catch2/internal/catch_debug_console.cpp.o

catch2/internal/catch_debug_console.i: catch2/internal/catch_debug_console.cpp.i
.PHONY : catch2/internal/catch_debug_console.i

# target to preprocess a source file
catch2/internal/catch_debug_console.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.i
.PHONY : catch2/internal/catch_debug_console.cpp.i

catch2/internal/catch_debug_console.s: catch2/internal/catch_debug_console.cpp.s
.PHONY : catch2/internal/catch_debug_console.s

# target to generate assembly for a file
catch2/internal/catch_debug_console.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debug_console.cpp.s
.PHONY : catch2/internal/catch_debug_console.cpp.s

catch2/internal/catch_debugger.o: catch2/internal/catch_debugger.cpp.o
.PHONY : catch2/internal/catch_debugger.o

# target to build an object file
catch2/internal/catch_debugger.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.o
.PHONY : catch2/internal/catch_debugger.cpp.o

catch2/internal/catch_debugger.i: catch2/internal/catch_debugger.cpp.i
.PHONY : catch2/internal/catch_debugger.i

# target to preprocess a source file
catch2/internal/catch_debugger.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.i
.PHONY : catch2/internal/catch_debugger.cpp.i

catch2/internal/catch_debugger.s: catch2/internal/catch_debugger.cpp.s
.PHONY : catch2/internal/catch_debugger.s

# target to generate assembly for a file
catch2/internal/catch_debugger.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_debugger.cpp.s
.PHONY : catch2/internal/catch_debugger.cpp.s

catch2/internal/catch_decomposer.o: catch2/internal/catch_decomposer.cpp.o
.PHONY : catch2/internal/catch_decomposer.o

# target to build an object file
catch2/internal/catch_decomposer.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.o
.PHONY : catch2/internal/catch_decomposer.cpp.o

catch2/internal/catch_decomposer.i: catch2/internal/catch_decomposer.cpp.i
.PHONY : catch2/internal/catch_decomposer.i

# target to preprocess a source file
catch2/internal/catch_decomposer.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.i
.PHONY : catch2/internal/catch_decomposer.cpp.i

catch2/internal/catch_decomposer.s: catch2/internal/catch_decomposer.cpp.s
.PHONY : catch2/internal/catch_decomposer.s

# target to generate assembly for a file
catch2/internal/catch_decomposer.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_decomposer.cpp.s
.PHONY : catch2/internal/catch_decomposer.cpp.s

catch2/internal/catch_enforce.o: catch2/internal/catch_enforce.cpp.o
.PHONY : catch2/internal/catch_enforce.o

# target to build an object file
catch2/internal/catch_enforce.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.o
.PHONY : catch2/internal/catch_enforce.cpp.o

catch2/internal/catch_enforce.i: catch2/internal/catch_enforce.cpp.i
.PHONY : catch2/internal/catch_enforce.i

# target to preprocess a source file
catch2/internal/catch_enforce.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.i
.PHONY : catch2/internal/catch_enforce.cpp.i

catch2/internal/catch_enforce.s: catch2/internal/catch_enforce.cpp.s
.PHONY : catch2/internal/catch_enforce.s

# target to generate assembly for a file
catch2/internal/catch_enforce.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enforce.cpp.s
.PHONY : catch2/internal/catch_enforce.cpp.s

catch2/internal/catch_enum_values_registry.o: catch2/internal/catch_enum_values_registry.cpp.o
.PHONY : catch2/internal/catch_enum_values_registry.o

# target to build an object file
catch2/internal/catch_enum_values_registry.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.o
.PHONY : catch2/internal/catch_enum_values_registry.cpp.o

catch2/internal/catch_enum_values_registry.i: catch2/internal/catch_enum_values_registry.cpp.i
.PHONY : catch2/internal/catch_enum_values_registry.i

# target to preprocess a source file
catch2/internal/catch_enum_values_registry.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.i
.PHONY : catch2/internal/catch_enum_values_registry.cpp.i

catch2/internal/catch_enum_values_registry.s: catch2/internal/catch_enum_values_registry.cpp.s
.PHONY : catch2/internal/catch_enum_values_registry.s

# target to generate assembly for a file
catch2/internal/catch_enum_values_registry.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_enum_values_registry.cpp.s
.PHONY : catch2/internal/catch_enum_values_registry.cpp.s

catch2/internal/catch_errno_guard.o: catch2/internal/catch_errno_guard.cpp.o
.PHONY : catch2/internal/catch_errno_guard.o

# target to build an object file
catch2/internal/catch_errno_guard.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.o
.PHONY : catch2/internal/catch_errno_guard.cpp.o

catch2/internal/catch_errno_guard.i: catch2/internal/catch_errno_guard.cpp.i
.PHONY : catch2/internal/catch_errno_guard.i

# target to preprocess a source file
catch2/internal/catch_errno_guard.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.i
.PHONY : catch2/internal/catch_errno_guard.cpp.i

catch2/internal/catch_errno_guard.s: catch2/internal/catch_errno_guard.cpp.s
.PHONY : catch2/internal/catch_errno_guard.s

# target to generate assembly for a file
catch2/internal/catch_errno_guard.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_errno_guard.cpp.s
.PHONY : catch2/internal/catch_errno_guard.cpp.s

catch2/internal/catch_exception_translator_registry.o: catch2/internal/catch_exception_translator_registry.cpp.o
.PHONY : catch2/internal/catch_exception_translator_registry.o

# target to build an object file
catch2/internal/catch_exception_translator_registry.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.o
.PHONY : catch2/internal/catch_exception_translator_registry.cpp.o

catch2/internal/catch_exception_translator_registry.i: catch2/internal/catch_exception_translator_registry.cpp.i
.PHONY : catch2/internal/catch_exception_translator_registry.i

# target to preprocess a source file
catch2/internal/catch_exception_translator_registry.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.i
.PHONY : catch2/internal/catch_exception_translator_registry.cpp.i

catch2/internal/catch_exception_translator_registry.s: catch2/internal/catch_exception_translator_registry.cpp.s
.PHONY : catch2/internal/catch_exception_translator_registry.s

# target to generate assembly for a file
catch2/internal/catch_exception_translator_registry.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_exception_translator_registry.cpp.s
.PHONY : catch2/internal/catch_exception_translator_registry.cpp.s

catch2/internal/catch_fatal_condition_handler.o: catch2/internal/catch_fatal_condition_handler.cpp.o
.PHONY : catch2/internal/catch_fatal_condition_handler.o

# target to build an object file
catch2/internal/catch_fatal_condition_handler.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.o
.PHONY : catch2/internal/catch_fatal_condition_handler.cpp.o

catch2/internal/catch_fatal_condition_handler.i: catch2/internal/catch_fatal_condition_handler.cpp.i
.PHONY : catch2/internal/catch_fatal_condition_handler.i

# target to preprocess a source file
catch2/internal/catch_fatal_condition_handler.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.i
.PHONY : catch2/internal/catch_fatal_condition_handler.cpp.i

catch2/internal/catch_fatal_condition_handler.s: catch2/internal/catch_fatal_condition_handler.cpp.s
.PHONY : catch2/internal/catch_fatal_condition_handler.s

# target to generate assembly for a file
catch2/internal/catch_fatal_condition_handler.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_fatal_condition_handler.cpp.s
.PHONY : catch2/internal/catch_fatal_condition_handler.cpp.s

catch2/internal/catch_floating_point_helpers.o: catch2/internal/catch_floating_point_helpers.cpp.o
.PHONY : catch2/internal/catch_floating_point_helpers.o

# target to build an object file
catch2/internal/catch_floating_point_helpers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.o
.PHONY : catch2/internal/catch_floating_point_helpers.cpp.o

catch2/internal/catch_floating_point_helpers.i: catch2/internal/catch_floating_point_helpers.cpp.i
.PHONY : catch2/internal/catch_floating_point_helpers.i

# target to preprocess a source file
catch2/internal/catch_floating_point_helpers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.i
.PHONY : catch2/internal/catch_floating_point_helpers.cpp.i

catch2/internal/catch_floating_point_helpers.s: catch2/internal/catch_floating_point_helpers.cpp.s
.PHONY : catch2/internal/catch_floating_point_helpers.s

# target to generate assembly for a file
catch2/internal/catch_floating_point_helpers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_floating_point_helpers.cpp.s
.PHONY : catch2/internal/catch_floating_point_helpers.cpp.s

catch2/internal/catch_getenv.o: catch2/internal/catch_getenv.cpp.o
.PHONY : catch2/internal/catch_getenv.o

# target to build an object file
catch2/internal/catch_getenv.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.o
.PHONY : catch2/internal/catch_getenv.cpp.o

catch2/internal/catch_getenv.i: catch2/internal/catch_getenv.cpp.i
.PHONY : catch2/internal/catch_getenv.i

# target to preprocess a source file
catch2/internal/catch_getenv.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.i
.PHONY : catch2/internal/catch_getenv.cpp.i

catch2/internal/catch_getenv.s: catch2/internal/catch_getenv.cpp.s
.PHONY : catch2/internal/catch_getenv.s

# target to generate assembly for a file
catch2/internal/catch_getenv.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_getenv.cpp.s
.PHONY : catch2/internal/catch_getenv.cpp.s

catch2/internal/catch_istream.o: catch2/internal/catch_istream.cpp.o
.PHONY : catch2/internal/catch_istream.o

# target to build an object file
catch2/internal/catch_istream.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.o
.PHONY : catch2/internal/catch_istream.cpp.o

catch2/internal/catch_istream.i: catch2/internal/catch_istream.cpp.i
.PHONY : catch2/internal/catch_istream.i

# target to preprocess a source file
catch2/internal/catch_istream.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.i
.PHONY : catch2/internal/catch_istream.cpp.i

catch2/internal/catch_istream.s: catch2/internal/catch_istream.cpp.s
.PHONY : catch2/internal/catch_istream.s

# target to generate assembly for a file
catch2/internal/catch_istream.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_istream.cpp.s
.PHONY : catch2/internal/catch_istream.cpp.s

catch2/internal/catch_jsonwriter.o: catch2/internal/catch_jsonwriter.cpp.o
.PHONY : catch2/internal/catch_jsonwriter.o

# target to build an object file
catch2/internal/catch_jsonwriter.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.o
.PHONY : catch2/internal/catch_jsonwriter.cpp.o

catch2/internal/catch_jsonwriter.i: catch2/internal/catch_jsonwriter.cpp.i
.PHONY : catch2/internal/catch_jsonwriter.i

# target to preprocess a source file
catch2/internal/catch_jsonwriter.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.i
.PHONY : catch2/internal/catch_jsonwriter.cpp.i

catch2/internal/catch_jsonwriter.s: catch2/internal/catch_jsonwriter.cpp.s
.PHONY : catch2/internal/catch_jsonwriter.s

# target to generate assembly for a file
catch2/internal/catch_jsonwriter.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_jsonwriter.cpp.s
.PHONY : catch2/internal/catch_jsonwriter.cpp.s

catch2/internal/catch_lazy_expr.o: catch2/internal/catch_lazy_expr.cpp.o
.PHONY : catch2/internal/catch_lazy_expr.o

# target to build an object file
catch2/internal/catch_lazy_expr.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.o
.PHONY : catch2/internal/catch_lazy_expr.cpp.o

catch2/internal/catch_lazy_expr.i: catch2/internal/catch_lazy_expr.cpp.i
.PHONY : catch2/internal/catch_lazy_expr.i

# target to preprocess a source file
catch2/internal/catch_lazy_expr.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.i
.PHONY : catch2/internal/catch_lazy_expr.cpp.i

catch2/internal/catch_lazy_expr.s: catch2/internal/catch_lazy_expr.cpp.s
.PHONY : catch2/internal/catch_lazy_expr.s

# target to generate assembly for a file
catch2/internal/catch_lazy_expr.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_lazy_expr.cpp.s
.PHONY : catch2/internal/catch_lazy_expr.cpp.s

catch2/internal/catch_leak_detector.o: catch2/internal/catch_leak_detector.cpp.o
.PHONY : catch2/internal/catch_leak_detector.o

# target to build an object file
catch2/internal/catch_leak_detector.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.o
.PHONY : catch2/internal/catch_leak_detector.cpp.o

catch2/internal/catch_leak_detector.i: catch2/internal/catch_leak_detector.cpp.i
.PHONY : catch2/internal/catch_leak_detector.i

# target to preprocess a source file
catch2/internal/catch_leak_detector.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.i
.PHONY : catch2/internal/catch_leak_detector.cpp.i

catch2/internal/catch_leak_detector.s: catch2/internal/catch_leak_detector.cpp.s
.PHONY : catch2/internal/catch_leak_detector.s

# target to generate assembly for a file
catch2/internal/catch_leak_detector.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_leak_detector.cpp.s
.PHONY : catch2/internal/catch_leak_detector.cpp.s

catch2/internal/catch_list.o: catch2/internal/catch_list.cpp.o
.PHONY : catch2/internal/catch_list.o

# target to build an object file
catch2/internal/catch_list.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.o
.PHONY : catch2/internal/catch_list.cpp.o

catch2/internal/catch_list.i: catch2/internal/catch_list.cpp.i
.PHONY : catch2/internal/catch_list.i

# target to preprocess a source file
catch2/internal/catch_list.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.i
.PHONY : catch2/internal/catch_list.cpp.i

catch2/internal/catch_list.s: catch2/internal/catch_list.cpp.s
.PHONY : catch2/internal/catch_list.s

# target to generate assembly for a file
catch2/internal/catch_list.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_list.cpp.s
.PHONY : catch2/internal/catch_list.cpp.s

catch2/internal/catch_main.o: catch2/internal/catch_main.cpp.o
.PHONY : catch2/internal/catch_main.o

# target to build an object file
catch2/internal/catch_main.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.o
.PHONY : catch2/internal/catch_main.cpp.o

catch2/internal/catch_main.i: catch2/internal/catch_main.cpp.i
.PHONY : catch2/internal/catch_main.i

# target to preprocess a source file
catch2/internal/catch_main.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.i
.PHONY : catch2/internal/catch_main.cpp.i

catch2/internal/catch_main.s: catch2/internal/catch_main.cpp.s
.PHONY : catch2/internal/catch_main.s

# target to generate assembly for a file
catch2/internal/catch_main.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/catch2/internal/catch_main.cpp.s
.PHONY : catch2/internal/catch_main.cpp.s

catch2/internal/catch_message_info.o: catch2/internal/catch_message_info.cpp.o
.PHONY : catch2/internal/catch_message_info.o

# target to build an object file
catch2/internal/catch_message_info.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.o
.PHONY : catch2/internal/catch_message_info.cpp.o

catch2/internal/catch_message_info.i: catch2/internal/catch_message_info.cpp.i
.PHONY : catch2/internal/catch_message_info.i

# target to preprocess a source file
catch2/internal/catch_message_info.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.i
.PHONY : catch2/internal/catch_message_info.cpp.i

catch2/internal/catch_message_info.s: catch2/internal/catch_message_info.cpp.s
.PHONY : catch2/internal/catch_message_info.s

# target to generate assembly for a file
catch2/internal/catch_message_info.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_message_info.cpp.s
.PHONY : catch2/internal/catch_message_info.cpp.s

catch2/internal/catch_output_redirect.o: catch2/internal/catch_output_redirect.cpp.o
.PHONY : catch2/internal/catch_output_redirect.o

# target to build an object file
catch2/internal/catch_output_redirect.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.o
.PHONY : catch2/internal/catch_output_redirect.cpp.o

catch2/internal/catch_output_redirect.i: catch2/internal/catch_output_redirect.cpp.i
.PHONY : catch2/internal/catch_output_redirect.i

# target to preprocess a source file
catch2/internal/catch_output_redirect.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.i
.PHONY : catch2/internal/catch_output_redirect.cpp.i

catch2/internal/catch_output_redirect.s: catch2/internal/catch_output_redirect.cpp.s
.PHONY : catch2/internal/catch_output_redirect.s

# target to generate assembly for a file
catch2/internal/catch_output_redirect.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_output_redirect.cpp.s
.PHONY : catch2/internal/catch_output_redirect.cpp.s

catch2/internal/catch_parse_numbers.o: catch2/internal/catch_parse_numbers.cpp.o
.PHONY : catch2/internal/catch_parse_numbers.o

# target to build an object file
catch2/internal/catch_parse_numbers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.o
.PHONY : catch2/internal/catch_parse_numbers.cpp.o

catch2/internal/catch_parse_numbers.i: catch2/internal/catch_parse_numbers.cpp.i
.PHONY : catch2/internal/catch_parse_numbers.i

# target to preprocess a source file
catch2/internal/catch_parse_numbers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.i
.PHONY : catch2/internal/catch_parse_numbers.cpp.i

catch2/internal/catch_parse_numbers.s: catch2/internal/catch_parse_numbers.cpp.s
.PHONY : catch2/internal/catch_parse_numbers.s

# target to generate assembly for a file
catch2/internal/catch_parse_numbers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_parse_numbers.cpp.s
.PHONY : catch2/internal/catch_parse_numbers.cpp.s

catch2/internal/catch_polyfills.o: catch2/internal/catch_polyfills.cpp.o
.PHONY : catch2/internal/catch_polyfills.o

# target to build an object file
catch2/internal/catch_polyfills.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.o
.PHONY : catch2/internal/catch_polyfills.cpp.o

catch2/internal/catch_polyfills.i: catch2/internal/catch_polyfills.cpp.i
.PHONY : catch2/internal/catch_polyfills.i

# target to preprocess a source file
catch2/internal/catch_polyfills.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.i
.PHONY : catch2/internal/catch_polyfills.cpp.i

catch2/internal/catch_polyfills.s: catch2/internal/catch_polyfills.cpp.s
.PHONY : catch2/internal/catch_polyfills.s

# target to generate assembly for a file
catch2/internal/catch_polyfills.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_polyfills.cpp.s
.PHONY : catch2/internal/catch_polyfills.cpp.s

catch2/internal/catch_random_number_generator.o: catch2/internal/catch_random_number_generator.cpp.o
.PHONY : catch2/internal/catch_random_number_generator.o

# target to build an object file
catch2/internal/catch_random_number_generator.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.o
.PHONY : catch2/internal/catch_random_number_generator.cpp.o

catch2/internal/catch_random_number_generator.i: catch2/internal/catch_random_number_generator.cpp.i
.PHONY : catch2/internal/catch_random_number_generator.i

# target to preprocess a source file
catch2/internal/catch_random_number_generator.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.i
.PHONY : catch2/internal/catch_random_number_generator.cpp.i

catch2/internal/catch_random_number_generator.s: catch2/internal/catch_random_number_generator.cpp.s
.PHONY : catch2/internal/catch_random_number_generator.s

# target to generate assembly for a file
catch2/internal/catch_random_number_generator.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_number_generator.cpp.s
.PHONY : catch2/internal/catch_random_number_generator.cpp.s

catch2/internal/catch_random_seed_generation.o: catch2/internal/catch_random_seed_generation.cpp.o
.PHONY : catch2/internal/catch_random_seed_generation.o

# target to build an object file
catch2/internal/catch_random_seed_generation.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.o
.PHONY : catch2/internal/catch_random_seed_generation.cpp.o

catch2/internal/catch_random_seed_generation.i: catch2/internal/catch_random_seed_generation.cpp.i
.PHONY : catch2/internal/catch_random_seed_generation.i

# target to preprocess a source file
catch2/internal/catch_random_seed_generation.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.i
.PHONY : catch2/internal/catch_random_seed_generation.cpp.i

catch2/internal/catch_random_seed_generation.s: catch2/internal/catch_random_seed_generation.cpp.s
.PHONY : catch2/internal/catch_random_seed_generation.s

# target to generate assembly for a file
catch2/internal/catch_random_seed_generation.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_random_seed_generation.cpp.s
.PHONY : catch2/internal/catch_random_seed_generation.cpp.s

catch2/internal/catch_reporter_registry.o: catch2/internal/catch_reporter_registry.cpp.o
.PHONY : catch2/internal/catch_reporter_registry.o

# target to build an object file
catch2/internal/catch_reporter_registry.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.o
.PHONY : catch2/internal/catch_reporter_registry.cpp.o

catch2/internal/catch_reporter_registry.i: catch2/internal/catch_reporter_registry.cpp.i
.PHONY : catch2/internal/catch_reporter_registry.i

# target to preprocess a source file
catch2/internal/catch_reporter_registry.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.i
.PHONY : catch2/internal/catch_reporter_registry.cpp.i

catch2/internal/catch_reporter_registry.s: catch2/internal/catch_reporter_registry.cpp.s
.PHONY : catch2/internal/catch_reporter_registry.s

# target to generate assembly for a file
catch2/internal/catch_reporter_registry.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_registry.cpp.s
.PHONY : catch2/internal/catch_reporter_registry.cpp.s

catch2/internal/catch_reporter_spec_parser.o: catch2/internal/catch_reporter_spec_parser.cpp.o
.PHONY : catch2/internal/catch_reporter_spec_parser.o

# target to build an object file
catch2/internal/catch_reporter_spec_parser.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.o
.PHONY : catch2/internal/catch_reporter_spec_parser.cpp.o

catch2/internal/catch_reporter_spec_parser.i: catch2/internal/catch_reporter_spec_parser.cpp.i
.PHONY : catch2/internal/catch_reporter_spec_parser.i

# target to preprocess a source file
catch2/internal/catch_reporter_spec_parser.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.i
.PHONY : catch2/internal/catch_reporter_spec_parser.cpp.i

catch2/internal/catch_reporter_spec_parser.s: catch2/internal/catch_reporter_spec_parser.cpp.s
.PHONY : catch2/internal/catch_reporter_spec_parser.s

# target to generate assembly for a file
catch2/internal/catch_reporter_spec_parser.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reporter_spec_parser.cpp.s
.PHONY : catch2/internal/catch_reporter_spec_parser.cpp.s

catch2/internal/catch_reusable_string_stream.o: catch2/internal/catch_reusable_string_stream.cpp.o
.PHONY : catch2/internal/catch_reusable_string_stream.o

# target to build an object file
catch2/internal/catch_reusable_string_stream.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.o
.PHONY : catch2/internal/catch_reusable_string_stream.cpp.o

catch2/internal/catch_reusable_string_stream.i: catch2/internal/catch_reusable_string_stream.cpp.i
.PHONY : catch2/internal/catch_reusable_string_stream.i

# target to preprocess a source file
catch2/internal/catch_reusable_string_stream.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.i
.PHONY : catch2/internal/catch_reusable_string_stream.cpp.i

catch2/internal/catch_reusable_string_stream.s: catch2/internal/catch_reusable_string_stream.cpp.s
.PHONY : catch2/internal/catch_reusable_string_stream.s

# target to generate assembly for a file
catch2/internal/catch_reusable_string_stream.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_reusable_string_stream.cpp.s
.PHONY : catch2/internal/catch_reusable_string_stream.cpp.s

catch2/internal/catch_run_context.o: catch2/internal/catch_run_context.cpp.o
.PHONY : catch2/internal/catch_run_context.o

# target to build an object file
catch2/internal/catch_run_context.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.o
.PHONY : catch2/internal/catch_run_context.cpp.o

catch2/internal/catch_run_context.i: catch2/internal/catch_run_context.cpp.i
.PHONY : catch2/internal/catch_run_context.i

# target to preprocess a source file
catch2/internal/catch_run_context.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.i
.PHONY : catch2/internal/catch_run_context.cpp.i

catch2/internal/catch_run_context.s: catch2/internal/catch_run_context.cpp.s
.PHONY : catch2/internal/catch_run_context.s

# target to generate assembly for a file
catch2/internal/catch_run_context.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_run_context.cpp.s
.PHONY : catch2/internal/catch_run_context.cpp.s

catch2/internal/catch_section.o: catch2/internal/catch_section.cpp.o
.PHONY : catch2/internal/catch_section.o

# target to build an object file
catch2/internal/catch_section.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.o
.PHONY : catch2/internal/catch_section.cpp.o

catch2/internal/catch_section.i: catch2/internal/catch_section.cpp.i
.PHONY : catch2/internal/catch_section.i

# target to preprocess a source file
catch2/internal/catch_section.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.i
.PHONY : catch2/internal/catch_section.cpp.i

catch2/internal/catch_section.s: catch2/internal/catch_section.cpp.s
.PHONY : catch2/internal/catch_section.s

# target to generate assembly for a file
catch2/internal/catch_section.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_section.cpp.s
.PHONY : catch2/internal/catch_section.cpp.s

catch2/internal/catch_singletons.o: catch2/internal/catch_singletons.cpp.o
.PHONY : catch2/internal/catch_singletons.o

# target to build an object file
catch2/internal/catch_singletons.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.o
.PHONY : catch2/internal/catch_singletons.cpp.o

catch2/internal/catch_singletons.i: catch2/internal/catch_singletons.cpp.i
.PHONY : catch2/internal/catch_singletons.i

# target to preprocess a source file
catch2/internal/catch_singletons.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.i
.PHONY : catch2/internal/catch_singletons.cpp.i

catch2/internal/catch_singletons.s: catch2/internal/catch_singletons.cpp.s
.PHONY : catch2/internal/catch_singletons.s

# target to generate assembly for a file
catch2/internal/catch_singletons.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_singletons.cpp.s
.PHONY : catch2/internal/catch_singletons.cpp.s

catch2/internal/catch_source_line_info.o: catch2/internal/catch_source_line_info.cpp.o
.PHONY : catch2/internal/catch_source_line_info.o

# target to build an object file
catch2/internal/catch_source_line_info.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.o
.PHONY : catch2/internal/catch_source_line_info.cpp.o

catch2/internal/catch_source_line_info.i: catch2/internal/catch_source_line_info.cpp.i
.PHONY : catch2/internal/catch_source_line_info.i

# target to preprocess a source file
catch2/internal/catch_source_line_info.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.i
.PHONY : catch2/internal/catch_source_line_info.cpp.i

catch2/internal/catch_source_line_info.s: catch2/internal/catch_source_line_info.cpp.s
.PHONY : catch2/internal/catch_source_line_info.s

# target to generate assembly for a file
catch2/internal/catch_source_line_info.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_source_line_info.cpp.s
.PHONY : catch2/internal/catch_source_line_info.cpp.s

catch2/internal/catch_startup_exception_registry.o: catch2/internal/catch_startup_exception_registry.cpp.o
.PHONY : catch2/internal/catch_startup_exception_registry.o

# target to build an object file
catch2/internal/catch_startup_exception_registry.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.o
.PHONY : catch2/internal/catch_startup_exception_registry.cpp.o

catch2/internal/catch_startup_exception_registry.i: catch2/internal/catch_startup_exception_registry.cpp.i
.PHONY : catch2/internal/catch_startup_exception_registry.i

# target to preprocess a source file
catch2/internal/catch_startup_exception_registry.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.i
.PHONY : catch2/internal/catch_startup_exception_registry.cpp.i

catch2/internal/catch_startup_exception_registry.s: catch2/internal/catch_startup_exception_registry.cpp.s
.PHONY : catch2/internal/catch_startup_exception_registry.s

# target to generate assembly for a file
catch2/internal/catch_startup_exception_registry.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_startup_exception_registry.cpp.s
.PHONY : catch2/internal/catch_startup_exception_registry.cpp.s

catch2/internal/catch_stdstreams.o: catch2/internal/catch_stdstreams.cpp.o
.PHONY : catch2/internal/catch_stdstreams.o

# target to build an object file
catch2/internal/catch_stdstreams.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.o
.PHONY : catch2/internal/catch_stdstreams.cpp.o

catch2/internal/catch_stdstreams.i: catch2/internal/catch_stdstreams.cpp.i
.PHONY : catch2/internal/catch_stdstreams.i

# target to preprocess a source file
catch2/internal/catch_stdstreams.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.i
.PHONY : catch2/internal/catch_stdstreams.cpp.i

catch2/internal/catch_stdstreams.s: catch2/internal/catch_stdstreams.cpp.s
.PHONY : catch2/internal/catch_stdstreams.s

# target to generate assembly for a file
catch2/internal/catch_stdstreams.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stdstreams.cpp.s
.PHONY : catch2/internal/catch_stdstreams.cpp.s

catch2/internal/catch_string_manip.o: catch2/internal/catch_string_manip.cpp.o
.PHONY : catch2/internal/catch_string_manip.o

# target to build an object file
catch2/internal/catch_string_manip.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.o
.PHONY : catch2/internal/catch_string_manip.cpp.o

catch2/internal/catch_string_manip.i: catch2/internal/catch_string_manip.cpp.i
.PHONY : catch2/internal/catch_string_manip.i

# target to preprocess a source file
catch2/internal/catch_string_manip.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.i
.PHONY : catch2/internal/catch_string_manip.cpp.i

catch2/internal/catch_string_manip.s: catch2/internal/catch_string_manip.cpp.s
.PHONY : catch2/internal/catch_string_manip.s

# target to generate assembly for a file
catch2/internal/catch_string_manip.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_string_manip.cpp.s
.PHONY : catch2/internal/catch_string_manip.cpp.s

catch2/internal/catch_stringref.o: catch2/internal/catch_stringref.cpp.o
.PHONY : catch2/internal/catch_stringref.o

# target to build an object file
catch2/internal/catch_stringref.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.o
.PHONY : catch2/internal/catch_stringref.cpp.o

catch2/internal/catch_stringref.i: catch2/internal/catch_stringref.cpp.i
.PHONY : catch2/internal/catch_stringref.i

# target to preprocess a source file
catch2/internal/catch_stringref.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.i
.PHONY : catch2/internal/catch_stringref.cpp.i

catch2/internal/catch_stringref.s: catch2/internal/catch_stringref.cpp.s
.PHONY : catch2/internal/catch_stringref.s

# target to generate assembly for a file
catch2/internal/catch_stringref.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_stringref.cpp.s
.PHONY : catch2/internal/catch_stringref.cpp.s

catch2/internal/catch_tag_alias_registry.o: catch2/internal/catch_tag_alias_registry.cpp.o
.PHONY : catch2/internal/catch_tag_alias_registry.o

# target to build an object file
catch2/internal/catch_tag_alias_registry.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.o
.PHONY : catch2/internal/catch_tag_alias_registry.cpp.o

catch2/internal/catch_tag_alias_registry.i: catch2/internal/catch_tag_alias_registry.cpp.i
.PHONY : catch2/internal/catch_tag_alias_registry.i

# target to preprocess a source file
catch2/internal/catch_tag_alias_registry.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.i
.PHONY : catch2/internal/catch_tag_alias_registry.cpp.i

catch2/internal/catch_tag_alias_registry.s: catch2/internal/catch_tag_alias_registry.cpp.s
.PHONY : catch2/internal/catch_tag_alias_registry.s

# target to generate assembly for a file
catch2/internal/catch_tag_alias_registry.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_tag_alias_registry.cpp.s
.PHONY : catch2/internal/catch_tag_alias_registry.cpp.s

catch2/internal/catch_test_case_info_hasher.o: catch2/internal/catch_test_case_info_hasher.cpp.o
.PHONY : catch2/internal/catch_test_case_info_hasher.o

# target to build an object file
catch2/internal/catch_test_case_info_hasher.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.o
.PHONY : catch2/internal/catch_test_case_info_hasher.cpp.o

catch2/internal/catch_test_case_info_hasher.i: catch2/internal/catch_test_case_info_hasher.cpp.i
.PHONY : catch2/internal/catch_test_case_info_hasher.i

# target to preprocess a source file
catch2/internal/catch_test_case_info_hasher.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.i
.PHONY : catch2/internal/catch_test_case_info_hasher.cpp.i

catch2/internal/catch_test_case_info_hasher.s: catch2/internal/catch_test_case_info_hasher.cpp.s
.PHONY : catch2/internal/catch_test_case_info_hasher.s

# target to generate assembly for a file
catch2/internal/catch_test_case_info_hasher.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_info_hasher.cpp.s
.PHONY : catch2/internal/catch_test_case_info_hasher.cpp.s

catch2/internal/catch_test_case_registry_impl.o: catch2/internal/catch_test_case_registry_impl.cpp.o
.PHONY : catch2/internal/catch_test_case_registry_impl.o

# target to build an object file
catch2/internal/catch_test_case_registry_impl.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.o
.PHONY : catch2/internal/catch_test_case_registry_impl.cpp.o

catch2/internal/catch_test_case_registry_impl.i: catch2/internal/catch_test_case_registry_impl.cpp.i
.PHONY : catch2/internal/catch_test_case_registry_impl.i

# target to preprocess a source file
catch2/internal/catch_test_case_registry_impl.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.i
.PHONY : catch2/internal/catch_test_case_registry_impl.cpp.i

catch2/internal/catch_test_case_registry_impl.s: catch2/internal/catch_test_case_registry_impl.cpp.s
.PHONY : catch2/internal/catch_test_case_registry_impl.s

# target to generate assembly for a file
catch2/internal/catch_test_case_registry_impl.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_registry_impl.cpp.s
.PHONY : catch2/internal/catch_test_case_registry_impl.cpp.s

catch2/internal/catch_test_case_tracker.o: catch2/internal/catch_test_case_tracker.cpp.o
.PHONY : catch2/internal/catch_test_case_tracker.o

# target to build an object file
catch2/internal/catch_test_case_tracker.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.o
.PHONY : catch2/internal/catch_test_case_tracker.cpp.o

catch2/internal/catch_test_case_tracker.i: catch2/internal/catch_test_case_tracker.cpp.i
.PHONY : catch2/internal/catch_test_case_tracker.i

# target to preprocess a source file
catch2/internal/catch_test_case_tracker.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.i
.PHONY : catch2/internal/catch_test_case_tracker.cpp.i

catch2/internal/catch_test_case_tracker.s: catch2/internal/catch_test_case_tracker.cpp.s
.PHONY : catch2/internal/catch_test_case_tracker.s

# target to generate assembly for a file
catch2/internal/catch_test_case_tracker.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_case_tracker.cpp.s
.PHONY : catch2/internal/catch_test_case_tracker.cpp.s

catch2/internal/catch_test_failure_exception.o: catch2/internal/catch_test_failure_exception.cpp.o
.PHONY : catch2/internal/catch_test_failure_exception.o

# target to build an object file
catch2/internal/catch_test_failure_exception.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.o
.PHONY : catch2/internal/catch_test_failure_exception.cpp.o

catch2/internal/catch_test_failure_exception.i: catch2/internal/catch_test_failure_exception.cpp.i
.PHONY : catch2/internal/catch_test_failure_exception.i

# target to preprocess a source file
catch2/internal/catch_test_failure_exception.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.i
.PHONY : catch2/internal/catch_test_failure_exception.cpp.i

catch2/internal/catch_test_failure_exception.s: catch2/internal/catch_test_failure_exception.cpp.s
.PHONY : catch2/internal/catch_test_failure_exception.s

# target to generate assembly for a file
catch2/internal/catch_test_failure_exception.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_failure_exception.cpp.s
.PHONY : catch2/internal/catch_test_failure_exception.cpp.s

catch2/internal/catch_test_registry.o: catch2/internal/catch_test_registry.cpp.o
.PHONY : catch2/internal/catch_test_registry.o

# target to build an object file
catch2/internal/catch_test_registry.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.o
.PHONY : catch2/internal/catch_test_registry.cpp.o

catch2/internal/catch_test_registry.i: catch2/internal/catch_test_registry.cpp.i
.PHONY : catch2/internal/catch_test_registry.i

# target to preprocess a source file
catch2/internal/catch_test_registry.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.i
.PHONY : catch2/internal/catch_test_registry.cpp.i

catch2/internal/catch_test_registry.s: catch2/internal/catch_test_registry.cpp.s
.PHONY : catch2/internal/catch_test_registry.s

# target to generate assembly for a file
catch2/internal/catch_test_registry.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_registry.cpp.s
.PHONY : catch2/internal/catch_test_registry.cpp.s

catch2/internal/catch_test_spec_parser.o: catch2/internal/catch_test_spec_parser.cpp.o
.PHONY : catch2/internal/catch_test_spec_parser.o

# target to build an object file
catch2/internal/catch_test_spec_parser.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.o
.PHONY : catch2/internal/catch_test_spec_parser.cpp.o

catch2/internal/catch_test_spec_parser.i: catch2/internal/catch_test_spec_parser.cpp.i
.PHONY : catch2/internal/catch_test_spec_parser.i

# target to preprocess a source file
catch2/internal/catch_test_spec_parser.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.i
.PHONY : catch2/internal/catch_test_spec_parser.cpp.i

catch2/internal/catch_test_spec_parser.s: catch2/internal/catch_test_spec_parser.cpp.s
.PHONY : catch2/internal/catch_test_spec_parser.s

# target to generate assembly for a file
catch2/internal/catch_test_spec_parser.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_test_spec_parser.cpp.s
.PHONY : catch2/internal/catch_test_spec_parser.cpp.s

catch2/internal/catch_textflow.o: catch2/internal/catch_textflow.cpp.o
.PHONY : catch2/internal/catch_textflow.o

# target to build an object file
catch2/internal/catch_textflow.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.o
.PHONY : catch2/internal/catch_textflow.cpp.o

catch2/internal/catch_textflow.i: catch2/internal/catch_textflow.cpp.i
.PHONY : catch2/internal/catch_textflow.i

# target to preprocess a source file
catch2/internal/catch_textflow.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.i
.PHONY : catch2/internal/catch_textflow.cpp.i

catch2/internal/catch_textflow.s: catch2/internal/catch_textflow.cpp.s
.PHONY : catch2/internal/catch_textflow.s

# target to generate assembly for a file
catch2/internal/catch_textflow.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_textflow.cpp.s
.PHONY : catch2/internal/catch_textflow.cpp.s

catch2/internal/catch_uncaught_exceptions.o: catch2/internal/catch_uncaught_exceptions.cpp.o
.PHONY : catch2/internal/catch_uncaught_exceptions.o

# target to build an object file
catch2/internal/catch_uncaught_exceptions.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.o
.PHONY : catch2/internal/catch_uncaught_exceptions.cpp.o

catch2/internal/catch_uncaught_exceptions.i: catch2/internal/catch_uncaught_exceptions.cpp.i
.PHONY : catch2/internal/catch_uncaught_exceptions.i

# target to preprocess a source file
catch2/internal/catch_uncaught_exceptions.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.i
.PHONY : catch2/internal/catch_uncaught_exceptions.cpp.i

catch2/internal/catch_uncaught_exceptions.s: catch2/internal/catch_uncaught_exceptions.cpp.s
.PHONY : catch2/internal/catch_uncaught_exceptions.s

# target to generate assembly for a file
catch2/internal/catch_uncaught_exceptions.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_uncaught_exceptions.cpp.s
.PHONY : catch2/internal/catch_uncaught_exceptions.cpp.s

catch2/internal/catch_wildcard_pattern.o: catch2/internal/catch_wildcard_pattern.cpp.o
.PHONY : catch2/internal/catch_wildcard_pattern.o

# target to build an object file
catch2/internal/catch_wildcard_pattern.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.o
.PHONY : catch2/internal/catch_wildcard_pattern.cpp.o

catch2/internal/catch_wildcard_pattern.i: catch2/internal/catch_wildcard_pattern.cpp.i
.PHONY : catch2/internal/catch_wildcard_pattern.i

# target to preprocess a source file
catch2/internal/catch_wildcard_pattern.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.i
.PHONY : catch2/internal/catch_wildcard_pattern.cpp.i

catch2/internal/catch_wildcard_pattern.s: catch2/internal/catch_wildcard_pattern.cpp.s
.PHONY : catch2/internal/catch_wildcard_pattern.s

# target to generate assembly for a file
catch2/internal/catch_wildcard_pattern.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_wildcard_pattern.cpp.s
.PHONY : catch2/internal/catch_wildcard_pattern.cpp.s

catch2/internal/catch_xmlwriter.o: catch2/internal/catch_xmlwriter.cpp.o
.PHONY : catch2/internal/catch_xmlwriter.o

# target to build an object file
catch2/internal/catch_xmlwriter.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.o
.PHONY : catch2/internal/catch_xmlwriter.cpp.o

catch2/internal/catch_xmlwriter.i: catch2/internal/catch_xmlwriter.cpp.i
.PHONY : catch2/internal/catch_xmlwriter.i

# target to preprocess a source file
catch2/internal/catch_xmlwriter.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.i
.PHONY : catch2/internal/catch_xmlwriter.cpp.i

catch2/internal/catch_xmlwriter.s: catch2/internal/catch_xmlwriter.cpp.s
.PHONY : catch2/internal/catch_xmlwriter.s

# target to generate assembly for a file
catch2/internal/catch_xmlwriter.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/internal/catch_xmlwriter.cpp.s
.PHONY : catch2/internal/catch_xmlwriter.cpp.s

catch2/matchers/catch_matchers.o: catch2/matchers/catch_matchers.cpp.o
.PHONY : catch2/matchers/catch_matchers.o

# target to build an object file
catch2/matchers/catch_matchers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.o
.PHONY : catch2/matchers/catch_matchers.cpp.o

catch2/matchers/catch_matchers.i: catch2/matchers/catch_matchers.cpp.i
.PHONY : catch2/matchers/catch_matchers.i

# target to preprocess a source file
catch2/matchers/catch_matchers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.i
.PHONY : catch2/matchers/catch_matchers.cpp.i

catch2/matchers/catch_matchers.s: catch2/matchers/catch_matchers.cpp.s
.PHONY : catch2/matchers/catch_matchers.s

# target to generate assembly for a file
catch2/matchers/catch_matchers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers.cpp.s
.PHONY : catch2/matchers/catch_matchers.cpp.s

catch2/matchers/catch_matchers_container_properties.o: catch2/matchers/catch_matchers_container_properties.cpp.o
.PHONY : catch2/matchers/catch_matchers_container_properties.o

# target to build an object file
catch2/matchers/catch_matchers_container_properties.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.o
.PHONY : catch2/matchers/catch_matchers_container_properties.cpp.o

catch2/matchers/catch_matchers_container_properties.i: catch2/matchers/catch_matchers_container_properties.cpp.i
.PHONY : catch2/matchers/catch_matchers_container_properties.i

# target to preprocess a source file
catch2/matchers/catch_matchers_container_properties.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.i
.PHONY : catch2/matchers/catch_matchers_container_properties.cpp.i

catch2/matchers/catch_matchers_container_properties.s: catch2/matchers/catch_matchers_container_properties.cpp.s
.PHONY : catch2/matchers/catch_matchers_container_properties.s

# target to generate assembly for a file
catch2/matchers/catch_matchers_container_properties.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_container_properties.cpp.s
.PHONY : catch2/matchers/catch_matchers_container_properties.cpp.s

catch2/matchers/catch_matchers_exception.o: catch2/matchers/catch_matchers_exception.cpp.o
.PHONY : catch2/matchers/catch_matchers_exception.o

# target to build an object file
catch2/matchers/catch_matchers_exception.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.o
.PHONY : catch2/matchers/catch_matchers_exception.cpp.o

catch2/matchers/catch_matchers_exception.i: catch2/matchers/catch_matchers_exception.cpp.i
.PHONY : catch2/matchers/catch_matchers_exception.i

# target to preprocess a source file
catch2/matchers/catch_matchers_exception.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.i
.PHONY : catch2/matchers/catch_matchers_exception.cpp.i

catch2/matchers/catch_matchers_exception.s: catch2/matchers/catch_matchers_exception.cpp.s
.PHONY : catch2/matchers/catch_matchers_exception.s

# target to generate assembly for a file
catch2/matchers/catch_matchers_exception.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_exception.cpp.s
.PHONY : catch2/matchers/catch_matchers_exception.cpp.s

catch2/matchers/catch_matchers_floating_point.o: catch2/matchers/catch_matchers_floating_point.cpp.o
.PHONY : catch2/matchers/catch_matchers_floating_point.o

# target to build an object file
catch2/matchers/catch_matchers_floating_point.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.o
.PHONY : catch2/matchers/catch_matchers_floating_point.cpp.o

catch2/matchers/catch_matchers_floating_point.i: catch2/matchers/catch_matchers_floating_point.cpp.i
.PHONY : catch2/matchers/catch_matchers_floating_point.i

# target to preprocess a source file
catch2/matchers/catch_matchers_floating_point.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.i
.PHONY : catch2/matchers/catch_matchers_floating_point.cpp.i

catch2/matchers/catch_matchers_floating_point.s: catch2/matchers/catch_matchers_floating_point.cpp.s
.PHONY : catch2/matchers/catch_matchers_floating_point.s

# target to generate assembly for a file
catch2/matchers/catch_matchers_floating_point.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_floating_point.cpp.s
.PHONY : catch2/matchers/catch_matchers_floating_point.cpp.s

catch2/matchers/catch_matchers_predicate.o: catch2/matchers/catch_matchers_predicate.cpp.o
.PHONY : catch2/matchers/catch_matchers_predicate.o

# target to build an object file
catch2/matchers/catch_matchers_predicate.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.o
.PHONY : catch2/matchers/catch_matchers_predicate.cpp.o

catch2/matchers/catch_matchers_predicate.i: catch2/matchers/catch_matchers_predicate.cpp.i
.PHONY : catch2/matchers/catch_matchers_predicate.i

# target to preprocess a source file
catch2/matchers/catch_matchers_predicate.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.i
.PHONY : catch2/matchers/catch_matchers_predicate.cpp.i

catch2/matchers/catch_matchers_predicate.s: catch2/matchers/catch_matchers_predicate.cpp.s
.PHONY : catch2/matchers/catch_matchers_predicate.s

# target to generate assembly for a file
catch2/matchers/catch_matchers_predicate.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_predicate.cpp.s
.PHONY : catch2/matchers/catch_matchers_predicate.cpp.s

catch2/matchers/catch_matchers_quantifiers.o: catch2/matchers/catch_matchers_quantifiers.cpp.o
.PHONY : catch2/matchers/catch_matchers_quantifiers.o

# target to build an object file
catch2/matchers/catch_matchers_quantifiers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.o
.PHONY : catch2/matchers/catch_matchers_quantifiers.cpp.o

catch2/matchers/catch_matchers_quantifiers.i: catch2/matchers/catch_matchers_quantifiers.cpp.i
.PHONY : catch2/matchers/catch_matchers_quantifiers.i

# target to preprocess a source file
catch2/matchers/catch_matchers_quantifiers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.i
.PHONY : catch2/matchers/catch_matchers_quantifiers.cpp.i

catch2/matchers/catch_matchers_quantifiers.s: catch2/matchers/catch_matchers_quantifiers.cpp.s
.PHONY : catch2/matchers/catch_matchers_quantifiers.s

# target to generate assembly for a file
catch2/matchers/catch_matchers_quantifiers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_quantifiers.cpp.s
.PHONY : catch2/matchers/catch_matchers_quantifiers.cpp.s

catch2/matchers/catch_matchers_string.o: catch2/matchers/catch_matchers_string.cpp.o
.PHONY : catch2/matchers/catch_matchers_string.o

# target to build an object file
catch2/matchers/catch_matchers_string.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.o
.PHONY : catch2/matchers/catch_matchers_string.cpp.o

catch2/matchers/catch_matchers_string.i: catch2/matchers/catch_matchers_string.cpp.i
.PHONY : catch2/matchers/catch_matchers_string.i

# target to preprocess a source file
catch2/matchers/catch_matchers_string.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.i
.PHONY : catch2/matchers/catch_matchers_string.cpp.i

catch2/matchers/catch_matchers_string.s: catch2/matchers/catch_matchers_string.cpp.s
.PHONY : catch2/matchers/catch_matchers_string.s

# target to generate assembly for a file
catch2/matchers/catch_matchers_string.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_string.cpp.s
.PHONY : catch2/matchers/catch_matchers_string.cpp.s

catch2/matchers/catch_matchers_templated.o: catch2/matchers/catch_matchers_templated.cpp.o
.PHONY : catch2/matchers/catch_matchers_templated.o

# target to build an object file
catch2/matchers/catch_matchers_templated.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.o
.PHONY : catch2/matchers/catch_matchers_templated.cpp.o

catch2/matchers/catch_matchers_templated.i: catch2/matchers/catch_matchers_templated.cpp.i
.PHONY : catch2/matchers/catch_matchers_templated.i

# target to preprocess a source file
catch2/matchers/catch_matchers_templated.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.i
.PHONY : catch2/matchers/catch_matchers_templated.cpp.i

catch2/matchers/catch_matchers_templated.s: catch2/matchers/catch_matchers_templated.cpp.s
.PHONY : catch2/matchers/catch_matchers_templated.s

# target to generate assembly for a file
catch2/matchers/catch_matchers_templated.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/catch_matchers_templated.cpp.s
.PHONY : catch2/matchers/catch_matchers_templated.cpp.s

catch2/matchers/internal/catch_matchers_impl.o: catch2/matchers/internal/catch_matchers_impl.cpp.o
.PHONY : catch2/matchers/internal/catch_matchers_impl.o

# target to build an object file
catch2/matchers/internal/catch_matchers_impl.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.o
.PHONY : catch2/matchers/internal/catch_matchers_impl.cpp.o

catch2/matchers/internal/catch_matchers_impl.i: catch2/matchers/internal/catch_matchers_impl.cpp.i
.PHONY : catch2/matchers/internal/catch_matchers_impl.i

# target to preprocess a source file
catch2/matchers/internal/catch_matchers_impl.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.i
.PHONY : catch2/matchers/internal/catch_matchers_impl.cpp.i

catch2/matchers/internal/catch_matchers_impl.s: catch2/matchers/internal/catch_matchers_impl.cpp.s
.PHONY : catch2/matchers/internal/catch_matchers_impl.s

# target to generate assembly for a file
catch2/matchers/internal/catch_matchers_impl.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/matchers/internal/catch_matchers_impl.cpp.s
.PHONY : catch2/matchers/internal/catch_matchers_impl.cpp.s

catch2/reporters/catch_reporter_automake.o: catch2/reporters/catch_reporter_automake.cpp.o
.PHONY : catch2/reporters/catch_reporter_automake.o

# target to build an object file
catch2/reporters/catch_reporter_automake.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.o
.PHONY : catch2/reporters/catch_reporter_automake.cpp.o

catch2/reporters/catch_reporter_automake.i: catch2/reporters/catch_reporter_automake.cpp.i
.PHONY : catch2/reporters/catch_reporter_automake.i

# target to preprocess a source file
catch2/reporters/catch_reporter_automake.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.i
.PHONY : catch2/reporters/catch_reporter_automake.cpp.i

catch2/reporters/catch_reporter_automake.s: catch2/reporters/catch_reporter_automake.cpp.s
.PHONY : catch2/reporters/catch_reporter_automake.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_automake.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_automake.cpp.s
.PHONY : catch2/reporters/catch_reporter_automake.cpp.s

catch2/reporters/catch_reporter_common_base.o: catch2/reporters/catch_reporter_common_base.cpp.o
.PHONY : catch2/reporters/catch_reporter_common_base.o

# target to build an object file
catch2/reporters/catch_reporter_common_base.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.o
.PHONY : catch2/reporters/catch_reporter_common_base.cpp.o

catch2/reporters/catch_reporter_common_base.i: catch2/reporters/catch_reporter_common_base.cpp.i
.PHONY : catch2/reporters/catch_reporter_common_base.i

# target to preprocess a source file
catch2/reporters/catch_reporter_common_base.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.i
.PHONY : catch2/reporters/catch_reporter_common_base.cpp.i

catch2/reporters/catch_reporter_common_base.s: catch2/reporters/catch_reporter_common_base.cpp.s
.PHONY : catch2/reporters/catch_reporter_common_base.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_common_base.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_common_base.cpp.s
.PHONY : catch2/reporters/catch_reporter_common_base.cpp.s

catch2/reporters/catch_reporter_compact.o: catch2/reporters/catch_reporter_compact.cpp.o
.PHONY : catch2/reporters/catch_reporter_compact.o

# target to build an object file
catch2/reporters/catch_reporter_compact.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.o
.PHONY : catch2/reporters/catch_reporter_compact.cpp.o

catch2/reporters/catch_reporter_compact.i: catch2/reporters/catch_reporter_compact.cpp.i
.PHONY : catch2/reporters/catch_reporter_compact.i

# target to preprocess a source file
catch2/reporters/catch_reporter_compact.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.i
.PHONY : catch2/reporters/catch_reporter_compact.cpp.i

catch2/reporters/catch_reporter_compact.s: catch2/reporters/catch_reporter_compact.cpp.s
.PHONY : catch2/reporters/catch_reporter_compact.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_compact.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_compact.cpp.s
.PHONY : catch2/reporters/catch_reporter_compact.cpp.s

catch2/reporters/catch_reporter_console.o: catch2/reporters/catch_reporter_console.cpp.o
.PHONY : catch2/reporters/catch_reporter_console.o

# target to build an object file
catch2/reporters/catch_reporter_console.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.o
.PHONY : catch2/reporters/catch_reporter_console.cpp.o

catch2/reporters/catch_reporter_console.i: catch2/reporters/catch_reporter_console.cpp.i
.PHONY : catch2/reporters/catch_reporter_console.i

# target to preprocess a source file
catch2/reporters/catch_reporter_console.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.i
.PHONY : catch2/reporters/catch_reporter_console.cpp.i

catch2/reporters/catch_reporter_console.s: catch2/reporters/catch_reporter_console.cpp.s
.PHONY : catch2/reporters/catch_reporter_console.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_console.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_console.cpp.s
.PHONY : catch2/reporters/catch_reporter_console.cpp.s

catch2/reporters/catch_reporter_cumulative_base.o: catch2/reporters/catch_reporter_cumulative_base.cpp.o
.PHONY : catch2/reporters/catch_reporter_cumulative_base.o

# target to build an object file
catch2/reporters/catch_reporter_cumulative_base.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.o
.PHONY : catch2/reporters/catch_reporter_cumulative_base.cpp.o

catch2/reporters/catch_reporter_cumulative_base.i: catch2/reporters/catch_reporter_cumulative_base.cpp.i
.PHONY : catch2/reporters/catch_reporter_cumulative_base.i

# target to preprocess a source file
catch2/reporters/catch_reporter_cumulative_base.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.i
.PHONY : catch2/reporters/catch_reporter_cumulative_base.cpp.i

catch2/reporters/catch_reporter_cumulative_base.s: catch2/reporters/catch_reporter_cumulative_base.cpp.s
.PHONY : catch2/reporters/catch_reporter_cumulative_base.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_cumulative_base.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_cumulative_base.cpp.s
.PHONY : catch2/reporters/catch_reporter_cumulative_base.cpp.s

catch2/reporters/catch_reporter_event_listener.o: catch2/reporters/catch_reporter_event_listener.cpp.o
.PHONY : catch2/reporters/catch_reporter_event_listener.o

# target to build an object file
catch2/reporters/catch_reporter_event_listener.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.o
.PHONY : catch2/reporters/catch_reporter_event_listener.cpp.o

catch2/reporters/catch_reporter_event_listener.i: catch2/reporters/catch_reporter_event_listener.cpp.i
.PHONY : catch2/reporters/catch_reporter_event_listener.i

# target to preprocess a source file
catch2/reporters/catch_reporter_event_listener.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.i
.PHONY : catch2/reporters/catch_reporter_event_listener.cpp.i

catch2/reporters/catch_reporter_event_listener.s: catch2/reporters/catch_reporter_event_listener.cpp.s
.PHONY : catch2/reporters/catch_reporter_event_listener.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_event_listener.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_event_listener.cpp.s
.PHONY : catch2/reporters/catch_reporter_event_listener.cpp.s

catch2/reporters/catch_reporter_helpers.o: catch2/reporters/catch_reporter_helpers.cpp.o
.PHONY : catch2/reporters/catch_reporter_helpers.o

# target to build an object file
catch2/reporters/catch_reporter_helpers.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.o
.PHONY : catch2/reporters/catch_reporter_helpers.cpp.o

catch2/reporters/catch_reporter_helpers.i: catch2/reporters/catch_reporter_helpers.cpp.i
.PHONY : catch2/reporters/catch_reporter_helpers.i

# target to preprocess a source file
catch2/reporters/catch_reporter_helpers.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.i
.PHONY : catch2/reporters/catch_reporter_helpers.cpp.i

catch2/reporters/catch_reporter_helpers.s: catch2/reporters/catch_reporter_helpers.cpp.s
.PHONY : catch2/reporters/catch_reporter_helpers.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_helpers.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_helpers.cpp.s
.PHONY : catch2/reporters/catch_reporter_helpers.cpp.s

catch2/reporters/catch_reporter_json.o: catch2/reporters/catch_reporter_json.cpp.o
.PHONY : catch2/reporters/catch_reporter_json.o

# target to build an object file
catch2/reporters/catch_reporter_json.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.o
.PHONY : catch2/reporters/catch_reporter_json.cpp.o

catch2/reporters/catch_reporter_json.i: catch2/reporters/catch_reporter_json.cpp.i
.PHONY : catch2/reporters/catch_reporter_json.i

# target to preprocess a source file
catch2/reporters/catch_reporter_json.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.i
.PHONY : catch2/reporters/catch_reporter_json.cpp.i

catch2/reporters/catch_reporter_json.s: catch2/reporters/catch_reporter_json.cpp.s
.PHONY : catch2/reporters/catch_reporter_json.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_json.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_json.cpp.s
.PHONY : catch2/reporters/catch_reporter_json.cpp.s

catch2/reporters/catch_reporter_junit.o: catch2/reporters/catch_reporter_junit.cpp.o
.PHONY : catch2/reporters/catch_reporter_junit.o

# target to build an object file
catch2/reporters/catch_reporter_junit.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.o
.PHONY : catch2/reporters/catch_reporter_junit.cpp.o

catch2/reporters/catch_reporter_junit.i: catch2/reporters/catch_reporter_junit.cpp.i
.PHONY : catch2/reporters/catch_reporter_junit.i

# target to preprocess a source file
catch2/reporters/catch_reporter_junit.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.i
.PHONY : catch2/reporters/catch_reporter_junit.cpp.i

catch2/reporters/catch_reporter_junit.s: catch2/reporters/catch_reporter_junit.cpp.s
.PHONY : catch2/reporters/catch_reporter_junit.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_junit.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_junit.cpp.s
.PHONY : catch2/reporters/catch_reporter_junit.cpp.s

catch2/reporters/catch_reporter_multi.o: catch2/reporters/catch_reporter_multi.cpp.o
.PHONY : catch2/reporters/catch_reporter_multi.o

# target to build an object file
catch2/reporters/catch_reporter_multi.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.o
.PHONY : catch2/reporters/catch_reporter_multi.cpp.o

catch2/reporters/catch_reporter_multi.i: catch2/reporters/catch_reporter_multi.cpp.i
.PHONY : catch2/reporters/catch_reporter_multi.i

# target to preprocess a source file
catch2/reporters/catch_reporter_multi.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.i
.PHONY : catch2/reporters/catch_reporter_multi.cpp.i

catch2/reporters/catch_reporter_multi.s: catch2/reporters/catch_reporter_multi.cpp.s
.PHONY : catch2/reporters/catch_reporter_multi.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_multi.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_multi.cpp.s
.PHONY : catch2/reporters/catch_reporter_multi.cpp.s

catch2/reporters/catch_reporter_registrars.o: catch2/reporters/catch_reporter_registrars.cpp.o
.PHONY : catch2/reporters/catch_reporter_registrars.o

# target to build an object file
catch2/reporters/catch_reporter_registrars.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.o
.PHONY : catch2/reporters/catch_reporter_registrars.cpp.o

catch2/reporters/catch_reporter_registrars.i: catch2/reporters/catch_reporter_registrars.cpp.i
.PHONY : catch2/reporters/catch_reporter_registrars.i

# target to preprocess a source file
catch2/reporters/catch_reporter_registrars.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.i
.PHONY : catch2/reporters/catch_reporter_registrars.cpp.i

catch2/reporters/catch_reporter_registrars.s: catch2/reporters/catch_reporter_registrars.cpp.s
.PHONY : catch2/reporters/catch_reporter_registrars.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_registrars.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_registrars.cpp.s
.PHONY : catch2/reporters/catch_reporter_registrars.cpp.s

catch2/reporters/catch_reporter_sonarqube.o: catch2/reporters/catch_reporter_sonarqube.cpp.o
.PHONY : catch2/reporters/catch_reporter_sonarqube.o

# target to build an object file
catch2/reporters/catch_reporter_sonarqube.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.o
.PHONY : catch2/reporters/catch_reporter_sonarqube.cpp.o

catch2/reporters/catch_reporter_sonarqube.i: catch2/reporters/catch_reporter_sonarqube.cpp.i
.PHONY : catch2/reporters/catch_reporter_sonarqube.i

# target to preprocess a source file
catch2/reporters/catch_reporter_sonarqube.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.i
.PHONY : catch2/reporters/catch_reporter_sonarqube.cpp.i

catch2/reporters/catch_reporter_sonarqube.s: catch2/reporters/catch_reporter_sonarqube.cpp.s
.PHONY : catch2/reporters/catch_reporter_sonarqube.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_sonarqube.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_sonarqube.cpp.s
.PHONY : catch2/reporters/catch_reporter_sonarqube.cpp.s

catch2/reporters/catch_reporter_streaming_base.o: catch2/reporters/catch_reporter_streaming_base.cpp.o
.PHONY : catch2/reporters/catch_reporter_streaming_base.o

# target to build an object file
catch2/reporters/catch_reporter_streaming_base.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.o
.PHONY : catch2/reporters/catch_reporter_streaming_base.cpp.o

catch2/reporters/catch_reporter_streaming_base.i: catch2/reporters/catch_reporter_streaming_base.cpp.i
.PHONY : catch2/reporters/catch_reporter_streaming_base.i

# target to preprocess a source file
catch2/reporters/catch_reporter_streaming_base.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.i
.PHONY : catch2/reporters/catch_reporter_streaming_base.cpp.i

catch2/reporters/catch_reporter_streaming_base.s: catch2/reporters/catch_reporter_streaming_base.cpp.s
.PHONY : catch2/reporters/catch_reporter_streaming_base.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_streaming_base.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_streaming_base.cpp.s
.PHONY : catch2/reporters/catch_reporter_streaming_base.cpp.s

catch2/reporters/catch_reporter_tap.o: catch2/reporters/catch_reporter_tap.cpp.o
.PHONY : catch2/reporters/catch_reporter_tap.o

# target to build an object file
catch2/reporters/catch_reporter_tap.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.o
.PHONY : catch2/reporters/catch_reporter_tap.cpp.o

catch2/reporters/catch_reporter_tap.i: catch2/reporters/catch_reporter_tap.cpp.i
.PHONY : catch2/reporters/catch_reporter_tap.i

# target to preprocess a source file
catch2/reporters/catch_reporter_tap.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.i
.PHONY : catch2/reporters/catch_reporter_tap.cpp.i

catch2/reporters/catch_reporter_tap.s: catch2/reporters/catch_reporter_tap.cpp.s
.PHONY : catch2/reporters/catch_reporter_tap.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_tap.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_tap.cpp.s
.PHONY : catch2/reporters/catch_reporter_tap.cpp.s

catch2/reporters/catch_reporter_teamcity.o: catch2/reporters/catch_reporter_teamcity.cpp.o
.PHONY : catch2/reporters/catch_reporter_teamcity.o

# target to build an object file
catch2/reporters/catch_reporter_teamcity.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.o
.PHONY : catch2/reporters/catch_reporter_teamcity.cpp.o

catch2/reporters/catch_reporter_teamcity.i: catch2/reporters/catch_reporter_teamcity.cpp.i
.PHONY : catch2/reporters/catch_reporter_teamcity.i

# target to preprocess a source file
catch2/reporters/catch_reporter_teamcity.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.i
.PHONY : catch2/reporters/catch_reporter_teamcity.cpp.i

catch2/reporters/catch_reporter_teamcity.s: catch2/reporters/catch_reporter_teamcity.cpp.s
.PHONY : catch2/reporters/catch_reporter_teamcity.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_teamcity.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_teamcity.cpp.s
.PHONY : catch2/reporters/catch_reporter_teamcity.cpp.s

catch2/reporters/catch_reporter_xml.o: catch2/reporters/catch_reporter_xml.cpp.o
.PHONY : catch2/reporters/catch_reporter_xml.o

# target to build an object file
catch2/reporters/catch_reporter_xml.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.o
.PHONY : catch2/reporters/catch_reporter_xml.cpp.o

catch2/reporters/catch_reporter_xml.i: catch2/reporters/catch_reporter_xml.cpp.i
.PHONY : catch2/reporters/catch_reporter_xml.i

# target to preprocess a source file
catch2/reporters/catch_reporter_xml.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.i
.PHONY : catch2/reporters/catch_reporter_xml.cpp.i

catch2/reporters/catch_reporter_xml.s: catch2/reporters/catch_reporter_xml.cpp.s
.PHONY : catch2/reporters/catch_reporter_xml.s

# target to generate assembly for a file
catch2/reporters/catch_reporter_xml.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/build.make 3rdparty/Catch2/src/CMakeFiles/Catch2.dir/catch2/reporters/catch_reporter_xml.cpp.s
.PHONY : catch2/reporters/catch_reporter_xml.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... Catch2"
	@echo "... Catch2WithMain"
	@echo "... catch2/benchmark/catch_chronometer.o"
	@echo "... catch2/benchmark/catch_chronometer.i"
	@echo "... catch2/benchmark/catch_chronometer.s"
	@echo "... catch2/benchmark/detail/catch_analyse.o"
	@echo "... catch2/benchmark/detail/catch_analyse.i"
	@echo "... catch2/benchmark/detail/catch_analyse.s"
	@echo "... catch2/benchmark/detail/catch_benchmark_function.o"
	@echo "... catch2/benchmark/detail/catch_benchmark_function.i"
	@echo "... catch2/benchmark/detail/catch_benchmark_function.s"
	@echo "... catch2/benchmark/detail/catch_run_for_at_least.o"
	@echo "... catch2/benchmark/detail/catch_run_for_at_least.i"
	@echo "... catch2/benchmark/detail/catch_run_for_at_least.s"
	@echo "... catch2/benchmark/detail/catch_stats.o"
	@echo "... catch2/benchmark/detail/catch_stats.i"
	@echo "... catch2/benchmark/detail/catch_stats.s"
	@echo "... catch2/catch_approx.o"
	@echo "... catch2/catch_approx.i"
	@echo "... catch2/catch_approx.s"
	@echo "... catch2/catch_assertion_result.o"
	@echo "... catch2/catch_assertion_result.i"
	@echo "... catch2/catch_assertion_result.s"
	@echo "... catch2/catch_config.o"
	@echo "... catch2/catch_config.i"
	@echo "... catch2/catch_config.s"
	@echo "... catch2/catch_get_random_seed.o"
	@echo "... catch2/catch_get_random_seed.i"
	@echo "... catch2/catch_get_random_seed.s"
	@echo "... catch2/catch_message.o"
	@echo "... catch2/catch_message.i"
	@echo "... catch2/catch_message.s"
	@echo "... catch2/catch_registry_hub.o"
	@echo "... catch2/catch_registry_hub.i"
	@echo "... catch2/catch_registry_hub.s"
	@echo "... catch2/catch_session.o"
	@echo "... catch2/catch_session.i"
	@echo "... catch2/catch_session.s"
	@echo "... catch2/catch_tag_alias_autoregistrar.o"
	@echo "... catch2/catch_tag_alias_autoregistrar.i"
	@echo "... catch2/catch_tag_alias_autoregistrar.s"
	@echo "... catch2/catch_test_case_info.o"
	@echo "... catch2/catch_test_case_info.i"
	@echo "... catch2/catch_test_case_info.s"
	@echo "... catch2/catch_test_spec.o"
	@echo "... catch2/catch_test_spec.i"
	@echo "... catch2/catch_test_spec.s"
	@echo "... catch2/catch_timer.o"
	@echo "... catch2/catch_timer.i"
	@echo "... catch2/catch_timer.s"
	@echo "... catch2/catch_tostring.o"
	@echo "... catch2/catch_tostring.i"
	@echo "... catch2/catch_tostring.s"
	@echo "... catch2/catch_totals.o"
	@echo "... catch2/catch_totals.i"
	@echo "... catch2/catch_totals.s"
	@echo "... catch2/catch_translate_exception.o"
	@echo "... catch2/catch_translate_exception.i"
	@echo "... catch2/catch_translate_exception.s"
	@echo "... catch2/catch_version.o"
	@echo "... catch2/catch_version.i"
	@echo "... catch2/catch_version.s"
	@echo "... catch2/generators/catch_generator_exception.o"
	@echo "... catch2/generators/catch_generator_exception.i"
	@echo "... catch2/generators/catch_generator_exception.s"
	@echo "... catch2/generators/catch_generators.o"
	@echo "... catch2/generators/catch_generators.i"
	@echo "... catch2/generators/catch_generators.s"
	@echo "... catch2/generators/catch_generators_random.o"
	@echo "... catch2/generators/catch_generators_random.i"
	@echo "... catch2/generators/catch_generators_random.s"
	@echo "... catch2/interfaces/catch_interfaces_capture.o"
	@echo "... catch2/interfaces/catch_interfaces_capture.i"
	@echo "... catch2/interfaces/catch_interfaces_capture.s"
	@echo "... catch2/interfaces/catch_interfaces_config.o"
	@echo "... catch2/interfaces/catch_interfaces_config.i"
	@echo "... catch2/interfaces/catch_interfaces_config.s"
	@echo "... catch2/interfaces/catch_interfaces_exception.o"
	@echo "... catch2/interfaces/catch_interfaces_exception.i"
	@echo "... catch2/interfaces/catch_interfaces_exception.s"
	@echo "... catch2/interfaces/catch_interfaces_generatortracker.o"
	@echo "... catch2/interfaces/catch_interfaces_generatortracker.i"
	@echo "... catch2/interfaces/catch_interfaces_generatortracker.s"
	@echo "... catch2/interfaces/catch_interfaces_registry_hub.o"
	@echo "... catch2/interfaces/catch_interfaces_registry_hub.i"
	@echo "... catch2/interfaces/catch_interfaces_registry_hub.s"
	@echo "... catch2/interfaces/catch_interfaces_reporter.o"
	@echo "... catch2/interfaces/catch_interfaces_reporter.i"
	@echo "... catch2/interfaces/catch_interfaces_reporter.s"
	@echo "... catch2/interfaces/catch_interfaces_reporter_factory.o"
	@echo "... catch2/interfaces/catch_interfaces_reporter_factory.i"
	@echo "... catch2/interfaces/catch_interfaces_reporter_factory.s"
	@echo "... catch2/interfaces/catch_interfaces_testcase.o"
	@echo "... catch2/interfaces/catch_interfaces_testcase.i"
	@echo "... catch2/interfaces/catch_interfaces_testcase.s"
	@echo "... catch2/internal/catch_assertion_handler.o"
	@echo "... catch2/internal/catch_assertion_handler.i"
	@echo "... catch2/internal/catch_assertion_handler.s"
	@echo "... catch2/internal/catch_case_insensitive_comparisons.o"
	@echo "... catch2/internal/catch_case_insensitive_comparisons.i"
	@echo "... catch2/internal/catch_case_insensitive_comparisons.s"
	@echo "... catch2/internal/catch_clara.o"
	@echo "... catch2/internal/catch_clara.i"
	@echo "... catch2/internal/catch_clara.s"
	@echo "... catch2/internal/catch_commandline.o"
	@echo "... catch2/internal/catch_commandline.i"
	@echo "... catch2/internal/catch_commandline.s"
	@echo "... catch2/internal/catch_console_colour.o"
	@echo "... catch2/internal/catch_console_colour.i"
	@echo "... catch2/internal/catch_console_colour.s"
	@echo "... catch2/internal/catch_context.o"
	@echo "... catch2/internal/catch_context.i"
	@echo "... catch2/internal/catch_context.s"
	@echo "... catch2/internal/catch_debug_console.o"
	@echo "... catch2/internal/catch_debug_console.i"
	@echo "... catch2/internal/catch_debug_console.s"
	@echo "... catch2/internal/catch_debugger.o"
	@echo "... catch2/internal/catch_debugger.i"
	@echo "... catch2/internal/catch_debugger.s"
	@echo "... catch2/internal/catch_decomposer.o"
	@echo "... catch2/internal/catch_decomposer.i"
	@echo "... catch2/internal/catch_decomposer.s"
	@echo "... catch2/internal/catch_enforce.o"
	@echo "... catch2/internal/catch_enforce.i"
	@echo "... catch2/internal/catch_enforce.s"
	@echo "... catch2/internal/catch_enum_values_registry.o"
	@echo "... catch2/internal/catch_enum_values_registry.i"
	@echo "... catch2/internal/catch_enum_values_registry.s"
	@echo "... catch2/internal/catch_errno_guard.o"
	@echo "... catch2/internal/catch_errno_guard.i"
	@echo "... catch2/internal/catch_errno_guard.s"
	@echo "... catch2/internal/catch_exception_translator_registry.o"
	@echo "... catch2/internal/catch_exception_translator_registry.i"
	@echo "... catch2/internal/catch_exception_translator_registry.s"
	@echo "... catch2/internal/catch_fatal_condition_handler.o"
	@echo "... catch2/internal/catch_fatal_condition_handler.i"
	@echo "... catch2/internal/catch_fatal_condition_handler.s"
	@echo "... catch2/internal/catch_floating_point_helpers.o"
	@echo "... catch2/internal/catch_floating_point_helpers.i"
	@echo "... catch2/internal/catch_floating_point_helpers.s"
	@echo "... catch2/internal/catch_getenv.o"
	@echo "... catch2/internal/catch_getenv.i"
	@echo "... catch2/internal/catch_getenv.s"
	@echo "... catch2/internal/catch_istream.o"
	@echo "... catch2/internal/catch_istream.i"
	@echo "... catch2/internal/catch_istream.s"
	@echo "... catch2/internal/catch_jsonwriter.o"
	@echo "... catch2/internal/catch_jsonwriter.i"
	@echo "... catch2/internal/catch_jsonwriter.s"
	@echo "... catch2/internal/catch_lazy_expr.o"
	@echo "... catch2/internal/catch_lazy_expr.i"
	@echo "... catch2/internal/catch_lazy_expr.s"
	@echo "... catch2/internal/catch_leak_detector.o"
	@echo "... catch2/internal/catch_leak_detector.i"
	@echo "... catch2/internal/catch_leak_detector.s"
	@echo "... catch2/internal/catch_list.o"
	@echo "... catch2/internal/catch_list.i"
	@echo "... catch2/internal/catch_list.s"
	@echo "... catch2/internal/catch_main.o"
	@echo "... catch2/internal/catch_main.i"
	@echo "... catch2/internal/catch_main.s"
	@echo "... catch2/internal/catch_message_info.o"
	@echo "... catch2/internal/catch_message_info.i"
	@echo "... catch2/internal/catch_message_info.s"
	@echo "... catch2/internal/catch_output_redirect.o"
	@echo "... catch2/internal/catch_output_redirect.i"
	@echo "... catch2/internal/catch_output_redirect.s"
	@echo "... catch2/internal/catch_parse_numbers.o"
	@echo "... catch2/internal/catch_parse_numbers.i"
	@echo "... catch2/internal/catch_parse_numbers.s"
	@echo "... catch2/internal/catch_polyfills.o"
	@echo "... catch2/internal/catch_polyfills.i"
	@echo "... catch2/internal/catch_polyfills.s"
	@echo "... catch2/internal/catch_random_number_generator.o"
	@echo "... catch2/internal/catch_random_number_generator.i"
	@echo "... catch2/internal/catch_random_number_generator.s"
	@echo "... catch2/internal/catch_random_seed_generation.o"
	@echo "... catch2/internal/catch_random_seed_generation.i"
	@echo "... catch2/internal/catch_random_seed_generation.s"
	@echo "... catch2/internal/catch_reporter_registry.o"
	@echo "... catch2/internal/catch_reporter_registry.i"
	@echo "... catch2/internal/catch_reporter_registry.s"
	@echo "... catch2/internal/catch_reporter_spec_parser.o"
	@echo "... catch2/internal/catch_reporter_spec_parser.i"
	@echo "... catch2/internal/catch_reporter_spec_parser.s"
	@echo "... catch2/internal/catch_reusable_string_stream.o"
	@echo "... catch2/internal/catch_reusable_string_stream.i"
	@echo "... catch2/internal/catch_reusable_string_stream.s"
	@echo "... catch2/internal/catch_run_context.o"
	@echo "... catch2/internal/catch_run_context.i"
	@echo "... catch2/internal/catch_run_context.s"
	@echo "... catch2/internal/catch_section.o"
	@echo "... catch2/internal/catch_section.i"
	@echo "... catch2/internal/catch_section.s"
	@echo "... catch2/internal/catch_singletons.o"
	@echo "... catch2/internal/catch_singletons.i"
	@echo "... catch2/internal/catch_singletons.s"
	@echo "... catch2/internal/catch_source_line_info.o"
	@echo "... catch2/internal/catch_source_line_info.i"
	@echo "... catch2/internal/catch_source_line_info.s"
	@echo "... catch2/internal/catch_startup_exception_registry.o"
	@echo "... catch2/internal/catch_startup_exception_registry.i"
	@echo "... catch2/internal/catch_startup_exception_registry.s"
	@echo "... catch2/internal/catch_stdstreams.o"
	@echo "... catch2/internal/catch_stdstreams.i"
	@echo "... catch2/internal/catch_stdstreams.s"
	@echo "... catch2/internal/catch_string_manip.o"
	@echo "... catch2/internal/catch_string_manip.i"
	@echo "... catch2/internal/catch_string_manip.s"
	@echo "... catch2/internal/catch_stringref.o"
	@echo "... catch2/internal/catch_stringref.i"
	@echo "... catch2/internal/catch_stringref.s"
	@echo "... catch2/internal/catch_tag_alias_registry.o"
	@echo "... catch2/internal/catch_tag_alias_registry.i"
	@echo "... catch2/internal/catch_tag_alias_registry.s"
	@echo "... catch2/internal/catch_test_case_info_hasher.o"
	@echo "... catch2/internal/catch_test_case_info_hasher.i"
	@echo "... catch2/internal/catch_test_case_info_hasher.s"
	@echo "... catch2/internal/catch_test_case_registry_impl.o"
	@echo "... catch2/internal/catch_test_case_registry_impl.i"
	@echo "... catch2/internal/catch_test_case_registry_impl.s"
	@echo "... catch2/internal/catch_test_case_tracker.o"
	@echo "... catch2/internal/catch_test_case_tracker.i"
	@echo "... catch2/internal/catch_test_case_tracker.s"
	@echo "... catch2/internal/catch_test_failure_exception.o"
	@echo "... catch2/internal/catch_test_failure_exception.i"
	@echo "... catch2/internal/catch_test_failure_exception.s"
	@echo "... catch2/internal/catch_test_registry.o"
	@echo "... catch2/internal/catch_test_registry.i"
	@echo "... catch2/internal/catch_test_registry.s"
	@echo "... catch2/internal/catch_test_spec_parser.o"
	@echo "... catch2/internal/catch_test_spec_parser.i"
	@echo "... catch2/internal/catch_test_spec_parser.s"
	@echo "... catch2/internal/catch_textflow.o"
	@echo "... catch2/internal/catch_textflow.i"
	@echo "... catch2/internal/catch_textflow.s"
	@echo "... catch2/internal/catch_uncaught_exceptions.o"
	@echo "... catch2/internal/catch_uncaught_exceptions.i"
	@echo "... catch2/internal/catch_uncaught_exceptions.s"
	@echo "... catch2/internal/catch_wildcard_pattern.o"
	@echo "... catch2/internal/catch_wildcard_pattern.i"
	@echo "... catch2/internal/catch_wildcard_pattern.s"
	@echo "... catch2/internal/catch_xmlwriter.o"
	@echo "... catch2/internal/catch_xmlwriter.i"
	@echo "... catch2/internal/catch_xmlwriter.s"
	@echo "... catch2/matchers/catch_matchers.o"
	@echo "... catch2/matchers/catch_matchers.i"
	@echo "... catch2/matchers/catch_matchers.s"
	@echo "... catch2/matchers/catch_matchers_container_properties.o"
	@echo "... catch2/matchers/catch_matchers_container_properties.i"
	@echo "... catch2/matchers/catch_matchers_container_properties.s"
	@echo "... catch2/matchers/catch_matchers_exception.o"
	@echo "... catch2/matchers/catch_matchers_exception.i"
	@echo "... catch2/matchers/catch_matchers_exception.s"
	@echo "... catch2/matchers/catch_matchers_floating_point.o"
	@echo "... catch2/matchers/catch_matchers_floating_point.i"
	@echo "... catch2/matchers/catch_matchers_floating_point.s"
	@echo "... catch2/matchers/catch_matchers_predicate.o"
	@echo "... catch2/matchers/catch_matchers_predicate.i"
	@echo "... catch2/matchers/catch_matchers_predicate.s"
	@echo "... catch2/matchers/catch_matchers_quantifiers.o"
	@echo "... catch2/matchers/catch_matchers_quantifiers.i"
	@echo "... catch2/matchers/catch_matchers_quantifiers.s"
	@echo "... catch2/matchers/catch_matchers_string.o"
	@echo "... catch2/matchers/catch_matchers_string.i"
	@echo "... catch2/matchers/catch_matchers_string.s"
	@echo "... catch2/matchers/catch_matchers_templated.o"
	@echo "... catch2/matchers/catch_matchers_templated.i"
	@echo "... catch2/matchers/catch_matchers_templated.s"
	@echo "... catch2/matchers/internal/catch_matchers_impl.o"
	@echo "... catch2/matchers/internal/catch_matchers_impl.i"
	@echo "... catch2/matchers/internal/catch_matchers_impl.s"
	@echo "... catch2/reporters/catch_reporter_automake.o"
	@echo "... catch2/reporters/catch_reporter_automake.i"
	@echo "... catch2/reporters/catch_reporter_automake.s"
	@echo "... catch2/reporters/catch_reporter_common_base.o"
	@echo "... catch2/reporters/catch_reporter_common_base.i"
	@echo "... catch2/reporters/catch_reporter_common_base.s"
	@echo "... catch2/reporters/catch_reporter_compact.o"
	@echo "... catch2/reporters/catch_reporter_compact.i"
	@echo "... catch2/reporters/catch_reporter_compact.s"
	@echo "... catch2/reporters/catch_reporter_console.o"
	@echo "... catch2/reporters/catch_reporter_console.i"
	@echo "... catch2/reporters/catch_reporter_console.s"
	@echo "... catch2/reporters/catch_reporter_cumulative_base.o"
	@echo "... catch2/reporters/catch_reporter_cumulative_base.i"
	@echo "... catch2/reporters/catch_reporter_cumulative_base.s"
	@echo "... catch2/reporters/catch_reporter_event_listener.o"
	@echo "... catch2/reporters/catch_reporter_event_listener.i"
	@echo "... catch2/reporters/catch_reporter_event_listener.s"
	@echo "... catch2/reporters/catch_reporter_helpers.o"
	@echo "... catch2/reporters/catch_reporter_helpers.i"
	@echo "... catch2/reporters/catch_reporter_helpers.s"
	@echo "... catch2/reporters/catch_reporter_json.o"
	@echo "... catch2/reporters/catch_reporter_json.i"
	@echo "... catch2/reporters/catch_reporter_json.s"
	@echo "... catch2/reporters/catch_reporter_junit.o"
	@echo "... catch2/reporters/catch_reporter_junit.i"
	@echo "... catch2/reporters/catch_reporter_junit.s"
	@echo "... catch2/reporters/catch_reporter_multi.o"
	@echo "... catch2/reporters/catch_reporter_multi.i"
	@echo "... catch2/reporters/catch_reporter_multi.s"
	@echo "... catch2/reporters/catch_reporter_registrars.o"
	@echo "... catch2/reporters/catch_reporter_registrars.i"
	@echo "... catch2/reporters/catch_reporter_registrars.s"
	@echo "... catch2/reporters/catch_reporter_sonarqube.o"
	@echo "... catch2/reporters/catch_reporter_sonarqube.i"
	@echo "... catch2/reporters/catch_reporter_sonarqube.s"
	@echo "... catch2/reporters/catch_reporter_streaming_base.o"
	@echo "... catch2/reporters/catch_reporter_streaming_base.i"
	@echo "... catch2/reporters/catch_reporter_streaming_base.s"
	@echo "... catch2/reporters/catch_reporter_tap.o"
	@echo "... catch2/reporters/catch_reporter_tap.i"
	@echo "... catch2/reporters/catch_reporter_tap.s"
	@echo "... catch2/reporters/catch_reporter_teamcity.o"
	@echo "... catch2/reporters/catch_reporter_teamcity.i"
	@echo "... catch2/reporters/catch_reporter_teamcity.s"
	@echo "... catch2/reporters/catch_reporter_xml.o"
	@echo "... catch2/reporters/catch_reporter_xml.i"
	@echo "... catch2/reporters/catch_reporter_xml.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

