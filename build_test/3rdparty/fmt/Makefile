# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\" \"fmt-core\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles /home/<USER>/mywork/poco_serverdemo/build_test/3rdparty/fmt//CMakeFiles/progress.marks
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/fmt/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/fmt/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/fmt/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/fmt/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/fmt/CMakeFiles/fmt.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3rdparty/fmt/CMakeFiles/fmt.dir/rule
.PHONY : 3rdparty/fmt/CMakeFiles/fmt.dir/rule

# Convenience name for target.
fmt: 3rdparty/fmt/CMakeFiles/fmt.dir/rule
.PHONY : fmt

# fast build rule for target.
fmt/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/build
.PHONY : fmt/fast

src/format.o: src/format.cc.o
.PHONY : src/format.o

# target to build an object file
src/format.cc.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/src/format.cc.o
.PHONY : src/format.cc.o

src/format.i: src/format.cc.i
.PHONY : src/format.i

# target to preprocess a source file
src/format.cc.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/src/format.cc.i
.PHONY : src/format.cc.i

src/format.s: src/format.cc.s
.PHONY : src/format.s

# target to generate assembly for a file
src/format.cc.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/src/format.cc.s
.PHONY : src/format.cc.s

src/os.o: src/os.cc.o
.PHONY : src/os.o

# target to build an object file
src/os.cc.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/src/os.cc.o
.PHONY : src/os.cc.o

src/os.i: src/os.cc.i
.PHONY : src/os.i

# target to preprocess a source file
src/os.cc.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/src/os.cc.i
.PHONY : src/os.cc.i

src/os.s: src/os.cc.s
.PHONY : src/os.s

# target to generate assembly for a file
src/os.cc.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f 3rdparty/fmt/CMakeFiles/fmt.dir/build.make 3rdparty/fmt/CMakeFiles/fmt.dir/src/os.cc.s
.PHONY : src/os.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... fmt"
	@echo "... src/format.o"
	@echo "... src/format.i"
	@echo "... src/format.s"
	@echo "... src/os.o"
	@echo "... src/os.i"
	@echo "... src/os.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

