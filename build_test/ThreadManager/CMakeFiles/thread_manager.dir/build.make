# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include ThreadManager/CMakeFiles/thread_manager.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include ThreadManager/CMakeFiles/thread_manager.dir/compiler_depend.make

# Include the progress variables for this target.
include ThreadManager/CMakeFiles/thread_manager.dir/progress.make

# Include the compile flags for this target's objects.
include ThreadManager/CMakeFiles/thread_manager.dir/flags.make

ThreadManager/CMakeFiles/thread_manager.dir/codegen:
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/codegen

ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/flags.make
ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp
ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o -MF CMakeFiles/thread_manager.dir/web_server.cpp.o.d -o CMakeFiles/thread_manager.dir/web_server.cpp.o -c /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp

ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/thread_manager.dir/web_server.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp > CMakeFiles/thread_manager.dir/web_server.cpp.i

ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/thread_manager.dir/web_server.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/ThreadManager/web_server.cpp -o CMakeFiles/thread_manager.dir/web_server.cpp.s

ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/flags.make
ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.cpp
ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o -MF CMakeFiles/thread_manager.dir/infer_server.cpp.o.d -o CMakeFiles/thread_manager.dir/infer_server.cpp.o -c /home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.cpp

ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/thread_manager.dir/infer_server.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.cpp > CMakeFiles/thread_manager.dir/infer_server.cpp.i

ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/thread_manager.dir/infer_server.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/ThreadManager/infer_server.cpp -o CMakeFiles/thread_manager.dir/infer_server.cpp.s

ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/flags.make
ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.cpp
ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o -MF CMakeFiles/thread_manager.dir/blkm_server.cpp.o.d -o CMakeFiles/thread_manager.dir/blkm_server.cpp.o -c /home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.cpp

ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/thread_manager.dir/blkm_server.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.cpp > CMakeFiles/thread_manager.dir/blkm_server.cpp.i

ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/thread_manager.dir/blkm_server.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/ThreadManager/blkm_server.cpp -o CMakeFiles/thread_manager.dir/blkm_server.cpp.s

ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/flags.make
ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o: /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.cpp
ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o: ThreadManager/CMakeFiles/thread_manager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o -MF CMakeFiles/thread_manager.dir/lkm_server.cpp.o.d -o CMakeFiles/thread_manager.dir/lkm_server.cpp.o -c /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.cpp

ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/thread_manager.dir/lkm_server.cpp.i"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.cpp > CMakeFiles/thread_manager.dir/lkm_server.cpp.i

ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/thread_manager.dir/lkm_server.cpp.s"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/ThreadManager/lkm_server.cpp -o CMakeFiles/thread_manager.dir/lkm_server.cpp.s

# Object files for target thread_manager
thread_manager_OBJECTS = \
"CMakeFiles/thread_manager.dir/web_server.cpp.o" \
"CMakeFiles/thread_manager.dir/infer_server.cpp.o" \
"CMakeFiles/thread_manager.dir/blkm_server.cpp.o" \
"CMakeFiles/thread_manager.dir/lkm_server.cpp.o"

# External object files for target thread_manager
thread_manager_EXTERNAL_OBJECTS =

bin/lib/libthread_manager.a: ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o
bin/lib/libthread_manager.a: ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o
bin/lib/libthread_manager.a: ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o
bin/lib/libthread_manager.a: ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o
bin/lib/libthread_manager.a: ThreadManager/CMakeFiles/thread_manager.dir/build.make
bin/lib/libthread_manager.a: ThreadManager/CMakeFiles/thread_manager.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library ../bin/lib/libthread_manager.a"
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && $(CMAKE_COMMAND) -P CMakeFiles/thread_manager.dir/cmake_clean_target.cmake
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/thread_manager.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
ThreadManager/CMakeFiles/thread_manager.dir/build: bin/lib/libthread_manager.a
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/build

ThreadManager/CMakeFiles/thread_manager.dir/clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager && $(CMAKE_COMMAND) -P CMakeFiles/thread_manager.dir/cmake_clean.cmake
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/clean

ThreadManager/CMakeFiles/thread_manager.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/ThreadManager /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager/CMakeFiles/thread_manager.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/depend

