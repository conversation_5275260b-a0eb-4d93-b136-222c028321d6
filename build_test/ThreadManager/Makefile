# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\" \"fmt-core\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles /home/<USER>/mywork/poco_serverdemo/build_test/ThreadManager//CMakeFiles/progress.marks
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ThreadManager/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ThreadManager/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ThreadManager/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ThreadManager/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
ThreadManager/CMakeFiles/thread_manager.dir/rule:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ThreadManager/CMakeFiles/thread_manager.dir/rule
.PHONY : ThreadManager/CMakeFiles/thread_manager.dir/rule

# Convenience name for target.
thread_manager: ThreadManager/CMakeFiles/thread_manager.dir/rule
.PHONY : thread_manager

# fast build rule for target.
thread_manager/fast:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/build
.PHONY : thread_manager/fast

blkm_server.o: blkm_server.cpp.o
.PHONY : blkm_server.o

# target to build an object file
blkm_server.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.o
.PHONY : blkm_server.cpp.o

blkm_server.i: blkm_server.cpp.i
.PHONY : blkm_server.i

# target to preprocess a source file
blkm_server.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.i
.PHONY : blkm_server.cpp.i

blkm_server.s: blkm_server.cpp.s
.PHONY : blkm_server.s

# target to generate assembly for a file
blkm_server.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/blkm_server.cpp.s
.PHONY : blkm_server.cpp.s

infer_server.o: infer_server.cpp.o
.PHONY : infer_server.o

# target to build an object file
infer_server.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.o
.PHONY : infer_server.cpp.o

infer_server.i: infer_server.cpp.i
.PHONY : infer_server.i

# target to preprocess a source file
infer_server.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.i
.PHONY : infer_server.cpp.i

infer_server.s: infer_server.cpp.s
.PHONY : infer_server.s

# target to generate assembly for a file
infer_server.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/infer_server.cpp.s
.PHONY : infer_server.cpp.s

lkm_server.o: lkm_server.cpp.o
.PHONY : lkm_server.o

# target to build an object file
lkm_server.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.o
.PHONY : lkm_server.cpp.o

lkm_server.i: lkm_server.cpp.i
.PHONY : lkm_server.i

# target to preprocess a source file
lkm_server.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.i
.PHONY : lkm_server.cpp.i

lkm_server.s: lkm_server.cpp.s
.PHONY : lkm_server.s

# target to generate assembly for a file
lkm_server.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/lkm_server.cpp.s
.PHONY : lkm_server.cpp.s

web_server.o: web_server.cpp.o
.PHONY : web_server.o

# target to build an object file
web_server.cpp.o:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.o
.PHONY : web_server.cpp.o

web_server.i: web_server.cpp.i
.PHONY : web_server.i

# target to preprocess a source file
web_server.cpp.i:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.i
.PHONY : web_server.cpp.i

web_server.s: web_server.cpp.s
.PHONY : web_server.s

# target to generate assembly for a file
web_server.cpp.s:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(MAKE) $(MAKESILENT) -f ThreadManager/CMakeFiles/thread_manager.dir/build.make ThreadManager/CMakeFiles/thread_manager.dir/web_server.cpp.s
.PHONY : web_server.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... thread_manager"
	@echo "... blkm_server.o"
	@echo "... blkm_server.i"
	@echo "... blkm_server.s"
	@echo "... infer_server.o"
	@echo "... infer_server.i"
	@echo "... infer_server.s"
	@echo "... lkm_server.o"
	@echo "... lkm_server.i"
	@echo "... lkm_server.s"
	@echo "... web_server.o"
	@echo "... web_server.i"
	@echo "... web_server.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

