# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include CMakeFiles/tests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/tests.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/tests.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/tests.dir/flags.make

CMakeFiles/tests.dir/codegen:
.PHONY : CMakeFiles/tests.dir/codegen

CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o: CMakeFiles/tests.dir/flags.make
CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/tests/core/video_capture.cpp
CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o: CMakeFiles/tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o -MF CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o.d -o CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/tests/core/video_capture.cpp

CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/tests/core/video_capture.cpp > CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.i

CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/tests/core/video_capture.cpp -o CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.s

CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o: CMakeFiles/tests.dir/flags.make
CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/tests/external/librga.cpp
CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o: CMakeFiles/tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o -MF CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o.d -o CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/tests/external/librga.cpp

CMakeFiles/tests.dir/infer/tests/external/librga.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tests.dir/infer/tests/external/librga.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/tests/external/librga.cpp > CMakeFiles/tests.dir/infer/tests/external/librga.cpp.i

CMakeFiles/tests.dir/infer/tests/external/librga.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tests.dir/infer/tests/external/librga.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/tests/external/librga.cpp -o CMakeFiles/tests.dir/infer/tests/external/librga.cpp.s

# Object files for target tests
tests_OBJECTS = \
"CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o" \
"CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o"

# External object files for target tests
tests_EXTERNAL_OBJECTS =

bin/tests: CMakeFiles/tests.dir/infer/tests/core/video_capture.cpp.o
bin/tests: CMakeFiles/tests.dir/infer/tests/external/librga.cpp.o
bin/tests: CMakeFiles/tests.dir/build.make
bin/tests: CMakeFiles/tests.dir/compiler_depend.ts
bin/tests: bin/lib/libcore.a
bin/tests: bin/lib/libthread_manager.a
bin/tests: bin/lib/libutils.a
bin/tests: bin/lib/libfmt.a
bin/tests: bin/lib/libCatch2Main.a
bin/tests: bin/lib/libcore.a
bin/tests: bin/lib/libbase.a
bin/tests: /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so
bin/tests: /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a
bin/tests: /usr/lib/libopencv_highgui.so.4.11.0
bin/tests: /usr/lib/libopencv_videoio.so.4.11.0
bin/tests: /usr/lib/libopencv_imgcodecs.so.4.11.0
bin/tests: /usr/lib/libopencv_imgproc.so.4.11.0
bin/tests: /usr/lib/libopencv_core.so.4.11.0
bin/tests: bin/lib/libblkmapi.a
bin/tests: /usr/lib/libPocoNetSSL.so.111
bin/tests: /usr/lib/libPocoNet.so.111
bin/tests: /usr/lib/libPocoUtil.so.111
bin/tests: /usr/lib/libPocoJSON.so.111
bin/tests: /usr/lib/libPocoXML.so.111
bin/tests: /usr/lib/libPocoCrypto.so.111
bin/tests: /usr/lib/libPocoFoundation.so.111
bin/tests: /usr/lib/libssl.so
bin/tests: /usr/lib/libcrypto.so
bin/tests: bin/lib/liblkmapi.a
bin/tests: bin/lib/libutils.a
bin/tests: bin/lib/libspdlog.a
bin/tests: bin/lib/libfmt.a
bin/tests: bin/lib/libcserialport.a
bin/tests: bin/lib/libalgorithm_lib.a
bin/tests: /usr/lib/libatomic.so
bin/tests: bin/lib/libCatch2.a
bin/tests: CMakeFiles/tests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable bin/tests"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tests.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/tests.dir/build: bin/tests
.PHONY : CMakeFiles/tests.dir/build

CMakeFiles/tests.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/tests.dir/cmake_clean.cmake
.PHONY : CMakeFiles/tests.dir/clean

CMakeFiles/tests.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/tests.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/tests.dir/depend

