# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include CMakeFiles/bl_infer.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/bl_infer.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/bl_infer.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/bl_infer.dir/flags.make

CMakeFiles/bl_infer.dir/codegen:
.PHONY : CMakeFiles/bl_infer.dir/codegen

CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o: CMakeFiles/bl_infer.dir/flags.make
CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp
CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o: CMakeFiles/bl_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o -MF CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o.d -o CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp

CMakeFiles/bl_infer.dir/infer/infer_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/bl_infer.dir/infer/infer_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp > CMakeFiles/bl_infer.dir/infer/infer_test.cpp.i

CMakeFiles/bl_infer.dir/infer/infer_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/bl_infer.dir/infer/infer_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp -o CMakeFiles/bl_infer.dir/infer/infer_test.cpp.s

# Object files for target bl_infer
bl_infer_OBJECTS = \
"CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o"

# External object files for target bl_infer
bl_infer_EXTERNAL_OBJECTS =

bin/bl_infer: CMakeFiles/bl_infer.dir/infer/infer_test.cpp.o
bin/bl_infer: CMakeFiles/bl_infer.dir/build.make
bin/bl_infer: CMakeFiles/bl_infer.dir/compiler_depend.ts
bin/bl_infer: bin/lib/libutils.a
bin/bl_infer: bin/lib/libthread_manager.a
bin/bl_infer: bin/lib/libalgorithm_lib.a
bin/bl_infer: bin/lib/libspdlog.a
bin/bl_infer: /usr/lib/libatomic.so
bin/bl_infer: bin/lib/libblkmapi.a
bin/bl_infer: /usr/lib/libPocoNetSSL.so.111
bin/bl_infer: /usr/lib/libPocoNet.so.111
bin/bl_infer: /usr/lib/libPocoUtil.so.111
bin/bl_infer: /usr/lib/libPocoJSON.so.111
bin/bl_infer: /usr/lib/libPocoXML.so.111
bin/bl_infer: /usr/lib/libPocoCrypto.so.111
bin/bl_infer: /usr/lib/libPocoFoundation.so.111
bin/bl_infer: /usr/lib/libssl.so
bin/bl_infer: /usr/lib/libcrypto.so
bin/bl_infer: bin/lib/libcore.a
bin/bl_infer: bin/lib/libbase.a
bin/bl_infer: /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so
bin/bl_infer: /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a
bin/bl_infer: /usr/lib/libopencv_highgui.so.4.11.0
bin/bl_infer: /usr/lib/libopencv_videoio.so.4.11.0
bin/bl_infer: /usr/lib/libopencv_imgcodecs.so.4.11.0
bin/bl_infer: /usr/lib/libopencv_imgproc.so.4.11.0
bin/bl_infer: /usr/lib/libopencv_core.so.4.11.0
bin/bl_infer: bin/lib/liblkmapi.a
bin/bl_infer: bin/lib/libutils.a
bin/bl_infer: bin/lib/libspdlog.a
bin/bl_infer: bin/lib/libfmt.a
bin/bl_infer: bin/lib/libalgorithm_lib.a
bin/bl_infer: /usr/lib/libatomic.so
bin/bl_infer: bin/lib/libcserialport.a
bin/bl_infer: CMakeFiles/bl_infer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/bl_infer"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bl_infer.dir/link.txt --verbose=$(VERBOSE)
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "复制 librknnrt.so、RGA库和模型文件到输出目录"
	/usr/bin/cmake -E make_directory /home/<USER>/mywork/poco_serverdemo/build_test/bin/lib
	/usr/bin/cmake -E copy /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so /home/<USER>/mywork/poco_serverdemo/build_test/bin/lib
	/usr/bin/cmake -E copy /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a /home/<USER>/mywork/poco_serverdemo/build_test/bin/lib/
	/usr/bin/cmake -E remove_directory /home/<USER>/mywork/poco_serverdemo/build_test/bin/model
	/usr/bin/cmake -E make_directory /home/<USER>/mywork/poco_serverdemo/build_test/bin/model
	/usr/bin/cmake -E copy_directory /home/<USER>/mywork/poco_serverdemo/infer/model /home/<USER>/mywork/poco_serverdemo/build_test/bin/model

# Rule to build all files generated by this target.
CMakeFiles/bl_infer.dir/build: bin/bl_infer
.PHONY : CMakeFiles/bl_infer.dir/build

CMakeFiles/bl_infer.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/bl_infer.dir/cmake_clean.cmake
.PHONY : CMakeFiles/bl_infer.dir/clean

CMakeFiles/bl_infer.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/bl_infer.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/bl_infer.dir/depend

