# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include CMakeFiles/infer_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/infer_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/infer_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/infer_test.dir/flags.make

CMakeFiles/infer_test.dir/codegen:
.PHONY : CMakeFiles/infer_test.dir/codegen

CMakeFiles/infer_test.dir/infer/infer_test.cpp.o: CMakeFiles/infer_test.dir/flags.make
CMakeFiles/infer_test.dir/infer/infer_test.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp
CMakeFiles/infer_test.dir/infer/infer_test.cpp.o: CMakeFiles/infer_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/infer_test.dir/infer/infer_test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/infer_test.dir/infer/infer_test.cpp.o -MF CMakeFiles/infer_test.dir/infer/infer_test.cpp.o.d -o CMakeFiles/infer_test.dir/infer/infer_test.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp

CMakeFiles/infer_test.dir/infer/infer_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/infer_test.dir/infer/infer_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp > CMakeFiles/infer_test.dir/infer/infer_test.cpp.i

CMakeFiles/infer_test.dir/infer/infer_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/infer_test.dir/infer/infer_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/infer_test.cpp -o CMakeFiles/infer_test.dir/infer/infer_test.cpp.s

# Object files for target infer_test
infer_test_OBJECTS = \
"CMakeFiles/infer_test.dir/infer/infer_test.cpp.o"

# External object files for target infer_test
infer_test_EXTERNAL_OBJECTS =

bin/infer_test: CMakeFiles/infer_test.dir/infer/infer_test.cpp.o
bin/infer_test: CMakeFiles/infer_test.dir/build.make
bin/infer_test: CMakeFiles/infer_test.dir/compiler_depend.ts
bin/infer_test: bin/lib/libthread_manager.a
bin/infer_test: bin/lib/libcore.a
bin/infer_test: bin/lib/libutils.a
bin/infer_test: bin/lib/libfmt.a
bin/infer_test: /usr/lib/libatomic.so
bin/infer_test: bin/lib/libbase.a
bin/infer_test: /home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/linux/librknnrt.so
bin/infer_test: /home/<USER>/mywork/poco_serverdemo/3rdparty/librga/linux/librga.a
bin/infer_test: /usr/lib/libopencv_highgui.so.4.11.0
bin/infer_test: /usr/lib/libopencv_videoio.so.4.11.0
bin/infer_test: /usr/lib/libopencv_imgcodecs.so.4.11.0
bin/infer_test: /usr/lib/libopencv_imgproc.so.4.11.0
bin/infer_test: /usr/lib/libopencv_core.so.4.11.0
bin/infer_test: bin/lib/libblkmapi.a
bin/infer_test: /usr/lib/libPocoNetSSL.so.111
bin/infer_test: /usr/lib/libPocoNet.so.111
bin/infer_test: /usr/lib/libPocoUtil.so.111
bin/infer_test: /usr/lib/libPocoJSON.so.111
bin/infer_test: /usr/lib/libPocoXML.so.111
bin/infer_test: /usr/lib/libPocoCrypto.so.111
bin/infer_test: /usr/lib/libPocoFoundation.so.111
bin/infer_test: /usr/lib/libssl.so
bin/infer_test: /usr/lib/libcrypto.so
bin/infer_test: bin/lib/liblkmapi.a
bin/infer_test: bin/lib/libutils.a
bin/infer_test: bin/lib/libspdlog.a
bin/infer_test: bin/lib/libfmt.a
bin/infer_test: bin/lib/libcserialport.a
bin/infer_test: bin/lib/libalgorithm_lib.a
bin/infer_test: /usr/lib/libatomic.so
bin/infer_test: CMakeFiles/infer_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/infer_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/infer_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/infer_test.dir/build: bin/infer_test
.PHONY : CMakeFiles/infer_test.dir/build

CMakeFiles/infer_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/infer_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/infer_test.dir/clean

CMakeFiles/infer_test.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/infer_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/infer_test.dir/depend

