
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Linux - 6.1.75-rkr3 - aarch64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/4.0.1/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/4.0.1/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6JJCxt"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6JJCxt"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6JJCxt'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_66758/fast
        /usr/bin/make  -f CMakeFiles/cmTC_66758.dir/build.make CMakeFiles/cmTC_66758.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6JJCxt'
        Building C object CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -o CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_66758.dir/'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1 -quiet -v /usr/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_66758.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccJ7kNms.s
        GNU C17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)
        	compiled by GNU C version 14.2.1 20250207, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        warning: MPFR header version 4.2.1 differs from library version 4.2.2.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include
         /usr/local/include
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed
         /usr/include
        End of search list.
        Compiler executable checksum: f2b56bb48c11a63d8426cff87deef4f7
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_66758.dir/'
         as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o /tmp/ccJ7kNms.s
        GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.'
        Linking C executable cmTC_66758
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_66758.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_66758' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_66758.'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cca1aQ5v.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_66758 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        collect2 version 14.2.1 20250207
        /usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cca1aQ5v.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_66758 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_66758' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_66758.'
        /usr/bin/cc  -v -Wl,-v CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o -o cmTC_66758
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6JJCxt'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
          add: [/usr/local/include]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include;/usr/local/include;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6JJCxt']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_66758/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_66758.dir/build.make CMakeFiles/cmTC_66758.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6JJCxt']
        ignore line: [Building C object CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_66758.dir/']
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1 -quiet -v /usr/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_66758.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccJ7kNms.s]
        ignore line: [GNU C17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)]
        ignore line: [	compiled by GNU C version 14.2.1 20250207  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [warning: MPFR header version 4.2.1 differs from library version 4.2.2.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: f2b56bb48c11a63d8426cff87deef4f7]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_66758.dir/']
        ignore line: [ as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o /tmp/ccJ7kNms.s]
        ignore line: [GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o' '-c' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_66758]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_66758.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_66758' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_66758.']
        link line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cca1aQ5v.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_66758 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cca1aQ5v.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [--fix-cortex-a53-835769] ==> ignore
          arg [--fix-cortex-a53-843419] ==> ignore
          arg [-pie] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_66758] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        ignore line: [collect2 version 14.2.1 20250207]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cca1aQ5v.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_66758 /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_66758.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        linker tool for 'C': /usr/bin/ld
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> [/usr/lib/Scrt1.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> [/usr/lib/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> [/usr/lib/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/usr/lib/Scrt1.o;/usr/lib/crti.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o;/usr/lib/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1;/usr/lib;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils) 2.44
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PW9dSl"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PW9dSl"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PW9dSl'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2000f/fast
        /usr/bin/make  -f CMakeFiles/cmTC_2000f.dir/build.make CMakeFiles/cmTC_2000f.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PW9dSl'
        Building CXX object CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2000f.dir/'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1plus -quiet -v -D_GNU_SOURCE /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_2000f.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccsjJDpl.s
        GNU C++17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)
        	compiled by GNU C version 14.2.1 20250207, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        warning: MPFR header version 4.2.1 differs from library version 4.2.2.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include
         /usr/local/include
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed
         /usr/include
        End of search list.
        Compiler executable checksum: f3b3ed6123a07cfdf28e9e3ba61a9363
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2000f.dir/'
         as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccsjJDpl.s
        GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_2000f
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2000f.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper
        Target: aarch64-unknown-linux-gnu
        Configured with: /build/gcc/src/gcc/configure --enable-languages=c,c++,d,fortran,go,lto,m2,objc,obj-c++,rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20250207 (GCC) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_2000f' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_2000f.'
         /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5qIWID.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2000f /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        collect2 version 14.2.1 20250207
        /usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5qIWID.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2000f /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_2000f' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_2000f.'
        /usr/bin/c++  -v -Wl,-v CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_2000f
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PW9dSl'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
          add: [/usr/local/include]
          add: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1] ==> [/usr/include/c++/14.2.1]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu] ==> [/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward] ==> [/usr/include/c++/14.2.1/backward]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/14.2.1;/usr/include/c++/14.2.1/aarch64-unknown-linux-gnu;/usr/include/c++/14.2.1/backward;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include;/usr/local/include;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PW9dSl']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2000f/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_2000f.dir/build.make CMakeFiles/cmTC_2000f.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PW9dSl']
        ignore line: [Building CXX object CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2000f.dir/']
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/cc1plus -quiet -v -D_GNU_SOURCE /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_2000f.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=armv8-a -mlittle-endian -mabi=lp64 -version -o /tmp/ccsjJDpl.s]
        ignore line: [GNU C++17 (GCC) version 14.2.1 20250207 (aarch64-unknown-linux-gnu)]
        ignore line: [	compiled by GNU C version 14.2.1 20250207  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [warning: MPFR header version 4.2.1 differs from library version 4.2.2.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../aarch64-unknown-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/aarch64-unknown-linux-gnu]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../include/c++/14.2.1/backward]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/include-fixed]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: f3b3ed6123a07cfdf28e9e3ba61a9363]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2000f.dir/']
        ignore line: [ as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccsjJDpl.s]
        ignore line: [GNU assembler version 2.44 (aarch64-unknown-linux-gnu) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_2000f]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2000f.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper]
        ignore line: [Target: aarch64-unknown-linux-gnu]
        ignore line: [Configured with: /build/gcc/src/gcc/configure --enable-languages=c c++ d fortran go lto m2 objc obj-c++ rust --enable-bootstrap --prefix=/usr --libdir=/usr/lib --libexecdir=/usr/lib --mandir=/usr/share/man --infodir=/usr/share/info --with-bugurl=https://github.com/archlinuxarm/PKGBUILDs/issues --with-linker-hash-style=gnu --with-system-zlib --enable-__cxa_atexit --enable-checking=release --enable-clocale=gnu --enable-default-pie --enable-default-ssp --enable-gnu-indirect-function --enable-gnu-unique-object --enable-libstdcxx-backtrace --enable-linker-build-id --enable-lto --enable-plugin --enable-shared --enable-threads=posix --disable-libssp --disable-libstdcxx-pch --disable-multilib --disable-werror --host=aarch64-unknown-linux-gnu --build=aarch64-unknown-linux-gnu --with-arch=armv8-a --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20250207 (GCC) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/:/lib/../lib/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_2000f' '-shared-libgcc' '-march=armv8-a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_2000f.']
        link line: [ /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2 -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5qIWID.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2000f /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cc5qIWID.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [--fix-cortex-a53-835769] ==> ignore
          arg [--fix-cortex-a53-843419] ==> ignore
          arg [-pie] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_2000f] ==> ignore
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o]
          arg [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        ignore line: [collect2 version 14.2.1 20250207]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5qIWID.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-835769 --fix-cortex-a53-843419 -pie -o cmTC_2000f /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1 -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib -L/lib/../lib -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../.. -v CMakeFiles/cmTC_2000f.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o /usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o]
        linker tool for 'CXX': /usr/bin/ld
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/Scrt1.o] ==> [/usr/lib/Scrt1.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crti.o] ==> [/usr/lib/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib/crtn.o] ==> [/usr/lib/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1] ==> [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib/Scrt1.o;/usr/lib/crti.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtbeginS.o;/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1/crtendS.o;/usr/lib/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-unknown-linux-gnu/14.2.1;/usr/lib;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils) 2.44
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "/usr/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "3rdparty/spdlog/CMakeLists.txt:151 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-o0zFW8"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-o0zFW8"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-o0zFW8'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_fdeac/fast
        /usr/bin/make  -f CMakeFiles/cmTC_fdeac.dir/build.make CMakeFiles/cmTC_fdeac.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-o0zFW8'
        Building C object CMakeFiles/cmTC_fdeac.dir/src.c.o
        /usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_fdeac.dir/src.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-o0zFW8/src.c
        Linking C executable cmTC_fdeac
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_fdeac.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_fdeac.dir/src.c.o -o cmTC_fdeac
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-o0zFW8'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "3rdparty/spdlog/CMakeLists.txt:250 (check_symbol_exists)"
    checks:
      - "Looking for fwrite_unlocked"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PJ3ujy"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PJ3ujy"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "HAVE_FWRITE_UNLOCKED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PJ3ujy'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_7de1b/fast
        /usr/bin/make  -f CMakeFiles/cmTC_7de1b.dir/build.make CMakeFiles/cmTC_7de1b.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PJ3ujy'
        Building C object CMakeFiles/cmTC_7de1b.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_7de1b.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PJ3ujy/CheckSymbolExists.c
        Linking C executable cmTC_7de1b
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7de1b.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_7de1b.dir/CheckSymbolExists.c.o -o cmTC_7de1b
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-PJ3ujy'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "3rdparty/Catch2/CMake/CatchMiscFunctions.cmake:12 (check_cxx_compiler_flag)"
      - "3rdparty/Catch2/CMake/CatchMiscFunctions.cmake:119 (add_cxx_flag_if_supported_to_targets)"
      - "3rdparty/Catch2/src/CMakeLists.txt:351 (add_build_reproducibility_settings)"
    checks:
      - "Performing Test HAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-DcE6o8"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-DcE6o8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -O3"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras;/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/CMake"
    buildResult:
      variable: "HAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-DcE6o8'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_af5c1/fast
        /usr/bin/make  -f CMakeFiles/cmTC_af5c1.dir/build.make CMakeFiles/cmTC_af5c1.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-DcE6o8'
        Building CXX object CMakeFiles/cmTC_af5c1.dir/src.cxx.o
        /usr/bin/c++ -DHAVE_FLAG__ffile_prefix_map__home_bred_mywork_poco_serverdemo_3rdparty_Catch2__  -O3  -std=c++20   -ffile-prefix-map=/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/= -o CMakeFiles/cmTC_af5c1.dir/src.cxx.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-DcE6o8/src.cxx
        Linking CXX executable cmTC_af5c1
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_af5c1.dir/link.txt --verbose=1
        /usr/bin/c++  -O3  CMakeFiles/cmTC_af5c1.dir/src.cxx.o -o cmTC_af5c1
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-DcE6o8'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:35 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for clock_gettime"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-WrlcY9"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-WrlcY9"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_CLOCK_GETTIME"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-WrlcY9'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_6aa96/fast
        /usr/bin/make  -f CMakeFiles/cmTC_6aa96.dir/build.make CMakeFiles/cmTC_6aa96.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-WrlcY9'
        Building C object CMakeFiles/cmTC_6aa96.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=clock_gettime -o CMakeFiles/cmTC_6aa96.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-WrlcY9/CheckFunctionExists.c
        Linking C executable cmTC_6aa96
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6aa96.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=clock_gettime CMakeFiles/cmTC_6aa96.dir/CheckFunctionExists.c.o -o cmTC_6aa96
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-WrlcY9'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:36 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pthread_condattr_setclock"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-FVXvIt"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-FVXvIt"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PTHREAD_CONDATTR_SETCLOCK"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-FVXvIt'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_8e9bc/fast
        /usr/bin/make  -f CMakeFiles/cmTC_8e9bc.dir/build.make CMakeFiles/cmTC_8e9bc.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-FVXvIt'
        Building C object CMakeFiles/cmTC_8e9bc.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_condattr_setclock -o CMakeFiles/cmTC_8e9bc.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-FVXvIt/CheckFunctionExists.c
        Linking C executable cmTC_8e9bc
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8e9bc.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_condattr_setclock CMakeFiles/cmTC_8e9bc.dir/CheckFunctionExists.c.o -o cmTC_8e9bc
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-FVXvIt'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:37 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pthread_setname_np"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-sKBSfe"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-sKBSfe"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PTHREAD_SETNAME_NP"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-sKBSfe'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_08441/fast
        /usr/bin/make  -f CMakeFiles/cmTC_08441.dir/build.make CMakeFiles/cmTC_08441.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-sKBSfe'
        Building C object CMakeFiles/cmTC_08441.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_setname_np -o CMakeFiles/cmTC_08441.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-sKBSfe/CheckFunctionExists.c
        Linking C executable cmTC_08441
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_08441.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_setname_np CMakeFiles/cmTC_08441.dir/CheckFunctionExists.c.o -o cmTC_08441
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-sKBSfe'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:38 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pthread_threadid_np"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-uT26Q4"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-uT26Q4"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PTHREAD_THREADID_NP"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-uT26Q4'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_21155/fast
        /usr/bin/make  -f CMakeFiles/cmTC_21155.dir/build.make CMakeFiles/cmTC_21155.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-uT26Q4'
        Building C object CMakeFiles/cmTC_21155.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_threadid_np -o CMakeFiles/cmTC_21155.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-uT26Q4/CheckFunctionExists.c
        Linking C executable cmTC_21155
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_21155.dir/link.txt --verbose=1
        /usr/bin/ld: CMakeFiles/cmTC_21155.dir/CheckFunctionExists.c.o: in function `main':
        CheckFunctionExists.c:(.text+0x10): undefined reference to `pthread_threadid_np'
        collect2: error: ld returned 1 exit status
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_threadid_np CMakeFiles/cmTC_21155.dir/CheckFunctionExists.c.o -o cmTC_21155
        make[1]: *** [CMakeFiles/cmTC_21155.dir/build.make:102: cmTC_21155] Error 1
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-uT26Q4'
        make: *** [Makefile:134: cmTC_21155/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:39 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for eventfd"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HUx48n"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HUx48n"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_EVENTFD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HUx48n'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_bff43/fast
        /usr/bin/make  -f CMakeFiles/cmTC_bff43.dir/build.make CMakeFiles/cmTC_bff43.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HUx48n'
        Building C object CMakeFiles/cmTC_bff43.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=eventfd -o CMakeFiles/cmTC_bff43.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HUx48n/CheckFunctionExists.c
        Linking C executable cmTC_bff43
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bff43.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=eventfd CMakeFiles/cmTC_bff43.dir/CheckFunctionExists.c.o -o cmTC_bff43
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HUx48n'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:40 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for pipe2"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HMrpEM"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HMrpEM"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_PIPE2"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HMrpEM'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_eef3f/fast
        /usr/bin/make  -f CMakeFiles/cmTC_eef3f.dir/build.make CMakeFiles/cmTC_eef3f.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HMrpEM'
        Building C object CMakeFiles/cmTC_eef3f.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pipe2 -o CMakeFiles/cmTC_eef3f.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HMrpEM/CheckFunctionExists.c
        Linking C executable cmTC_eef3f
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_eef3f.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pipe2 CMakeFiles/cmTC_eef3f.dir/CheckFunctionExists.c.o -o cmTC_eef3f
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-HMrpEM'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:41 (check_function_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for syslog"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-742ueS"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-742ueS"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_SYSLOG"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-742ueS'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_155ad/fast
        /usr/bin/make  -f CMakeFiles/cmTC_155ad.dir/build.make CMakeFiles/cmTC_155ad.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-742ueS'
        Building C object CMakeFiles/cmTC_155ad.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=syslog -o CMakeFiles/cmTC_155ad.dir/CheckFunctionExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-742ueS/CheckFunctionExists.c
        Linking C executable cmTC_155ad
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_155ad.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=syslog CMakeFiles/cmTC_155ad.dir/CheckFunctionExists.c.o -o cmTC_155ad
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-742ueS'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFiles.cmake:141 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:43 (check_include_files)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for include file asm/types.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-nE5EMd"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-nE5EMd"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_ASM_TYPES_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-nE5EMd'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_a7545/fast
        /usr/bin/make  -f CMakeFiles/cmTC_a7545.dir/build.make CMakeFiles/cmTC_a7545.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-nE5EMd'
        Building C object CMakeFiles/cmTC_a7545.dir/HAVE_ASM_TYPES_H.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_a7545.dir/HAVE_ASM_TYPES_H.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-nE5EMd/HAVE_ASM_TYPES_H.c
        Linking C executable cmTC_a7545
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a7545.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_a7545.dir/HAVE_ASM_TYPES_H.c.o -o cmTC_a7545
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-nE5EMd'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFiles.cmake:141 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:45 (check_include_files)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for include file string.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-8eo16T"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-8eo16T"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_STRING_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-8eo16T'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_e5f19/fast
        /usr/bin/make  -f CMakeFiles/cmTC_e5f19.dir/build.make CMakeFiles/cmTC_e5f19.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-8eo16T'
        Building C object CMakeFiles/cmTC_e5f19.dir/HAVE_STRING_H.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_e5f19.dir/HAVE_STRING_H.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-8eo16T/HAVE_STRING_H.c
        Linking C executable cmTC_e5f19
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e5f19.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_e5f19.dir/HAVE_STRING_H.c.o -o cmTC_e5f19
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-8eo16T'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFiles.cmake:141 (try_compile)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:46 (check_include_files)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for include file sys/time.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-pN5E3r"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-pN5E3r"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_SYS_TIME_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-pN5E3r'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_a9453/fast
        /usr/bin/make  -f CMakeFiles/cmTC_a9453.dir/build.make CMakeFiles/cmTC_a9453.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-pN5E3r'
        Building C object CMakeFiles/cmTC_a9453.dir/HAVE_SYS_TIME_H.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_a9453.dir/HAVE_SYS_TIME_H.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-pN5E3r/HAVE_SYS_TIME_H.c
        Linking C executable cmTC_a9453
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a9453.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_a9453.dir/HAVE_SYS_TIME_H.c.o -o cmTC_a9453
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-pN5E3r'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:48 (check_symbol_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for timerfd_create"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-TgwbAC"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-TgwbAC"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_TIMERFD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-TgwbAC'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_5096e/fast
        /usr/bin/make  -f CMakeFiles/cmTC_5096e.dir/build.make CMakeFiles/cmTC_5096e.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-TgwbAC'
        Building C object CMakeFiles/cmTC_5096e.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_5096e.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-TgwbAC/CheckSymbolExists.c
        Linking C executable cmTC_5096e
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5096e.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_5096e.dir/CheckSymbolExists.c.o -o cmTC_5096e
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-TgwbAC'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:49 (check_symbol_exists)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Looking for nfds_t"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_NFDS_T"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2ac25/fast
        /usr/bin/make  -f CMakeFiles/cmTC_2ac25.dir/build.make CMakeFiles/cmTC_2ac25.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv'
        Building C object CMakeFiles/cmTC_2ac25.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_2ac25.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv/CheckSymbolExists.c
        /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv/CheckSymbolExists.c: In function ‘main’:
        /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv/CheckSymbolExists.c:8:19: error: expected expression before ‘nfds_t’
            8 |   return ((int*)(&nfds_t))[argc];
              |                   ^~~~~~
        make[1]: *** [CMakeFiles/cmTC_2ac25.dir/build.make:81: CMakeFiles/cmTC_2ac25.dir/CheckSymbolExists.c.o] Error 1
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-6UFghv'
        make: *** [Makefile:134: cmTC_2ac25/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:51 (check_struct_has_member)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Performing Test HAVE_STRUCT_TIMESPEC"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-ooCwVk"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-ooCwVk"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_STRUCT_TIMESPEC"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-ooCwVk'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b12d8/fast
        /usr/bin/make  -f CMakeFiles/cmTC_b12d8.dir/build.make CMakeFiles/cmTC_b12d8.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-ooCwVk'
        Building C object CMakeFiles/cmTC_b12d8.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_TIMESPEC   -o CMakeFiles/cmTC_b12d8.dir/src.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-ooCwVk/src.c
        Linking C executable cmTC_b12d8
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b12d8.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_b12d8.dir/src.c.o -o cmTC_b12d8
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-ooCwVk'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:74 (check_c_compiler_flag)"
      - "3rdparty/libusb-cmake/CMakeLists.txt:108 (generate_config_file)"
    checks:
      - "Performing Test HAVE_VISIBILITY"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-aKK85p"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-aKK85p"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/extras"
    buildResult:
      variable: "HAVE_VISIBILITY"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-aKK85p'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_eaebe/fast
        /usr/bin/make  -f CMakeFiles/cmTC_eaebe.dir/build.make CMakeFiles/cmTC_eaebe.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-aKK85p'
        Building C object CMakeFiles/cmTC_eaebe.dir/src.c.o
        /usr/bin/cc -DHAVE_VISIBILITY  -fvisibility=hidden -o CMakeFiles/cmTC_eaebe.dir/src.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-aKK85p/src.c
        Linking C executable cmTC_eaebe
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_eaebe.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_eaebe.dir/src.c.o -o cmTC_eaebe
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-aKK85p'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "/usr/share/cmake/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "backend/CMakeLists.txt:17 (CHECK_CXX_COMPILER_FLAG)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_MARCH_NATIVE"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SqoFdA"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SqoFdA"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -O3"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_MARCH_NATIVE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SqoFdA'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_9ea46/fast
        /usr/bin/make  -f CMakeFiles/cmTC_9ea46.dir/build.make CMakeFiles/cmTC_9ea46.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SqoFdA'
        Building CXX object CMakeFiles/cmTC_9ea46.dir/src.cxx.o
        /usr/bin/c++ -DCOMPILER_SUPPORTS_MARCH_NATIVE  -O3  -std=c++17   -march=native -o CMakeFiles/cmTC_9ea46.dir/src.cxx.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SqoFdA/src.cxx
        Linking CXX executable cmTC_9ea46
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_9ea46.dir/link.txt --verbose=1
        /usr/bin/c++  -O3  CMakeFiles/cmTC_9ea46.dir/src.cxx.o -o cmTC_9ea46
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SqoFdA'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "backend/CMakeLists.txt:119 (CHECK_INCLUDE_FILE)"
    checks:
      - "Looking for netinet/in.h"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-Hb5uCb"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-Hb5uCb"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "HAVE_NETINET_IN_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-Hb5uCb'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f9782/fast
        /usr/bin/make  -f CMakeFiles/cmTC_f9782.dir/build.make CMakeFiles/cmTC_f9782.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-Hb5uCb'
        Building C object CMakeFiles/cmTC_f9782.dir/CheckIncludeFile.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_f9782.dir/CheckIncludeFile.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-Hb5uCb/CheckIncludeFile.c
        Linking C executable cmTC_f9782
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f9782.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_f9782.dir/CheckIncludeFile.c.o -o cmTC_f9782
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-Hb5uCb'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "backend/CMakeLists.txt:120 (CHECK_SYMBOL_EXISTS)"
    checks:
      - "Looking for SO_REUSEPORT"
    directories:
      source: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SDhowq"
      binary: "/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SDhowq"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "HAVE_SO_REUSEPORT"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SDhowq'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_cd0df/fast
        /usr/bin/make  -f CMakeFiles/cmTC_cd0df.dir/build.make CMakeFiles/cmTC_cd0df.dir/build
        make[1]: Entering directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SDhowq'
        Building C object CMakeFiles/cmTC_cd0df.dir/CheckSymbolExists.c.o
        /usr/bin/cc    -o CMakeFiles/cmTC_cd0df.dir/CheckSymbolExists.c.o -c /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SDhowq/CheckSymbolExists.c
        Linking C executable cmTC_cd0df
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cd0df.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_cd0df.dir/CheckSymbolExists.c.o -o cmTC_cd0df
        make[1]: Leaving directory '/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/CMakeScratch/TryCompile-SDhowq'
        
      exitCode: 0
...
