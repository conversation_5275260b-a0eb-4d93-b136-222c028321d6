# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DBUILDING_LIBCSERIALPORT

CXX_INCLUDES = -I/home/<USER>/mywork/poco_serverdemo -I/home/<USER>/mywork/poco_serverdemo/utils -I/home/<USER>/mywork/poco_serverdemo/ThreadManager -I/home/<USER>/mywork/poco_serverdemo/infer -I/home/<USER>/mywork/poco_serverdemo/lkm -I/home/<USER>/mywork/poco_serverdemo/algorithm -I/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/nlohmann_json/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/librga/include -I/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src -I/home/<USER>/mywork/poco_serverdemo/3rdparty/CImg

CXX_FLAGS =  -O3 -O3 -DNDEBUG -std=c++20

