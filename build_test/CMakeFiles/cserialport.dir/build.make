# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include CMakeFiles/cserialport.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/cserialport.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/cserialport.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/cserialport.dir/flags.make

CMakeFiles/cserialport.dir/codegen:
.PHONY : CMakeFiles/cserialport.dir/codegen

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o: CMakeFiles/cserialport.dir/flags.make
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPort.cpp
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o: CMakeFiles/cserialport.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o -MF CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o.d -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPort.cpp

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPort.cpp > CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.i

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPort.cpp -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.s

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o: CMakeFiles/cserialport.dir/flags.make
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortBase.cpp
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o: CMakeFiles/cserialport.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o -MF CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o.d -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortBase.cpp

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortBase.cpp > CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.i

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortBase.cpp -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.s

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o: CMakeFiles/cserialport.dir/flags.make
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o: CMakeFiles/cserialport.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o -MF CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o.d -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp > CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.i

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.s

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o: CMakeFiles/cserialport.dir/flags.make
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o: CMakeFiles/cserialport.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o -MF CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o.d -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp > CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.i

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.s

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o: CMakeFiles/cserialport.dir/flags.make
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o: CMakeFiles/cserialport.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o -MF CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o.d -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp > CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.i

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.s

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o: CMakeFiles/cserialport.dir/flags.make
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o: /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp
CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o: CMakeFiles/cserialport.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o -MF CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o.d -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o -c /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp > CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.i

CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp -o CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.s

# Object files for target cserialport
cserialport_OBJECTS = \
"CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o" \
"CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o" \
"CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o" \
"CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o" \
"CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o" \
"CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o"

# External object files for target cserialport
cserialport_EXTERNAL_OBJECTS =

bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o
bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o
bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o
bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o
bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o
bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o
bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/build.make
bin/lib/libcserialport.a: CMakeFiles/cserialport.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX static library bin/lib/libcserialport.a"
	$(CMAKE_COMMAND) -P CMakeFiles/cserialport.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/cserialport.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/cserialport.dir/build: bin/lib/libcserialport.a
.PHONY : CMakeFiles/cserialport.dir/build

CMakeFiles/cserialport.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/cserialport.dir/cmake_clean.cmake
.PHONY : CMakeFiles/cserialport.dir/clean

CMakeFiles/cserialport.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/cserialport.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/cserialport.dir/depend

