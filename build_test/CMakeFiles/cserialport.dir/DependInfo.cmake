
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPort.cpp" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o" "gcc" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortBase.cpp" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o" "gcc" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o" "gcc" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o" "gcc" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o" "gcc" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o.d"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o" "gcc" "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
