file(REMOVE_RECURSE
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPort.cpp.o.d"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortBase.cpp.o.d"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfo.cpp.o.d"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoBase.cpp.o.d"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortInfoUnixBase.cpp.o.d"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o"
  "CMakeFiles/cserialport.dir/3rdparty/CSerialPort-master/src/SerialPortUnixBase.cpp.o.d"
  "bin/lib/libcserialport.a"
  "bin/lib/libcserialport.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/cserialport.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
