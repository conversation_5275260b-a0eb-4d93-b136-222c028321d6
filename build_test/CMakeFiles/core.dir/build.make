# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include CMakeFiles/core.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/core.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/core.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/core.dir/flags.make

CMakeFiles/core.dir/codegen:
.PHONY : CMakeFiles/core.dir/codegen

CMakeFiles/core.dir/infer/core/model/model.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/model/model.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/model/model.cpp
CMakeFiles/core.dir/infer/core/model/model.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/core.dir/infer/core/model/model.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/model/model.cpp.o -MF CMakeFiles/core.dir/infer/core/model/model.cpp.o.d -o CMakeFiles/core.dir/infer/core/model/model.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/model/model.cpp

CMakeFiles/core.dir/infer/core/model/model.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/model/model.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/model/model.cpp > CMakeFiles/core.dir/infer/core/model/model.cpp.i

CMakeFiles/core.dir/infer/core/model/model.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/model/model.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/model/model.cpp -o CMakeFiles/core.dir/infer/core/model/model.cpp.s

CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/model/rknn/model_rknn.cpp
CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o -MF CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o.d -o CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/model/rknn/model_rknn.cpp

CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/model/rknn/model_rknn.cpp > CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.i

CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/model/rknn/model_rknn.cpp -o CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.s

CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v6.cpp
CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o -MF CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o.d -o CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v6.cpp

CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v6.cpp > CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.i

CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v6.cpp -o CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.s

CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v8.cpp
CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o -MF CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o.d -o CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v8.cpp

CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v8.cpp > CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.i

CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo_v8.cpp -o CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.s

CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo.cpp
CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o -MF CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o.d -o CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo.cpp

CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo.cpp > CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.i

CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/model/yolo/yolo.cpp -o CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.s

CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/model/object_detector.cpp
CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o -MF CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o.d -o CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/model/object_detector.cpp

CMakeFiles/core.dir/infer/core/model/object_detector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/model/object_detector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/model/object_detector.cpp > CMakeFiles/core.dir/infer/core/model/object_detector.cpp.i

CMakeFiles/core.dir/infer/core/model/object_detector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/model/object_detector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/model/object_detector.cpp -o CMakeFiles/core.dir/infer/core/model/object_detector.cpp.s

CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/video/video_capture.cpp
CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o -MF CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o.d -o CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/video/video_capture.cpp

CMakeFiles/core.dir/infer/core/video/video_capture.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/video/video_capture.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/video/video_capture.cpp > CMakeFiles/core.dir/infer/core/video/video_capture.cpp.i

CMakeFiles/core.dir/infer/core/video/video_capture.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/video/video_capture.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/video/video_capture.cpp -o CMakeFiles/core.dir/infer/core/video/video_capture.cpp.s

CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_fps.cpp
CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o -MF CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o.d -o CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_fps.cpp

CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_fps.cpp > CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.i

CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_fps.cpp -o CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.s

CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_debug.cpp
CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o -MF CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o.d -o CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_debug.cpp

CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_debug.cpp > CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.i

CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/aimbot_debug.cpp -o CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.s

CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/data_collector.cpp
CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o -MF CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o.d -o CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/data_collector.cpp

CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/data_collector.cpp > CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.i

CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/data_collector.cpp -o CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.s

CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o: CMakeFiles/core.dir/flags.make
CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o: /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/infer_collector.cpp
CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o: CMakeFiles/core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o -MF CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o.d -o CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o -c /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/infer_collector.cpp

CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/infer_collector.cpp > CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.i

CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/infer/core/aimbot/infer_collector.cpp -o CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.s

# Object files for target core
core_OBJECTS = \
"CMakeFiles/core.dir/infer/core/model/model.cpp.o" \
"CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o" \
"CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o" \
"CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o" \
"CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o" \
"CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o" \
"CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o" \
"CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o" \
"CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o" \
"CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o" \
"CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o"

# External object files for target core
core_EXTERNAL_OBJECTS =

bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/model/model.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/model/rknn/model_rknn.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/model/yolo/yolo_v6.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/model/yolo/yolo_v8.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/model/yolo/yolo.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/model/object_detector.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/video/video_capture.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/aimbot/aimbot_fps.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/aimbot/aimbot_debug.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/aimbot/data_collector.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/infer/core/aimbot/infer_collector.cpp.o
bin/lib/libcore.a: CMakeFiles/core.dir/build.make
bin/lib/libcore.a: CMakeFiles/core.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX static library bin/lib/libcore.a"
	$(CMAKE_COMMAND) -P CMakeFiles/core.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/core.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/core.dir/build: bin/lib/libcore.a
.PHONY : CMakeFiles/core.dir/build

CMakeFiles/core.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/core.dir/cmake_clean.cmake
.PHONY : CMakeFiles/core.dir/clean

CMakeFiles/core.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/core.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/core.dir/depend

