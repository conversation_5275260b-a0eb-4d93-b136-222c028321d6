# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include CMakeFiles/blkmapi.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/blkmapi.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/blkmapi.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/blkmapi.dir/flags.make

CMakeFiles/blkmapi.dir/codegen:
.PHONY : CMakeFiles/blkmapi.dir/codegen

CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o: CMakeFiles/blkmapi.dir/flags.make
CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o: /home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.cpp
CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o: CMakeFiles/blkmapi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o -MF CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o.d -o CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o -c /home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.cpp

CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.cpp > CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.i

CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/blkm/blkmsdk.cpp -o CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.s

CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o: CMakeFiles/blkmapi.dir/flags.make
CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o: /home/<USER>/mywork/poco_serverdemo/blkm/crypto_utils.cpp
CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o: CMakeFiles/blkmapi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o -MF CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o.d -o CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o -c /home/<USER>/mywork/poco_serverdemo/blkm/crypto_utils.cpp

CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/blkm/crypto_utils.cpp > CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.i

CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/blkm/crypto_utils.cpp -o CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.s

CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o: CMakeFiles/blkmapi.dir/flags.make
CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o: /home/<USER>/mywork/poco_serverdemo/blkm/cloud_update.cpp
CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o: CMakeFiles/blkmapi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o -MF CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o.d -o CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o -c /home/<USER>/mywork/poco_serverdemo/blkm/cloud_update.cpp

CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/blkm/cloud_update.cpp > CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.i

CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/blkm/cloud_update.cpp -o CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.s

# Object files for target blkmapi
blkmapi_OBJECTS = \
"CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o" \
"CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o" \
"CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o"

# External object files for target blkmapi
blkmapi_EXTERNAL_OBJECTS =

bin/lib/libblkmapi.a: CMakeFiles/blkmapi.dir/blkm/blkmsdk.cpp.o
bin/lib/libblkmapi.a: CMakeFiles/blkmapi.dir/blkm/crypto_utils.cpp.o
bin/lib/libblkmapi.a: CMakeFiles/blkmapi.dir/blkm/cloud_update.cpp.o
bin/lib/libblkmapi.a: CMakeFiles/blkmapi.dir/build.make
bin/lib/libblkmapi.a: CMakeFiles/blkmapi.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library bin/lib/libblkmapi.a"
	$(CMAKE_COMMAND) -P CMakeFiles/blkmapi.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/blkmapi.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/blkmapi.dir/build: bin/lib/libblkmapi.a
.PHONY : CMakeFiles/blkmapi.dir/build

CMakeFiles/blkmapi.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/blkmapi.dir/cmake_clean.cmake
.PHONY : CMakeFiles/blkmapi.dir/clean

CMakeFiles/blkmapi.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/blkmapi.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/blkmapi.dir/depend

