# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mywork/poco_serverdemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mywork/poco_serverdemo/build_test

# Include any dependencies generated for this target.
include CMakeFiles/lkmapi.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/lkmapi.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/lkmapi.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/lkmapi.dir/flags.make

CMakeFiles/lkmapi.dir/codegen:
.PHONY : CMakeFiles/lkmapi.dir/codegen

CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o: CMakeFiles/lkmapi.dir/flags.make
CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o: /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.cpp
CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o: CMakeFiles/lkmapi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o -MF CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o.d -o CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o -c /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.cpp

CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.cpp > CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.i

CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/lkm/LkmApi.cpp -o CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.s

CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o: CMakeFiles/lkmapi.dir/flags.make
CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o: /home/<USER>/mywork/poco_serverdemo/lkm/keymouse.cpp
CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o: CMakeFiles/lkmapi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o -MF CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o.d -o CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o -c /home/<USER>/mywork/poco_serverdemo/lkm/keymouse.cpp

CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/lkm/keymouse.cpp > CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.i

CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/lkm/keymouse.cpp -o CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.s

CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o: CMakeFiles/lkmapi.dir/flags.make
CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o: /home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.cpp
CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o: CMakeFiles/lkmapi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o -MF CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o.d -o CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o -c /home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.cpp

CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.cpp > CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.i

CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/mywork/poco_serverdemo/lkm/KeyMouseConfig.cpp -o CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.s

# Object files for target lkmapi
lkmapi_OBJECTS = \
"CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o" \
"CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o" \
"CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o"

# External object files for target lkmapi
lkmapi_EXTERNAL_OBJECTS =

bin/lib/liblkmapi.a: CMakeFiles/lkmapi.dir/lkm/LkmApi.cpp.o
bin/lib/liblkmapi.a: CMakeFiles/lkmapi.dir/lkm/keymouse.cpp.o
bin/lib/liblkmapi.a: CMakeFiles/lkmapi.dir/lkm/KeyMouseConfig.cpp.o
bin/lib/liblkmapi.a: CMakeFiles/lkmapi.dir/build.make
bin/lib/liblkmapi.a: CMakeFiles/lkmapi.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library bin/lib/liblkmapi.a"
	$(CMAKE_COMMAND) -P CMakeFiles/lkmapi.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lkmapi.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/lkmapi.dir/build: bin/lib/liblkmapi.a
.PHONY : CMakeFiles/lkmapi.dir/build

CMakeFiles/lkmapi.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/lkmapi.dir/cmake_clean.cmake
.PHONY : CMakeFiles/lkmapi.dir/clean

CMakeFiles/lkmapi.dir/depend:
	cd /home/<USER>/mywork/poco_serverdemo/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test /home/<USER>/mywork/poco_serverdemo/build_test/CMakeFiles/lkmapi.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/lkmapi.dir/depend

