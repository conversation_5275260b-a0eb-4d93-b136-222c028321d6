# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CImg/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/CMake/CatchConfigOptions.cmake"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/CMake/CatchMiscFunctions.cmake"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/Catch2/src/catch2/catch_user_config.hpp.in"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/support/cmake/JoinPaths.cmake"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/support/cmake/fmt-config.cmake.in"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/fmt/support/cmake/fmt.pc.in"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/hid-rp/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/librga/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusb-cmake/config.h.in"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/libusbgx/include/usbg/usbg_version.h.in"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/rknn/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/cmake/ide.cmake"
  "/home/<USER>/mywork/poco_serverdemo/3rdparty/spdlog/cmake/utils.cmake"
  "/home/<USER>/mywork/poco_serverdemo/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/ThreadManager/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/algorithm/algorithm.cmake"
  "/home/<USER>/mywork/poco_serverdemo/backend/CMakeLists.txt"
  "/home/<USER>/mywork/poco_serverdemo/blkm/blkm.make"
  "CMakeFiles/4.0.1/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.1/CMakeSystem.cmake"
  "/home/<USER>/mywork/poco_serverdemo/infer/infer.cmake"
  "/home/<USER>/mywork/poco_serverdemo/lkm/lkm.make"
  "/home/<USER>/mywork/poco_serverdemo/utils/utils.cmake"
  "/usr/lib/cmake/Poco/PocoConfig.cmake"
  "/usr/lib/cmake/Poco/PocoConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoCryptoConfig.cmake"
  "/usr/lib/cmake/Poco/PocoCryptoConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoCryptoTargets-release.cmake"
  "/usr/lib/cmake/Poco/PocoCryptoTargets.cmake"
  "/usr/lib/cmake/Poco/PocoFoundationConfig.cmake"
  "/usr/lib/cmake/Poco/PocoFoundationConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoFoundationTargets-release.cmake"
  "/usr/lib/cmake/Poco/PocoFoundationTargets.cmake"
  "/usr/lib/cmake/Poco/PocoJSONConfig.cmake"
  "/usr/lib/cmake/Poco/PocoJSONConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoJSONTargets-release.cmake"
  "/usr/lib/cmake/Poco/PocoJSONTargets.cmake"
  "/usr/lib/cmake/Poco/PocoNetConfig.cmake"
  "/usr/lib/cmake/Poco/PocoNetConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoNetSSLConfig.cmake"
  "/usr/lib/cmake/Poco/PocoNetSSLConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoNetSSLTargets-release.cmake"
  "/usr/lib/cmake/Poco/PocoNetSSLTargets.cmake"
  "/usr/lib/cmake/Poco/PocoNetTargets-release.cmake"
  "/usr/lib/cmake/Poco/PocoNetTargets.cmake"
  "/usr/lib/cmake/Poco/PocoUtilConfig.cmake"
  "/usr/lib/cmake/Poco/PocoUtilConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoUtilTargets-release.cmake"
  "/usr/lib/cmake/Poco/PocoUtilTargets.cmake"
  "/usr/lib/cmake/Poco/PocoXMLConfig.cmake"
  "/usr/lib/cmake/Poco/PocoXMLConfigVersion.cmake"
  "/usr/lib/cmake/Poco/PocoXMLTargets-release.cmake"
  "/usr/lib/cmake/Poco/PocoXMLTargets.cmake"
  "/usr/lib/cmake/fmt/fmt-config-version.cmake"
  "/usr/lib/cmake/fmt/fmt-config.cmake"
  "/usr/lib/cmake/opencv4/OpenCVConfig-version.cmake"
  "/usr/lib/cmake/opencv4/OpenCVConfig.cmake"
  "/usr/lib/cmake/opencv4/OpenCVModules-release.cmake"
  "/usr/lib/cmake/opencv4/OpenCVModules.cmake"
  "/usr/share/cmake/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in"
  "/usr/share/cmake/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake/Modules/CMakeDependentOption.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/share/cmake/Modules/CMakeParseArguments.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/share/cmake/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake/Modules/CheckCCompilerFlag.cmake"
  "/usr/share/cmake/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake/Modules/CheckCXXCompilerFlag.cmake"
  "/usr/share/cmake/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake/Modules/CheckFunctionExists.cmake"
  "/usr/share/cmake/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake/Modules/CheckIncludeFiles.cmake"
  "/usr/share/cmake/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake/Modules/CheckSourceCompiles.cmake"
  "/usr/share/cmake/Modules/CheckStructHasMember.cmake"
  "/usr/share/cmake/Modules/CheckSymbolExists.cmake"
  "/usr/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/FindOpenSSL.cmake"
  "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake/Modules/FindThreads.cmake"
  "/usr/share/cmake/Modules/GNUInstallDirs.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeInspectCXXLinker.cmake"
  "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake"
  "/usr/share/cmake/Modules/Internal/CheckFlagCommonConfig.cmake"
  "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake/Modules/Linker/GNU-C.cmake"
  "/usr/share/cmake/Modules/Linker/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-C.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake/Modules/Platform/Linux.cmake"
  "/usr/share/cmake/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/4.0.1/CMakeSystem.cmake"
  "CMakeFiles/4.0.1/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.1/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.1/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/fmt/fmt-config-version.cmake"
  "3rdparty/fmt/fmt.pc"
  "3rdparty/fmt/fmt-config.cmake"
  "3rdparty/fmt/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/spdlog/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/libusbgx/usbg_version.h"
  "3rdparty/libusbgx/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/librga/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/rknn/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/hid-rp/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/CImg/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/Catch2/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/Catch2/generated-includes/catch2/catch_user_config.hpp"
  "3rdparty/Catch2/src/CMakeFiles/CMakeDirectoryInformation.cmake"
  "3rdparty/libusb-cmake/gen_include/config.h"
  "3rdparty/libusb-cmake/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ThreadManager/CMakeFiles/CMakeDirectoryInformation.cmake"
  "backend/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/cserialport.dir/DependInfo.cmake"
  "CMakeFiles/lkmapi.dir/DependInfo.cmake"
  "CMakeFiles/utils.dir/DependInfo.cmake"
  "CMakeFiles/algorithm_lib.dir/DependInfo.cmake"
  "CMakeFiles/base.dir/DependInfo.cmake"
  "CMakeFiles/core.dir/DependInfo.cmake"
  "CMakeFiles/tests.dir/DependInfo.cmake"
  "CMakeFiles/infer_test.dir/DependInfo.cmake"
  "CMakeFiles/blkmapi.dir/DependInfo.cmake"
  "CMakeFiles/blkm_test.dir/DependInfo.cmake"
  "CMakeFiles/BL.dir/DependInfo.cmake"
  "CMakeFiles/bl_infer.dir/DependInfo.cmake"
  "CMakeFiles/lkm_test.dir/DependInfo.cmake"
  "3rdparty/fmt/CMakeFiles/fmt.dir/DependInfo.cmake"
  "3rdparty/spdlog/CMakeFiles/spdlog.dir/DependInfo.cmake"
  "3rdparty/libusbgx/CMakeFiles/libusbgx.dir/DependInfo.cmake"
  "3rdparty/Catch2/src/CMakeFiles/Catch2.dir/DependInfo.cmake"
  "3rdparty/Catch2/src/CMakeFiles/Catch2WithMain.dir/DependInfo.cmake"
  "3rdparty/libusb-cmake/CMakeFiles/usb-1.0.dir/DependInfo.cmake"
  "ThreadManager/CMakeFiles/thread_manager.dir/DependInfo.cmake"
  "backend/CMakeFiles/blweb_backend.dir/DependInfo.cmake"
  "backend/CMakeFiles/bl_server.dir/DependInfo.cmake"
  )
